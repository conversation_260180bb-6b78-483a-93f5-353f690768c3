package com.sgs.ecom.member.dto.rpc;

import java.math.BigDecimal;

public class InsPriceDTO {
	private BigDecimal price;

	private String originalPrice;

	private String discountPrice;

	private String memo;

	private Integer sampleNums;

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public String getOriginalPrice() {
		return originalPrice;
	}

	public void setOriginalPrice(String originalPrice) {
		this.originalPrice = originalPrice;
	}

	public String getDiscountPrice() {
		return discountPrice;
	}

	public void setDiscountPrice(String discountPrice) {
		this.discountPrice = discountPrice;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public Integer getSampleNums() {
		return sampleNums;
	}

	public void setSampleNums(Integer sampleNums) {
		this.sampleNums = sampleNums;
	}
}
