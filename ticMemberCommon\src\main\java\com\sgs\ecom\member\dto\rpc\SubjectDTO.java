package com.sgs.ecom.member.dto.rpc;

import java.util.List;

public class SubjectDTO {
	private String subjectNo;
	private String questionTitle;
	private int questionType;
	private int sortShow;
	private String partNo;
	private List<OptionDTO> options;

	public String getSubjectNo() {
		return subjectNo;
	}

	public void setSubjectNo(String subjectNo) {
		this.subjectNo = subjectNo;
	}

	public String getQuestionTitle() {
		return questionTitle;
	}

	public void setQuestionTitle(String questionTitle) {
		this.questionTitle = questionTitle;
	}

	public int getQuestionType() {
		return questionType;
	}

	public void setQuestionType(int questionType) {
		this.questionType = questionType;
	}

	public String getPartNo() {
		return partNo;
	}

	public void setPartNo(String partNo) {
		this.partNo = partNo;
	}

	public List<OptionDTO> getOptions() {
		return options;
	}

	public void setOptions(List<OptionDTO> options) {
		this.options = options;
	}

	public int getSortShow() {
		return sortShow;
	}

	public void setSortShow(int sortShow) {
		this.sortShow = sortShow;
	}
}
