package com.sgs.ecom.member.controller.user;

import javax.annotation.Resource;

import com.sgs.ecom.member.dto.user.UserDTO;
import com.sgs.ecom.member.dto.user.UserInfoDTO;
import com.sgs.ecom.member.enumtool.ResultEnumCode;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.ecom.member.service.user.interfaces.IUserPromotionSV;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import com.sgs.ecom.member.util.ResultCode;
import com.sgs.ecom.member.vo.VOUserInfo;
import com.sgs.ecom.member.vo.VOUserPromotion;
import com.sgs.redis.RedisClient;

@RestController
@RequestMapping("/business/api.v2.user/promotion")
@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
public class UserPromotionController extends ControllerUtil {

	@Resource
	private RedisClient redisClient;
	@Resource
	private IUserPromotionSV userPromotionSV;
	
	/**   
	 * 
	* @Function: joinPromotion
	* @Description: 参与推广获得
	* 
   	* @param: req
   	* @param: res
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2022-03-15
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2022-03-15  shenyi    v1.0                 新增
   	*/
    @AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "join", method = { RequestMethod.POST })
	public ResultBody joinPromotion(@RequestBody VOUserPromotion userPromotion,
		@RequestHeader(value="appId") String appId,
		@RequestHeader(value = "operatorSource",defaultValue = "") String operatorSource,
	    @RequestHeader(value="accessToken") String token) throws Exception {
    	String userStr = getUser(token);
    	VOUserInfo user = JSON.parseObject(userStr, VOUserInfo.class);
		userPromotion.setActivitySource(operatorSource);
    	userPromotion.setuId(user.getUId());
    	userPromotion.setUserId(user.getUserId());
    	userPromotion.setUserPhone(user.getUserPhone());
		userPromotion.setUserEmail(user.getUserEmail());
		userPromotion.setAccountType(StringUtils.isBlank(user.getUserPhone())?"email":"phone");
		long time = System.currentTimeMillis();
		String value = String.valueOf(time + 10 * 1000);
		String key = "PROMOTION_" + user.getUserPhone();
	 	boolean flag = redisClient.setLock(key, value, 10);
	 	if (!flag)
	 		throw new BusinessException(ResultEnumCode.USER_PROMOTION_TO_SUBMIT);
	 		
	 	ResultBody body = ResultBody.newInstance(userPromotionSV.joinPromotion(userPromotion, user.getLevelId() == 1 ? true : false));
		redisClient.releaseLock(key, value);
		return body;
   	}

	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "mod", method = { RequestMethod.POST })
	public ResultBody modPromotion(@RequestBody VOUserPromotion userPromotion,
									@RequestHeader(value = "operatorSource",defaultValue = "") String operatorSource,
									@RequestHeader(value="accessToken") String token) throws Exception {
		UserDTO userDTO=getUserDTO(token);
		long time = System.currentTimeMillis();
		String value = String.valueOf(time + 10 * 1000);
		String key = "PROMOTION_" + userDTO.getUserPhone();
		boolean flag = redisClient.setLock(key, value, 10);
		if (!flag)
			throw new BusinessException(ResultCode.SYSTEM_IS_BUSINESS_USER);

		userPromotionSV.modPromotion(userPromotion,userDTO);
		redisClient.releaseLock(key, value);
		return ResultBody.success();
	}


	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "qryPromotion", method = { RequestMethod.POST })
	public ResultBody qryPromotionInfo(@RequestBody VOUserPromotion userPromotion,
								   @RequestHeader(value = "operatorSource",defaultValue = "") String operatorSource,
								   @RequestHeader(value="accessToken") String token) throws Exception {
		return ResultBody.newInstance(userPromotionSV.qryPromotionInfo(userPromotion,getUserDTO(token)));
	}
    
    /**   
	 * 
	* @Function: qryPromotion
	* @Description: 查询参数活动列表
	* 
   	* @param: req
   	* @param: res
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2022-03-15
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2022-03-15  shenyi    v1.0                 新增
   	*/
    @AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "qrySubmit", method = { RequestMethod.POST })
	public ResultBody qryPromotion(@RequestBody VOUserPromotion userPromotion,
		@RequestHeader(value="appId") String appId,
	    @RequestHeader(value="accessToken") String token) throws Exception {
    	userPromotion.setUserId(getUserId(appId, token));
		return ResultBody.newInstance(userPromotionSV.qryUserSubmitPromotion(userPromotion));
   	}
}
