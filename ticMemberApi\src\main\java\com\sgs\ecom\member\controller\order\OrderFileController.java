package com.sgs.ecom.member.controller.order;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.base.BaseBean;
import com.sgs.ecom.member.domain.order.service.interfaces.IOrderAttachmentDomainService;
import com.sgs.ecom.member.request.OrderPayReq;
import com.sgs.ecom.member.request.OrderReportReq;
import com.sgs.ecom.member.request.wine.OrderAttachmentReq;
import com.sgs.ecom.member.service.order.interfaces.IOrderFileService;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.order/file")
public class OrderFileController extends ControllerUtil {
    @Autowired
    private IOrderFileService orderFileService;

    @Autowired
    private IOrderAttachmentDomainService orderAttachmentDomainService;

    /**
     *@Function: fileList
     *@Description 订单的报告或者发票
     *@param: [orderReportReq, token]
     *@author: Xiwei_Qiu
     *@date: 2020/12/18
     **/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "fileList", method = { RequestMethod.POST })
    public ResultBody fileList(
            @RequestBody OrderReportReq orderReportReq,
            @RequestHeader(value="accessToken") String accessToken) throws Exception {
        return ResultBody.newInstance(orderFileService.selectFile(orderReportReq,getUserDTO(accessToken)));
    }

    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "savePayFile", method = { RequestMethod.POST })
    public ResultBody savePayFile(
        @Validated(value = BaseBean.Default.class)
        @RequestBody OrderPayReq orderPayReq,
        @RequestHeader(value="accessToken") String token) throws Exception {
        orderFileService.savePayFile(orderPayReq,getUserDTO(token),token);
        return ResultBody.success();
    }

    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "delListByOrderNo", method = { RequestMethod.POST })
    public ResultBody delListByOrderNo(
            @Validated(value = BaseBean.Update.class)
            @RequestBody OrderAttachmentReq orderAttachmentReq,
            @RequestHeader(value="accessToken") String token) throws Exception {
        orderAttachmentDomainService.delListByOrderNo(orderAttachmentReq.getOrderNo(),orderAttachmentReq.getDeleteList());
        return ResultBody.success();
    }
}
