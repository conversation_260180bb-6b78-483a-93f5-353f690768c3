package com.sgs.ecom.member.controller.user;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.base.BaseBean;
import com.sgs.ecom.member.request.user.UserInvoiceIdReq;
import com.sgs.ecom.member.request.user.UserInvoiceListReq;
import com.sgs.ecom.member.request.user.UserInvoiceReq;
import com.sgs.ecom.member.service.user.interfaces.IUserInvoiceService;
import com.sgs.ecom.member.service.util.interfaces.IUserService;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/business/api.v1.user/invoice")
@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
public class UserInvoiceController extends ControllerUtil {

	@Autowired
	private IUserService userService;
	@Autowired
	private IUserInvoiceService userInvoiceService;

	/**
	*@Function: add
	*@Description 新增发票
	*@param: [token, userInvoiceReq]
	*@author: Xiwei_Qiu @date: 2022/6/8 @version:
	**/
	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "add", method = { RequestMethod.POST })
	public ResultBody add(
		@RequestHeader(value="accessToken") String token,
		@RequestBody UserInvoiceReq userInvoiceReq) throws Exception {
		return ResultBody.newInstance(userService.addInvoice(userInvoiceReq,getUserDTO(token)));
	}

	/**
	*@Function: mod
	*@Description 修改发票
	*@param: [token, userInvoiceReq]
	*@author: Xiwei_Qiu @date: 2022/6/8 @version:
	**/
	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "mod", method = { RequestMethod.POST })
	public ResultBody mod(
		@RequestHeader(value="accessToken") String token,
		@RequestBody UserInvoiceReq userInvoiceReq) throws Exception {
		return ResultBody.newInstance(userService.modInvoice(userInvoiceReq,getUserDTO(token)));
	}

	/**
	*@Function: checkTaxNo
	*@Description
	*@param: [token, userInvoiceReq]
	*@author: Xiwei_Qiu @date: 2021/11/9 @version: 
	**/
	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "checkTaxNo", method = { RequestMethod.POST })
	public ResultBody checkTaxNo(
		@RequestHeader(value="accessToken") String token,
		@RequestBody UserInvoiceReq userInvoiceReq) throws Exception {
		return ResultBody.newInstance(userService.checkTaxNo(getUserDTO(token),userInvoiceReq));
	}


	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "del", method = { RequestMethod.POST })
	public ResultBody del(
			@RequestHeader(value="accessToken") String token,
			@Validated(BaseBean.Query.class)
			@RequestBody UserInvoiceIdReq userInvoiceIdReq) throws Exception {
		userService.delInvoice(getUserDTO(token),userInvoiceIdReq);
		return ResultBody.success();
	}

	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "isDefault", method = { RequestMethod.POST })
	public ResultBody isDefault(
			@RequestHeader(value="accessToken") String token,
			@Validated(BaseBean.Query.class)
			@RequestBody UserInvoiceIdReq userInvoiceIdReq) throws Exception {
		userService.defaultInvoice(getUserDTO(token),userInvoiceIdReq);
		return ResultBody.success();
	}

	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "list", method = { RequestMethod.POST })
	public ResultBody list(
			@RequestHeader(value="accessToken") String token,
			@RequestBody UserInvoiceListReq userInvoiceListReq) throws Exception {
		return ResultBody.newInstance(userInvoiceService.getPageList(userInvoiceListReq,getUserDTO(token)));
	}
}
