package com.sgs.ecom.member.domain.order;



import com.platform.util.ValidationUtil;
import com.sgs.base.BaseCopyObj;
import com.sgs.ecom.member.dto.oiq.applicationInfo.OiqSampleDTO;
import com.sgs.ecom.member.dto.order.OrderSampleRelateDTO;
import com.sgs.ecom.member.entity.order.OrderDetail;
import com.sgs.ecom.member.entity.order.OrderSampleRelate;
import com.sgs.ecom.member.request.oiq.OiqSampleReq;
import com.sgs.ecom.member.util.order.UseDateUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

public class OrderSampleRelateDO extends OrderSampleRelate {


   public OrderSampleRelate getSampleRelate(OrderDetail orderDetail, String sampleNo){
       OrderSampleRelate orderSampleRelate=new OrderSampleRelate();
       orderSampleRelate.setDetailId(orderDetail.getDetailId());
       orderSampleRelate.setOrderNo(orderDetail.getOrderNo());
       orderSampleRelate.setGroupNo(orderDetail.getGroupNo());
       orderSampleRelate.setSampleNo(sampleNo);
       orderSampleRelate.setState(1);
       orderSampleRelate.setCreateDate(UseDateUtil.getDateString(new Date()));
       orderSampleRelate.setStateDate(UseDateUtil.getDateString(new Date()));
       return orderSampleRelate;
   }

   public List<OiqSampleDTO>  getSampleListByRelate(List<OrderSampleRelateDTO> list, Map<String,OiqSampleDTO> sampleReqMap){
       if(ValidationUtil.isEmpty(list)){
           return new ArrayList<>();
       }
       List<OiqSampleDTO> sampleDTOList=new ArrayList<>();
       for(int n=0;n<list.size();n++){
           OrderSampleRelateDTO orderSampleRelateDTO=list.get(n);
           OiqSampleDTO base=new OiqSampleDTO();
           BaseCopyObj baseCopyObj=new BaseCopyObj();
           baseCopyObj.copyWithNull(base,sampleReqMap.getOrDefault(orderSampleRelateDTO.getSampleNo(),null));
           if(!ValidationUtil.isEmpty(base)){
               base.setSampleFromDTOList(new ArrayList<>());
               sampleDTOList.add(base);
           }

       }
       return sampleDTOList;
   }


}
