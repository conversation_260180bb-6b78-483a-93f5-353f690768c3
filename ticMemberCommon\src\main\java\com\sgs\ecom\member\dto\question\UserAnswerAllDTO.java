package com.sgs.ecom.member.dto.question;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;

import java.util.List;

public class UserAnswerAllDTO {

    @ApiAnno(groups={BaseQryFilter.Default.class})
   private QuestionOptionNameDTO questionOptionNameDTO;
    @ApiAnno(groups={BaseQryFilter.Default.class})
   private List<UserAnswerDTO> userAnswerDTOList;
    @ApiAnno(groups={BaseQryFilter.Default.class})
   private String partNo;




    public QuestionOptionNameDTO getQuestionOptionNameDTO() {
        return questionOptionNameDTO;
    }

    public void setQuestionOptionNameDTO(QuestionOptionNameDTO questionOptionNameDTO) {
        this.questionOptionNameDTO = questionOptionNameDTO;
    }

    public List<UserAnswerDTO> getUserAnswerDTOList() {
        return userAnswerDTOList;
    }

    public void setUserAnswerDTOList(List<UserAnswerDTO> userAnswerDTOList) {
        this.userAnswerDTOList = userAnswerDTOList;
    }

    public String getPartNo() {
        return partNo;
    }

    public void setPartNo(String partNo) {
        this.partNo = partNo;
    }
}
