package com.sgs.ecom.member.entity.sobot;

import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.sgs.ecom.member.base.BaseOrderFilter;

/**
 * <AUTHOR>
 * @Description :
 * @date 2023/6/27
 */
public class SobotExtendField {

    @ApiAnno(groups={BaseOrderFilter.Default.class})
    @BeanAnno(alias="fieldid")
    private String fieldId;

    @ApiAnno(groups={BaseOrderFilter.Default.class})
    @BeanAnno(alias="field_value")
    private String fieldValue;

    public String getFieldId() {
        return fieldId;
    }

    public void setFieldId(String fieldId) {
        this.fieldId = fieldId;
    }

    public String getFieldValue() {
        return fieldValue;
    }

    public void setFieldValue(String fieldValue) {
        this.fieldValue = fieldValue;
    }
}
