<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.user.dao.UserTaxMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.vo.VOUserTax" >
    <id column="ID" property="id" jdbcType="BIGINT" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="TAX_NO" property="taxNo" jdbcType="VARCHAR" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, USER_ID, TAX_NO, CREATE_DATE, STATE_DATE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from USER_TAX
    where ID = #{id,jdbcType=BIGINT}
  </select>

  <insert id="insertSelective" parameterType="com.sgs.ecom.member.vo.VOUserTax" >
    insert into USER_TAX
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
      <if test="taxNo != null" >
        TAX_NO,
      </if>
      <if test="createDate != null" >
        CREATE_DATE,
      </if>
      <if test="stateDate != null" >
        STATE_DATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="taxNo != null" >
        #{taxNo,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        #{stateDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>


  <select id="selectCountByTaxNo"  resultType="java.lang.Integer">
    select count(1) as num
    from USER_TAX
    where  USER_ID=#{userId}  and TAX_NO=#{taxNo}
  </select>



</mapper>