package com.sgs.ecom.member.config;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
/**
 * <AUTHOR>
 * @Description :
 * @date 2024/6/20
 */
@RefreshScope
@Component
@ConfigurationProperties(prefix = "order.shop")
public class EnumDomainProperties {
    /**
     * WEB SITE配置
     */



    private List<String> shopCode = new ArrayList<>();

    private Map<String, List<String>> splitOrder = Maps.newHashMap();


    public List<String> getShopCode() {
        return shopCode;
    }

    public void setShopCode(List<String> shopCode) {
        this.shopCode = shopCode;
    }

    public Map<String, List<String>> getSplitOrder() {
        return splitOrder;
    }

    public void setSplitOrder(Map<String, List<String>> splitOrder) {
        this.splitOrder = splitOrder;
    }
}
