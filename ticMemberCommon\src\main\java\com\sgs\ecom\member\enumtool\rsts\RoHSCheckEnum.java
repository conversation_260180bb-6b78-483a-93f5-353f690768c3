package com.sgs.ecom.member.enumtool.rsts;

import com.platform.util.ValidationUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;

public enum RoHSCheckEnum {

//    RoHS("roHS", 1, "rohs 4项镀层"),
//    SPEC("spec", 2, "X客户专用"),
//    EN71("EN71", 3, "EN71-3"),
//    PAHS("PAHS", 4, "多环芳烃(PAHs)"),


    RoHS("OTHER-ROHS", 1, "rohs 4项镀层"),
    EN71("OTHER-EN71", 2, "EN71-3测试"),
    PAHS("OTHER-PAHS", 3, "多环芳烃（PAHs）"),
    SPEC("OTHER-CUSTX", 4, "X客户专用"),
    ;

    RoHSCheckEnum(String name, int index, String nameCh) {
        this.name = name;
        this.index = index;
        this.nameCh = nameCh;
    }

    //校验是否需要必填
    public static Boolean getIsCheckRequired(String name){
        if(ValidationUtil.isEmpty(name))
            return false;

        for (RoHSCheckEnum c : RoHSCheckEnum.values()) {
            if (name.contains(c.getName()))
                return true;

        }
        return false;
    }




    private String name;
    private int index;
    private String nameCh;

    public static List<String> addnotXList() {
        List<String> list = new ArrayList<>();
        list.add(RoHSCheckEnum.RoHS.getName());
        list.add(RoHSCheckEnum.PAHS.getName());
        list.add(RoHSCheckEnum.EN71.getName());
        return list;
    }

    public static List<String> addXList() {
        List<String> list = new ArrayList<>();
        list.add(RoHSCheckEnum.SPEC.getName());
        return list;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }
}
