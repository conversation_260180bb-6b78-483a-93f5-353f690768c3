package com.sgs.ecom.member.enumtool.pay;

import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 */

public enum PaymentEnum {

    OIQ("OIQ","210000"),
    SHOP("SHOP","100000"),
    RSTS("RSTS","300000"),
    ;



    PaymentEnum(String key, String keyValue) {
        this.key = key;
        this.keyValue = keyValue;
    }

    private String key;
    private String keyValue;

    public static String getKeyValue(String index) {
        if(StringUtils.isBlank(index)){
            return "100000";
        }

        for (PaymentEnum c : PaymentEnum.values()) {
            if (c.getKey().equals(index)) {
                return c.getKeyValue();
            }
        }
        return "";
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getKeyValue() {
        return keyValue;
    }

    public void setKeyValue(String keyValue) {
        this.keyValue = keyValue;
    }
}
