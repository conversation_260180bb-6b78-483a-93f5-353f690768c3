package com.sgs.ecom.member.base;



import com.sgs.ecom.member.dto.send.OiqOtherDTO;
import com.sgs.ecom.member.enumtool.OiqMailEnum;

import java.util.HashMap;
import java.util.Map;

public class BaseMailSend {
	// mailType 1客户 2客服
	//String orderNo, String mail, String sendCC, Map map, OiqMailEnum oiqMailEnum, String buType, Long mailType
	private String orderNo;
	private String orderType;
	private OiqMailEnum oiqMailEnum;

	private String sendMail;
	private String sendCC;
	private Map<String,Object> mapData=new HashMap<>();
	private String type;
	private String buType;
	private Long mailType;
	private OiqOtherDTO oiqOtherDTO;



	public BaseMailSend(String orderNo, String orderType, OiqMailEnum oiqMailEnum, Long mailType){
		this.orderNo = orderNo;
		this.oiqMailEnum = oiqMailEnum;
		this.orderType=orderType;
		this.mailType=mailType;
	}

	public BaseMailSend(String orderNo, String orderType, OiqMailEnum oiqMailEnum, Long mailType,OiqOtherDTO oiqOtherDTO){
		this.orderNo = orderNo;
		this.oiqMailEnum = oiqMailEnum;
		this.orderType=orderType;
		this.mailType=mailType;
		this.oiqOtherDTO=oiqOtherDTO;
	}


	public String getOrderType() {
		return orderType;
	}

	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getSendMail() {
		return sendMail;
	}

	public void setSendMail(String sendMail) {
		this.sendMail = sendMail;
	}

	public String getSendCC() {
		return sendCC;
	}

	public void setSendCC(String sendCC) {
		this.sendCC = sendCC;
	}

	public Map<String, Object> getMapData() {
		return mapData;
	}

	public void setMapData(Map<String, Object> mapData) {
		this.mapData = mapData;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getBuType() {
		return buType;
	}

	public void setBuType(String buType) {
		this.buType = buType;
	}

	public Long getMailType() {
		return mailType;
	}

	public void setMailType(Long mailType) {
		this.mailType = mailType;
	}

	public OiqMailEnum getOiqMailEnum() {
		return oiqMailEnum;
	}

	public void setOiqMailEnum(OiqMailEnum oiqMailEnum) {
		this.oiqMailEnum = oiqMailEnum;
	}

	public OiqOtherDTO getOiqOtherDTO() {
		return oiqOtherDTO;
	}

	public void setOiqOtherDTO(OiqOtherDTO oiqOtherDTO) {
		this.oiqOtherDTO = oiqOtherDTO;
	}
}
