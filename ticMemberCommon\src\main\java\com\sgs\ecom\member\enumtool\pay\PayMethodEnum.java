package com.sgs.ecom.member.enumtool.pay;



public enum PayMethodEnum {
    ZFB("zfb","100000","支付宝",1,"Alipay"),
    BANK("bank","100002","中国银联网关支付",3,"UnionPay"),
    OFFLINE("offline","300000","对公转账",5,"Transfer to Company"),
    OFFLINE_ACCOUNT("offlineAccount","310000","线下到账",6," Offline Transfers"),
    OFFLINE_TO_ACCOUNT("offlineToAccount","320000","线下到账",6," Offline Transfers"),
    MONTH("month","300001","月结支付",4,"Monthly Payment"),
    WX("wx","100001","微信支付",2,"Wechat Pay"),

    ;

    private String name;
    private String index;
    private String nameCh;
    private String nameEn;
    private int sortShow;

    PayMethodEnum(String name, String index, String nameCh,int sortShow,String nameEn) {
        this.name = name;
        this.index = index;
        this.nameCh = nameCh;
        this.sortShow=sortShow;
        this.nameEn=nameEn;
    }

    public static String getNameCh(String index) {
        for (PayMethodEnum c : PayMethodEnum.values()) {
            if (c.getIndex().equals(index)) {
                return c.getNameCh();
            }
        }
        return null;
    }

    public static PayMethodEnum getEnum(String index) {
        for (PayMethodEnum c : PayMethodEnum.values()) {
            if (c.getIndex().equals(index)) {
                return c;
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }

    public int getSortShow() {
        return sortShow;
    }

    public void setSortShow(int sortShow) {
        this.sortShow = sortShow;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }
}
