package com.sgs.ecom.member.enums;

/**
 * RSTS-日志表中-isComfirm字段的枚举
 */
public enum RSTSIsConfirmEnum {

    ZORE("无需确认",0),
    ONE("要确认",1),
    TWO("已确认",2),
    THREE("前端不显示文案的状态",0);



    // 成员变量
    private String name;
    private int  index;

    RSTSIsConfirmEnum(String name, int index) {
        this.name = name;
        this.index = index;
    }

    // get set 方法
    public String getName() {  
        return name;  
    }

    public int getIndex() {
        return index;
    }
}
