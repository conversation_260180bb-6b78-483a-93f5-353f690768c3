package com.sgs.ecom.member.fegin;

import com.platform.annotation.AuthRequired;
import com.sgs.ecom.member.request.rpc.LeadsOrderListReq;
import com.sgs.ecom.member.request.tic.TicOrderReq;
import com.sgs.ecom.member.service.tic.interfaces.ITicOrderService;
import com.sgs.ecom.member.service.util.interfaces.IOrderLeadsRpcService;
import com.sgs.ecom.member.service.util.interfaces.IOrderMallRpcService;
import com.sgs.ecom.member.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/business/rpc.v2.mall/order")
public class MallServiceRpc {

    @Autowired
    private IOrderMallRpcService iOrderMallRpcService;

    /**
     *@Function: bbc
     *@Description 根据商品id集合查询
     *@param: [ticOrderReq]
     *@author: sundeqing @date: 2022/10/21 @version:
     **/
    @AuthRequired(login = "NULL", sign = true)
    @RequestMapping(value = "queryProductNumList", method = { RequestMethod.POST })
    public ResultBody queryProductNumList (
            @RequestBody List<Integer> itemIdList) throws Exception {
        return ResultBody.newInstance(iOrderMallRpcService.queryProductNumList(itemIdList));
    }
}
