package com.sgs.ecom.member.dto.user;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.enumtool.bbc.BbcStateEnum;
import com.sgs.ecom.member.enumtool.user.UserCenterStateEnum;

public class UserCenterStateDTO {
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int state;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String stateShow;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String stateCode;

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int num;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int shopOrderCode;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int inquiryOrderCode;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int inquirySchemeCode;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String bbcState;



    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public String getStateShow() {
        return UserCenterStateEnum.getNameCh(state);
    }

    public void setStateShow(String stateShow) {
        this.stateShow = stateShow;
    }

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

    public int getShopOrderCode() {
        return shopOrderCode;
    }

    public void setShopOrderCode(int shopOrderCode) {
        this.shopOrderCode = shopOrderCode;
    }

    public int getInquiryOrderCode() {
        return inquiryOrderCode;
    }

    public void setInquiryOrderCode(int inquiryOrderCode) {
        this.inquiryOrderCode = inquiryOrderCode;
    }

    public String getStateCode() {
        return stateCode;
    }

    public void setStateCode(String stateCode) {
        this.stateCode = stateCode;
    }

    public int getInquirySchemeCode() {
        return inquirySchemeCode;
    }

    public void setInquirySchemeCode(int inquirySchemeCode) {
        this.inquirySchemeCode = inquirySchemeCode;
    }

    public String getBbcState() {
        return BbcStateEnum.getMinByIndex(state);
    }

    public void setBbcState(String bbcState) {
        this.bbcState = bbcState;
    }
}
