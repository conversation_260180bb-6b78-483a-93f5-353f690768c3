package com.sgs.ecom.member.dto.rpc;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter.Default;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 问题流程关系
 *
 * <AUTHOR>
 * @since 2025-05-14 14:34
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionFlowRelate {

    /**
     * 关系ID
     */
    @ApiAnno(groups={Default.class})
    private Long relateId;

    /**
     * 问卷章节所属业务
     */
    @ApiAnno(groups={Default.class})
    private String businessType;

    /**
     * 所属业务对应CODE或ID
     */
    @ApiAnno(groups={Default.class})
    private String businessCode;

    /**
     * 章节编码
     */
    @ApiAnno(groups={Default.class})
    private String partNo;

    /**
     * 问题编码
     */
    @ApiAnno(groups={Default.class})
    private String subjectNo;

    /**
     * 选项编码
     */
    @ApiAnno(groups={Default.class})
    private String optionNo;

    /**
     * 关联章节编码
     */
    @ApiAnno(groups={Default.class})
    private String relatePartNo;

    /**
     * 关联问题编码
     */
    @ApiAnno(groups={Default.class})
    private String relateSubjectNo;

    /**
     * 状态 1：有效 0：无效
     */
    private Integer state;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 修改时间
     */
    private Date stateDate;

    /**
     * 修改人
     */
    private String modifiedBy;
}
