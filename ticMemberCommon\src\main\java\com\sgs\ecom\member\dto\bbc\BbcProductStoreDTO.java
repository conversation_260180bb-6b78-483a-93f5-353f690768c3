package com.sgs.ecom.member.dto.bbc;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;

public class BbcProductStoreDTO {
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String bu;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String buName;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String subBuCode;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String subBuCodeName;

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private int  state;

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private int  prodId;

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private boolean multiType;

	public int getProdId() {
		return prodId;
	}

	public void setProdId(int prodId) {
		this.prodId = prodId;
	}

	public int getState() {
		return state;
	}

	public void setState(int state) {
		this.state = state;
	}

	public String getBu() {
		return bu;
	}

	public void setBu(String bu) {
		this.bu = bu;
	}

	public String getBuName() {
		return buName;
	}

	public void setBuName(String buName) {
		this.buName = buName;
	}

	public String getSubBuCode() {
		return subBuCode;
	}

	public void setSubBuCode(String subBuCode) {
		this.subBuCode = subBuCode;
	}

	public String getSubBuCodeName() {
		return subBuCodeName;
	}

	public void setSubBuCodeName(String subBuCodeName) {
		this.subBuCodeName = subBuCodeName;
	}

	public boolean isMultiType() {
		return multiType;
	}

	public void setMultiType(boolean multiType) {
		this.multiType = multiType;
	}
}
