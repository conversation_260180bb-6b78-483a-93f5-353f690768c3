<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.order.dao.OrderAttributeMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.vo.VOOrderAttribute" >
    <id column="ID" property="id" jdbcType="BIGINT" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="GROUP_NO" property="groupNo" jdbcType="VARCHAR" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="ATTR_NAME" property="attrName" jdbcType="VARCHAR" />
    <result column="ATTR_VALUE" property="attrValue" jdbcType="VARCHAR" />
    <result column="IS_DEFAULT" property="isDefault" jdbcType="TINYINT" />
    <result column="ATTR_ID" property="attrId" jdbcType="BIGINT" />
    <result column="ATTR_AMOUNT" property="attrAmount" jdbcType="DECIMAL" />
    <result column="ATTR_CODE" property="attrCode" jdbcType="VARCHAR" />
    <result column="ATTR_EXTEND" property="attrExtend" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, ORDER_NO, ATTR_NAME, ATTR_VALUE, IS_DEFAULT, GROUP_NO, CREATE_DATE, ATTR_ID, 
    ATTR_AMOUNT,ATTR_CODE,ATTR_EXTEND
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from ORDER_ATTRIBUTE
    where ID = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectVOListByMap" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from ORDER_ATTRIBUTE
    <include refid="baseQueryWhere"/>
  </select>




  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from ORDER_ATTRIBUTE
    where ID = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" parameterType="com.sgs.ecom.member.vo.VOOrderAttribute" >
    insert into ORDER_ATTRIBUTE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="orderNo != null" >
        ORDER_NO,
      </if>
      <if test="attrName != null" >
        ATTR_NAME,
      </if>
      <if test="attrValue != null" >
        ATTR_VALUE,
      </if>
      <if test="isDefault != null" >
        IS_DEFAULT,
      </if>
      <if test="groupNo != null" >
        GROUP_NO,
      </if>
      <if test="createDate != null" >
        CREATE_DATE,
      </if>
      <if test="attrId != null" >
        ATTR_ID,
      </if>
      <if test="attrAmount != null" >
        ATTR_AMOUNT,
      </if>
      <if test="attrCode != null" >
        ATTR_CODE,
      </if>
      <if test="attrExtend != null" >
        ATTR_EXTEND,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="attrName != null" >
        #{attrName,jdbcType=VARCHAR},
      </if>
      <if test="attrValue != null" >
        #{attrValue,jdbcType=VARCHAR},
      </if>
      <if test="isDefault != null" >
        #{isDefault,jdbcType=TINYINT},
      </if>
      <if test="groupNo != null" >
        #{groupNo,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="attrId != null" >
        #{attrId,jdbcType=BIGINT},
      </if>
      <if test="attrAmount != null" >
        #{attrAmount,jdbcType=DECIMAL},
      </if>
      <if test="attrCode != null" >
        #{attrCode,jdbcType=VARCHAR},
      </if>
      <if test="attrExtend != null" >
        #{attrExtend,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.ecom.member.vo.VOOrderAttribute" >
    update ORDER_ATTRIBUTE
    <set >
      <if test="orderNo != null" >
        ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="attrName != null" >
        ATTR_NAME = #{attrName,jdbcType=VARCHAR},
      </if>
      <if test="attrValue != null" >
        ATTR_VALUE = #{attrValue,jdbcType=VARCHAR},
      </if>
      <if test="isDefault != null" >
        IS_DEFAULT = #{isDefault,jdbcType=TINYINT},
      </if>
      <if test="groupNo != null" >
        GROUP_NO = #{groupNo,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="attrId != null" >
        ATTR_ID = #{attrId,jdbcType=BIGINT},
      </if>
      <if test="attrAmount != null" >
        ATTR_AMOUNT = #{attrAmount,jdbcType=DECIMAL},
      </if>
    </set>
    where ID = #{id,jdbcType=BIGINT}
  </update>



  <!-- 自定义-->
  <resultMap id="resultDTO" type="com.sgs.ecom.member.dto.OrderAttributeDTO" >
    <id column="ID" property="id" jdbcType="BIGINT" />
    <result column="ATTR_NAME" property="attrName" jdbcType="VARCHAR" />
    <result column="ATTR_VALUE" property="attrValue" jdbcType="VARCHAR" />
    <result column="IS_DEFAULT" property="isDefault" jdbcType="TINYINT" />
    <result column="ATTR_ID" property="attrId" jdbcType="BIGINT" />
    <result column="ATTR_AMOUNT" property="attrAmount" jdbcType="DECIMAL" />
    <result column="ATTR_CODE" property="attrCode" jdbcType="VARCHAR" />
    <result column="ATTR_EXTEND" property="attrExtend" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="baseDTO" >
    ID,ATTR_NAME, ATTR_VALUE, IS_DEFAULT,ATTR_ID,ATTR_AMOUNT,ATTR_CODE,ATTR_EXTEND
  </sql>

  <select id="selectListByMap" resultMap="resultDTO" >
    select
    <include refid="baseDTO" />
    from ORDER_ATTRIBUTE
    <include refid="baseQueryWhere"/>
  </select>

  <select id="selectCustom" resultMap="resultDTO" >
    select
    <include refid="baseDTO" />
    from ORDER_ATTRIBUTE
    <include refid="baseQueryWhere"/>
    order by id desc
    limit 1
  </select>



  <delete id="delAttributeByMap">
    delete from ORDER_ATTRIBUTE where order_no=#{orderNo} and GROUP_NO=#{groupNo}
  </delete>


  <insert id="insertForeach"  >
    insert into ORDER_ATTRIBUTE (
    ORDER_NO, ATTR_NAME,
    ATTR_VALUE, IS_DEFAULT, GROUP_NO,
    CREATE_DATE, ATTR_ID, ATTR_AMOUNT,ATTR_CODE,ATTR_EXTEND
    )
    values
    <foreach collection ="list" item="attribute" index= "index" separator =",">
      (
      #{attribute.orderNo,jdbcType=VARCHAR}, #{attribute.attrName,jdbcType=VARCHAR},
      #{attribute.attrValue,jdbcType=VARCHAR}, #{attribute.isDefault,jdbcType=TINYINT}, #{attribute.groupNo,jdbcType=VARCHAR},
      #{attribute.createDate,jdbcType=TIMESTAMP}, #{attribute.attrId}, #{attribute.attrAmount},
      #{attribute.attrCode,jdbcType=VARCHAR}, #{attribute.attrExtend,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>

  <sql id="baseQueryWhere">
    where 1=1
    <if test="orderNo != null">
      AND ORDER_NO=#{orderNo}
    </if>
    <if test="groupNo != null">
      AND GROUP_NO=#{groupNo}
    </if>
    <if test="isDefault != null">
      AND IS_DEFAULT=#{isDefault}
    </if>
    <if test="attrValue != null">
      AND ATTR_VALUE=#{attrValue}
    </if>
    <if test="attrValueList != null ">
      AND ATTR_VALUE in
      <foreach collection="attrValueList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </sql>


  <update id="updateUserLabelAttribute">
    update  ORDER_ATTRIBUTE set ATTR_NAME="0" where ATTR_VALUE="TIC_REPURCHASE" and ATTR_NAME="1"	and GROUP_NO='customGroup'
                                                and order_no in(
        select order_no from ORDER_BASE_INFO obi   where ORDER_TYPE=100000 and USER_ID =#{userId} and state!=91
                                                and CREATE_DATE &lt;#{createDate}
    )
  </update>
</mapper>