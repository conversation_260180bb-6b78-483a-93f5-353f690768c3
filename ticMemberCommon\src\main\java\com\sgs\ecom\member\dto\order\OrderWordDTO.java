package com.sgs.ecom.member.dto.order;

import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.dto.OrderDetailDTO;
import com.sgs.ecom.member.dto.OrderOperatorLogDTO;
import com.sgs.ecom.member.dto.base.BaseOrderDTO;
import com.sgs.ecom.member.dto.center.LabDTO;
import com.sgs.ecom.member.dto.detail.OrderApplicationFormDTO;
import com.sgs.ecom.member.dto.detail.OrderInvoiceDTO;
import com.sgs.ecom.member.dto.detail.OrderReportDTO;
import com.sgs.ecom.member.dto.oiq.OiqHeadPdfDTO;
import com.sgs.ecom.member.dto.pay.BankDTO;
import com.sgs.ecom.member.dto.pdf.FormHeadDTO;
import com.sgs.ecom.member.dto.pdf.FormTitleDTO;
import com.sgs.ecom.member.dto.pdf.QuotationDTO;
import com.sgs.ecom.member.dto.sample.SampleCategoryDTO;
import com.sgs.ecom.member.dto.sample.WordSampleDTO;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class OrderWordDTO {
    //银行信息
    @ApiAnno(groups={BaseOrderFilter.WordQuotation.class})
    private BankDTO bank;
    @ApiAnno(groups={BaseOrderFilter.WordQuotation.class})
    private LabDTO lab;
    @ApiAnno(groups={BaseOrderFilter.WordQuotation.class})
    private List<SubOrderDTO> subOrderList= new ArrayList<>();

    @ApiAnno(groups={BaseOrderFilter.WordQuotation.class,BaseOrderFilter.WordForm.class})
    private BaseOrderDTO orderBaseInfo;



    @ApiAnno(groups={BaseOrderFilter.WordQuotation.class,BaseOrderFilter.WordForm.class})
    private OrderInvoiceDTO orderInvoice;
    @ApiAnno(groups={BaseOrderFilter.WordQuotation.class})
    private QuotationDTO quotation;
    @ApiAnno(groups={BaseOrderFilter.WordQuotation.class})
    private List<OrderOperatorLogDTO> logList=new ArrayList<>();
    @ApiAnno(groups={BaseOrderFilter.WordQuotation.class,BaseOrderFilter.WordForm.class})
    @BeanAnno(dtocls = {OrderDetailDTO.class})
    private List<OrderDetailDTO> testItem=new ArrayList<>();

    //申请表
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private OrderApplicationFormDTO orderApplicationForm;

    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private OrderReportDTO orderReport;
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    @BeanAnno(dtocls = {WordSampleDTO.class})
    private List<WordSampleDTO> sampleItem = new ArrayList<>();
    @ApiAnno(groups={BaseOrderFilter.WordQuotation.class,BaseOrderFilter.WordForm.class})
    private OiqHeadPdfDTO fromPdfInfo;
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private FormTitleDTO formTitle;
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private FormHeadDTO head;
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private SampleCategoryDTO sampleCategory;

    private String useOrderNo;
    private String useGroupNo;
    private Integer isForeign;
    private Integer disCountZeroFlg;
    private String businessCode;



    public BankDTO getBank() {
        return bank;
    }

    public void setBank(BankDTO bank) {
        this.bank = bank;
    }

    public LabDTO getLab() {
        return lab;
    }

    public void setLab(LabDTO lab) {
        this.lab = lab;
    }

    public List<SubOrderDTO> getSubOrderList() {
        return subOrderList;
    }

    public void setSubOrderList(List<SubOrderDTO> subOrderList) {
        this.subOrderList = subOrderList;
    }

    public BaseOrderDTO getOrderBaseInfo() {
        return orderBaseInfo;
    }

    public void setOrderBaseInfo(BaseOrderDTO orderBaseInfo) {
        this.orderBaseInfo = orderBaseInfo;
    }

    public OrderApplicationFormDTO getOrderApplicationForm() {
        return orderApplicationForm;
    }

    public void setOrderApplicationForm(OrderApplicationFormDTO orderApplicationForm) {
        this.orderApplicationForm = orderApplicationForm;
    }

    public OrderInvoiceDTO getOrderInvoice() {
        return orderInvoice;
    }

    public void setOrderInvoice(OrderInvoiceDTO orderInvoice) {
        this.orderInvoice = orderInvoice;
    }


    public QuotationDTO getQuotation() {
        return quotation;
    }

    public void setQuotation(QuotationDTO quotation) {
        this.quotation = quotation;
    }

    public List<OrderOperatorLogDTO> getLogList() {
        return logList;
    }

    public void setLogList(List<OrderOperatorLogDTO> logList) {
        this.logList = logList;
    }

    public List<OrderDetailDTO> getTestItem() {
        return testItem;
    }

    public void setTestItem(List<OrderDetailDTO> testItem) {
        this.testItem = testItem;
    }

    public OrderReportDTO getOrderReport() {
        return orderReport;
    }

    public void setOrderReport(OrderReportDTO orderReport) {
        this.orderReport = orderReport;
    }


    public String getUseOrderNo() {
        return useOrderNo;
    }

    public void setUseOrderNo(String useOrderNo) {
        this.useOrderNo = useOrderNo;
    }

    public String getUseGroupNo() {
        return useGroupNo;
    }

    public void setUseGroupNo(String useGroupNo) {
        this.useGroupNo = useGroupNo;
    }

    public OiqHeadPdfDTO getFromPdfInfo() {
        return fromPdfInfo;
    }

    public void setFromPdfInfo(OiqHeadPdfDTO fromPdfInfo) {
        this.fromPdfInfo = fromPdfInfo;
    }

    public FormTitleDTO getFormTitle() {
        return formTitle;
    }

    public void setFormTitle(FormTitleDTO formTitle) {
        this.formTitle = formTitle;
    }

    public Integer getIsForeign() {
        return isForeign;
    }

    public void setIsForeign(Integer isForeign) {
        this.isForeign = isForeign;
    }

    public FormHeadDTO getHead() {
        return head;
    }

    public void setHead(FormHeadDTO head) {
        this.head = head;
    }

    public SampleCategoryDTO getSampleCategory() {
        return sampleCategory;
    }

    public void setSampleCategory(SampleCategoryDTO sampleCategory) {
        this.sampleCategory = sampleCategory;
    }

    public List<WordSampleDTO> getSampleItem() {
        return sampleItem;
    }

    public void setSampleItem(List<WordSampleDTO> sampleItem) {
        this.sampleItem = sampleItem;
    }

    public Integer getDisCountZeroFlg() {
        return disCountZeroFlg;
    }

    public void setDisCountZeroFlg(Integer disCountZeroFlg) {
        this.disCountZeroFlg = disCountZeroFlg;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }
}
