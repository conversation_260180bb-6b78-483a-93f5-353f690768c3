package com.sgs.ecom.member.dto.question;
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import com.platform.annotation.ApiAnno; 
import com.platform.annotation.BeanAnno;
import com.platform.annotation.IsNullAnno;
import com.platform.annotation.UpdateAnno; 

public class UserQuestionDTO{
 
 	public static final String CREATE_SQL = "select CLOUD_ID,QUESTION_NAME_EN,STATE_DATE,STATE,USER_ID,ORDER_NO,FINAL_SCORE,QUESTION_NAME,CREATE_DATE,LANGUAGE_FLAG,CURRENT_PART,QUESTION_ID,REPLY_ID,REAL_SCORE,ATTACHMENT_NAME,FILE_KEY from TB_USER_QUESTION"; 
 
 
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="CLOUD_ID", getName="getCloudId", setName="setCloudId")
 	private String cloudId;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="QUESTION_NAME_EN", getName="getQuestionNameEn", setName="setQuestionNameEn")
 	private String questionNameEn;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private Timestamp stateDate;
 	@ApiAnno(serviceName={"qryQuestion"})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@IsNullAnno(serviceName={"createQuestion","qryQuestion","qryUserReport","qryAnswerByPart","submitAnswer"})
 	@ApiAnno(serviceName={"qryQuestion"})
 	@BeanAnno(value="USER_ID", getName="getUserId", setName="setUserId")
 	private long userId;
 	@IsNullAnno(serviceName={"createQuestion","qryQuestion","qryUserReport","qryAnswerByPart","submitAnswer"})
 	@ApiAnno(serviceName={"qryQuestion"})
 	@BeanAnno(value="ORDER_NO", getName="getOrderNo", setName="setOrderNo")
 	private String orderNo;
 	@ApiAnno(serviceName={"qryQuestion"})
 	@BeanAnno(value="FINAL_SCORE", getName="getFinalScore", setName="setFinalScore")
 	private long finalScore;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="QUESTION_NAME", getName="getQuestionName", setName="setQuestionName")
 	private String questionName;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(serviceName={"qryQuestion"})
 	@BeanAnno(value="LANGUAGE_FLAG", getName="getLanguageFlag", setName="setLanguageFlag")
 	private String languageFlag;
 	@UpdateAnno(serviceName={"backQuestuon"})
 	@ApiAnno(serviceName={"qryQuestion"})
 	@BeanAnno(value="CURRENT_PART", getName="getCurrentPart", setName="setCurrentPart")
 	private String currentPart;
 	@IsNullAnno(serviceName={"createQuestion","qryAnswerByPart"})
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="QUESTION_ID", getName="getQuestionId", setName="setQuestionId")
 	private long questionId;
 	@IsNullAnno(serviceName={"getDownUrl","submitAnswer"})
 	@ApiAnno(serviceName={"createQuestion","qryQuestion"})
 	@BeanAnno(value="REPLY_ID", getName="getReplyId", setName="setReplyId")
 	private long replyId;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="REAL_SCORE", getName="getRealScore", setName="setRealScore")
 	private long realScore;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="ATTACHMENT_NAME", getName="getAttachmentName", setName="setAttachmentName")
 	private String attachmentName;
 	@ApiAnno(serviceName={"qryQuestion"})
 	@BeanAnno(value="FILE_KEY", getName="getFileKey", setName="setFileKey")
 	private String fileKey;

 	public void setCloudId(String cloudId){
 		 this.cloudId=cloudId;
 	}
 	public String getCloudId(){
 		 return this.cloudId;
 	}
 
 	 
 	public void setQuestionNameEn(String questionNameEn){
 		 this.questionNameEn=questionNameEn;
 	}
 	public String getQuestionNameEn(){
 		 return this.questionNameEn;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setUserId(long userId){
 		 this.userId=userId;
 	}
 	public long getUserId(){
 		 return this.userId;
 	}
 
 	 
 	public void setOrderNo(String orderNo){
 		 this.orderNo=orderNo;
 	}
 	public String getOrderNo(){
 		 return this.orderNo;
 	}
 
 	 
 	public void setFinalScore(long finalScore){
 		 this.finalScore=finalScore;
 	}
 	public long getFinalScore(){
 		 return this.finalScore;
 	}
 
 	 
 	public void setQuestionName(String questionName){
 		 this.questionName=questionName;
 	}
 	public String getQuestionName(){
 		 return this.questionName;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setLanguageFlag(String languageFlag){
 		 this.languageFlag=languageFlag;
 	}
 	public String getLanguageFlag(){
 		 return this.languageFlag;
 	}
 
 	 
 	public void setCurrentPart(String currentPart){
 		 this.currentPart=currentPart;
 	}
 	public String getCurrentPart(){
 		 return this.currentPart;
 	}
 
 	 
 	public void setQuestionId(long questionId){
 		 this.questionId=questionId;
 	}
 	public long getQuestionId(){
 		 return this.questionId;
 	}
 
 	 
 	public void setReplyId(long replyId){
 		 this.replyId=replyId;
 	}
 	public long getReplyId(){
 		 return this.replyId;
 	}
 
 	 
 	public void setRealScore(long realScore){
 		 this.realScore=realScore;
 	}
 	public long getRealScore(){
 		 return this.realScore;
 	}
 
 	 
 	public void setAttachmentName(String attachmentName){
 		 this.attachmentName=attachmentName;
 	}
 	public String getAttachmentName(){
 		 return this.attachmentName;
 	}
 
 	 
 	public void setFileKey(String fileKey){
 		 this.fileKey=fileKey;
 	}
 	public String getFileKey(){
 		 return this.fileKey;
 	}
 
 	 
}