package com.sgs.ecom.member.controller.rsts;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixProperty;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.base.BaseBean;
import com.sgs.ecom.member.annotation.VerifyCust;
import com.sgs.ecom.member.enumtool.event.EventEnum;
import com.sgs.ecom.member.event.EventApiUtil;
import com.sgs.ecom.member.request.OrderNoReq;
import com.sgs.ecom.member.request.rsts.CopyRedisReq;
import com.sgs.ecom.member.request.rsts.RstsCrmCreateReq;
import com.sgs.ecom.member.request.rsts.RstsOrderSaveReq;
import com.sgs.ecom.member.request.rsts.RstsPersonalCenterReq;
import com.sgs.ecom.member.service.rsts.interfaces.IRSTSOrderService;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.rsts/order")
public class RSTSOrderController extends ControllerUtil {

    @Autowired
    private IRSTSOrderService irstsOrderService;
    @Autowired
    private EventApiUtil eventApiUtil;

    /**
     * @Description :暂存/提交订单-rsts-new
     * <AUTHOR> Zhang
     * @Date  2023/10/30
     * @param token:
     * @param rstsSaveReq:
     * @return: com.sgs.ecom.member.util.ResultBody
     **/
    @HystrixCommand(defaultFallback = "timeoutMethod",commandProperties = {@HystrixProperty(name = "execution.isolation.thread.timeoutInMilliseconds", value = "59000")})
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "saveOrderNew", method = { RequestMethod.POST })
    public ResultBody saveOrderNew (
            @RequestHeader(value="accessToken") String token,
            @Validated(BaseBean.Query.class)
            @RequestBody RstsOrderSaveReq rstsSaveReq) throws Exception {
        irstsOrderService.saveOrderNew(rstsSaveReq,getUserDTO(token));
        eventApiUtil.saveEvent(rstsSaveReq.getOrderNo(), EventEnum.SAVE_INVOICE_BOSS);
        return ResultBody.newInstance(rstsSaveReq.getOrderNo());
    }

    /**
    * @params [token, rstsSaveReq]
    * @return com.sgs.ecom.member.util.ResultBody
    * @description 客服登录发送创建订单的邮件
    * <AUTHOR> || created at 2023/4/6 13:23
    */
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "crmSendMail", method = { RequestMethod.POST })
    public ResultBody crmSendMail (
            @RequestHeader(value="accessToken") String token,
            @RequestBody RstsCrmCreateReq rstsCrmCreateReq) throws Exception {
        irstsOrderService.crmSendMail(rstsCrmCreateReq,getUserDTO(token));
        return ResultBody.success();
    }




    /**
     *@Function: copyQuestion
     *@Description 复制拷贝rsts的缓存
     *@param: [orderNoReq, token]
     *@author: Xiwei_Qiu @date: 2021/9/10 @version:
     **/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "copyRedis", method = { RequestMethod.POST })
    public ResultBody copyRedis(
            @RequestBody CopyRedisReq copyRedisReq,
            @RequestHeader(value="accessToken") String token) throws Exception {
        return ResultBody.newInstance(irstsOrderService.copyRedis(copyRedisReq,getUserDTO(token)));
    }

    /**
    *@Function: checkUser
    *@Description 检查用户的销售情况
    *@param: [orderNoReq, token]
    *@author: Xiwei_Qiu @date: 2022/6/29 @version:
    **/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "checkUser", method = { RequestMethod.POST })
    public ResultBody checkUser(
        @RequestHeader(value="accessToken") String token) throws Exception {
        irstsOrderService.checkUser(getUserDTO(token));
        return ResultBody.success();
    }

    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "getCustId", method = { RequestMethod.POST })
    public ResultBody getCustId(
            @RequestHeader(value="accessToken") String token,
            @RequestBody OrderNoReq orderNoReq) throws Exception {
        return ResultBody.newInstance(irstsOrderService.getCustId(orderNoReq,getUserDTO(token)));
    }


    /**
     * @Description :新版个人中心统计各个订单状态的数量
     * <AUTHOR> Zhang
     * @Date  2023/11/22
     * @param token:
     * @param rstsPersonalCenterReq:
     * @return: com.sgs.ecom.member.util.ResultBody
     **/
    @VerifyCust
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "qryOrderNumByState", method = { RequestMethod.POST })
    public ResultBody qryOrderNumByState(
            @RequestHeader(value="accessToken") String token,
            @Validated(BaseBean.Query.class)
            @RequestBody RstsPersonalCenterReq rstsPersonalCenterReq) throws Exception {
        return ResultBody.newInstance(irstsOrderService.qryOrderNumByState(rstsPersonalCenterReq,getUserDTO(token)));
    }

    /**
     * @Description :新版个人中心统计最新的的订单信息
     * <AUTHOR> Zhang
     * @Date  2023/11/22
     * @param token:
     * @param rstsPersonalCenterReq:
     * @return: com.sgs.ecom.member.util.ResultBody
     **/
    @VerifyCust
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "qryNewOrderList", method = { RequestMethod.POST })
    public ResultBody qryNewOrderList(
            @RequestHeader(value="accessToken") String token,
            @Validated(BaseBean.Query.class)
            @RequestBody RstsPersonalCenterReq rstsPersonalCenterReq) throws Exception {
        return ResultBody.newInstance(irstsOrderService.qryNewOrderList(rstsPersonalCenterReq,getUserDTO(token)));
    }

    /**
     * @Description :新版个人中心-查询收藏和热门标记
     * <AUTHOR> Zhang
     * @Date  2023/11/29
     * @param token:
     * @param rstsPersonalCenterReq:
     * @return: com.sgs.ecom.member.util.ResultBody
     **/
    @VerifyCust
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "qryCustRecommend", method = { RequestMethod.POST })
    public ResultBody qryCustRecommend(
            @RequestHeader(value="accessToken") String token,
            @Validated(BaseBean.Query.class)
            @RequestBody RstsPersonalCenterReq rstsPersonalCenterReq) throws Exception {
        return ResultBody.newInstance(irstsOrderService.qryCustRecommend(rstsPersonalCenterReq,getUserDTO(token)));
    }

    public static void main(String[] args) {
        String a="";
        for(int n=0;n<500;n++){
            a=a+n+"";
        }
        System.out.println(a);
    }


}
