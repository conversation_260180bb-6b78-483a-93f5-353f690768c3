<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.order.dao.OrderSampleRelateMapper" >


    <resultMap id="BaseResultMap" type="com.sgs.ecom.member.vo.VOOrderSampleRelate" >
            <id column="RELATE_ID" property="relateId" jdbcType="BIGINT" />
            <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
            <result column="SAMPLE_NO" property="sampleNo" jdbcType="VARCHAR" />
            <result column="DETAIL_ID" property="detailId" jdbcType="BIGINT" />
            <result column="STATE" property="state" jdbcType="INTEGER" />
            <result column="BUY_NUMS" property="buyNums" jdbcType="INTEGER" />
            <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
            <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
            <result column="GROUP_NO" property="groupNo" jdbcType="VARCHAR" />
        </resultMap>
        <sql id="Base_Column_List" >
            RELATE_ID, ORDER_NO, SAMPLE_NO, DETAIL_ID, STATE, CREATE_DATE, STATE_DATE, GROUP_NO
         </sql>



    <insert id="insertVOSelective" parameterType="com.sgs.ecom.member.vo.VOOrderSampleRelate" >
        insert into ORDER_SAMPLE_RELATE
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="relateId != null" >
                RELATE_ID,
            </if>
            <if test="orderNo != null" >
                ORDER_NO,
            </if>
            <if test="sampleNo != null" >
                SAMPLE_NO,
            </if>
            <if test="detailId != null" >
                DETAIL_ID,
            </if>
            <if test="state != null" >
                STATE,
            </if>
            <if test="createDate != null" >
                CREATE_DATE,
            </if>
            <if test="stateDate != null" >
                STATE_DATE,
            </if>
            <if test="groupNo != null" >
                GROUP_NO,
            </if>
            <if test="buyNums != null" >
                BUY_NUMS,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="relateId != null" >
                #{relateId,jdbcType=BIGINT},
            </if>
            <if test="orderNo != null" >
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="sampleNo != null" >
                #{sampleNo,jdbcType=VARCHAR},
            </if>
            <if test="detailId != null" >
                #{detailId,jdbcType=BIGINT},
            </if>
            <if test="state != null" >
                #{state,jdbcType=INTEGER},
            </if>
            <if test="createDate != null" >
                #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="stateDate != null" >
                #{stateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="groupNo != null" >
                #{groupNo,jdbcType=VARCHAR},
            </if>
            <if test="buyNums != null" >
                #{buyNums,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>


  <!--自定义 -->
  <resultMap id="resultDTO" type="com.sgs.ecom.member.dto.order.OrderSampleRelateDTO" >
    <result column="GROUP_NO" property="groupNo" jdbcType="VARCHAR" />
    <result column="SAMPLE_NO" property="sampleNo" jdbcType="VARCHAR" />
    <result column="DETAIL_ID" property="detailId" jdbcType="VARCHAR" />
      <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
      <result column="BUY_NUMS" property="buyNums" jdbcType="INTEGER" />
  </resultMap>

  <sql id="sqlListDTO" >
    GROUP_NO,SAMPLE_NO,DETAIL_ID,ORDER_NO,BUY_NUMS
  </sql>

  <select id="selectListByMap" resultMap="resultDTO" >
    select
    <include refid="sqlListDTO" />
    from ORDER_SAMPLE_RELATE
    <include refid="baseQueryWhere"/>
    order by RELATE_ID asc
  </select>
    <sql id="baseQueryWhere">
    where 1=1 and state=1
    <if test="orderNo != null">
      AND ORDER_NO=#{orderNo}
    </if>
    <if test="detailId != null">
      AND DETAIL_ID=#{detailId}
    </if>
    <if test="groupNo != null">
      AND GROUP_NO=#{groupNo}
    </if>
  <if test="orderNoList != null ">
      AND ORDER_NO in
      <foreach collection="orderNoList" index="index" item="item" open="(" separator="," close=")">
          #{item}
      </foreach>
  </if>
  </sql>

  <insert id="copySampleRelateBySql"  >
 insert into ORDER_SAMPLE_RELATE(CREATE_DATE,DETAIL_ID,GROUP_NO,STATE_DATE,STATE,SAMPLE_NO,ORDER_NO)
 select now(),DETAIL_ID,#{newGroupNo},now(),STATE,SAMPLE_NO,#{newOrderNo}
 from ORDER_SAMPLE_RELATE  where GROUP_NO = #{oldGroupNo} and ORDER_NO=#{orderNo} ORDER BY RELATE_ID asc
  </insert>

  <update id="updateSampleRelateBySql"  >
 update ORDER_SAMPLE_RELATE as A
 set A.DETAIL_ID=(select B.DETAIL_ID  from ORDER_DETAIL as B where B.DETAIL_NO=A.DETAIL_ID
 and B.ORDER_NO=#{newOrderNo}
 ) where A.GROUP_NO= #{newGroupNo} and A.ORDER_NO=#{newOrderNo}
  </update>

    <delete id="delRelateByMap">
    delete from ORDER_SAMPLE_RELATE where order_no=#{orderNo} and GROUP_NO=#{groupNo}
  </delete>
    <delete id="deleteByOrderNo">
        update ORDER_SAMPLE_RELATE set STATE=0 where ORDER_NO=#{orderNo}
    </delete>


    <insert id="insertForeachOld"  >
        insert into ORDER_SAMPLE_RELATE (
         ORDER_NO, SAMPLE_NO, DETAIL_ID, STATE, CREATE_DATE, STATE_DATE, GROUP_NO,BUY_NUMS
        )
        values
        <foreach collection ="list" item="relate" index= "index" separator =",">
            (
            #{relate.orderNo},#{relate.sampleNo},#{relate.detailId},
            #{relate.state},#{relate.createDate},#{relate.stateDate},
            #{relate.groupNo},#{relate.buyNums}
            )
        </foreach>
    </insert>

    <insert id="insertForeach"  >
        insert into ORDER_SAMPLE_RELATE (
        ORDER_NO, SAMPLE_NO, DETAIL_ID, STATE, CREATE_DATE, STATE_DATE, GROUP_NO,BUY_NUMS
        )
        values
        <foreach collection ="list" item="relate" index= "index" separator =",">
            (
            #{relate.orderNo},#{relate.sampleNo},#{relate.detailId},
            #{relate.state},#{relate.createDate},#{relate.stateDate},
            #{relate.groupNo},#{relate.buyNums}
            )
        </foreach>
    </insert>
</mapper>