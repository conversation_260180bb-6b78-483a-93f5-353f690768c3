package com.sgs.ecom.member.dto.order;

import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.dto.detail.OrderAttachmentDTO;
import com.sgs.ecom.member.dto.oiq.applicationInfo.OiqSampleDTO;
import com.sgs.ecom.member.request.FileReq;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class OrderSampleMoreDTO {
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String orderNo;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String groupNo;
	@ApiAnno(groups={BaseQryFilter.Default.class, BaseOrderFilter.OiqFormInfo.class})
	private String sampleName;
	@ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqFormInfo.class})
	private String sampleNameCn;
	@ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqFormInfo.class})
	private String sampleNameEn;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String sampleNo;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String sampleId;
	@ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqFormInfo.class})
	private String row;
	@ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqFormInfo.class})
	private String sampleShapeCode;
	@ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqFormInfo.class})
	private String sampleCategoryCode;

	private int state;


	@ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqFormInfo.class})
	@BeanAnno(dtocls = {OrderSampleFromDTO.class})
	private List<OrderSampleFromDTO> sampleFromDTOList=new ArrayList<>();

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private Map<String,Object> sampleFromDTOMap;
	private List<List<OrderSampleFromDTO>> samplePDF=new ArrayList<>();

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private List<OrderAttachmentDTO> fileReqList=new ArrayList<>();//附件信息
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private List<OrderSampleRelateDTO> orderSampleRelateDTOList= new ArrayList<>();

	public List<OrderSampleRelateDTO> getOrderSampleRelateDTOList() {
		return orderSampleRelateDTOList;
	}

	public void setOrderSampleRelateDTOList(List<OrderSampleRelateDTO> orderSampleRelateDTOList) {
		this.orderSampleRelateDTOList = orderSampleRelateDTOList;
	}

	public List<OrderAttachmentDTO> getFileReqList() {
		return fileReqList;
	}

	public void setFileReqList(List<OrderAttachmentDTO> fileReqList) {
		this.fileReqList = fileReqList;
	}


	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getGroupNo() {
		return groupNo;
	}

	public void setGroupNo(String groupNo) {
		this.groupNo = groupNo;
	}

	public String getSampleName() {
		return sampleName;
	}

	public void setSampleName(String sampleName) {
		this.sampleName = sampleName;
	}

	public String getSampleNameCn() {
		return sampleNameCn;
	}

	public void setSampleNameCn(String sampleNameCn) {
		this.sampleNameCn = sampleNameCn;
	}

	public String getSampleNameEn() {
		return sampleNameEn;
	}

	public void setSampleNameEn(String sampleNameEn) {
		this.sampleNameEn = sampleNameEn;
	}

	public String getSampleNo() {
		return sampleNo;
	}

	public void setSampleNo(String sampleNo) {
		this.sampleNo = sampleNo;
	}

	public int getState() {
		return state;
	}

	public void setState(int state) {
		this.state = state;
	}

	public String getRow() {
		return row;
	}

	public void setRow(String row) {
		this.row = row;
	}

	public List<OrderSampleFromDTO> getSampleFromDTOList() {
		return sampleFromDTOList;
	}

	public void setSampleFromDTOList(List<OrderSampleFromDTO> sampleFromDTOList) {
		this.sampleFromDTOList = sampleFromDTOList;
	}

	public List<List<OrderSampleFromDTO>> getSamplePDF() {
		return samplePDF;
	}

	public void setSamplePDF(List<List<OrderSampleFromDTO>> samplePDF) {
		this.samplePDF = samplePDF;
	}

	public String getSampleId() {
		return sampleId;
	}

	public void setSampleId(String sampleId) {
		this.sampleId = sampleId;
	}

	public Map<String,Object> getSampleFromDTOMap() {
		if(ValidationUtil.isEmpty(sampleFromDTOList)){
			return new HashMap();
		}
		return sampleFromDTOList.stream().collect(Collectors.toMap(OrderSampleFromDTO::getSampleKey, OrderSampleFromDTO::getSampleValue, (key1, key2) -> key2));
	}



	public String getSampleCategoryCode() {
		return sampleCategoryCode;
	}

	public void setSampleCategoryCode(String sampleCategoryCode) {
		this.sampleCategoryCode = sampleCategoryCode;
	}

	public String getSampleShapeCode() {
		return sampleShapeCode;
	}

	public void setSampleShapeCode(String sampleShapeCode) {
		this.sampleShapeCode = sampleShapeCode;
	}

	public void setSampleFromDTOMap(Map<String, Object> sampleFromDTOMap) {
		this.sampleFromDTOMap = sampleFromDTOMap;
	}
}
