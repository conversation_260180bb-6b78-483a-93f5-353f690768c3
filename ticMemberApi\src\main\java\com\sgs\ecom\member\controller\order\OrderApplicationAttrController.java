package com.sgs.ecom.member.controller.order;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.base.BaseBean;
import com.sgs.ecom.member.request.OrderApplicationOtherReq;
import com.sgs.ecom.member.service.util.interfaces.IOrderApplicationService;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * RSTS-SDS特殊信息保存ControllerUtil
 */
@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.order/applicationOther")
public class OrderApplicationAttrController extends ControllerUtil {

    @Resource
    private IOrderApplicationService orderApplicationService;



    /**
     * @Description : LV3 针对RSTS-SDS申请表查询化学资料信息接口-嵌入lv3页面-需要登录校验
     * <AUTHOR> Zhang
     * @Date  2023/9/22
     * @param token:
     * @param orderApplicationOtherReq:
     * @return: com.sgs.ecom.member.util.ResultBody
     **/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "qryApplicationInfoByOrderNoAndPermission", method = { RequestMethod.POST })
    public ResultBody qryApplicationInfoByOrderNoAndPermission (
            @RequestHeader(value="accessToken") String token,
            @Validated(BaseBean.Query.class)
            @RequestBody OrderApplicationOtherReq orderApplicationOtherReq)throws Exception{
        return ResultBody.newInstance(orderApplicationService.qryApplicationInfoByOrderNo(orderApplicationOtherReq,getUserDTO(token)));
    }


    /**
     * @Description : LV3 针对RSTS-SDS申请表查询化学资料信息接口-独立页面-不需要登录校验
     * <AUTHOR> Zhang
     * @Date  2023/9/22
     * @param orderApplicationOtherReq:
     * @return: com.sgs.ecom.member.util.ResultBody
     **/
    @HystrixCommand
    @AuthRequired(login = "NULL", sign = true)
    @RequestMapping(value = "qryApplicationInfoByOrderNo", method = { RequestMethod.POST })
    public ResultBody qryApplicationInfoByOrderNo (
            @RequestBody OrderApplicationOtherReq orderApplicationOtherReq)throws Exception{
        return ResultBody.newInstance(orderApplicationService.qryApplicationInfoByOrderNo(orderApplicationOtherReq,null));
    }

    /**
     * @Description : LV3 针对RSTS-SDS申请表保存化学资料信息接口-嵌入lv3页面-需要登录校验
     * <AUTHOR> Zhang
     * @Date  2023/9/23
     * @param token:
     * @param orderApplicationOtherReq:
     * @return: com.sgs.ecom.member.util.ResultBody
     **/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "saveApplicationInfoByOrderNoAndPermission", method = { RequestMethod.POST })
    public ResultBody saveApplicationInfoByOrderNoAndPermission (
            @RequestHeader(value="accessToken") String token,
            @Validated(BaseBean.Insert.class)
            @RequestBody OrderApplicationOtherReq orderApplicationOtherReq)throws Exception{
        return ResultBody.newInstance(orderApplicationService.saveApplicationInfoByOrderNo(orderApplicationOtherReq,getUserDTO(token)));
    }

    /**
     * @Description : LV3 针对RSTS-SDS申请表保存化学资料信息接口-独立页面-不需要登录校验
     * <AUTHOR> Zhang
     * @Date  2023/9/23
     * @param orderApplicationOtherReq:
     * @return: com.sgs.ecom.member.util.ResultBody
     **/
    @HystrixCommand
    @AuthRequired(login = "NULL", sign = true)
    @RequestMapping(value = "saveApplicationInfoByOrderNo", method = { RequestMethod.POST })
    public ResultBody saveApplicationInfoByOrderNo (
            @RequestBody OrderApplicationOtherReq orderApplicationOtherReq)throws Exception{
        return ResultBody.newInstance(orderApplicationService.saveApplicationInfoByOrderNo(orderApplicationOtherReq,null));
    }



}
