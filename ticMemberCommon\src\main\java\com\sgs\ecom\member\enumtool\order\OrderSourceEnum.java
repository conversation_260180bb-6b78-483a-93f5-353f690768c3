package com.sgs.ecom.member.enumtool.order;

public enum OrderSourceEnum {
    A("a", "100", "静默单"),//tic
    <PERSON>("b", "101", "线下来源"),
    C("c", "102", "TICleads"),//oiq
    D("d", "103", "商品页"),//tic
    <PERSON>("e", "104", "旺旺"),
    F("f", "105", "转介绍"),//tic
    <PERSON>("g", "106", "重点客户"),
    H("h", "107", "复购"),
    I("i", "108", "CRM引导"),//oiq
    J("j", "109", "其他"),//oiq
    K("k", "111", "线下销售引导"),//oiq
    L("l", "110", "TIC LEADS"),//tic
    M("m", "112", "线下单"),
    N("n", "120", "静默下单"),//oiq
    O("o", "199", "空"),//oiq
    p("p", "113", "微信原好友"),//tic
    Q("q", "114", "其他渠道"),//tic
    R("r", "115", "订单列表"),//tic
    ;
    OrderSourceEnum(String name, String index, String nameCh) {
        this.name = name;
        this.index = index;
        this.nameCh = nameCh;
    }



    public static String getNameCh(String index) {
        for (OrderSourceEnum c : OrderSourceEnum.values()) {
            if (c.getIndex().equals(index)) {
                return c.getNameCh();
            }
        }
        return "";
    }





    private String name;
    private String index;
    private String nameCh;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }
}
