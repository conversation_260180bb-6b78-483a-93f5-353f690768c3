package com.sgs.ecom.member.dto.min;

import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.sgs.ecom.member.base.BaseOrderFilter;

public class Style {
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    @BeanAnno(dtocls = {Font.class})
    private Font font;

    public Font getFont() {
        return font;
    }

    public void setFont(Font font) {
        this.font = font;
    }
}
