package com.sgs.ecom.member.dto.pay;


import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.enumtool.pay.PayMethodEnum;


public class PaymentInfoDTO {
    private String payMethod;
    private String payMethodValue;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int isDefault;
    private int sortShow;

    private BankDTO bankDTO;

    public PaymentInfoDTO() {
    }

    public PaymentInfoDTO(String key,String lua) {
        this.payMethod = key;
        PayMethodEnum payMethodEnum=PayMethodEnum.getEnum(key);
        if(payMethodEnum!=null){
            if("zh_CN".equals(lua)){
                this.payMethodValue = payMethodEnum.getNameCh();
            }
            if("en_US".equals(lua)){
                this.payMethodValue = payMethodEnum.getNameEn();
            }
            this.sortShow=payMethodEnum.getSortShow();
        }
    }

    public String getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(String payMethod) {
        this.payMethod = payMethod;
    }

    public String getPayMethodValue() {
        return payMethodValue;
    }

    public void setPayMethodValue(String payMethodValue) {
        this.payMethodValue = payMethodValue;
    }

    public int getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(int isDefault) {
        this.isDefault = isDefault;
    }

    public BankDTO getBankDTO() {
        return bankDTO;
    }

    public void setBankDTO(BankDTO bankDTO) {
        this.bankDTO = bankDTO;
    }

    public int getSortShow() {
        return sortShow;
    }

    public void setSortShow(int sortShow) {
        this.sortShow = sortShow;
    }
}
