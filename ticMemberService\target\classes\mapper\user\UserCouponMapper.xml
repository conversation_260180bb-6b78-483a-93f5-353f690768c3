<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.user.dao.UserCouponMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.vo.VOUserCoupon" >
    <id column="COUPON_ID" property="couponId" jdbcType="BIGINT" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="COUPON_CODE" property="couponCode" jdbcType="VARCHAR" />
    <result column="COUPON_NAME" property="couponName" jdbcType="VARCHAR" />
    <result column="COUPON_TYPE" property="couponType" jdbcType="INTEGER" />
    <result column="COUPON_SCOPE" property="couponScope" jdbcType="VARCHAR" />
    <result column="COUPON_DESC" property="couponDesc" jdbcType="VARCHAR" />
    <result column="USE_LIMIT" property="useLimit" jdbcType="INTEGER" />
    <result column="USE_AMOUNT" property="useAmount" jdbcType="INTEGER" />
    <result column="DISCOUNT_RULE" property="discountRule" jdbcType="INTEGER" />
    <result column="DISCOUNT_NUMS" property="discountNums" jdbcType="INTEGER" />
    <result column="DISCOUNT_RATE" property="discountRate" jdbcType="DECIMAL" />
    <result column="IS_COMPOSIT" property="isComposit" jdbcType="INTEGER" />
    <result column="BUY_PRICE" property="buyPrice" jdbcType="INTEGER" />
    <result column="STORE_ID" property="storeId" jdbcType="BIGINT" />
    <result column="SKU_ID" property="skuId" jdbcType="BIGINT" />
    <result column="AREA_ID" property="areaId" jdbcType="BIGINT" />
    <result column="EFF_DATE" property="effDate" jdbcType="DATE" />
    <result column="EXP_DATE" property="expDate" jdbcType="DATE" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="INTEGER" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
    <result column="COUPON_NO" property="couponNo" jdbcType="VARCHAR" />
    <result column="DISCOUNT_AMOUNT" property="discountAmount" jdbcType="DECIMAL" />
    <result column="BUSINESS_RELATE" property="businessRelateStr" jdbcType="VARCHAR" />
    <result column="COUPON_RANGE" property="couponRange" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    COUPON_ID, USER_ID, COUPON_CODE, COUPON_NAME, COUPON_TYPE, COUPON_SCOPE, COUPON_DESC, 
    USE_LIMIT, USE_AMOUNT, DISCOUNT_RULE, DISCOUNT_NUMS, DISCOUNT_RATE, IS_COMPOSIT, 
    BUY_PRICE, STORE_ID, SKU_ID, AREA_ID, EFF_DATE, EXP_DATE, ORDER_NO, STATE, CREATE_DATE, 
    STATE_DATE,COUPON_NO,DISCOUNT_AMOUNT,BUSINESS_RELATE,COUPON_RANGE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from USER_COUPON
    where COUPON_ID = #{couponId,jdbcType=BIGINT}
  </select>
  <select id="selectVOCouponList" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from USER_COUPON
    <include refid="baseQueryWhere"/>
    order by STATE asc,CREATE_DATE desc,COUPON_ID desc ;
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from USER_COUPON
    where COUPON_ID = #{couponId,jdbcType=BIGINT}
  </delete>

  <insert id="insertSelective" parameterType="com.sgs.ecom.member.vo.VOUserCoupon" >
    insert into USER_COUPON
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="couponId != null" >
        COUPON_ID,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
      <if test="couponCode != null" >
        COUPON_CODE,
      </if>
      <if test="couponName != null" >
        COUPON_NAME,
      </if>
      <if test="couponType != null" >
        COUPON_TYPE,
      </if>
      <if test="couponScope != null" >
        COUPON_SCOPE,
      </if>
      <if test="couponDesc != null" >
        COUPON_DESC,
      </if>
      <if test="useLimit != null" >
        USE_LIMIT,
      </if>
      <if test="useAmount != null" >
        USE_AMOUNT,
      </if>
      <if test="discountRule != null" >
        DISCOUNT_RULE,
      </if>
      <if test="discountNums != null" >
        DISCOUNT_NUMS,
      </if>
      <if test="discountRate != null" >
        DISCOUNT_RATE,
      </if>
      <if test="isComposit != null" >
        IS_COMPOSIT,
      </if>
      <if test="buyPrice != null" >
        BUY_PRICE,
      </if>
      <if test="storeId != null" >
        STORE_ID,
      </if>
      <if test="skuId != null" >
        SKU_ID,
      </if>
      <if test="areaId != null" >
        AREA_ID,
      </if>
      <if test="effDate != null" >
        EFF_DATE,
      </if>
      <if test="expDate != null" >
        EXP_DATE,
      </if>
      <if test="orderNo != null" >
        ORDER_NO,
      </if>
      <if test="state != null" >
        STATE,
      </if>
      <if test="createDate != null" >
        CREATE_DATE,
      </if>
      <if test="stateDate != null" >
        STATE_DATE,
      </if>
      <if test="couponNo != null" >
        COUPON_NO,
      </if>
      <if test="couponMemo != null" >
        COUPON_MEMO,
      </if>
      <if test="discountAmount != null" >
        DISCOUNT_AMOUNT,
      </if>
      <if test="csCode != null" >
        CS_CODE,
      </if>
      <if test="businessRelateStr != null" >
        BUSINESS_RELATE,
      </if>
      <if test="showName != null" >
        SHOW_NAME,
      </if>
      <if test="couponRange != null" >
        COUPON_RANGE,
      </if>
      <if test="couponExplain != null" >
        COUPON_EXPLAIN,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="couponId != null" >
        #{couponId,jdbcType=BIGINT},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="couponCode != null" >
        #{couponCode,jdbcType=VARCHAR},
      </if>
      <if test="couponName != null" >
        #{couponName,jdbcType=VARCHAR},
      </if>
      <if test="couponType != null" >
        #{couponType,jdbcType=INTEGER},
      </if>
      <if test="couponScope != null" >
        #{couponScope,jdbcType=VARCHAR},
      </if>
      <if test="couponDesc != null" >
        #{couponDesc,jdbcType=VARCHAR},
      </if>
      <if test="useLimit != null" >
        #{useLimit,jdbcType=INTEGER},
      </if>
      <if test="useAmount != null" >
        #{useAmount,jdbcType=INTEGER},
      </if>
      <if test="discountRule != null" >
        #{discountRule,jdbcType=INTEGER},
      </if>
      <if test="discountNums != null" >
        #{discountNums,jdbcType=INTEGER},
      </if>
      <if test="discountRate != null" >
        #{discountRate},
      </if>
      <if test="isComposit != null" >
        #{isComposit,jdbcType=INTEGER},
      </if>
      <if test="buyPrice != null" >
        #{buyPrice,jdbcType=INTEGER},
      </if>
      <if test="storeId != null" >
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="skuId != null" >
        #{skuId,jdbcType=BIGINT},
      </if>
      <if test="areaId != null" >
        #{areaId,jdbcType=BIGINT},
      </if>
      <if test="effDate != null" >
        #{effDate,jdbcType=DATE},
      </if>
      <if test="expDate != null" >
        #{expDate,jdbcType=DATE},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        #{state,jdbcType=INTEGER},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        #{stateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="couponNo != null" >
        #{couponNo,jdbcType=VARCHAR},
      </if>
      <if test="couponMemo != null" >
        #{couponMemo,jdbcType=VARCHAR},
      </if>
      <if test="discountAmount != null" >
        #{discountAmount},
      </if>
      <if test="csCode != null" >
        #{csCode},
      </if>
      <if test="businessRelateStr != null" >
        #{businessRelateStr},
      </if>
      <if test="showName != null" >
        #{showName},
      </if>
      <if test="couponRange != null" >
        #{couponRange},
      </if>
      <if test="couponExplain != null" >
        #{couponExplain},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.ecom.member.vo.VOUserCoupon" >
    update USER_COUPON
    <set >
      <if test="userId != null" >
        USER_ID = #{userId,jdbcType=BIGINT},
      </if>
      <if test="couponCode != null" >
        COUPON_CODE = #{couponCode,jdbcType=VARCHAR},
      </if>
      <if test="couponName != null" >
        COUPON_NAME = #{couponName,jdbcType=VARCHAR},
      </if>
      <if test="couponType != null" >
        COUPON_TYPE = #{couponType,jdbcType=INTEGER},
      </if>
      <if test="couponScope != null" >
        COUPON_SCOPE = #{couponScope,jdbcType=VARCHAR},
      </if>
      <if test="couponDesc != null" >
        COUPON_DESC = #{couponDesc,jdbcType=VARCHAR},
      </if>
      <if test="useLimit != null" >
        USE_LIMIT = #{useLimit,jdbcType=INTEGER},
      </if>
      <if test="useAmount != null" >
        USE_AMOUNT = #{useAmount,jdbcType=INTEGER},
      </if>
      <if test="discountRule != null" >
        DISCOUNT_RULE = #{discountRule,jdbcType=INTEGER},
      </if>
      <if test="discountNums != null" >
        DISCOUNT_NUMS = #{discountNums,jdbcType=INTEGER},
      </if>
      <if test="discountRate != null" >
        DISCOUNT_RATE = #{discountRate,jdbcType=DOUBLE},
      </if>
      <if test="isComposit != null" >
        IS_COMPOSIT = #{isComposit,jdbcType=INTEGER},
      </if>
      <if test="buyPrice != null" >
        BUY_PRICE = #{buyPrice,jdbcType=INTEGER},
      </if>
      <if test="storeId != null" >
        STORE_ID = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="skuId != null" >
        SKU_ID = #{skuId,jdbcType=BIGINT},
      </if>
      <if test="areaId != null" >
        AREA_ID = #{areaId,jdbcType=BIGINT},
      </if>
      <if test="effDate != null" >
        EFF_DATE = #{effDate,jdbcType=DATE},
      </if>
      <if test="expDate != null" >
        EXP_DATE = #{expDate,jdbcType=DATE},
      </if>
      <if test="orderNo != null" >
        ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        STATE = #{state,jdbcType=INTEGER},
      </if>
      <if test="createDate != null" >
        CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        STATE_DATE = #{stateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="couponNo != null" >
        COUPON_NO = #{couponNo},
      </if>
      <if test="discountAmount != null" >
        DISCOUNT_AMOUNT = #{discountAmount},
      </if>
    </set>
    where COUPON_ID = #{couponId,jdbcType=BIGINT}
  </update>


  <!--自定义 -->
  <sql id="sqlListDTO" >
    COUPON_ID, USER_ID, COUPON_CODE, COUPON_NAME, COUPON_TYPE, COUPON_SCOPE, COUPON_DESC,
    USE_LIMIT, USE_AMOUNT, DISCOUNT_RULE, DISCOUNT_NUMS, DISCOUNT_RATE, IS_COMPOSIT,
    BUY_PRICE, STORE_ID, SKU_ID, AREA_ID, EFF_DATE, EXP_DATE, ORDER_NO, CREATE_DATE,
    STATE_DATE,COUPON_NO,DISCOUNT_AMOUNT,IF(STATE=0 and CURDATE() &gt; EXP_DATE,2,STATE) STATE,
    if(EFF_DATE &gt;CURDATE(),1,0) TIME_FLG,SHOW_NAME,COUPON_EXPLAIN,COUPON_RANGE
  </sql>
  <resultMap id="resultDTO" type="com.sgs.ecom.member.dto.user.UserCouponDTO" >
    <id column="COUPON_ID" property="couponId" jdbcType="BIGINT" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="COUPON_CODE" property="couponCode" jdbcType="VARCHAR" />
    <result column="COUPON_NAME" property="couponName" jdbcType="VARCHAR" />
    <result column="COUPON_TYPE" property="couponType" jdbcType="INTEGER" />
    <result column="COUPON_SCOPE" property="couponScope" jdbcType="VARCHAR" />
    <result column="COUPON_DESC" property="couponDesc" jdbcType="VARCHAR" />
    <result column="USE_LIMIT" property="useLimit" jdbcType="INTEGER" />
    <result column="USE_AMOUNT" property="useAmount" jdbcType="DECIMAL" />
    <result column="DISCOUNT_RULE" property="discountRule" jdbcType="INTEGER" />
    <result column="DISCOUNT_NUMS" property="discountNums" jdbcType="INTEGER" />
    <result column="DISCOUNT_RATE" property="discountRate" jdbcType="DECIMAL" />
    <result column="IS_COMPOSIT" property="isComposit" jdbcType="INTEGER" />
    <result column="BUY_PRICE" property="buyPrice" jdbcType="INTEGER" />
    <result column="STORE_ID" property="storeId" jdbcType="BIGINT" />
    <result column="SKU_ID" property="skuId" jdbcType="BIGINT" />
    <result column="AREA_ID" property="areaId" jdbcType="BIGINT" />
    <result column="EFF_DATE" property="effDate" jdbcType="DATE" />
    <result column="EXP_DATE" property="expDate" jdbcType="DATE" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="INTEGER" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
    <result column="COUPON_NO" property="couponNo" jdbcType="VARCHAR" />
    <result column="DISCOUNT_AMOUNT" property="discountAmount" jdbcType="DECIMAL" />
    <result column="TIME_FLG" property="timeFlg" jdbcType="INTEGER" />
    <result column="SHOW_NAME" property="showName" jdbcType="VARCHAR" />
    <result column="COUPON_EXPLAIN" property="couponExplain" jdbcType="VARCHAR" />
    <result column="COUPON_RANGE" property="couponRange" jdbcType="VARCHAR" />
  </resultMap>

  <select id="selectListByMap" resultMap="resultDTO" >
    select
    <include refid="sqlListDTO" />
    from USER_COUPON
    <include refid="baseQueryWhere"/>
    order by STATE asc,TIME_FLG asc, CREATE_DATE desc,COUPON_ID desc
  </select>


  <resultMap id="resultCouponOrder" type="com.sgs.ecom.member.dto.user.OrderCouponDTO" >
    <id column="COUPON_ID" property="couponId" jdbcType="BIGINT" />
    <result column="COUPON_NAME" property="couponName" jdbcType="VARCHAR" />
    <result column="USE_AMOUNT" property="useAmount" jdbcType="DECIMAL" />
    <result column="DISCOUNT_AMOUNT" property="discountAmount" jdbcType="DECIMAL" />
    <result column="EFF_DATE" property="effDate" jdbcType="DATE" />
    <result column="EXP_DATE" property="expDate" jdbcType="DATE" />
    <result column="COUPON_RANGE" property="couponRange" jdbcType="VARCHAR" />
    <result column="BUSINESS_RELATE" property="businessRelate" jdbcType="VARCHAR" />
  </resultMap>
  <select id="selectCouponOrderList" resultMap="resultCouponOrder" >
    select
    COUPON_ID,COUPON_NAME,USE_AMOUNT,DISCOUNT_AMOUNT,EFF_DATE, EXP_DATE,COUPON_RANGE,BUSINESS_RELATE
    from USER_COUPON
    <include refid="baseQueryWhere"/>
    order by STATE asc,CREATE_DATE desc,COUPON_ID desc
  </select>

  <sql id="baseQueryWhere">
    where 1=1 and STATE!=3
    <if test="userId != null">
      AND USER_ID=#{userId}
    </if>
    <if test="orderNo != null">
      AND ORDER_NO=#{orderNo}
    </if>

    <if test="state != null">
      <if test="state == 0">
        AND STATE=0 AND EXP_DATE &gt;= CURDATE()
      </if>
      <if test="state == 1">
        AND STATE=1
      </if>
      <if test="state == 2">
        AND ((STATE=0 AND EXP_DATE &lt; CURDATE()) or STATE=2)
      </if>
    </if>


    <if test="couponCodeList != null ">
      AND COUPON_CODE in
      <foreach collection="couponCodeList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="couponRangeList != null ">
      AND COUPON_RANGE in
      <foreach collection="couponRangeList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>

  </sql>

  <resultMap id="stateAndNum" type="com.sgs.ecom.member.dto.custom.StateAndNumDTO" >
    <result column="STATE" property="state" jdbcType="VARCHAR" />
    <result column="num" property="num" jdbcType="BIGINT" />
  </resultMap>
  <select id="selectCountByState"  resultMap="stateAndNum"  >
    select STATE ,count(1) as num from USER_COUPON
    where 1=1
    <if test="userId != null">
      AND USER_ID=#{userId}
    </if>
    group by STATE
  </select>

  <select id="selectExpNum" resultType="java.lang.Integer" >
    select count(1) as num from USER_COUPON
    where STATE=0 AND USER_ID=#{userId}
    <if test="use == 1">
      AND EXP_DATE &gt;= CURDATE()
    </if>
    <if test="use == 0">
      AND EXP_DATE &lt; CURDATE()
    </if>
  </select>



  <select id="selectMaxCouponNum" resultType="java.lang.Integer" >
    select count(1) as num from USER_COUPON
    where STATE!=3 AND USER_ID=#{userId}  AND COUPON_CODE=#{couponCode}
  </select>

  <resultMap id="keyAndNum" type="com.sgs.ecom.member.dto.custom.KeyNumDTO" >
    <result column="COUPON_CODE" property="key" jdbcType="VARCHAR" />
    <result column="num" property="num" jdbcType="BIGINT" />
  </resultMap>

  <select id="selectCouponNum"  resultMap="keyAndNum"  >
    select COUPON_CODE,count(1) as num from USER_COUPON
    where  STATE=0 AND EXP_DATE &gt;= CURDATE() AND USER_ID=#{userId} group by COUPON_CODE
  </select>




</mapper>