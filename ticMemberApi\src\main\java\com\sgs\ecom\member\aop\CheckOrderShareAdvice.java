package com.sgs.ecom.member.aop;

import com.platform.util.ValidationUtil;
import com.sgs.ecom.member.domain.cust.service.interfaces.ICustApplyRelateDomainService;
import com.sgs.ecom.member.domain.order.service.interfaces.IOrderBaseInfoDomainService;
import com.sgs.ecom.member.domain.order.service.interfaces.IOrderCustomerDomainService;
import com.sgs.ecom.member.dto.base.BaseOrderDTO;
import com.sgs.ecom.member.dto.cust.CustUserDTO;
import com.sgs.ecom.member.dto.order.CustomerIdDTO;
import com.sgs.ecom.member.dto.user.UserDTO;
import com.sgs.ecom.member.enumtool.ResultEnumCode;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.check.CheckUtil;
import com.sgs.ecom.member.util.collection.MapUtil;
import com.sgs.ecom.member.util.select.SelectBaseUtil;
import org.apache.commons.lang.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Aspect
@Component
public class CheckOrderShareAdvice extends ControllerUtil {

	@Resource
	private IOrderBaseInfoDomainService orderBaseInfoDomainService;
	@Resource
	private IOrderCustomerDomainService orderCustomerDomainService;
	@Resource
	private ICustApplyRelateDomainService custApplyRelateDomainService;

	@Pointcut("@annotation(com.sgs.ecom.member.annotation.CheckOrderShare)")
	@Order(10)
	public void checkOrderShare() {
	}

	@Before("checkOrderShare()")
	public void doBefore(JoinPoint joinPoint) throws Exception {

		HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
		//第一个为token 第2个为对象 对象包含orderNo
		Object[] args = joinPoint.getArgs();
		if (args.length < 2 && args[1] instanceof String) {//当小于两个对象的时候不处理
			return;
		}
		Map<String, Object> map = MapUtil.objectToMap(args[1]);
		if (!map.containsKey(SelectBaseUtil.ORDER_NO) || ValidationUtil.isEmpty(map.get(SelectBaseUtil.ORDER_NO))) {
			return;
		}
		String orderNo=map.get(SelectBaseUtil.ORDER_NO).toString();
		if(orderNo.contains(",")){
			return;
		}
		String bu = request.getHeader("bu");
		if(StringUtils.isNotBlank(bu)){
			BaseOrderDTO baseOrderDTO=orderBaseInfoDomainService.selectBaseByOrderNo(orderNo);
			if(!baseOrderDTO.getBu().equals(bu)){
				throw new BusinessException(ResultEnumCode.USER_IS_NULL);
			}
		}

		String accessToken = request.getHeader("accessToken");
		UserDTO userDTO =getUserDTO(accessToken);
		//获取订单的权限
		List<CustomerIdDTO> customerIdDTOList=orderCustomerDomainService.qryCustomerByOrderNo(orderNo);
		//查询用户挂在是否有挂在企业下面
		if(ValidationUtil.isEmpty(customerIdDTOList)){
			throw new BusinessException(ResultEnumCode.USER_IS_NULL);
		}

		List<Long> custIdList=new ArrayList<>();
		for(int n=0;n<customerIdDTOList.size();n++){
			CustomerIdDTO customerIdDTO=customerIdDTOList.get(n);
			if(customerIdDTO.getUserId()!=null && CheckUtil.isSame(customerIdDTO.getUserId(),userDTO.getUserId())){
				return;
			}
			if(customerIdDTO.getCustId()!=null){
				custIdList.add(customerIdDTO.getCustId());
			}
		}

		if(ValidationUtil.isEmpty(custIdList)){
			throw new BusinessException(ResultEnumCode.USER_IS_NULL);
		}

		List<CustUserDTO> list=custApplyRelateDomainService.qryMarkUserByCust(custIdList);
		Map<Long,CustUserDTO> userMap=list.stream().collect(Collectors.toMap(CustUserDTO::getUserId, Function.identity(),
				(key1, key2) -> key1, LinkedHashMap::new));
		if(!userMap.containsKey(userDTO.getUserId())){
			throw new BusinessException(ResultEnumCode.USER_IS_NULL);
		}


	}
}
