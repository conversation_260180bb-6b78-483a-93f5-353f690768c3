package com.sgs.ecom.member.enumtool.bbc;

public enum AppFormEnum {

	CRITICAL_DEFECT("criticalDefect", "致命缺陷","levelTest"),
	SERIOUS_DEFECT("seriousDefect", "重缺陷","levelTest"),
	MILD_DEFECT("mildDefect", "轻缺陷","levelTest"),
	PRODUCT_NAME("productName", "产品名称","inspectionInformation"),
	CREDIT_NO("creditNo", "信用证号","inspectionInformation"),
	ORDER_NO("orderNo", "订单号","inspectionInformation"),
	NUMBER("number", "款号","inspectionInformation"),
	MAKER("maker", "目的地","inspectionInformation"),
	SAIL_SCHEDULE("sailSchedule", "船期","inspectionInformation"),

	FACTORY_NAME("factoryName", "工厂名称","inspectionAddr"),

	CONTACT_PERSON("contactPerson", "联系人","inspectionAddr"),
	PHONE("phone", "电话","inspectionAddr"),
	PROVINCE("province", "省","inspectionAddr"),
	CITY("city", "市","inspectionAddr"),
	ADDRESS("address", "详细地址","inspectionAddr"),

	//验货资料是图片 是多个
	INS_CHECK_IMG("insCheckImg","验货资料","inspectionData"),

	FORM_MEMO("formMemo", "申请表备注","formMemo"),
	//用户表的地址字段
	USER_NAME("userName", "联系人",""),
	USER_PHONE("userPhone", "电话",""),
	COMPANY_ADDRESS("companyAddress", "详细地址",""),
	COMPANY_NAME("companyName", "公司名称",""),
	USER_ID("userId", "用户id",""),
	ADDRESS_ID("addressId", "地址id",""),
	SAMPLE_NUMS("sampleNums", "抽样数量",""),
	USER_MAIL("userMail", "用户邮箱",""),
	TOWN("town", "区",""),

	CHECK_DATE("checkDate", "检验日期",""),

	//oiq的实验室code
	LAB_CODE("labCode", "实验室编号",""),
	//5合1的样品code
	SAMPLE_CODE("sampleCode", "样品编号",""),

	SAMPLE_ID("sampleId", "样品Id",""),
	EXPRESS_CODE("expressCode", "物流公司",""),
	EXPRESS_NO("expressNo", "物流单号",""),
	;

	AppFormEnum(String name,  String nameCh,String formCode) {
		this.name = name;
		this.nameCh = nameCh;
		this.formCode=formCode;
	}

	public static String getNameCh(String name) {
		for (AppFormEnum c : AppFormEnum.values()) {
			if (c.getName().equals(name)) {
				return c.getNameCh();
			}
		}
		return "";
	}

	public static String getFormCode(String name) {
		for (AppFormEnum c : AppFormEnum.values()) {
			if (c.getName().equals(name)) {
				return c.getFormCode();
			}
		}
		return null;
	}

	private String name;
	private String nameCh;
	private String formCode;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}


	public String getNameCh() {
		return nameCh;
	}

	public void setNameCh(String nameCh) {
		this.nameCh = nameCh;
	}

	public String getFormCode() {
		return formCode;
	}

	public void setFormCode(String formCode) {
		this.formCode = formCode;
	}
}
