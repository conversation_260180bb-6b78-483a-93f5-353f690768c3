package com.sgs.ecom.member.controller.tic;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.base.BaseBean;
import com.sgs.ecom.member.request.OrderNoReq;
import com.sgs.ecom.member.request.tic.InsOrderReq;
import com.sgs.ecom.member.request.tic.TicOrderReq;
import com.sgs.ecom.member.request.tic.TicOrderSaveReq;
import com.sgs.ecom.member.infrastructure.fegin.sso.rpc.SSORpcService;
import com.sgs.ecom.member.service.tic.interfaces.ITicOrderService;
import com.sgs.ecom.member.service.tic.interfaces.ITicRedisService;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import com.sgs.ecom.member.valid.OrderValid;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.tic/order")
public class TicOrderController extends ControllerUtil {

	@Autowired
	private SSORpcService ssoRpcService;
	@Autowired
	private ITicRedisService iTicRedisService;
	@Autowired
	private ITicOrderService ticOrderService;


	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "submitInsOrder", method = {RequestMethod.POST})
	public ResultBody submitInsOrder(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody InsOrderReq insOrderReq)throws Exception {
		return ResultBody.success(iTicRedisService.submitOrderByRedis(insOrderReq, getUserDTO(token)));
	}

	/**
	*@Function: submitOrder
	*@Description 保存tic缓存的订单
	*@param: [token, insOrderReq]
	*@author: Xiwei_Qiu @date: 2022/7/27 @version:
	**/
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "submitOrder", method = {RequestMethod.POST})
	public ResultBody submitOrder(
		@RequestHeader(value = "accessToken") String token,
		@Validated(BaseBean.Insert.class)
		@RequestBody TicOrderReq ticOrderReq)throws Exception {
		return ResultBody.success(iTicRedisService.submitOrder(ticOrderReq, getUserDTO(token)));
	}

	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "getOrder", method = {RequestMethod.POST})
	public ResultBody getOrder(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody TicOrderReq ticOrderReq) throws Exception {
		return ResultBody.newInstance(iTicRedisService.getTicOrder(ticOrderReq.getTicKey(), getUserDTO(token)));
	}

	/**
	 * @Description : 新的模式下，通过缓存key查询订单信息-做拆合单规则处理数据
	 * <AUTHOR> Zhang
	 * @Date  2024/6/19
	 * @param token:
	 * @param ticOrderReq:
	 * @return: com.sgs.ecom.member.util.ResultBody
	 **/
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "qrySubmitOrder", method = {RequestMethod.POST})
	public ResultBody qrySubmitOrder(
			@RequestHeader(value = "accessToken") String token,
			@RequestBody TicOrderReq ticOrderReq) throws Exception {
		return ResultBody.newInstance(iTicRedisService.qrySubmitOrder(ticOrderReq.getTicKey(), getUserDTO(token)));
	}

	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "getUserCoupon", method = {RequestMethod.POST})
	public ResultBody getUserCoupon(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody TicOrderReq ticOrderReq) throws Exception {
		return ResultBody.newInstance(ticOrderService.getUserCoupon(ticOrderReq.getTicKey(), getUserDTO(token)));
	}


	/**
	 *@Function: saveOrder
	 *@Description 保存生成tic的订单
	 *@param: [token, insOrderSaveReq]
	 *@author: Xiwei_Qiu @date: 2022/2/28 @version:
	 **/
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "saveOrder", method = {RequestMethod.POST})
	public ResultBody saveOrder(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody TicOrderSaveReq ticOrderSaveReq) throws Exception {
		JSONObject jsonObject=ticOrderService.saveOrder(ticOrderSaveReq, getUserDTO(token),token);
	/*	List<String> list=ticOrderSaveReq.getOrderNoList();
		if(!ValidationUtil.isEmpty(list)){
			eventApiUtil.saveEvent(list.get(0), EventEnum.MOD_USER_LABEL);
		}*/

		ssoRpcService.refresh(new JSONObject().toString());
		return ResultBody.newInstance(jsonObject);
	}

	/**
	 * @Description :LV3提交订单
	 * <AUTHOR> Zhang
	 * @Date  2024/6/19
	 * @param token:
	 * @param ticOrderSaveReq:
	 * @return: com.sgs.ecom.member.util.ResultBody
	 **/
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "saveOrderNewRuler", method = {RequestMethod.POST})
	public ResultBody saveOrderNewRuler(
			@RequestHeader(value = "accessToken") String token,
			@Validated(value = {OrderValid.ShopOrderCheck.class})
			@RequestBody TicOrderSaveReq ticOrderSaveReq) throws Exception {
		JSONObject jsonObject=ticOrderService.saveShopOrder(ticOrderSaveReq, getUserDTO(token),token);
	/*	List<String> list=ticOrderSaveReq.getOrderNoList();
		if(!ValidationUtil.isEmpty(list)){
			eventApiUtil.saveEvent(list.get(0), EventEnum.MOD_USER_LABEL);
		}*/

		ssoRpcService.refresh(new JSONObject().toString());
		return ResultBody.newInstance(jsonObject);
	}

	/**
	 * @Description :测试自动化接口
	 * <AUTHOR> Zhang
	 * @Date  2024/10/10
	 * @param token:
	 * @param ticOrderSaveReq:
	 * @return: com.sgs.ecom.member.util.ResultBody
	 **/
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "saveOrderNewRulerTest", method = {RequestMethod.POST})
	public ResultBody saveOrderNewRulerTets(
			@RequestHeader(value = "accessToken") String token,
			@Validated(value = {OrderValid.ShopOrderCheck.class})
			@RequestBody TicOrderSaveReq ticOrderSaveReq) throws Exception {
		JSONObject jsonObject=ticOrderService.saveOrderNewRulerTest(ticOrderSaveReq, getUserDTO(token),token);
		ssoRpcService.refresh(new JSONObject().toString());
		return ResultBody.newInstance(jsonObject);
	}


	/**
	 *@Function: saveOrder
	 *@Description 保存生成tic的订单
	 *@param: [token, insOrderSaveReq]
	 *@author: Xiwei_Qiu @date: 2022/2/28 @version:
	 **/
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "saveOrderTest", method = {RequestMethod.POST})
	public ResultBody saveOrderTest(
			@RequestHeader(value = "accessToken") String token,
			@RequestBody TicOrderSaveReq ticOrderSaveReq) throws Exception {
		JSONObject jsonObject=ticOrderService.saveOrderTest(ticOrderSaveReq, getUserDTO(token),token);
		ssoRpcService.refresh(new JSONObject().toString());
		return ResultBody.newInstance(jsonObject);
	}
	
	 /**
     *@Function: bbc
     *@Description bbc 查询商品下过单的数量
     *@param: [ticOrderReq]
     *@author: sundeqing @date: 2022/10/21 @version:
     **/
    @AuthRequired(login = "NULL", sign = true)
    @RequestMapping(value = "queryProductNum", method = { RequestMethod.POST })
    public ResultBody queryProductNum (
    		@RequestBody TicOrderReq ticOrderReq) throws Exception {
    	return ResultBody.newInstance(ticOrderService.queryProductNum(ticOrderReq));
    }



	
}
