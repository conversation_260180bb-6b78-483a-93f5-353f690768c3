package com.sgs.ecom.member.dto.order;

import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.dto.application.OrderApplicationAttrDTO;
import com.sgs.ecom.member.dto.base.BaseOrderDTO;
import com.sgs.ecom.member.dto.center.SysApplicationAttrDTO;
import com.sgs.ecom.member.dto.center.SysApplicationBasicDTO;
import com.sgs.ecom.member.dto.detail.OrderApplicationFormDTO;
import com.sgs.ecom.member.dto.detail.OrderAttachmentDTO;
import com.sgs.ecom.member.dto.detail.OrderReportDTO;
import com.sgs.ecom.member.entity.order.OrderComponent;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Zhang
 * @Description :
 * @date 2023/9/22
 */
public class OrderApplicationOtherDTO extends BaseOrderFilter {

//    @ApiAnno(groups={OrderApplicationOther.class})
//    @BeanAnno(dtocls={OrderApplicationBasicDTO.class})
    private List<OrderApplicationBasicDTO> orderApplicationBasicDTOList = new ArrayList<>();

    @ApiAnno(groups={OrderApplicationOther.class})
    private List<OrderComponentDTO> orderComponentDTOList = new ArrayList<>();

    @ApiAnno(groups={OrderApplicationOther.class})
    private String orderNo;

    @ApiAnno(groups={OrderApplicationOther.class})
    private String url;

    @ApiAnno(groups={OrderApplicationOther.class})
    private Map<String,Object> item;

    @ApiAnno(groups={OrderApplicationOther.class})
    private boolean isFileFlag;

    private String qryOrderNo;

    private long attachmentCount;

    private  List<OrderAttachmentDTO> attachmentDTOList;


    private List<OrderApplicationAttrDTO> attrDTOList;

    public List<OrderAttachmentDTO> getAttachmentDTOList() {
        return attachmentDTOList;
    }

    public void setAttachmentDTOList(List<OrderAttachmentDTO> attachmentDTOList) {
        this.attachmentDTOList = attachmentDTOList;
    }

    public List<OrderApplicationAttrDTO> getAttrDTOList() {
        return attrDTOList;
    }

    public void setAttrDTOList(List<OrderApplicationAttrDTO> attrDTOList) {
        this.attrDTOList = attrDTOList;
    }

    public String getQryOrderNo() {
        return qryOrderNo;
    }

    public void setQryOrderNo(String qryOrderNo) {
        this.qryOrderNo = qryOrderNo;
    }

    public boolean isFileFlag() {
        return isFileFlag;
    }

    public void setFileFlag(boolean fileFlag) {
        isFileFlag = fileFlag;
    }

    public Map<String, Object> getItem() {
        return item;
    }

    public void setItem(Map<String, Object> item) {
        this.item = item;
    }

    public List<OrderApplicationBasicDTO> getOrderApplicationBasicDTOList() {
        return orderApplicationBasicDTOList;
    }

    public void setOrderApplicationBasicDTOList(List<OrderApplicationBasicDTO> orderApplicationBasicDTOList) {
        this.orderApplicationBasicDTOList = orderApplicationBasicDTOList;
    }

    public List<OrderComponentDTO> getOrderComponentDTOList() {
        return orderComponentDTOList;
    }

    public void setOrderComponentDTOList(List<OrderComponentDTO> orderComponentDTOList) {
        this.orderComponentDTOList = orderComponentDTOList;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public long getAttachmentCount() {
        return attachmentCount;
    }

    public void setAttachmentCount(long attachmentCount) {
        this.attachmentCount = attachmentCount;
    }
}
