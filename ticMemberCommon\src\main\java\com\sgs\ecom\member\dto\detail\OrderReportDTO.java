package com.sgs.ecom.member.dto.detail;

import com.platform.annotation.ApiAnno;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.enumtool.aplication.ReportMethodEnum;
import com.sgs.ecom.member.enumtool.oiq.OiqReportMethodEnum;
import lombok.Data;

@Data
public class OrderReportDTO {

	@ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OrderList.class,BaseOrderFilter.OrderApplicationOther.class,BaseOrderFilter.WordForm.class})
	private String reportCompanyNameCn;
	@ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OrderList.class,BaseOrderFilter.WordForm.class})
	private String reportCompanyNameEn;
	@ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OrderList.class,BaseOrderFilter.OrderApplicationOther.class,BaseOrderFilter.WordForm.class})
	private String reportAddressCn;
	@ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OrderList.class,BaseOrderFilter.OrderApplicationOther.class,BaseOrderFilter.WordForm.class})
	private String reportAddressEn;
	private Long reportId;
	private Integer reportMethod;
	private String reportMethodMemo;
	private String reportMethodShow;
	private String reportForm;
	private String reportFormCode;
	private Integer reportTitleType;
	private String reportSendCc;
	@ApiAnno(groups={BaseOrderFilter.OrderApplicationOther.class})
	private String reportLuaCode;
	@ApiAnno(groups={BaseOrderFilter.OrderApplicationOther.class})
	private String reportLua;
	@ApiAnno(groups={BaseOrderFilter.OrderApplicationOther.class})
	private String orderNo;

	private String province;

	private String city;

	private String town;

	private Integer state;

	// 检测场所类别(原始json数据)
	private String detectionSiteCategory;
	// 检测场所类别(申请表打印用)
	private String detectionSiteCategoryStr;
	// 检测场所类别其他备注(申请表打印用)
	private String detectionSiteCategoryRemark;

	private String reportPerson; //报告联系列表
	private String oiqReportMethodShow;

	private String reportLanguage;
	private String labInfo;
	private String serviceType;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String reportMethodName;//

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private Integer reportType;

	private Integer reportRequirementsFlg;

	private String reportRequirements;

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private Integer isJudge;

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String judgeMemo;


	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String judgeMemoFile;


	@ApiAnno(groups={BaseQryFilter.Default.class})
	private Integer isPicture;

	public String getReportMethodName() {
		if(ValidationUtil.isEmpty(reportMethod) || reportMethod == 0){
			return "";
		}
		return ReportMethodEnum.getNameCh(reportMethod);
	}

	public String getReportMethodShow() {
		if(reportMethod==null){
			return "";
		}

		if(ReportMethodEnum.OTHER.getIndex()==reportMethod){
			return reportMethodMemo;
		}
		return ReportMethodEnum.getNameCh(reportMethod);
	}

	public String getOiqReportMethodShow() {
		if(reportMethod==null){
			return "";
		}

		if(ReportMethodEnum.OTHER.getIndex()==reportMethod){
			return reportMethodMemo;
		}
		return OiqReportMethodEnum.getNameCh(reportMethod);
	}
}
