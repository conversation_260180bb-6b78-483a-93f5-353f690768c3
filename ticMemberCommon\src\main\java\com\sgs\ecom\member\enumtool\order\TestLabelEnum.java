package com.sgs.ecom.member.enumtool.order;

public enum TestLabelEnum {
    CG("cg", 1, "常规测试"),
    FCG("fcg", 2, "非常规标准"),
    FB("fb", 3, "非标定制化"),
    XQ("xq", 4, "需求不清楚"),
    ;
    TestLabelEnum(String name, int index, String nameCh) {
        this.name = name;
        this.index = index;
        this.nameCh = nameCh;
    }


    public static String getNameCh(Integer index) {
        if(index==null){
            return "";
        }
        for (TestLabelEnum c :TestLabelEnum.values()) {
            if (c.getIndex()==index) {
                return c.getNameCh();
            }
        }
        return "";
    }





    private String name;
    private int index;
    private String nameCh;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }
}
