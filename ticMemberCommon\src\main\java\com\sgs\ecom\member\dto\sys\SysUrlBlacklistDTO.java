package com.sgs.ecom.member.dto.sys;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;

public class SysUrlBlacklistDTO {
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private Long id;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String type;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String content;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private Integer state;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content == null ? null : content.trim();
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }
}