package com.sgs.ecom.member.dto.oiq;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.dto.order.SubOrderDTO;
import com.sgs.ecom.member.util.serializer.PriceNumFormatSerializer;

import java.math.BigDecimal;
import java.util.List;

public class OiqSubDTO {

    @ApiAnno(groups={BaseOrderFilter.OiqSubList.class})
    @BeanAnno(dtocls = {SubOrderDTO.class})
    private List<SubOrderDTO> subOrderDTOList;
    @ApiAnno(groups={BaseOrderFilter.OiqSubList.class})
    private int num;
    @JsonSerialize(using = PriceNumFormatSerializer.class)
    @ApiAnno(groups={BaseOrderFilter.OiqSubList.class})
    private BigDecimal price;
    @ApiAnno(groups={BaseOrderFilter.OiqSubList.class})
    private BigDecimal differenceStatistics;

    public List<SubOrderDTO> getSubOrderDTOList() {
        return subOrderDTOList;
    }

    public void setSubOrderDTOList(List<SubOrderDTO> subOrderDTOList) {
        this.subOrderDTOList = subOrderDTOList;
    }

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getDifferenceStatistics() {
        return differenceStatistics;
    }

    public void setDifferenceStatistics(BigDecimal differenceStatistics) {
        this.differenceStatistics = differenceStatistics;
    }
}
