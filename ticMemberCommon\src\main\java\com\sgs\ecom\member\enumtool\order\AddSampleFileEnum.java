package com.sgs.ecom.member.enumtool.order;

/**
 * @Description :  样品详情回写是给出当前申请表字段关联附件
 * <AUTHOR>
 * @Date  2023/8/24
 **/
public enum AddSampleFileEnum {
    TEST_MEMO("testMemo", "AFL-DEFAULT"),

    ;

    private String sampleKey;
    private String applicationFormType;

    AddSampleFileEnum(String sampleKey, String applicationFormType) {
        this.sampleKey = sampleKey;
        this.applicationFormType = applicationFormType;
    }



    public String getSampleKey() {
        return sampleKey;
    }

    public void setSampleKey(String sampleKey) {
        this.sampleKey = sampleKey;
    }

    public String getApplicationFormType() {
        return applicationFormType;
    }

    public void setApplicationFormType(String applicationFormType) {
        this.applicationFormType = applicationFormType;
    }
}
