package com.sgs.ecom.member.enumtool.rsts;

/**
 * @Description : rsts样品字段枚举
 * <AUTHOR>
 * @Date  2023/10/13
 **/
public enum RstsSampleAreaEnum {

    BASIC("BASIC", 1, "基本信息"),//基础信息
    EXTEND("EXTEND", 2, "扩展信息"),//附件信息
    OTHER_ROHS("OTHER-ROHS", 3, "RoHS镀层测试"),//rohs4项
    OTHER_EN71("OTHER-EN71", 4, "EN71-3测试"),//EN71测试
    OTHER_PAHS("OTHER-PAHS", 5, "多环芳烃（PAHs）"),//多环芳烃信息
    OTHER_CUSTX("OTHER-CUSTX", 6, "X客户专用"),//X客户信息
    OTHER_LENOVO_BATTERY("OTHER-LENOVO_BATTERY", 7, "联想Outgas电池测试");//联想Outgas电池测试

    RstsSampleAreaEnum(String name, int index, String nameCh) {
        this.name = name;
        this.index = index;
        this.nameCh = nameCh;
    }

    public static Boolean getEnum(String name) {
        for (RstsSampleAreaEnum c : RstsSampleAreaEnum.values()) {
            if (name.equals(c.getName())) {
                return true;
            }
        }
        return false;
    }

    public static String getNameChByName(String name) {
        for (RstsSampleAreaEnum c : RstsSampleAreaEnum.values()) {
            if (name.equals(c.getName())) {
                return c.getNameCh();
            }
        }
        return "";
    }




    private String name;
    private int index;
    private String nameCh;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }
}
