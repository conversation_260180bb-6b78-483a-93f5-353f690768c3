package com.sgs.ecom.member.domain.order;

import com.alibaba.fastjson.JSON;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseCopyObj;
import com.sgs.ecom.member.dto.bbc.BbcSkuAttrDTO;
import com.sgs.ecom.member.entity.order.OrderProduct;
import com.sgs.ecom.member.enums.CreateOrderSplitType;
import com.sgs.ecom.member.enumtool.bbc.BbcSkuAttrEnum;
import com.sgs.ecom.member.request.tic.TicProductReq;
import com.sgs.ecom.member.util.order.OrderUtil;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * @ClassName OrderProductDO
 * <AUTHOR>
 * @Date 2024-06-23 17:32
 */
public class OrderProductDO extends OrderProduct {

    private BaseCopyObj baseCopy = new BaseCopyObj();

    public OrderProductDO() {
        super();
    }

    public OrderProduct init(TicProductReq ticProductReq, String orderNo, String dateStr, String colName, int nums) {
        OrderProduct product = new OrderProduct();
        product.setCreateDate(dateStr);
        product.setOrderNo(orderNo);
        baseCopy.copyWithNull(product, ticProductReq);
        product.setState(1);
        if(ValidationUtil.isEmpty(product.getSubBuCode())){
            product.setSubBuCode(ticProductReq.getStoreId() + "DEFAULT");
        }
        product.setProductImg(ticProductReq.getPicPath());
        product.setTotalPrice(ticProductReq.getPrice().multiply(new BigDecimal(ticProductReq.getQuantity())));
        if (CreateOrderSplitType.byNums.getName().equals(colName)) {
            product.setTotalPrice(ticProductReq.getPrice().multiply(new BigDecimal(1)));
            product.setQuantity(1);
            product.setRealTotalPrice(ticProductReq.getRealTotalPrice());
        }
        //新增提交订单的操作类型 1-新的合单模式 2-老的拆单逻辑 3- 老的合单逻辑
        List<BbcSkuAttrDTO> bbcSkuAttrDTOList =  !ValidationUtil.isEmpty(ticProductReq.getNewRulerSkuAttrList())
                ? ticProductReq.getNewRulerSkuAttrList() : ticProductReq.getSkuAttrList();
        product.setSkuAttr(getSkuAttr(bbcSkuAttrDTOList));
        product.setShopDisAmount(OrderUtil.toZero(product.getShopDisAmount()));
        product.setSubDiscountAmount(OrderUtil.toZero(product.getSubDiscountAmount()));
        product.setSubCsDiscountAmount(OrderUtil.toZero(product.getSubCsDiscountAmount()));
        product.setSubServiceAmount(OrderUtil.toZero(product.getSubServiceAmount()));
        // 最低售价
        product.setLowestPrice(OrderUtil.toZero(product.getLowestPrice()));
        // SKU费用
        product.setSkuPrice(OrderUtil.toZero(product.getSkuPrice()));
        // 最低售价补收差额
        product.setLowestPriceMargin(OrderUtil.toZero(product.getLowestPriceMargin()));
        return product;
    }

    private String getSkuAttr(List<BbcSkuAttrDTO> bbcSkuAttrDTOList) {
//        for(BbcSkuAttrDTO bbcSkuAttrDTO:bbcSkuAttrDTOList){
//            BbcSkuAttrEnum attrEnum=BbcSkuAttrEnum.getEnumByNameCh(bbcSkuAttrDTO.getAttrName());
//            if(!ValidationUtil.isEmpty(attrEnum) ){
//                bbcSkuAttrDTO.setAttrKey(attrEnum.getName());
//                bbcSkuAttrDTO.setSortShow(attrEnum.getSortShow());
//                bbcSkuAttrDTO.setShow(true);
//            }
//            if(BbcSkuAttrEnum.SAMPLING.getName().equals(bbcSkuAttrDTO.getAttrKey())){
//                bbcSkuAttrDTO.setShow(false);
//            }
//        }
        bbcSkuAttrDTOList.sort(Comparator.comparing(BbcSkuAttrDTO::getSortShow));
        return JSON.toJSONString(bbcSkuAttrDTOList);
    }
}
