package com.sgs.ecom.member.dto.center;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;

/**
 * <AUTHOR>
 */
public class BusinessLineDTO {

    public static final String CATEGORY="CATEGORY";
    public static final String LINE="LINE";
    @ApiAnno(groups={BaseQryFilter.QueryDtl.class})
    private String bu;
    @ApiAnno(groups={BaseQryFilter.QueryDtl.class})
    private String configName;
    @ApiAnno(groups={BaseQryFilter.QueryDtl.class})
    private Long configId;
    @ApiAnno(groups={BaseQryFilter.QueryDtl.class})
    private String configCode;
    @ApiAnno(groups={BaseQryFilter.QueryDtl.class})
    private String platformCode;
    @ApiAnno(groups={BaseQryFilter.QueryDtl.class})
    private Integer closeDays;

    public String getBu() {
        return bu;
    }

    public void setBu(String bu) {
        this.bu = bu;
    }

    public String getConfigName() {
        return configName;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public Long getConfigId() {
        return configId;
    }

    public void setConfigId(Long configId) {
        this.configId = configId;
    }

    public String getConfigCode() {
        return configCode;
    }

    public void setConfigCode(String configCode) {
        this.configCode = configCode;
    }

    public String getPlatformCode() {
        return platformCode;
    }

    public void setPlatformCode(String platformCode) {
        this.platformCode = platformCode;
    }

    public Integer getCloseDays() {
        return closeDays;
    }

    public void setCloseDays(Integer closeDays) {
        this.closeDays = closeDays;
    }
}
