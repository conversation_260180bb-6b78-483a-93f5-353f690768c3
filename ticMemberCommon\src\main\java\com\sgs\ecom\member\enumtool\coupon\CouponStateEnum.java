package com.sgs.ecom.member.enumtool.coupon;

public enum CouponStateEnum {
    //1：满减2：折扣3：抵扣、4：兑换、5：满送、6：满免）
    NOT_USE("notUse", 0, "未使用"),
    USE("use", 1, "已使用"),
    INVALID("invalid", 2, "失效"),
    DEL("del", 3, "删除"),
    ;

    CouponStateEnum(String name, int index, String nameCh) {
        this.name = name;
        this.index = index;
        this.nameCh = nameCh;
    }

    public static CouponStateEnum getEnum(int index) {

        for (CouponStateEnum c : CouponStateEnum.values()) {
            if (c.getIndex()==index) {
                return c;
            }
        }
        return null;
    }



    private String name;
    private int index;
    private String nameCh;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }
}
