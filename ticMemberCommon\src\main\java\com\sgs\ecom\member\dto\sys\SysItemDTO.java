package com.sgs.ecom.member.dto.sys;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.json.ScienceFormatSerializer;
import com.platform.util.json.TimeFormatSerializer;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.entity.sys.SysPackagePrice;

import java.sql.Timestamp;
import java.util.List;

public class SysItemDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select UNIT,PRICE,TEST_MEMO,STATE_DATE,ITEM_ID,STATE,ITEM_ALIAS,ITEM_NAME,CREATE_DATE,BU,SAMPLE_REQUIREMENTS,BUSINESS_LINE,TEST_DAYS,STANDARD_CODE,MEMO from SYS_ITEM"; 
 
 	@ApiAnno(groups={Default.class,QueryList.class,QueryDtl.class,QuerySummary.class,QueryCustom.class})
 	@BeanAnno(value="UNIT", getName="getUnit", setName="setUnit")
 	private String unit;
 	@ApiAnno(groups={Default.class,QueryList.class,QueryDtl.class,QuerySummary.class,QueryCustom.class})
 	@BeanAnno(value="PRICE", getName="getPrice", setName="setPrice")
 	private double price;
 	@ApiAnno(groups={Default.class,QueryList.class,QueryDtl.class,QuerySummary.class,QueryCustom.class})
 	@BeanAnno(value="TEST_MEMO", getName="getTestMemo", setName="setTestMemo")
 	private String testMemo;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private Timestamp stateDate;
 	@ApiAnno(groups={Default.class,QueryList.class,QueryDtl.class,QuerySummary.class,QueryCustom.class})
 	@BeanAnno(value="ITEM_ID", getName="getItemId", setName="setItemId")
 	private Long itemId;
 	@ApiAnno(groups={Default.class,QueryList.class,QueryDtl.class})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="ITEM_ALIAS", getName="getItemAlias", setName="setItemAlias")
 	private String itemAlias;
 	@ApiAnno(groups={Default.class,QueryList.class,QueryDtl.class,QuerySummary.class,QueryCustom.class})
 	@BeanAnno(value="ITEM_NAME", getName="getItemName", setName="setItemName")
 	private String itemName;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(groups={Default.class,QueryList.class,QueryDtl.class})
 	@BeanAnno(value="BU", getName="getBu", setName="setBu")
 	private String bu;
 	@ApiAnno(groups={Default.class,QueryList.class,QueryDtl.class,QuerySummary.class,QueryCustom.class})
 	@BeanAnno(value="SAMPLE_REQUIREMENTS", getName="getSampleRequirements", setName="setSampleRequirements")
 	private String sampleRequirements;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="BUSINESS_LINE", getName="getBusinessLine", setName="setBusinessLine")
 	private String businessLine;
 	@ApiAnno(groups={Default.class,QueryList.class,QueryDtl.class,QuerySummary.class,QueryCustom.class})
 	@BeanAnno(value="TEST_DAYS", getName="getTestDays", setName="setTestDays")
 	private double testDays;
 	@ApiAnno(groups={Default.class,QueryList.class,QueryDtl.class})
 	@BeanAnno(value="STANDARD_CODE", getName="getStandardCode", setName="setStandardCode")
 	private String standardCode;
 	@ApiAnno(groups={Default.class,QueryList.class,QueryDtl.class,QuerySummary.class,QueryCustom.class})
 	@BeanAnno(value="MEMO", getName="getMemo", setName="setMemo")
 	private String memo;
 	@ApiAnno(groups={Default.class,QueryList.class,QueryDtl.class})
 	@BeanAnno(value="LAB_NAME", getName="getLabName", setName="setLabName")
 	private String labName;
 	@ApiAnno(groups={Default.class,QueryList.class,QueryDtl.class})
 	@BeanAnno(value="MAS_LAB", getName="getCmaLab", setName="setCmaLab")
 	private String cmaLab;
 	@ApiAnno(groups={Default.class,QueryList.class,QueryDtl.class})
 	@BeanAnno(value="CNAS_LAB", getName="getCnasLab", setName="setCnasLab")
 	private String cnasLab;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="PERSON_CODE", getName="getPersonCode", setName="setPersonCode")
 	private String personCode;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="QUESTION_ID", getName="getQuestionId", setName="setQuestionId")
 	private long questionId;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="SUBJECT_NO", getName="getSubjectNo", setName="setSubjectNo")
 	private String subjectNo;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="OPTION_NO", getName="getOptionNo", setName="setOptionNo")
 	private String optionNo;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="BUY_NUMS", getName="getBuyNums", setName="setBuyNums")
 	private int buyNums;
 	@ApiAnno(groups={Default.class,QueryList.class,QueryDtl.class,QuerySummary.class,QueryCustom.class})
 	@BeanAnno(value="SALES_PRICE", getName="getSalesPrice", setName="setSalesPrice")
 	private double salesPrice;
 	@ApiAnno(groups={Default.class,QueryDtl.class})
 	@BeanAnno(value="DISCOUNT_RATE", getName="getDiscountRate", setName="setDiscountRate")
 	private double discountRate;
 	@ApiAnno(groups={Default.class,QueryDtl.class})
 	@BeanAnno(value="PACKAGE_LABEL", getName="getPackageLabel", setName="setPackageLabel")
 	private String packageLabel;
 	@ApiAnno(groups={Default.class,QueryDtl.class,QueryCustom.class,QuerySummary.class})
 	@BeanAnno(value="APPLY_FORM_CODE", getName="getApplyFormCode", setName="setApplyFormCode")
 	private String applyFormCode;
 	@ApiAnno(groups={Default.class,QueryList.class,QueryDtl.class,QuerySummary.class,QueryCustom.class})
 	@BeanAnno(value="TEST_METHOD", getName="getTestMethod", setName="setTestMethod")
 	private String testMethod;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="PACKAGE_NAME", getName="getPackageName", setName="setPackageName")
 	private String packageName;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="IS_OFTEN", getName="getIsOften", setName="setIsOften")
 	private int isOften;
 	@ApiAnno(groups={Default.class,QueryList.class,QuerySummary.class,QueryCustom.class})
 	@BeanAnno(value="CATEGORY_PATH", getName="getCategoryPath", setName="setCategoryPath")
 	private String categoryPath;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="CATEGORY_IDS", getName="getCategoryIds", setName="setCategoryIds")
 	private String categoryIds;
 	@ApiAnno(groups={Default.class,QueryDtl.class,QueryList.class})
 	@BeanAnno(value="LABEL_NAME", getName="getLabelName", setName="setLabelName")
 	private String labelName;
 	@ApiAnno(groups={Default.class,QueryDtl.class,QueryList.class})
 	@BeanAnno(value="IS_OPTIONAL", getName="getIsOptional", setName="setIsOptional")
 	private int isOptional;
 	@ApiAnno(groups={Default.class,QueryList.class,QuerySummary.class,QueryCustom.class})
 	@BeanAnno(value="CUST_CATEGORY_PATH", getName="getCustCategoryPath", setName="setCustCategoryPath")
 	private String custCategoryPath;
	@ApiAnno(groups={Default.class,QueryDtl.class,QueryList.class})
	@BeanAnno(value="IS_HOT", getName="getIsHot", setName="setIsHot")
	private int isHot;

	@ApiAnno(groups={QuerySummary.class,QueryCustom.class})
	private String busiCode;
 	private String currency;
 	@ApiAnno(groups={QuerySummary.class,QueryCustom.class})
 	private String labIds;
	@ApiAnno(groups={QuerySummary.class,QueryCustom.class})
	private String accountType;
	@ApiAnno(groups={QuerySummary.class,QueryCustom.class})
	private String accountCode;
 	private int unionType;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	private List<SysPackagePrice> prices;
 	@ApiAnno(groups={Default.class,QuerySummary.class,QueryCustom.class})
 	private String categoryMemo;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(dtocls={SysPackageCategoryDTO.class})
 	private List<SysPackageCategoryDTO> categories;
	@ApiAnno(groups={QuerySummary.class,QueryCustom.class})
	private Long packageId;

 	public void setUnit(String unit){
 		 this.unit=unit;
 	}
 	public String getUnit(){
 		 return this.unit;
 	}
 
 	 
 	public void setPrice(double price){
 		 this.price=price;
 	}
 	@JsonSerialize(using = ScienceFormatSerializer.class) 
 	public double getPrice(){
 		 return this.price;
 	}
 
 	 
 	public void setTestMemo(String testMemo){
 		 this.testMemo=testMemo;
 	}
 	public String getTestMemo(){
 		 return this.testMemo;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setItemId(Long itemId){
 		 this.itemId=itemId;
 	}
 	public Long getItemId(){
 		 return this.itemId;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setItemAlias(String itemAlias){
 		 this.itemAlias=itemAlias;
 	}
 	public String getItemAlias(){
 		 return this.itemAlias;
 	}
 
 	 
 	public void setItemName(String itemName){
 		 this.itemName=itemName;
 	}
 	public String getItemName(){
 		 return this.itemName;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setBu(String bu){
 		 this.bu=bu;
 	}
 	public String getBu(){
 		 return this.bu;
 	}
 
 	 
 	public void setSampleRequirements(String sampleRequirements){
 		 this.sampleRequirements=sampleRequirements;
 	}
 	public String getSampleRequirements(){
 		 return this.sampleRequirements;
 	}
 
 	 
 	public void setBusinessLine(String businessLine){
 		 this.businessLine=businessLine;
 	}
 	public String getBusinessLine(){
 		 return this.businessLine;
 	}
 
 	 
 	public void setTestDays(double testDays){
 		 this.testDays=testDays;
 	}
 	public double getTestDays(){
 		 return this.testDays;
 	}
 
 	 
 	public void setStandardCode(String standardCode){
 		 this.standardCode=standardCode;
 	}
 	public String getStandardCode(){
 		 return this.standardCode;
 	}
 
 	 
 	public void setMemo(String memo){
 		 this.memo=memo;
 	}
 	public String getMemo(){
 		 return this.memo;
 	}
	public String getLabName() {
		return labName;
	}
	public void setLabName(String labName) {
		this.labName = labName;
	}
	public String getCmaLab() {
		return cmaLab;
	}
	public void setCmaLab(String cmaLab) {
		this.cmaLab = cmaLab;
	}
	public String getCnasLab() {
		return cnasLab;
	}
	public void setCnasLab(String cnasLab) {
		this.cnasLab = cnasLab;
	}
	public long getQuestionId() {
		return questionId;
	}
	public void setQuestionId(long questionId) {
		this.questionId = questionId;
	}
	public String getSubjectNo() {
		return subjectNo;
	}
	public void setSubjectNo(String subjectNo) {
		this.subjectNo = subjectNo;
	}
	public String getOptionNo() {
		return optionNo;
	}
	public void setOptionNo(String optionNo) {
		this.optionNo = optionNo;
	}
	public int getBuyNums() {
		return buyNums;
	}
	public void setBuyNums(int buyNums) {
		this.buyNums = buyNums;
	}
	public String getPersonCode() {
		return personCode;
	}
	public void setPersonCode(String personCode) {
		this.personCode = personCode;
	}
	public int getUnionType() {
		return unionType;
	}
	public void setUnionType(int unionType) {
		this.unionType = unionType;
	}
	public double getSalesPrice() {
		return salesPrice;
	}
	public void setSalesPrice(double salesPrice) {
		this.salesPrice = salesPrice;
	}
	public double getDiscountRate() {
		return discountRate;
	}
	public void setDiscountRate(double discountRate) {
		this.discountRate = discountRate;
	}
	public String getPackageLabel() {
		return packageLabel;
	}
	public void setPackageLabel(String packageLabel) {
		this.packageLabel = packageLabel;
	}
	public String getApplyFormCode() {
		return applyFormCode;
	}
	public void setApplyFormCode(String applyFormCode) {
		this.applyFormCode = applyFormCode;
	}
	public String getTestMethod() {
		return testMethod;
	}
	public void setTestMethod(String testMethod) {
		this.testMethod = testMethod;
	}
	public String getPackageName() {
		return packageName;
	}
	public void setPackageName(String packageName) {
		this.packageName = packageName;
	}
	public int getIsOften() {
		return isOften;
	}
	public void setIsOften(int isOften) {
		this.isOften = isOften;
	}
	public String getCategoryPath() {
		return categoryPath;
	}
	public void setCategoryPath(String categoryPath) {
		this.categoryPath = categoryPath;
	}
	public String getCategoryIds() {
		return categoryIds;
	}
	public void setCategoryIds(String categoryIds) {
		this.categoryIds = categoryIds;
	}
	public String getLabelName() {
		return labelName;
	}
	public void setLabelName(String labelName) {
		this.labelName = labelName;
	}
	public int getIsOptional() {
		return isOptional;
	}
	public void setIsOptional(int isOptional) {
		this.isOptional = isOptional;
	}
	public String getCustCategoryPath() {
		return custCategoryPath;
	}
	public void setCustCategoryPath(String custCategoryPath) {
		this.custCategoryPath = custCategoryPath;
	}
	public List<SysPackagePrice> getPrices() {
		return prices;
	}
	public void setPrices(List<SysPackagePrice> prices) {
		this.prices = prices;
	}
	public String getCurrency() {
		return currency;
	}
	public void setCurrency(String currency) {
		this.currency = currency;
	}
	public String getLabIds() {
		return labIds;
	}
	public void setLabIds(String labIds) {
		this.labIds = labIds;
	}
	public String getCategoryMemo() {
		return categoryMemo;
	}
	public void setCategoryMemo(String categoryMemo) {
		this.categoryMemo = categoryMemo;
	}
	public List<SysPackageCategoryDTO> getCategories() {
		return categories;
	}
	public void setCategories(List<SysPackageCategoryDTO> categories) {
		this.categories = categories;
	}

	public String getBusiCode() {
		return busiCode;
	}

	public void setBusiCode(String busiCode) {
		this.busiCode = busiCode;
	}

	public String getAccountType() {
		return accountType;
	}

	public void setAccountType(String accountType) {
		this.accountType = accountType;
	}

	public String getAccountCode() {
		return accountCode;
	}

	public void setAccountCode(String accountCode) {
		this.accountCode = accountCode;
	}

	public Long getPackageId() {
		return packageId;
	}

	public void setPackageId(Long packageId) {
		this.packageId = packageId;
	}

	public int getIsHot() {
		return isHot;
	}

	public void setIsHot(int isHot) {
		this.isHot = isHot;
	}
}