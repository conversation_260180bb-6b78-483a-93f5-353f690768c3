package com.sgs.ecom.member.enumtool.order;

public enum GroupTypeEnum {
	//操作类型11用户PC，12用户H5，21客服PC
	WEB("web", 11, "用户PC"),
	MOBILE("mobile", 12, "用户H5"),
	CRM("crm", 21, "客服PC"),
	;

	GroupTypeEnum(String name, int index, String nameCh) {
		this.name = name;
		this.index = index;
		this.nameCh = nameCh;
	}

	public static String getName(int index) {
		for (GroupTypeEnum c :GroupTypeEnum.values()) {
			if (c.getIndex()==index) {
				return c.getName();
			}
		}
		return null;
	}

	public static GroupTypeEnum getEnum(int index) {
		for (GroupTypeEnum c : GroupTypeEnum.values()) {
			if (c.getIndex()==index) {
				return c;
			}
		}
		return null;
	}

	public static String getNameCh(int index) {
		for (GroupTypeEnum c : GroupTypeEnum.values()) {
			if (c.getIndex()==index) {
				return c.getNameCh();
			}
		}
		return null;
	}





	private String name;
	private int index;
	private String nameCh;


	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getNameCh() {
		return nameCh;
	}

	public void setNameCh(String nameCh) {
		this.nameCh = nameCh;
	}

	public int getIndex() {
		return index;
	}

	public void setIndex(int index) {
		this.index = index;
	}
}
