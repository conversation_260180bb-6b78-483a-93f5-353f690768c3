<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.user.dao.OrderInvoiceMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.vo.VOOrderInvoice" >
    <id column="ID" property="id" jdbcType="BIGINT" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="DETAIL_NO" property="detailNo" jdbcType="VARCHAR" />
    <result column="INVOICE_ID" property="invoiceId" jdbcType="BIGINT" />
    <result column="INVOICE_TYPE" property="invoiceType" jdbcType="TINYINT" />
    <result column="INVOICE_TITLE" property="invoiceTitle" jdbcType="VARCHAR" />
    <result column="TAX_NO" property="taxNo" jdbcType="VARCHAR" />
    <result column="REGISTER_ADDR" property="registerAddr" jdbcType="VARCHAR" />
    <result column="REGISTER_PHONE" property="registerPhone" jdbcType="VARCHAR" />
    <result column="BANK_ADDR" property="bankAddr" jdbcType="VARCHAR" />
    <result column="BANK_NUMBER" property="bankNumber" jdbcType="VARCHAR" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="DELIVERY_NAME" property="deliveryName" jdbcType="VARCHAR" />
    <result column="DELIVERY_PHONE" property="deliveryPhone" jdbcType="VARCHAR" />
    <result column="DELIVERY_ADDR" property="deliveryAddr" jdbcType="VARCHAR" />
    <result column="DELIVERY_PROVINCE" property="deliveryProvince" jdbcType="VARCHAR" />
    <result column="DELIVERY_CITY" property="deliveryCity" jdbcType="VARCHAR" />
    <result column="DELIVERY_TOWN" property="deliveryTown" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="TINYINT" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
    <result column="COUNTRY" property="country" jdbcType="VARCHAR" />
    <result column="FOREIGN_CITY" property="foreignCity" jdbcType="VARCHAR" />
    <result column="POST_CODE" property="postCode" jdbcType="VARCHAR" />
    <result column="PAYER_NAME" property="payerName" jdbcType="VARCHAR" />
    <result column="PAYER_PHONE" property="payerPhone" jdbcType="VARCHAR" />
    <result column="PAYER_EMAIL" property="payerEmail" jdbcType="VARCHAR" />
  </resultMap>
  <resultMap id="BaseResult" type="com.sgs.ecom.member.entity.order.OrderInvoice" >
    <id column="ID" property="id" jdbcType="BIGINT" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="DETAIL_NO" property="detailNo" jdbcType="VARCHAR" />
    <result column="INVOICE_ID" property="invoiceId" jdbcType="BIGINT" />
    <result column="INVOICE_TYPE" property="invoiceType" jdbcType="TINYINT" />
    <result column="INVOICE_TITLE" property="invoiceTitle" jdbcType="VARCHAR" />
    <result column="TAX_NO" property="taxNo" jdbcType="VARCHAR" />
    <result column="REGISTER_ADDR" property="registerAddr" jdbcType="VARCHAR" />
    <result column="REGISTER_PHONE" property="registerPhone" jdbcType="VARCHAR" />
    <result column="BANK_ADDR" property="bankAddr" jdbcType="VARCHAR" />
    <result column="BANK_NUMBER" property="bankNumber" jdbcType="VARCHAR" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="DELIVERY_NAME" property="deliveryName" jdbcType="VARCHAR" />
    <result column="DELIVERY_PHONE" property="deliveryPhone" jdbcType="VARCHAR" />
    <result column="DELIVERY_ADDR" property="deliveryAddr" jdbcType="VARCHAR" />
    <result column="DELIVERY_PROVINCE" property="deliveryProvince" jdbcType="VARCHAR" />
    <result column="DELIVERY_CITY" property="deliveryCity" jdbcType="VARCHAR" />
    <result column="DELIVERY_TOWN" property="deliveryTown" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="TINYINT" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
    <result column="COUNTRY" property="country" jdbcType="VARCHAR" />
    <result column="FOREIGN_CITY" property="foreignCity" jdbcType="VARCHAR" />
    <result column="POST_CODE" property="postCode" jdbcType="VARCHAR" />
    <result column="PAYER_NAME" property="payerName" jdbcType="VARCHAR" />
    <result column="PAYER_PHONE" property="payerPhone" jdbcType="VARCHAR" />
    <result column="PAYER_EMAIL" property="payerEmail" jdbcType="VARCHAR" />
    <result column="IS_FOREIGN" property="isForeign" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, ORDER_NO, DETAIL_NO, INVOICE_ID, INVOICE_TYPE, INVOICE_TITLE, TAX_NO, REGISTER_ADDR, 
    REGISTER_PHONE, BANK_ADDR, BANK_NUMBER, USER_ID, DELIVERY_NAME, DELIVERY_PHONE, DELIVERY_ADDR, 
    DELIVERY_PROVINCE, DELIVERY_CITY, DELIVERY_TOWN, STATE, CREATE_DATE, STATE_DATE,COUNTRY,FOREIGN_CITY,POST_CODE,PAYER_NAME,PAYER_PHONE,PAYER_EMAIL
  </sql>
  <select id="selectVOByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from ORDER_INVOICE
    where ID = #{id,jdbcType=BIGINT}
  </select>


  <insert id="insertVOSelective" parameterType="com.sgs.ecom.member.vo.VOOrderInvoice" >
    insert into ORDER_INVOICE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="orderNo != null" >
        ORDER_NO,
      </if>
      <if test="detailNo != null" >
        DETAIL_NO,
      </if>
      <if test="invoiceId != null" >
        INVOICE_ID,
      </if>
      <if test="invoiceType != null" >
        INVOICE_TYPE,
      </if>
      <if test="invoiceTitle != null" >
        INVOICE_TITLE,
      </if>
      <if test="taxNo != null" >
        TAX_NO,
      </if>
      <if test="registerAddr != null" >
        REGISTER_ADDR,
      </if>
      <if test="registerPhone != null" >
        REGISTER_PHONE,
      </if>
      <if test="bankAddr != null" >
        BANK_ADDR,
      </if>
      <if test="bankNumber != null" >
        BANK_NUMBER,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
      <if test="deliveryName != null" >
        DELIVERY_NAME,
      </if>
      <if test="deliveryPhone != null" >
        DELIVERY_PHONE,
      </if>
      <if test="deliveryAddr != null" >
        DELIVERY_ADDR,
      </if>
      <if test="deliveryProvince != null" >
        DELIVERY_PROVINCE,
      </if>
      <if test="deliveryCity != null" >
        DELIVERY_CITY,
      </if>
      <if test="deliveryTown != null" >
        DELIVERY_TOWN,
      </if>
      <if test="state != null" >
        STATE,
      </if>
      <if test="createDate != null" >
        CREATE_DATE,
      </if>
      <if test="stateDate != null" >
        STATE_DATE,
      </if>
      <if test="addressId != null" >
        ADDRESS_ID,
      </if>
      <if test="deliverCompany != null" >
        DELIVER_COMPANY,
      </if>
      <if test="deliverMail != null" >
        DELIVER_MAIL,
      </if>
      <if test="linkPerson != null" >
        LINK_PERSON,
      </if>
      <if test="linkPhone != null" >
        LINK_PHONE,
      </if>
      <if test="linkEmail != null" >
        LINK_EMAIL,
      </if>
      <if test="frontImg != null" >
        FRONT_IMG,
      </if>
      <if test="backImg != null" >
        BACK_IMG,
      </if>
      <if test="bossNo != null" >
        BOSS_NO,
      </if>
      <if test="bossTag != null" >
        BOSS_TAG,
      </if>
      <if test="monthCompanyName != null" >
        MONTH_COMPANY_NAME,
      </if>
      <if test="monthCompanyNameEn != null" >
        MONTH_COMPANY_NAME_EN,
      </if>
      <if test="monthAddress != null" >
        MONTH_ADDRESS,
      </if>
      <if test="monthAddressEn != null" >
        MONTH_ADDRESS_EN,
      </if>
      <if test="isForeign != null" >
        IS_FOREIGN,
      </if>
      <if test="country != null" >
        COUNTRY,
      </if>
      <if test="foreignCity != null" >
        FOREIGN_CITY ,
      </if>
      <if test="postCode != null" >
        POST_CODE ,
      </if>
      <if test="contact != null" >
        CONTACT ,
      </if>
      <if test="payerName != null" >
        PAYER_NAME ,
      </if>
      <if test="payerPhone != null" >
        PAYER_PHONE ,
      </if>
      <if test="payerEmail != null" >
        PAYER_EMAIL ,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="detailNo != null" >
        #{detailNo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceId != null" >
        #{invoiceId,jdbcType=BIGINT},
      </if>
      <if test="invoiceType != null" >
        #{invoiceType,jdbcType=TINYINT},
      </if>
      <if test="invoiceTitle != null" >
        #{invoiceTitle,jdbcType=VARCHAR},
      </if>
      <if test="taxNo != null" >
        #{taxNo,jdbcType=VARCHAR},
      </if>
      <if test="registerAddr != null" >
        #{registerAddr,jdbcType=VARCHAR},
      </if>
      <if test="registerPhone != null" >
        #{registerPhone,jdbcType=VARCHAR},
      </if>
      <if test="bankAddr != null" >
        #{bankAddr,jdbcType=VARCHAR},
      </if>
      <if test="bankNumber != null" >
        #{bankNumber,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="deliveryName != null" >
        #{deliveryName,jdbcType=VARCHAR},
      </if>
      <if test="deliveryPhone != null" >
        #{deliveryPhone,jdbcType=VARCHAR},
      </if>
      <if test="deliveryAddr != null" >
        #{deliveryAddr,jdbcType=VARCHAR},
      </if>
      <if test="deliveryProvince != null" >
        #{deliveryProvince,jdbcType=VARCHAR},
      </if>
      <if test="deliveryCity != null" >
        #{deliveryCity,jdbcType=VARCHAR},
      </if>
      <if test="deliveryTown != null" >
        #{deliveryTown,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        #{state,jdbcType=TINYINT},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        #{stateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="addressId != null" >
        #{addressId},
      </if>
      <if test="deliverCompany != null" >
        #{deliverCompany},
      </if>
      <if test="deliverMail != null" >
        #{deliverMail},
      </if>
      <if test="linkPerson != null" >
        #{linkPerson,jdbcType=VARCHAR},
      </if>
      <if test="linkPhone != null" >
        #{linkPhone,jdbcType=VARCHAR},
      </if>
      <if test="linkEmail != null" >
        #{linkEmail,jdbcType=VARCHAR},
      </if>
      <if test="frontImg != null" >
        #{frontImg,jdbcType=VARCHAR},
      </if>
      <if test="backImg != null" >
        #{backImg,jdbcType=VARCHAR},
      </if>
      <if test="bossNo != null" >
        #{bossNo,jdbcType=VARCHAR},
      </if>
      <if test="bossTag != null" >
        #{bossTag,jdbcType=VARCHAR},
      </if>
      <if test="monthCompanyName != null" >
        #{monthCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="monthCompanyNameEn != null" >
        #{monthCompanyNameEn,jdbcType=VARCHAR},
      </if>
      <if test="monthAddress != null" >
        #{monthAddress,jdbcType=VARCHAR},
      </if>
      <if test="monthAddressEn != null" >
        #{monthAddressEn,jdbcType=VARCHAR},
      </if>
      <if test="isForeign != null" >
       #{isForeign},
      </if>
      <if test="country != null" >
        #{country},
      </if>
      <if test="foreignCity != null" >
        #{foreignCity},
      </if>
      <if test="postCode != null" >
        #{postCode},
      </if>
      <if test="contact != null" >
        #{contact},
      </if>
      <if test="payerName != null" >
        #{payerName},
      </if>
      <if test="payerPhone != null" >
        #{payerPhone},
      </if>
      <if test="payerEmail != null" >
        #{payerEmail},
      </if>
    </trim>
  </insert>
  <update id="updateVOByPrimaryKeySelective" parameterType="com.sgs.ecom.member.vo.VOOrderInvoice" >
    update ORDER_INVOICE
    <set >
      <if test="orderNo != null" >
        ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="detailNo != null" >
        DETAIL_NO = #{detailNo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceId != null" >
        INVOICE_ID = #{invoiceId,jdbcType=BIGINT},
      </if>
      <if test="invoiceType != null" >
        INVOICE_TYPE = #{invoiceType,jdbcType=TINYINT},
      </if>
      <if test="invoiceTitle != null" >
        INVOICE_TITLE = #{invoiceTitle,jdbcType=VARCHAR},
      </if>
      <if test="taxNo != null" >
        TAX_NO = #{taxNo,jdbcType=VARCHAR},
      </if>
      <if test="registerAddr != null" >
        REGISTER_ADDR = #{registerAddr,jdbcType=VARCHAR},
      </if>
      <if test="registerPhone != null" >
        REGISTER_PHONE = #{registerPhone,jdbcType=VARCHAR},
      </if>
      <if test="bankAddr != null" >
        BANK_ADDR = #{bankAddr,jdbcType=VARCHAR},
      </if>
      <if test="bankNumber != null" >
        BANK_NUMBER = #{bankNumber,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        USER_ID = #{userId,jdbcType=BIGINT},
      </if>
      <if test="deliveryName != null" >
        DELIVERY_NAME = #{deliveryName,jdbcType=VARCHAR},
      </if>
      <if test="deliveryPhone != null" >
        DELIVERY_PHONE = #{deliveryPhone,jdbcType=VARCHAR},
      </if>
      <if test="deliveryAddr != null" >
        DELIVERY_ADDR = #{deliveryAddr,jdbcType=VARCHAR},
      </if>
      <if test="deliveryProvince != null" >
        DELIVERY_PROVINCE = #{deliveryProvince,jdbcType=VARCHAR},
      </if>
      <if test="deliveryCity != null" >
        DELIVERY_CITY = #{deliveryCity,jdbcType=VARCHAR},
      </if>
      <if test="deliveryTown != null" >
        DELIVERY_TOWN = #{deliveryTown,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        STATE = #{state,jdbcType=TINYINT},
      </if>
      <if test="createDate != null" >
        CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        STATE_DATE = #{stateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="addressId != null" >
        ADDRESS_ID = #{addressId},
      </if>
      <if test="deliverCompany != null" >
        DELIVER_COMPANY = #{deliverCompany},
      </if>
      <if test="deliverMail != null" >
        DELIVER_MAIL = #{deliverMail},
      </if>
      <if test="linkPerson != null" >
        LINK_PERSON = #{linkPerson,jdbcType=VARCHAR},
      </if>
      <if test="linkPhone != null" >
        LINK_PHONE = #{linkPhone,jdbcType=VARCHAR},
      </if>
      <if test="linkEmail != null" >
        LINK_EMAIL = #{linkEmail,jdbcType=VARCHAR},
      </if>
      <if test="frontImg != null" >
        FRONT_IMG = #{frontImg,jdbcType=VARCHAR},
      </if>
      <if test="backImg != null" >
        BACK_IMG = #{backImg,jdbcType=VARCHAR},
      </if>
      <if test="bossTag != null" >
        BOSS_TAG = #{bossTag,jdbcType=VARCHAR},
      </if>
      <if test="country != null" >
        COUNTRY = #{country},
      </if>
      <if test="foreignCity != null" >
        FOREIGN_CITY = #{foreignCity},
      </if>
      <if test="postCode != null" >
        POST_CODE = #{postCode},
      </if>
      <if test="contact != null" >
        CONTACT = #{contact},
      </if>
      <if test="payerName != null" >
        PAYER_NAME = #{payerName},
      </if>
      <if test="payerPhone != null" >
        PAYER_PHONE = #{payerPhone},
      </if>
      <if test="payerEmail != null" >
        PAYER_EMAIL = #{payerEmail},
      </if>
    </set>
    where ID = #{id,jdbcType=BIGINT}
  </update>

  <!--自定义 -->
  <resultMap id="resultDTO" type="com.sgs.ecom.member.dto.detail.OrderInvoiceDTO" >
    <id column="ID" property="id" jdbcType="BIGINT" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="DETAIL_NO" property="detailNo" jdbcType="VARCHAR" />
    <result column="INVOICE_ID" property="invoiceId" jdbcType="BIGINT" />
    <result column="INVOICE_TYPE" property="invoiceType" jdbcType="TINYINT" />
    <result column="INVOICE_TITLE" property="invoiceTitle" jdbcType="VARCHAR" />
    <result column="TAX_NO" property="taxNo" jdbcType="VARCHAR" />
    <result column="BOSS_NO" property="bossNo" jdbcType="VARCHAR" />
    <result column="REGISTER_ADDR" property="regAddress" jdbcType="VARCHAR" />
    <result column="REGISTER_PHONE" property="regPhone" jdbcType="VARCHAR" />
    <result column="BANK_ADDR" property="bankName" jdbcType="VARCHAR" />
    <result column="BANK_NUMBER" property="bankNumber" jdbcType="VARCHAR" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="ADDRESS_ID" property="addressId" jdbcType="BIGINT" />
    <result column="DELIVERY_NAME" property="userName" jdbcType="VARCHAR" />
    <result column="DELIVERY_PHONE" property="userPhone" jdbcType="VARCHAR" />
    <result column="DELIVERY_ADDR" property="companyAddress" jdbcType="VARCHAR" />
    <result column="DELIVERY_PROVINCE" property="province" jdbcType="VARCHAR" />
    <result column="DELIVERY_CITY" property="city" jdbcType="VARCHAR" />
    <result column="DELIVERY_TOWN" property="town" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="TINYINT" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
    <result column="BACK_IMG" property="backImg" jdbcType="VARCHAR" />
    <result column="FRONT_IMG" property="frontImg" jdbcType="VARCHAR" />
    <result column="DELIVER_COMPANY" property="deliverCompany" jdbcType="VARCHAR" />
    <result column="DELIVER_MAIL" property="deliverMail" jdbcType="VARCHAR" />
    <result column="LINK_PERSON" property="linkPerson" jdbcType="VARCHAR" />
    <result column="LINK_PHONE" property="linkPhone" jdbcType="VARCHAR" />
    <result column="LINK_EMAIL" property="linkEmail" jdbcType="VARCHAR" />
    <result column="BOSS_TAG" property="bossTag" jdbcType="VARCHAR" />
    <result column="MONTH_COMPANY_NAME" property="monthCompanyName" jdbcType="VARCHAR" />
    <result column="MONTH_COMPANY_NAME_EN" property="monthCompanyNameEn" jdbcType="VARCHAR" />
    <result column="MONTH_ADDRESS" property="monthAddress" jdbcType="VARCHAR" />
    <result column="MONTH_ADDRESS_EN" property="monthAddressEn" jdbcType="VARCHAR" />
    <result column="IS_FOREIGN" property="isForeign" jdbcType="INTEGER" />
    <result column="COUNTRY" property="country" jdbcType="VARCHAR" />
    <result column="FOREIGN_CITY" property="foreignCity" jdbcType="VARCHAR" />
    <result column="POST_CODE" property="postCode" jdbcType="VARCHAR" />
    <result column="CONTACT" property="contact" jdbcType="VARCHAR" />
    <result column="PAYER_NAME" property="payerName" jdbcType="VARCHAR" />
    <result column="PAYER_PHONE" property="payerPhone" jdbcType="VARCHAR" />
    <result column="PAYER_EMAIL" property="payerEmail" jdbcType="VARCHAR" />
  </resultMap>




  <sql id="sqlListDTO" >
   ID, ORDER_NO, DETAIL_NO, INVOICE_ID, INVOICE_TYPE, INVOICE_TITLE, TAX_NO,BOSS_NO, REGISTER_ADDR,
    REGISTER_PHONE, BANK_ADDR, BANK_NUMBER, USER_ID, DELIVERY_NAME, DELIVERY_PHONE, DELIVERY_ADDR,
    DELIVERY_PROVINCE, DELIVERY_CITY, DELIVERY_TOWN, STATE, CREATE_DATE,
    STATE_DATE,ADDRESS_ID,DELIVER_COMPANY,DELIVER_MAIL,
    LINK_PERSON, LINK_PHONE, LINK_EMAIL,
if(INVOICE_TYPE=2,FRONT_IMG,if(FRONT_IMG is null or LENGTH(FRONT_IMG) = 0,if(BACK_IMG is null or LENGTH(BACK_IMG) = 0,'',BACK_IMG),CONCAT(FRONT_IMG,if(BACK_IMG is null or LENGTH(BACK_IMG) = 0,'',CONCAT(',',BACK_IMG))))) FRONT_IMG,    if(invoice_type=2,BACK_IMG,'') BACK_IMG,BOSS_TAG,
    MONTH_COMPANY_NAME,MONTH_COMPANY_NAME_EN,MONTH_ADDRESS,MONTH_ADDRESS_EN,IS_FOREIGN,COUNTRY,FOREIGN_CITY,POST_CODE,CONTACT,PAYER_NAME,PAYER_PHONE,PAYER_EMAIL

  </sql>

  <select id="selectListByMap" resultMap="resultDTO" >
    select
    <include refid="sqlListDTO" />
    from ORDER_INVOICE
    <include refid="baseQueryWhere"/>
  </select>

  <sql id="sqlListDTONew" >
    ID, ORDER_NO, DETAIL_NO, INVOICE_ID, INVOICE_TYPE, INVOICE_TITLE, TAX_NO, REGISTER_ADDR,
    REGISTER_PHONE, BANK_ADDR, BANK_NUMBER, USER_ID, DELIVERY_NAME, DELIVERY_PHONE, DELIVERY_ADDR,
    DELIVERY_PROVINCE, DELIVERY_CITY, DELIVERY_TOWN,IS_FOREIGN,
     STATE, CREATE_DATE, STATE_DATE,ADDRESS_ID,DELIVER_COMPANY,DELIVER_MAIL,
     LINK_PERSON, LINK_PHONE, LINK_EMAIL, FRONT_IMG, BACK_IMG,BOSS_TAG,MONTH_COMPANY_NAME,MONTH_COMPANY_NAME_EN,
     MONTH_ADDRESS,MONTH_ADDRESS_EN,COUNTRY,FOREIGN_CITY,POST_CODE,CONTACT,
     PAYER_NAME,PAYER_PHONE,PAYER_EMAIL
  </sql>
  <select id="selectListByMapNew" resultMap="resultDTO" >
    select
    <include refid="sqlListDTONew" />
    from ORDER_INVOICE
    <include refid="baseQueryWhere"/>
  </select>


  <sql id="baseQueryWhere">
    where 1=1
    <if test="orderNo != null">
      AND ORDER_NO=#{orderNo}
    </if>
    <if test="state != null">
      AND STATE=#{state}
    </if>
    <if test="userId != null">
      AND USER_ID=#{userId}
    </if>
    <if test="orderNoList != null ">
      AND ORDER_NO in
      <foreach collection="orderNoList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </sql>

  <resultMap id="resultOldInvoice" type="com.sgs.ecom.member.dto.detail.OrderInvoiceOldDTO" >
    <result column="ADDRESS_ID" property="addressId" jdbcType="BIGINT" />
    <result column="INVOICE_ID" property="invoiceId" jdbcType="BIGINT" />
    <result column="INVOICE_TYPE" property="invoiceType" jdbcType="INTEGER" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
  </resultMap>

  <select id="selectOldInvoice" resultMap="resultOldInvoice" >
    select oi.ID,oi.INVOICE_ID,oi.ADDRESS_ID,oi.INVOICE_TYPE,oi.ORDER_NO
    from ORDER_INVOICE oi,
    (
     select i.INVOICE_TYPE,max(i.ID) ID from ORDER_INVOICE i,ORDER_BASE_INFO obi
	 where 1=1  and i.ORDER_NO=obi.ORDER_NO
    <if test="userId != null">
      AND obi.USER_ID=#{userId}
    </if>
    <if test="bu != null">
      AND obi.bu=#{bu}
    </if>
	group by i.INVOICE_TYPE
    ) m
    where oi.ID=m.ID order by oi.ID DESC

  </select>

  <select id="selectInvoiceType" resultMap="resultOldInvoice" >
    select INVOICE_ID,ADDRESS_ID,INVOICE_TYPE,ORDER_NO
    from ORDER_INVOICE
    <include refid="baseQueryWhere"/>
    group by ORDER_NO
  </select>

  <update id="updateOrderInvoiceByState"  >
    update ORDER_INVOICE set STATE=0 where STATE=1 and ORDER_NO=#{orderNo}
  </update>

  <delete id="delInvoiceByMap">
    delete from ORDER_INVOICE where ORDER_NO=#{orderNo}
  </delete>


  <select id="selectLastOrderInvoiceByMap" resultMap="resultDTO" >
    select oi.ID,oi.INVOICE_ID,oi.ADDRESS_ID,oi.INVOICE_TYPE,oi.ORDER_NO
    from ORDER_INVOICE oi,ORDER_BASE_INFO obi where oi.ORDER_NO=obi.ORDER_NO
    <if test="userId != null">
      AND obi.USER_ID=#{userId}
    </if>
    <if test="orderType != null">
      AND obi.ORDER_TYPE=#{orderType}
    </if>
	order by oi.ID desc limit 1
    </select>

  <select id="qryOneOrderInvoiceByOrder" resultMap="resultDTO" >
    select
    <include refid="sqlListDTONew" />
    from ORDER_INVOICE
    where ORDER_NO =#{orderNo} and state=1
    order by ID desc
  </select>


  <select id="selectInvoiceTypeByVO" resultMap="resultOldInvoice" >
    select INVOICE_ID,ADDRESS_ID,INVOICE_TYPE,ORDER_NO
    from ORDER_INVOICE
    <where>
      <if test="orderNoList != null ">
        AND ORDER_NO in
        <foreach collection="orderNoList" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
  </where>
    group by ORDER_NO
  </select>
    <select id="qryInvoiceDTO" resultMap="BaseResult">
      select
      <include refid="sqlListDTO" />
      from ORDER_INVOICE
      <include refid="baseQueryWhere"/>
        limit 1
    </select>
    <insert id="copyByOrderNo" parameterType="com.sgs.ecom.member.dto.order.CopyByOrder">
    insert into ORDER_INVOICE (
      ORDER_NO,DETAIL_NO,INVOICE_ID,INVOICE_TYPE,IS_FOREIGN,INVOICE_TITLE,TAX_NO,BOSS_NO,BOSS_TAG,REGISTER_ADDR,REGISTER_PHONE,BANK_ADDR,
      BANK_NUMBER,USER_ID,DELIVERY_NAME,DELIVERY_PHONE,DELIVERY_ADDR,DELIVERY_PROVINCE,DELIVERY_CITY,DELIVERY_TOWN,STATE,
      CREATE_DATE,STATE_DATE,FRONT_IMG,BACK_IMG,DELIVER_COMPANY,DELIVER_MAIL,ADDRESS_ID,LINK_PERSON,LINK_PHONE,LINK_EMAIL,
      MONTH_COMPANY_NAME,MONTH_COMPANY_NAME_EN,MONTH_ADDRESS,MONTH_ADDRESS_EN,COUNTRY,FOREIGN_CITY,POST_CODE,CONTACT
    )select
     #{toOrderNo},DETAIL_NO,INVOICE_ID,INVOICE_TYPE,IS_FOREIGN,INVOICE_TITLE,TAX_NO,BOSS_NO,BOSS_TAG,REGISTER_ADDR,REGISTER_PHONE,BANK_ADDR,
     BANK_NUMBER,USER_ID,DELIVERY_NAME,DELIVERY_PHONE,DELIVERY_ADDR,DELIVERY_PROVINCE,DELIVERY_CITY,DELIVERY_TOWN,1,
     now(),now(),FRONT_IMG,BACK_IMG,DELIVER_COMPANY,DELIVER_MAIL,ADDRESS_ID,LINK_PERSON,LINK_PHONE,LINK_EMAIL,
     MONTH_COMPANY_NAME,MONTH_COMPANY_NAME_EN,MONTH_ADDRESS,MONTH_ADDRESS_EN,COUNTRY,FOREIGN_CITY,POST_CODE,CONTACT
    from ORDER_INVOICE where ORDER_NO= #{useOrderNo} and state=1
  </insert>

</mapper>