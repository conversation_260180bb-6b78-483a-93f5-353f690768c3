package com.sgs.ecom.member.enumtool.aplication;

public enum InvoiceTypeEnum {
	COMMON_COMPANY("commonCompany", 1, "增值税电子普通发票"),
	COMMON_PERSON("commonPerson", 2, "个人发票"),
	SPECIAL_COMPANY("specialCompany", 3, "增值税电子专用发票"),



	;

	InvoiceTypeEnum(String name, int index, String nameCh) {
		this.name = name;
		this.index = index;
		this.nameCh = nameCh;
	}

	public static String getName(int index) {
		for (InvoiceTypeEnum c : InvoiceTypeEnum.values()) {
			if (c.getIndex()==index) {
				return c.getName();
			}
		}
		return "";
	}

	public static String getNameCh(int index) {
		for (InvoiceTypeEnum c : InvoiceTypeEnum.values()) {
			if (c.getIndex()==index) {
				return c.getNameCh();
			}
		}
		return "";
	}


	private String name;
	private int index;
	private String nameCh;


	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}


	public String getNameCh() {
		return nameCh;
	}

	public void setNameCh(String nameCh) {
		this.nameCh = nameCh;
	}

	public int getIndex() {
		return index;
	}

	public void setIndex(int index) {
		this.index = index;
	}
}
