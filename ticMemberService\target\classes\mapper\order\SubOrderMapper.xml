<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.order.dao.SubOrderMapper" >



  <sql id="sqlListDTO" >
    ORDER_ID,ORDER_NO,ORDER_TYPE,RELATE_ORDER_NO,REAL_AMOUNT,OPERATOR_CODE,CREATE_DATE,
    PAY_STATE,PAY_METHOD,TOTAL_NUMS,RECOMMEND_REASON,IS_PAY_RECEIVED,REFUND_STATE,STATE,
    IS_REMIND,GROUP_NO,CURRENCY
  </sql>
  <resultMap id="resultDTO" type="com.sgs.ecom.member.dto.order.SubOrderDTO" >
    <id column="ORDER_ID" property="orderId" jdbcType="BIGINT" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="GROUP_NO" property="groupNo" jdbcType="VARCHAR" />
    <result column="ORDER_TYPE" property="orderType" jdbcType="VARCHAR" />
    <result column="RELATE_ORDER_NO" property="relateOrderNo" jdbcType="VARCHAR" />
    <result column="REAL_AMOUNT" property="realAmount" jdbcType="DECIMAL" />
    <result column="OPERATOR_CODE" property="operatorCode" jdbcType="VARCHAR" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="PAY_STATE" property="payState" jdbcType="INTEGER" />
    <result column="PAY_METHOD" property="payMethod" jdbcType="INTEGER" />
    <result column="STATE" property="state" jdbcType="INTEGER" />
    <result column="REFUND_STATE" property="refundState" jdbcType="INTEGER" />
    <result column="IS_PAY_RECEIVED" property="isPayReceived" jdbcType="INTEGER" />
    <result column="TOTAL_NUMS" property="totalNums" jdbcType="INTEGER" />
    <result column="RECOMMEND_REASON" property="recommendReason" jdbcType="VARCHAR" />
    <result column="IS_REMIND" property="isRemind" jdbcType="INTEGER" />
    <result column="CURRENCY" property="currency" jdbcType="VARCHAR" />
  </resultMap>

  <select id="selectListByMap" resultMap="resultDTO" >
    select
    <include refid="sqlListDTO" />
    from ORDER_BASE_INFO obi
    <include refid="baseQueryWhere"/>
    order by obi.ORDER_ID DESC
  </select>

  <select id="selectCountByMap" resultType="Integer" >
    select
    count(1)
    from ORDER_BASE_INFO obi
    <include refid="baseQueryWhere"/>
  </select>

  <select id="selectSubOrderInfoDTO" resultType="com.sgs.ecom.member.dto.order.SubOrderInfoDTO">
    select count(1) as num,IFNULL(sum(REAL_AMOUNT),0) as price from ORDER_BASE_INFO obi
    <include refid="baseQueryWhere"/>
  </select>



  <select id="selectSubOrderInfoBySubVO" resultType="com.sgs.ecom.member.dto.order.SubOrderInfoDTO">
    select count(1) as num,IFNULL(sum(obi.REAL_AMOUNT),0) as price,IFNULL(obi.RELATE_ORDER_NO,0) as orderNo from ORDER_BASE_INFO obi
    where obi.state!=91 and obi.PAY_STATE!=1
    <if test="relateOrderNoList != null ">
      AND obi.RELATE_ORDER_NO in
      <foreach collection="relateOrderNoList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    group by obi.RELATE_ORDER_NO
  </select>

  <sql id="baseQueryWhere">
    where 1=1
    <if test="orderNo != null">
      AND obi.ORDER_NO=#{orderNo}
    </if>
    <if test="subOrderType!= null">
      AND ORDER_TYPE in (101000,211000,301000)
    </if>
    <if test="payState != null">
      AND obi.PAY_STATE=#{payState}
    </if>
    <if test="payStateNot != null">
      AND obi.PAY_STATE!=#{payStateNot}
    </if>
    <if test="relateOrderNo != null">
      AND obi.RELATE_ORDER_NO=#{relateOrderNo}
    </if>
    <if test="isPayReceived != null">
      AND obi.IS_PAY_RECEIVED=#{isPayReceived}
    </if>
    <if test="stateNot != null">
      AND obi.STATE !=#{stateNot}
    </if>
    <if test="roPayState != null">
      <if test="roPayState==0">
        and obi.PAY_METHOD is null
      </if>
      <if test="roPayState==1">
        and obi.PAY_METHOD is not null
      </if>
    </if>

    <if test="relateOrderNoList != null ">
      AND obi.RELATE_ORDER_NO in
      <foreach collection="relateOrderNoList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>

  </sql>


</mapper>