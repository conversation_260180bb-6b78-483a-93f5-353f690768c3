package com.sgs.ecom.member.dto.min;

public class MinItemDTO {

	public String name;//测试项目   itemName
	public String testMethod;//测试方法 testName
	public String sampleRequirement;//样品要求
	public String remark;//
	public Long roId;//itemId
	public String amt;//price
	public String originalAmt;//price
	public String testCondition;//"可选测试条件",
	public String analyteId;//111
	public String version;//2021
	public String method;//method
	public String ppId;//987654321
	public String regulationId;//regulation id
	public String [] samples;
	private String businessCombo;
	private String businessComboAmt;
	private String businessComboOriginalAmt;
	private Long parentItemId;
	public Long runTimes;//执行次数


	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getTestMethod() {
		return testMethod;
	}

	public void setTestMethod(String testMethod) {
		this.testMethod = testMethod;
	}

	public String getSampleRequirement() {
		return sampleRequirement;
	}

	public void setSampleRequirement(String sampleRequirement) {
		this.sampleRequirement = sampleRequirement;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Long getRoId() {
		return roId;
	}

	public void setRoId(Long roId) {
		this.roId = roId;
	}

	public String getAmt() {
		return amt;
	}

	public void setAmt(String amt) {
		this.amt = amt;
	}

	public String getOriginalAmt() {
		return originalAmt;
	}

	public void setOriginalAmt(String originalAmt) {
		this.originalAmt = originalAmt;
	}

	public String getTestCondition() {
		return testCondition;
	}

	public void setTestCondition(String testCondition) {
		this.testCondition = testCondition;
	}

	public String getAnalyteId() {
		return analyteId;
	}

	public void setAnalyteId(String analyteId) {
		this.analyteId = analyteId;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getMethod() {
		return method;
	}

	public void setMethod(String method) {
		this.method = method;
	}

	public String getPpId() {
		return ppId;
	}

	public void setPpId(String ppId) {
		this.ppId = ppId;
	}

	public String getRegulationId() {
		return regulationId;
	}

	public void setRegulationId(String regulationId) {
		this.regulationId = regulationId;
	}

	public Long getRunTimes() {
		return runTimes;
	}

	public void setRunTimes(Long runTimes) {
		this.runTimes = runTimes;
	}

	public String[] getSamples() {
		return samples;
	}

	public void setSamples(String[] samples) {
		this.samples = samples;
	}

	public String getBusinessCombo() {
		return businessCombo;
	}

	public void setBusinessCombo(String businessCombo) {
		this.businessCombo = businessCombo;
	}

	public Long getParentItemId() {
		return parentItemId;
	}

	public void setParentItemId(Long parentItemId) {
		this.parentItemId = parentItemId;
	}

	public String getBusinessComboAmt() {
		return businessComboAmt;
	}

	public void setBusinessComboAmt(String businessComboAmt) {
		this.businessComboAmt = businessComboAmt;
	}

	public String getBusinessComboOriginalAmt() {
		return businessComboOriginalAmt;
	}

	public void setBusinessComboOriginalAmt(String businessComboOriginalAmt) {
		this.businessComboOriginalAmt = businessComboOriginalAmt;
	}
}
