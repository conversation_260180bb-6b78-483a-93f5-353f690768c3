package com.sgs.ecom.member.dto.center;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.json.TimeFormatSerializer;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.base.BaseOrderFilter;

import java.sql.Timestamp;

public class SysApplicationAttrDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select ATTR_NAME,BASIC_ID,ROW_NUM,STATE_DATE,STATE,ATTR_RULES,BUSINESS_CODE,AREA_CODE,ATTR_CODE,CREATE_DATE,BUSINESS_TYPE,IS_MUST,LUA,FILL_NOTICE,SORT_SHOW,ENUM_CONFIG,TYPE,DATA_TYPE,FILL_LEN from SYS_APPLICATION_ATTR"; 
 
 
 	@ApiAnno(groups={Default.class,QueryList.class, BaseOrderFilter.OrderApplicationOther.class})
 	private String attrName;
 	@ApiAnno(groups={Default.class})
 	private long basicId;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	private int rowNum;
 	@ApiAnno(groups={Default.class})
 	private Timestamp stateDate;
 	@ApiAnno(groups={Default.class})
 	private int state;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	private String attrRules;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	private String businessCode;
 	@ApiAnno(groups={Default.class,QueryList.class, BaseOrderFilter.OrderApplicationOther.class})
 	private String areaCode;
 	@ApiAnno(groups={Default.class,QueryList.class, BaseOrderFilter.OrderApplicationOther.class})
 	private String attrCode;
 	@ApiAnno(groups={Default.class,QueryList.class, BaseOrderFilter.OrderApplicationOther.class})
 	private String attrValue ="";
 	@ApiAnno(groups={Default.class})
 	private Timestamp createDate;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	private String businessType;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	private int isMust;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	private String lua;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	private String fillNotice;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	private int sortShow;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	private String enumConfig;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	private int type;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	private String dataType;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	private int fillLen;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	private String regexRule;

	public String getRegexRule() {
		return regexRule;
	}

	public void setRegexRule(String regexRule) {
		this.regexRule = regexRule;
	}

	public String getAttrValue() {
		return attrValue;
	}

	public void setAttrValue(String attrValue) {
		this.attrValue = attrValue;
	}

	@ApiAnno(groups={Default.class,QueryList.class})
 	private String enums;
 	
 	public void setAttrName(String attrName){
 		 this.attrName=attrName;
 	}
 	public String getAttrName(){
 		 return this.attrName;
 	}
 
 	 
 	public void setBasicId(long basicId){
 		 this.basicId=basicId;
 	}
 	public long getBasicId(){
 		 return this.basicId;
 	}
 
 	 
 	public void setRowNum(int rowNum){
 		 this.rowNum=rowNum;
 	}
 	public int getRowNum(){
 		 return this.rowNum;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setAttrRules(String attrRules){
 		 this.attrRules=attrRules;
 	}
 	public String getAttrRules(){
 		 return this.attrRules;
 	}
 
 	 
 	public void setBusinessCode(String businessCode){
 		 this.businessCode=businessCode;
 	}
 	public String getBusinessCode(){
 		 return this.businessCode;
 	}
 
 	 
 	public void setAreaCode(String areaCode){
 		 this.areaCode=areaCode;
 	}
 	public String getAreaCode(){
 		 return this.areaCode;
 	}
 
 	 
 	public void setAttrCode(String attrCode){
 		 this.attrCode=attrCode;
 	}
 	public String getAttrCode(){
 		 return this.attrCode;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setBusinessType(String businessType){
 		 this.businessType=businessType;
 	}
 	public String getBusinessType(){
 		 return this.businessType;
 	}
 
 	 
 	public void setIsMust(int isMust){
 		 this.isMust=isMust;
 	}
 	public int getIsMust(){
 		 return this.isMust;
 	}
 
 	 
 	public void setLua(String lua){
 		 this.lua=lua;
 	}
 	public String getLua(){
 		 return this.lua;
 	}
 
 	 
 	public void setFillNotice(String fillNotice){
 		 this.fillNotice=fillNotice;
 	}
 	public String getFillNotice(){
 		 return this.fillNotice;
 	}
 
 	 
 	public void setSortShow(int sortShow){
 		 this.sortShow=sortShow;
 	}
 	public int getSortShow(){
 		 return this.sortShow;
 	}
 
 	 
 	public void setEnumConfig(String enumConfig){
 		 this.enumConfig=enumConfig;
 	}
 	public String getEnumConfig(){
 		 return this.enumConfig;
 	}
 
 	 
 	public void setType(int type){
 		 this.type=type;
 	}
 	public int getType(){
 		 return this.type;
 	}
 
 	 
 	public void setDataType(String dataType){
 		 this.dataType=dataType;
 	}
 	public String getDataType(){
 		 return this.dataType;
 	}
 
 	 
 	public void setFillLen(int fillLen){
 		 this.fillLen=fillLen;
 	}
 	public int getFillLen(){
 		 return this.fillLen;
 	}
//	public String getRegexRule() {
//		return regexRule;
//	}
//	public void setRegexRule(String regexRule) {
//		this.regexRule = regexRule;
//	}
	public String getEnums() {
		return enums;
	}
	public void setEnums(String enums) {
		this.enums = enums;
	}
}