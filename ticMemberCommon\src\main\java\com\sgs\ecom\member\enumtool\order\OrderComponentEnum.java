package com.sgs.ecom.member.enumtool.order;

public enum OrderComponentEnum {
	COMPOSITION("composition", "10", "RSTS-SDS的组成成分及含量"),
	;





	OrderComponentEnum(String name, String index, String nameCh) {
		this.name = name;
		this.index = index;
		this.nameCh = nameCh;
	}

	private String name;
	private String index;
	private String nameCh;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getIndex() {
		return index;
	}

	public void setIndex(String index) {
		this.index = index;
	}

	public String getNameCh() {
		return nameCh;
	}

	public void setNameCh(String nameCh) {
		this.nameCh = nameCh;
	}
}
