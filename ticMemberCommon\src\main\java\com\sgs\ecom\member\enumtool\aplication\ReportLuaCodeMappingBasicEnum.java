package com.sgs.ecom.member.enumtool.aplication;

import org.apache.commons.lang.StringUtils;

/**
 * RSTS-MSDS/SDS
 * 报告语言对应的动态字段的枚举值
 */
public enum ReportLuaCodeMappingBasicEnum {
    ZNEN("ZNEN", "CHI-EN", "中文+英文报告"),
    ZNANDEN("ZNANDEN", "CHI-EN", "中英文报告"),
    CHI("ZN", "CHI", "中文报告"),
    EN("EN", "EN", "英文报告"),




    ;

    ReportLuaCodeMappingBasicEnum(String name, String index, String nameCh) {
        this.name = name;
        this.index = index;
        this.nameCh = nameCh;
    }

    public static String getName(String index) {
        for (ReportLuaCodeMappingBasicEnum c : ReportLuaCodeMappingBasicEnum.values()) {
            if (c.getIndex().equals(index)) {
                return c.getName();
            }
        }
        return "3";
    }


    public static String getIndex(String name) {
        for (ReportLuaCodeMappingBasicEnum c : ReportLuaCodeMappingBasicEnum.values()) {
            if (c.name().equals(name)) {
                return c.getIndex();
            }
        }
        return "";
    }

    public static String getNameChByName(String name) {
        if(StringUtils.isBlank(name)){
            return "";
        }

        for (ReportLuaCodeMappingBasicEnum c : ReportLuaCodeMappingBasicEnum.values()) {
            if (c.getName().equals(name.toLowerCase())) {
                return c.getNameCh();
            }
        }
        return "";
    }


    public static String getNameByNameCh(String nameCh) {
        for (ReportLuaCodeMappingBasicEnum c : ReportLuaCodeMappingBasicEnum.values()) {
            if (c.getNameCh().equals(nameCh)) {
                return c.getName().toUpperCase();
            }
        }
        return "";
    }


    public static String getIndexBynameCh(String name) {
        for (ReportLuaCodeMappingBasicEnum c : ReportLuaCodeMappingBasicEnum.values()) {
            if (c.getNameCh().equals(name)) {
                return c.getIndex();
            }
        }
        return "";
    }


    private String name;
    private String index;
    private String nameCh;

    public static boolean checkIsReportLua(String reportLua) {
        for (ReportLuaCodeMappingBasicEnum c : ReportLuaCodeMappingBasicEnum.values()) {
            if (c.getIndex().equals(reportLua)) {
                return true;
            }
        }

        return false;
    }


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }
}
