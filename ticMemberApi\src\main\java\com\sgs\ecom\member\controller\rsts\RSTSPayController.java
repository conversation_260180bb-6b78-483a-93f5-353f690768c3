package com.sgs.ecom.member.controller.rsts;

import com.alibaba.fastjson.JSONObject;
import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.base.BaseBean;
import com.sgs.ecom.member.request.OrderNoReq;
import com.sgs.ecom.member.request.OrderPayTransReq;
import com.sgs.ecom.member.request.rpc.PayReq;
import com.sgs.ecom.member.service.rsts.interfaces.IRSTSPayService;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.rsts/pay")
public class RSTSPayController extends ControllerUtil {

    @Resource
    private IRSTSPayService rstsPayService;

    /**
    * @params [orderPayTransReq, token]
    * @return com.sgs.ecom.member.util.ResultBody
    * @description rsts去支付的逻辑
    * <AUTHOR> || created at 2023/9/11 9:46
    */
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "toPay", method = { RequestMethod.POST })
    public ResultBody toPay(
            @RequestBody OrderPayTransReq orderPayTransReq,
            @RequestHeader(value="accessToken") String token) throws Exception {
        return ResultBody.newInstance(JSONObject.parseObject(rstsPayService.toPay(orderPayTransReq,getUserDTO(token))));
    }

    /**
    * @params [orderPayTransReq, token]
    * @return com.sgs.ecom.member.util.ResultBody
    * @description
    * <AUTHOR> || created at 2023/9/12 15:50
    */
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "qryList", method = { RequestMethod.POST })
    public ResultBody qryList(
            @RequestHeader(value="accessToken") String token,
            @RequestBody OrderNoReq orderNoReq) throws Exception {
        return ResultBody.newInstance(rstsPayService.qryList(orderNoReq,getUserDTO(token)));
    }

    /**
     *@Function: orderList
     *@Description 支付成功详情
     *@param: [orderNoReq, token]
     *@author: Xiwei_Qiu @date: 2022/5/28 @version:
     **/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "orderList", method = { RequestMethod.POST })
    public ResultBody orderList(@Validated(BaseBean.Query.class)
                                @RequestBody PayReq payReq,
                                @RequestHeader(value="accessToken") String token) throws Exception {
        return ResultBody.newInstance(rstsPayService.orderList(payReq,getUserDTO(token)));
    }

    /**
     *@Function: checkPay
     *@Description
     *@param: [token, orderNoReq]
     *@author: Xiwei_Qiu @date: 2022/7/20 @version:
     **/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "checkPay", method = { RequestMethod.POST })
    public ResultBody checkPay (
            @RequestHeader(value="accessToken") String token,
            @RequestBody OrderPayTransReq orderPayTransReq)throws Exception{
        rstsPayService.checkPay(orderPayTransReq,getUserDTO(token));
        return ResultBody.success();
    }

}
