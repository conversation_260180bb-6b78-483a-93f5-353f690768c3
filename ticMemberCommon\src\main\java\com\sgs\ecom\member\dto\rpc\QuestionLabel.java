package com.sgs.ecom.member.dto.rpc;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter.Default;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 问题标签
 *
 * <AUTHOR>
 * @since 2025-05-13 16:02
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionLabel {

    /**
     * 标签ID
     */
    @ApiAnno(groups={Default.class})
    private Long labelId;

    /**
     * 问卷章节所属业务
     */
    @ApiAnno(groups={Default.class})
    private String businessType;

    /**
     * 所属业务对应CODE或ID
     */
    @ApiAnno(groups={Default.class})
    private String businessCode;

    /**
     * 标签编码
     */
    @ApiAnno(groups={Default.class})
    private String labelCode;

    /**
     * 标签名称
     */
    @ApiAnno(groups={Default.class})
    private String labelName;

    /**
     * 标签描述
     */
    @ApiAnno(groups={Default.class})
    private String labelDesc;

    /**
     * 提示文案
     */
    @ApiAnno(groups={Default.class})
    private String fillPrompt;

    /**
     * 是否可修改 1：是 0：否
     */
    @ApiAnno(groups={Default.class})
    private Integer isEdit;

    /**
     * 状态 1：有效 0：无效
     */
    private Integer state;

    /**
     * 创建时间
     */
    @ApiAnno(groups={Default.class})
    private Date createDate;

    /**
     * 创建人
     */
    @ApiAnno(groups={Default.class})
    private String createBy;

    /**
     * 修改时间
     */
    @ApiAnno(groups={Default.class})
    private Date stateDate;

    /**
     * 修改人
     */
    @ApiAnno(groups={Default.class})
    private String modifiedBy;

    /**
     * 标签选项
     */
    @ApiAnno(groups={Default.class})
    private List<QuestionLabelOption> labelOptionList;
}
