package com.sgs.ecom.member.dto.user;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.json.DateFormatSerializer;
import com.platform.util.json.TimeFormatSerializer;
import com.sgs.base.BaseQryFilter;

import java.sql.Timestamp;
import java.util.Date;

public class UserToolkitExpReportDTO extends BaseQryFilter {



	private long recordId;

	private String userId;

	private int state;

	private String toolCode;

	private String makeTitle;

	private String makeNo;

	private String makeType;

	private String showMode;

	private String createDate;

	private Date stateDate;


	private String qrCode;
	private String qryType;
	private String reportType;
	private String isSuccess;
	private String reportNo;
	private String failType;
	private String url;
	private String userName;
	private String userPhone;
	private String userEmail;
	private String companyName;
	private String fileName;
	private String frontUrl;
	private String isArtificial;
	private String qrCodePass;
	private String dssSignatureVerificationTime;
	private String qrCodeVerificationTime;
	private String getQrCodeTime;
	private String dssFlag;
	private String javaDssFlag;
	private String netDssFlag;

	private String reportVerificationFlag;
	private String qrCodeVerificationFlag;
	private String transTimes;
	private String scanTimes;
	private String firstQrCode;//无论是图片/pdf直接第一次获取的qrcode二维码地址

	private String isBlacklist;//二维码在黑名单中 0-否 1-是

	public String getIsBlacklist() {
		return isBlacklist;
	}

	public void setIsBlacklist(String isBlacklist) {
		this.isBlacklist = isBlacklist;
	}

	public String getFirstQrCode() {
		return firstQrCode;
	}

	public void setFirstQrCode(String firstQrCode) {
		this.firstQrCode = firstQrCode;
	}

	public long getRecordId() {
		return recordId;
	}

	public void setRecordId(long recordId) {
		this.recordId = recordId;
	}



	public void setState(int state) {
		this.state = state;
	}

	public String getMakeTitle() {
		return makeTitle;
	}

	public void setMakeTitle(String makeTitle) {
		this.makeTitle = makeTitle;
	}

	public String getMakeNo() {
		return makeNo;
	}

	public void setMakeNo(String makeNo) {
		this.makeNo = makeNo;
	}

	public String getMakeType() {
		return makeType;
	}

	public void setMakeType(String makeType) {
		this.makeType = makeType;
	}

	public String getShowMode() {
		return showMode;
	}

	public void setShowMode(String showMode) {
		this.showMode = showMode;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getToolCode() {
		return toolCode;
	}

	public void setToolCode(String toolCode) {
		this.toolCode = toolCode;
	}



	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public String getCreateDate() {
		String[] newstr = createDate.split("\\.");
		return newstr[0];
	}

	public void setCreateDate(String createDate) {
		this.createDate = createDate;
	}

	public Date getStateDate() {
		return stateDate;
	}

	public void setStateDate(Date stateDate) {
		this.stateDate = stateDate;
	}

	public String getQrCode() {
		return qrCode;
	}

	public void setQrCode(String qrCode) {
		this.qrCode = qrCode;
	}

	public String getQryType() {
		return qryType;
	}

	public void setQryType(String qryType) {
		this.qryType = qryType;
	}

	public String getReportType() {
		return reportType;
	}

	public void setReportType(String reportType) {
		this.reportType = reportType;
	}

	public String getIsSuccess() {
		return isSuccess;
	}

	public void setIsSuccess(String isSuccess) {
		this.isSuccess = isSuccess;
	}

	public String getReportNo() {
		return reportNo;
	}

	public void setReportNo(String reportNo) {
		this.reportNo = reportNo;
	}

	public String getFailType() {
		return failType;
	}

	public void setFailType(String failType) {
		this.failType = failType;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getUserPhone() {
		return userPhone;
	}

	public void setUserPhone(String userPhone) {
		this.userPhone = userPhone;
	}

	public String getUserEmail() {
		return userEmail;
	}

	public void setUserEmail(String userEmail) {
		this.userEmail = userEmail;
	}

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getFrontUrl() {
		return frontUrl;
	}

	public void setFrontUrl(String frontUrl) {
		this.frontUrl = frontUrl;
	}

	public String getIsArtificial() {
		return isArtificial;
	}

	public void setIsArtificial(String isArtificial) {
		this.isArtificial = isArtificial;
	}

	public String getQrCodePass() {
		return qrCodePass;
	}

	public void setQrCodePass(String qrCodePass) {
		this.qrCodePass = qrCodePass;
	}

	public String getDssSignatureVerificationTime() {
		return dssSignatureVerificationTime;
	}

	public void setDssSignatureVerificationTime(String dssSignatureVerificationTime) {
		this.dssSignatureVerificationTime = dssSignatureVerificationTime;
	}

	public String getQrCodeVerificationTime() {
		return qrCodeVerificationTime;
	}

	public void setQrCodeVerificationTime(String qrCodeVerificationTime) {
		this.qrCodeVerificationTime = qrCodeVerificationTime;
	}

	public String getGetQrCodeTime() {
		return getQrCodeTime;
	}

	public void setGetQrCodeTime(String getQrCodeTime) {
		this.getQrCodeTime = getQrCodeTime;
	}

	public String getDssFlag() {
		return dssFlag;
	}

	public void setDssFlag(String dssFlag) {
		this.dssFlag = dssFlag;
	}

	public String getReportVerificationFlag() {
		return reportVerificationFlag;
	}

	public void setReportVerificationFlag(String reportVerificationFlag) {
		this.reportVerificationFlag = reportVerificationFlag;
	}

	public String getQrCodeVerificationFlag() {
		return qrCodeVerificationFlag;
	}

	public void setQrCodeVerificationFlag(String qrCodeVerificationFlag) {
		this.qrCodeVerificationFlag = qrCodeVerificationFlag;
	}

	public String getTransTimes() {
		return transTimes;
	}

	public void setTransTimes(String transTimes) {
		this.transTimes = transTimes;
	}

	public String getScanTimes() {
		return scanTimes;
	}

	public void setScanTimes(String scanTimes) {
		this.scanTimes = scanTimes;
	}

	public String getJavaDssFlag() {
		return javaDssFlag;
	}

	public void setJavaDssFlag(String javaDssFlag) {
		this.javaDssFlag = javaDssFlag;
	}

	public String getNetDssFlag() {
		return netDssFlag;
	}

	public void setNetDssFlag(String netDssFlag) {
		this.netDssFlag = netDssFlag;
	}
}