<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.order.dao.OrderBaseInfoCustomMapper" >


  <!--自定义-->

  <resultMap id="indexMap" type="com.sgs.ecom.member.dto.custom.StateAndNumDTO" >
    <result column="STATE" property="state" jdbcType="INTEGER" />
    <result column="NUM" property="num" jdbcType="BIGINT" />
  </resultMap>

  <select id="selectStateGroup"  resultMap="indexMap"  >
     select STATE,count(1) as NUM from ORDER_BASE_INFO
      where 1=1
    <if test="userId != null">
      AND USER_ID=#{userId}
    </if>
    <if test="orderType != null">
      AND ORDER_TYPE=#{orderType}
    </if>
    <if test="orderTypeList != null ">
          AND ORDER_TYPE in
        <foreach collection="orderTypeList" index="index" item="item" open="(" separator="," close=")">
           #{item}
        </foreach>
    </if>
    <if test="notState != null">
      AND state !=#{notState}
    </if>
    <if test="subStateNot != null">
      AND SUB_STATE!=#{subStateNot}
    </if>
    <if test="isDelete != null">
      AND IS_DELETE =#{isDelete}
    </if>
      <if test="bu != null">
          AND BU =#{bu}
      </if>
      <if test="custIdList != null">
          AND CUST_ID in
          <foreach collection="custIdList" separator="," open="(" close=")" item="item">
              #{item}
          </foreach>
      </if>
      group by STATE
  </select>
  <select id="selectExpressNumState"   resultType="java.lang.Integer">
      SELECT COUNT(DISTINCT TT.order_no) FROM (
      SELECT
      DISTINCT oe.order_no
      FROM
      order_base_info obi,
      order_express oe
      WHERE
      obi.order_no = oe.order_no
      AND oe.DELIVER_TYPE = 10
      AND obi.STATE = 12
      AND oe.IS_CS = 1
      <if test="userId != null">
          AND obi.USER_ID=#{userId}
      </if>
      <if test="orderType != null">
          AND obi.ORDER_TYPE=#{orderType}
      </if>
      <if test="isDelete != null">
          AND obi.IS_DELETE =#{isDelete}
      </if>
      GROUP BY
      oe.order_no

      UNION ALL
      select DISTINCT op.ORDER_NO from order_base_info obi,order_product op where
      op.ORDER_NO =  obi.ORDER_NO and op.state = 1
      AND obi.STATE = 12
      <if test="userId != null">
          AND obi.USER_ID=#{userId}
      </if>
      <if test="orderType != null">
          AND obi.ORDER_TYPE=#{orderType}
      </if>
      <if test="isDelete != null">
          AND obi.IS_DELETE =#{isDelete}
      </if>
      AND (op.STORE_ID in ('INSP','CBE','EHS')  or op.SUB_BU_CODE in ('SL-PX','RSTS-SDS'))) TT

  </select>

    <select id="selectMaxUrgentByOrderNo"   resultType="java.lang.Integer">
        select IFNULL(max(URGENT_TYPE),1) from order_detail where ORDER_NO=#{orderNo} and GROUP_NO=#{groupNo}
    </select>

    <select id="selectEndNumStateByRead"   resultType="java.lang.Integer">
        select count(1) as num from ORDER_BASE_INFO
        where 1=1 AND IS_READ=0 AND STATE=80
        <if test="userId != null">
            AND USER_ID=#{userId}
        </if>
        <if test="orderType != null">
            AND ORDER_TYPE=#{orderType}
        </if>
        <if test="orderTypeList != null ">
            AND ORDER_TYPE in
            <foreach collection="orderTypeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

    </select>

  <update id="updateStateByRead"  >
    update ORDER_BASE_INFO set IS_READ=1
    where 1=1 AND IS_READ=0 AND STATE=80  AND USER_ID=#{userId}
  </update>



  <resultMap id="indexTotalNum" type="com.sgs.ecom.member.dto.OrderTypeNumDTO" >
    <result column="ORDER_TYPE" property="orderType" jdbcType="VARCHAR" />
    <result column="num" property="num" jdbcType="BIGINT" />
  </resultMap>
  <select id="selectTotalNum"  resultMap="indexTotalNum"  >
    select ORDER_TYPE ,count(1) as num from ORDER_BASE_INFO
    where 1=1 and IS_DELETE=0
    <if test="userId != null">
      AND USER_ID=#{userId}
    </if>
    group by ORDER_TYPE
  </select>




  <resultMap id="indexLab" type="com.sgs.ecom.member.dto.custom.OrderLabDTO" >
    <result column="LAB_ID" property="labId" jdbcType="BIGINT" />
    <result column="LAB_NAME" property="labName" jdbcType="VARCHAR" />
  </resultMap>
  <select id="selectLab"  resultMap="indexLab"  >
    select LAB_ID ,LAB_NAME from ORDER_BASE_INFO
    where 1=1
    <if test="userId != null">
      AND USER_ID=#{userId}
    </if>
    <if test="orderType != null">
      AND ORDER_TYPE=#{orderType}
    </if>
    group by LAB_ID
  </select>

    <resultMap id="indexStore" type="com.sgs.ecom.member.dto.bbc.BbcStoreDTO" >
        <result column="BU" property="storeId" jdbcType="BIGINT" />
    </resultMap>
    <select id="getStore"  resultMap="indexStore"  >
        select BU from ORDER_BASE_INFO
        where 1=1
        <if test="userId != null">
            AND USER_ID=#{userId}
        </if>
        <if test="orderType != null">
            AND ORDER_TYPE=#{orderType}
        </if>
        group by BU
    </select>





  <insert id="copyOrder"  >
insert into ORDER_BASE_INFO ( USER_SEX,USER_EMAIL,PAY_DATE,USER_ID,RECOMMEND_REASON,HIS_STATE,URGENT_AMOUNT,PLATFORM,LINE_ID,
SAMPLE_REQUIREMENTS,BUSINESS_LINE,OFFER_DATE,REAL_AMOUNT,RELATE_ORDER_NO,IS_READ,STATE_DATE,TMP_GROUP_NO,PRODUCT_NAME,
COMPANY_NAME,REPORT_FORM,CREATE_DATE,CS_NAME,CS_NAME_EN,STATE,ORDER_NO,CS_BRANCH,CS_EMAIL,LAB_ID,LAB_NAME,IS_URGENT,BU,REPORT_LUA,
TEST_CYCLE,TEST_CYCLE_MEMO,QUESTION_ID,CS_CODE,ORDER_TYPE,IS_REMIND,USER_NAME,PLATFORM_ORDER,ORDER_AMOUNT,USER_PHONE,CITY,GROUP_NO,
CATEGORY_PATH,CATAGORY_ID,DISCOUNT_AMOUNT,PROVINCE,ORDER_EXP_DATE,SERVICE_AMOUNT,SUB_STATE,TOTAL_NUMS,PAY_STATE,IS_TEST,
IS_INVOICE,IS_DELETE,IS_PAY_RECEIVED,REPORT_LUA_CODE,REPORT_FORM_CODE,ACCOUNT_NO,ORDER_SOURCE,LEADS_CODE,OPERATOR_CODE,TEST_LABEL,CURRENCY,EXCHANGE_RATE,TAX_RATES )select
 USER_SEX,USER_EMAIL,PAY_DATE,USER_ID,RECOMMEND_REASON,HIS_STATE,URGENT_AMOUNT,PLATFORM,LINE_ID,SAMPLE_REQUIREMENTS,
 BUSINESS_LINE,OFFER_DATE,REAL_AMOUNT,#{orderNo},0,now(),"",PRODUCT_NAME,COMPANY_NAME,REPORT_FORM,now(),
CS_NAME,CS_NAME_EN,11,#{newOrderNo},CS_BRANCH,CS_EMAIL,LAB_ID,LAB_NAME,#{isUrgent},BU,REPORT_LUA,TEST_CYCLE,TEST_CYCLE_MEMO,QUESTION_ID,CS_CODE,
 210000,IS_REMIND,USER_NAME,PLATFORM_ORDER,REAL_AMOUNT,USER_PHONE,CITY,#{groupNo},CATEGORY_PATH,CATAGORY_ID,DISCOUNT_AMOUNT,
 PROVINCE,ORDER_EXP_DATE,SERVICE_AMOUNT,0,1,0,#{isTest},0,0,0,REPORT_LUA_CODE,REPORT_FORM_CODE,ACCOUNT_NO,ORDER_SOURCE,LEADS_CODE,#{operatorCode},
 TEST_LABEL,CURRENCY,EXCHANGE_RATE,TAX_RATES
 from ORDER_BASE_INFO
 where order_no=#{orderNo}
  </insert>

    <insert id="copyAttribute"  >
   INSERT into ORDER_ATTRIBUTE (ORDER_NO,ATTR_NAME,ATTR_VALUE,IS_DEFAULT,GROUP_NO,CREATE_DATE,ATTR_ID,ATTR_AMOUNT,ATTR_CODE,ATTR_EXTEND)
   select #{newOrderNo},ATTR_NAME,ATTR_VALUE,IS_DEFAULT,#{newGroupNo},now(),ATTR_ID,ATTR_AMOUNT,ATTR_CODE,ATTR_EXTEND from ORDER_ATTRIBUTE where GROUP_NO = #{oldGroupNo} and ORDER_NO=#{orderNo}
  </insert>

  <resultMap id="orderBasePay" type="com.sgs.ecom.member.dto.pay.OrderBasePayDTO" >
    <id column="ORDER_ID" property="orderId" jdbcType="BIGINT" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="RELATE_ORDER_NO" property="relateOrderNo" jdbcType="VARCHAR" />
    <result column="ORDER_TYPE" property="orderType" jdbcType="VARCHAR" />
    <result column="LAB_ID" property="labId" jdbcType="INTEGER" />
    <result column="PAY_STATE" property="payState" jdbcType="INTEGER" />
    <result column="REAL_AMOUNT" property="realAmount" jdbcType="DECIMAL" />
    <result column="ORDER_AMOUNT" property="orderAmount" jdbcType="DECIMAL" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="USER_NAME" property="userName" jdbcType="VARCHAR" />
    <result column="REPORT_LUA" property="reportLua" jdbcType="VARCHAR" />
    <result column="REPORT_FORM" property="reportForm" jdbcType="VARCHAR" />
    <result column="CATEGORY_PATH" property="categoryPath" jdbcType="VARCHAR" />
    <result column="SUB_STATE" property="subState" jdbcType="INTEGER" />
    <result column="REFUND_STATE" property="refundState" jdbcType="INTEGER" />
      <result column="STATE" property="state" jdbcType="INTEGER" />
      <result column="GROUP_NO" property="groupNo" jdbcType="VARCHAR" />
      <result column="PAY_METHOD" property="payMethod" jdbcType="VARCHAR" />
      <result column="BU" property="bu" jdbcType="VARCHAR" />
      <result column="IS_PAY_RECEIVED" property="isPayReceived" jdbcType="TINYINT"/>
      <result column="OPERATOR_CODE" property="operatorCode" jdbcType="BIGINT" />
      <result column="PAY_TYPE" property="payType" jdbcType="INTEGER" />
      <result column="ACCOUNT_NAME" property="accountName" jdbcType="VARCHAR" />
      <result column="CREATE_DATE" property="createDate" jdbcType="VARCHAR" />
      <result column="CURRENCY" property="currency" jdbcType="VARCHAR" />
      <result column="RECOMMEND_REASON" property="recommendReason" jdbcType="VARCHAR" />
      <result column="SHOP_DIS_AMOUNT" property="shopDisAmount" jdbcType="DECIMAL" />
      <result column="CS_DISCOUNT_AMOUNT" property="csDiscountAmount" jdbcType="DECIMAL"/>
      <result column="DISCOUNT_AMOUNT" property="discountAmount" jdbcType="DECIMAL" />
      <result column="SERVICE_AMOUNT" property="serviceAmount" jdbcType="DECIMAL" />
  </resultMap>
  <select id="selectOrderBasePayDTO"  resultMap="orderBasePay"  >
    select
    <include refid="orderBasePaySql"/>
    from ORDER_BASE_INFO
    where ORDER_NO = #{orderNo}
  </select>


  <resultMap id="orderBaseForm" type="com.sgs.ecom.member.dto.pdf.OrderFormPDFDTO" >
    <id column="ORDER_ID" property="orderId" jdbcType="BIGINT" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="REPORT_LUA" property="reportLua" jdbcType="VARCHAR" />
    <result column="REPORT_FORM" property="reportForm" jdbcType="VARCHAR" />
    <result column="REPORT_LUA_CODE" property="reportLuaCode" jdbcType="VARCHAR" />
    <result column="REPORT_FORM_CODE" property="reportFormCode" jdbcType="VARCHAR" />
      <result column="BUSINESS_LINE" property="businessLine" jdbcType="VARCHAR" />
    <result column="TEST_CYCLE" property="testCycle" jdbcType="DECIMAL" />
      <result column="IS_URGENT" property="isUrgent" jdbcType="BIGINT" />
    <result column="TEST_CYCLE_MEMO" property="testCycleMemo" jdbcType="VARCHAR" />
    <result column="LAB_ID" property="labId" jdbcType="BIGINT" />
    <result column="CS_EMAIL" property="csEmail" jdbcType="VARCHAR" />
    <result column="CS_CODE" property="csCode" jdbcType="VARCHAR" />
    <result column="RELATE_ORDER_NO" property="relateOrderNo" jdbcType="VARCHAR" />
    <result column="CATEGORY_PATH" property="categoryPath" jdbcType="VARCHAR" />
    <result column="CREATE_DATE" property="createDate" jdbcType="VARCHAR" />
      <result column="ORDER_TYPE" property="orderType" jdbcType="VARCHAR" />
  </resultMap>
  <select id="selectOrderFormPDFDTO"  resultMap="orderBaseForm"  >
    select
    ORDER_ID,ORDER_NO,REPORT_LUA,REPORT_LUA_CODE,REPORT_FORM,REPORT_FORM_CODE,BUSINESS_LINE,
    LAB_ID,TEST_CYCLE,IS_URGENT,CS_EMAIL,CS_CODE,TEST_CYCLE_MEMO,RELATE_ORDER_NO,CATEGORY_PATH,
    CREATE_DATE,ORDER_TYPE
    from ORDER_BASE_INFO
    where ORDER_NO = #{orderNo}
  </select>
    <resultMap id="orderBaseShare" type="com.sgs.ecom.member.dto.pdf.OrderSharePDFDTO" >
        <id column="ORDER_ID" property="orderId" jdbcType="BIGINT" />
        <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
        <result column="STATE" property="state" jdbcType="VARCHAR" />
        <result column="STATE_DATE" property="stateDate" jdbcType="VARCHAR" />
        <result column="CS_CODE" property="csCode" jdbcType="VARCHAR" />
        <result column="USER_NAME" property="userName" jdbcType="VARCHAR" />
        <result column="USER_SEX" property="userSex" jdbcType="VARCHAR" />
        <result column="CATEGORY_PATH" property="categoryPath" jdbcType="VARCHAR" />
        <result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR" />
        <result column="OFFER_DATE" property="offerDate" jdbcType="VARCHAR" />
        <result column="ORDER_EXP_DATE" property="orderExpDate" jdbcType="VARCHAR" />
        <result column="SAMPLE_REQUIREMENTS" property="sampleRequirements" jdbcType="VARCHAR" />
        <result column="RECOMMEND_REASON" property="recommendReason" jdbcType="VARCHAR" />
        <result column="CS_NAME" property="csName" jdbcType="VARCHAR" />
        <result column="REPORT_FORM" property="reportFormValue" jdbcType="VARCHAR" />
        <result column="LAB_NAME" property="labNameValue" jdbcType="VARCHAR" />
        <result column="REPORT_LUA" property="reportLuaValue" jdbcType="VARCHAR" />
        <result column="BUSINESS_LINE" property="businessLine" jdbcType="VARCHAR" />

        <result column="ORDER_AMOUNT" property="orderAmount" jdbcType="DECIMAL" />
        <result column="REAL_AMOUNT" property="realAmount" jdbcType="DECIMAL" />
        <result column="TEST_CYCLE" property="testCycle" jdbcType="DECIMAL" />
        <result column="DISCOUNT_AMOUNT" property="discountAmount" jdbcType="DECIMAL" />
        <result column="SERVICE_AMOUNT" property="serviceAmount" jdbcType="DECIMAL" />
        <result column="URGENT_AMOUNT" property="urgentAmount" jdbcType="DECIMAL" />
        <result column="GROUP_NO" property="groupNo" jdbcType="VARCHAR" />
        <result column="ORDER_TYPE" property="orderType" jdbcType="VARCHAR" />
        <result column="CURRENCY" property="currency" jdbcType="VARCHAR" />
        <result column="TAX_RATES" property="taxRates" jdbcType="DECIMAL" />
    </resultMap>

    <select id="selectOrderSharePDFDTO"  resultMap="orderBaseShare">
    select
    ORDER_ID,ORDER_NO,CS_CODE,USER_NAME,USER_SEX,CATEGORY_PATH,COMPANY_NAME,OFFER_DATE,ORDER_EXP_DATE,STATE,STATE_DATE,
    SAMPLE_REQUIREMENTS,RECOMMEND_REASON,CS_NAME,ORDER_AMOUNT,REAL_AMOUNT,REPORT_FORM,LAB_NAME,REPORT_LUA,
    TEST_CYCLE,DISCOUNT_AMOUNT,SERVICE_AMOUNT,URGENT_AMOUNT,GROUP_NO,ORDER_TYPE,BUSINESS_LINE,CURRENCY,TAX_RATES
    from ORDER_BASE_INFO
    where ORDER_NO = #{orderNo}
    </select>





  <resultMap id="indexMailDTO" type="com.sgs.ecom.member.dto.custom.MailDTO" >
    <result column="USER_PHONE" property="userPhone" jdbcType="INTEGER" />
    <result column="USER_NAME" property="userName" jdbcType="VARCHAR" />
    <result column="USER_SEX" property="userSex" jdbcType="VARCHAR" />
      <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="USER_EMAIL" property="userEmail" jdbcType="VARCHAR" />
    <result column="CS_CODE" property="csCode" jdbcType="VARCHAR" />
    <result column="CS_EMAIL" property="csEmail" jdbcType="VARCHAR" />
    <result column="CS_NAME" property="csName" jdbcType="VARCHAR" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="ORDER_ID" property="orderId" jdbcType="BIGINT" />
    <result column="REAL_AMOUNT" property="realAmount" jdbcType="DECIMAL" />
    <result column="AUDIT_CODE" property="auditCode" jdbcType="VARCHAR" />
    <result column="OPERATOR_CODE" property="operatorCode" jdbcType="VARCHAR" />
    <result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR" />
    <result column="GROUP_NO" property="groupNo" jdbcType="VARCHAR" />
    <result column="TEST_CYCLE" property="testCycle" jdbcType="DECIMAL" />
    <result column="BUSINESS_LINE" property="businessLine" jdbcType="VARCHAR" />
    <result column="IS_DELETE" property="isDelete" jdbcType="TINYINT" />
    <result column="CREATE_DATE" property="createDate" jdbcType="VARCHAR" />
      <result column="BU" property="bu" jdbcType="VARCHAR" />
      <result column="LINE_ID" property="lineId" jdbcType="VARCHAR" />
      <result column="IS_TEST" property="isTest" jdbcType="INTEGER" />
      <result column="ORDER_TYPE" property="orderType" jdbcType="VARCHAR" />
      <result column="RELATE_ORDER_NO" property="relateOrderNo" jdbcType="VARCHAR" />
      <result column="CATAGORY_ID" property="categoryId" jdbcType="VARCHAR" />
      <result column="BUSINESS_PERSON_EMAIL" property="businessPersonEmail" jdbcType="VARCHAR" />
  </resultMap>


  <select id="selectMailDTO"  resultMap="indexMailDTO"  >
    select
    USER_PHONE,USER_NAME,USER_SEX,USER_EMAIL,CS_CODE,CS_EMAIL,CS_NAME,ORDER_NO,ORDER_ID,REAL_AMOUNT,
    AUDIT_CODE,OPERATOR_CODE,COMPANY_NAME,GROUP_NO,TEST_CYCLE,BUSINESS_LINE,IS_DELETE,CREATE_DATE,
    BU,LINE_ID,IS_TEST,ORDER_TYPE,RELATE_ORDER_NO,CATAGORY_ID,BUSINESS_PERSON_EMAIL, USER_ID
    from ORDER_BASE_INFO
    where 1=1
    <if test="orderId != null">
          AND ORDER_ID=#{orderId}
    </if>
    <if test="orderNo != null">
          AND ORDER_NO=#{orderNo}
    </if>
    order by ORDER_ID desc limit 1
  </select>



    <resultMap id="ticIndexMailDTO" type="com.sgs.ecom.member.dto.send.TicMailDTO" >
        <result column="ORDER_ID" property="orderId" jdbcType="BIGINT" />
        <result column="OPERATOR_CODE" property="operatorCode" jdbcType="BIGINT" />
        <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
        <result column="ORDER_TYPE" property="orderType" jdbcType="VARCHAR" />
        <result column="HIS_STATE" property="hisState" jdbcType="INTEGER" />
        <result column="GROUP_NO" property="groupNo" jdbcType="VARCHAR" />
        <result column="LINK_PERSON" property="linkPerson" jdbcType="INTEGER" />
        <result column="LINK_PHONE" property="linkPhone" jdbcType="VARCHAR" />
        <result column="LINK_EMAIL" property="linkEmail" jdbcType="VARCHAR" />
        <result column="REPORT_SEND_CC" property="reportSendCc" jdbcType="VARCHAR" />
        <result column="CS_SEND_CC" property="csSendCc" jdbcType="VARCHAR" />
        <result column="PROD_ID" property="prodId" jdbcType="INTEGER" />
        <result column="STORE_ID" property="storeId" jdbcType="VARCHAR" />
        <result column="CREATE_DATE" property="createDate" jdbcType="VARCHAR" />
        <result column="ORDER_AMOUNT" property="orderAmount" jdbcType="VARCHAR" />
        <result column="REAL_AMOUNT" property="realAmount" jdbcType="VARCHAR" />
        <result column="BU" property="bu" jdbcType="VARCHAR" />
        <result column="STORE_NAME" property="storeName" jdbcType="VARCHAR" />
        <result column="IS_TEST" property="isTest" jdbcType="INTEGER" />
        <result column="STATE" property="state" jdbcType="INTEGER" />
        <result column="COMPANY_NAME_CN" property="companyNameCn" jdbcType="VARCHAR" />
        <result column="COMPANY_NAME_EN" property="companyNameEn" jdbcType="VARCHAR" />
        <result column="COMPANY_ADDRESS_CN" property="companyAddressCn" jdbcType="VARCHAR" />
        <result column="COMPANY_ADDRESS_EN" property="companyAddressEn" jdbcType="VARCHAR" />
        <result column="LAB_ID" property="labId" jdbcType="BIGINT" />
        <result column="RECOMMEND_REASON" property="recommendReason" jdbcType="VARCHAR" />
        <result column="AUDIT_CODE" property="auditCode" jdbcType="VARCHAR"/>
        <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    </resultMap>


    <select id="selectTicMailDTOByOrderId" resultMap="ticIndexMailDTO">
       SELECT distinct obi.ORDER_ID,obi.OPERATOR_CODE,obi.AUDIT_CODE,obi.ORDER_NO,obi.ORDER_TYPE,obi.HIS_STATE,
              obi.LAB_ID, obi.USER_ID,
  IF(oaf.FORM_ID is null ,obi.USER_NAME,oaf.LINK_PERSON) LINK_PERSON,
        IF(oaf.FORM_ID is null ,obi.USER_PHONE,oaf.LINK_PHONE) LINK_PHONE,
        IF(oaf.FORM_ID is null ,obi.USER_EMAIL,oaf.LINK_EMAIL) LINK_EMAIL,
        IF(oaf.FORM_ID is null ,obi.COMPANY_NAME,oaf.COMPANY_NAME_CN) COMPANY_NAME_CN,
        IF(oaf.FORM_ID is null ,CONCAT_WS("",obi.PROVINCE,obi.CITY,obi.TOWN,obi.COMPANY_ADDRESS_CN),CONCAT_WS("",oaf.PROVINCE,oaf.CITY,oaf.TOWN,oaf.COMPANY_ADDRESS_CN)) COMPANY_ADDRESS_CN,
        IF(oaf.FORM_ID is null ,obi.COMPANY_ADDRESS_EN,oaf.COMPANY_ADDRESS_EN) COMPANY_ADDRESS_EN,
        IF(oaf.FORM_ID is null ,obi.company_NAME_EN,oaf.company_NAME_EN) COMPANY_NAME_EN,
oaf.REPORT_SEND_CC, op.PROD_ID, op.STORE_ID,obi.CREATE_DATE,obi.bu,obi.ORDER_AMOUNT,obi.IS_TEST,obi.GROUP_NO,obi.REAL_AMOUNT, op.STORE_NAME,obi.STATE ,
ot.REPORT_SEND_CC CS_SEND_CC,obi.RECOMMEND_REASON
FROM ORDER_BASE_INFO obi
left JOIN ORDER_APPLICATION_FORM oaf on obi.ORDER_NO=oaf.ORDER_NO
left JOIN ORDER_PRODUCT op on obi.ORDER_NO=op.ORDER_NO and op.state = 1
left join ORDER_REPORT ot on obi.ORDER_NO=ot.ORDER_NO and ot.STATE=1
where obi.ORDER_ID= #{orderId}
    </select>


    <select id="qryOrderBySubOrderNo" resultMap="ticIndexMailDTO">
        SELECT distinct obi.ORDER_ID,obi.OPERATOR_CODE,obi.AUDIT_CODE,obi.ORDER_NO,obi.ORDER_TYPE,obi.HIS_STATE,
               obi.LAB_ID,
               IF(oaf.FORM_ID is null ,obi.USER_NAME,oaf.LINK_PERSON) LINK_PERSON,
               IF(oaf.FORM_ID is null ,obi.USER_PHONE,oaf.LINK_PHONE) LINK_PHONE,
               IF(oaf.FORM_ID is null ,obi.USER_EMAIL,oaf.LINK_EMAIL) LINK_EMAIL,
               IF(oaf.FORM_ID is null ,obi.COMPANY_NAME,oaf.COMPANY_NAME_CN) COMPANY_NAME_CN,
               IF(oaf.FORM_ID is null ,CONCAT_WS("",obi.PROVINCE,obi.CITY,obi.TOWN,obi.COMPANY_ADDRESS_CN),CONCAT_WS("",oaf.PROVINCE,oaf.CITY,oaf.TOWN,oaf.COMPANY_ADDRESS_CN)) COMPANY_ADDRESS_CN,
               IF(oaf.FORM_ID is null ,obi.COMPANY_ADDRESS_EN,oaf.COMPANY_ADDRESS_EN) COMPANY_ADDRESS_EN,
               IF(oaf.FORM_ID is null ,obi.company_NAME_EN,oaf.company_NAME_EN) COMPANY_NAME_EN,
               oaf.REPORT_SEND_CC, op.PROD_ID, op.STORE_ID,obi.CREATE_DATE,obi.bu,obi.ORDER_AMOUNT,obi.IS_TEST,obi.GROUP_NO,obi.REAL_AMOUNT, op.STORE_NAME,obi.STATE ,
               ot.REPORT_SEND_CC CS_SEND_CC,obi.RECOMMEND_REASON
        FROM ORDER_BASE_INFO obi
                 left JOIN ORDER_APPLICATION_FORM oaf on obi.ORDER_NO=oaf.ORDER_NO
                 left JOIN ORDER_PRODUCT op on obi.ORDER_NO=op.ORDER_NO and op.state = 1
                 left join ORDER_REPORT ot on obi.ORDER_NO=ot.ORDER_NO and ot.STATE=1
        where
            obi.ORDER_NO = (SELECT RELATE_ORDER_NO from order_base_info where  ORDER_NO = #{orderNo})
    </select>













  <select id="selectRelateOrderNoByOrderNo"   resultType="java.lang.String">
    select ORDER_NO from ORDER_BASE_INFO where RELATE_ORDER_NO=#{orderNo}
      <if test="notState != null">
          AND STATE!=#{notState}
      </if>
     order by ORDER_ID desc  limit 1
  </select>









    <update id="updatePrice" parameterType="com.sgs.ecom.member.vo.VOOrderBaseInfo">
        UPDATE ORDER_BASE_INFO
        <set >
            <if test="payState != null" >
                PAY_STATE = #{payState},
            </if>
            <if test="monthPay != null" >
                MONTH_PAY = #{monthPay},
            </if>
            <if test="payDate != null" >
                PAY_DATE = #{payDate},
            </if>
            <if test="state != null" >
                STATE = #{state},
            </if>
            <if test="isPayReceived != null" >
                IS_PAY_RECEIVED = #{isPayReceived},
            </if>
            <if test="payMethod != null" >
                PAY_METHOD = #{payMethod},
            </if>
            <if test="isElectron != null" >
                IS_ELECTRON = #{isElectron},
            </if>
        </set>
        where ORDER_ID = #{orderId,jdbcType=BIGINT} and REAL_AMOUNT=#{realAmount} and PAY_STATE=0
    </update>

    <resultMap id="headDTO" type="com.sgs.ecom.member.dto.order.OrderHeadDTO" >
        <result column="ORDER_ID" property="orderId" jdbcType="BIGINT" />
        <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
        <result column="USER_SEX" property="userSex" jdbcType="VARCHAR" />
        <result column="USER_NAME" property="userName" jdbcType="VARCHAR" />
        <result column="CATEGORY_PATH" property="categoryPath" jdbcType="VARCHAR" />
        <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
        <result column="ITEM_NAME" property="itemNameList" jdbcType="VARCHAR" />
        <result column="CITY" property="city" jdbcType="VARCHAR" />
        <result column="CREATE_DATE" property="createDate" jdbcType="VARCHAR" />
        <result column="END_DATE" property="endDate" jdbcType="VARCHAR" />
    </resultMap>
    <select id="qryHeadList"   resultMap="headDTO">
        select obi.ORDER_NO,obi.ORDER_ID,obi.USER_SEX,obi.USER_NAME,obi.CATEGORY_PATH,obi.STATE_DATE,obi.CITY,
        GROUP_CONCAT(od.ITEM_NAME separator ",") as ITEM_NAME, obi.CREATE_DATE,
        (select OPERATOR_DATE from ORDER_OPERATOR_LOG ool where ool.order_no=obi.order_no and operator_type=3 limit 1) as END_DATE
        from ORDER_BASE_INFO obi left join ORDER_DETAIL od
        on obi.ORDER_NO=od.ORDER_NO and obi.GROUP_NO=od.GROUP_NO
        where obi.STATE=4 and obi.IS_TEST=0 and obi.ORDER_TYPE=200000
        <if test="filterTime != null">
            and
            ( DATE_ADD(obi.CREATE_DATE, INTERVAL #{filterTime} HOUR) &gt;
            (select operator_date from ORDER_OPERATOR_LOG ool where ool.order_no=obi.order_no and operator_type=3 limit 1)
            )
        </if>
        <if test="bu != null">
            AND obi.bu=#{bu}
        </if>
        <if test="orderNo != null">
            AND obi.ORDER_NO=#{orderNo}
        </if>
        group by obi.ORDER_NO order by obi.STATE_DATE desc
        <if test="limitStart != null and limitEnd != null">
            limit #{limitStart},#{limitEnd}
        </if>
    </select>


    <resultMap id="checkDTO" type="com.sgs.ecom.member.dto.custom.OrderCheckStateDTO" >
        <id column="ORDER_ID" property="orderId" jdbcType="BIGINT" />
        <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
        <result column="STATE" property="state" jdbcType="INTEGER" />
        <result column="PAY_STATE" property="payState" jdbcType="INTEGER" />
        <result column="SUB_STATE" property="subState" jdbcType="INTEGER" />
        <result column="ORDER_TYPE" property="orderType" jdbcType="INTEGER" />
        <result column="HIS_STATE" property="hisState" jdbcType="INTEGER" />
        <result column="IS_DELETE" property="isDelete" jdbcType="INTEGER" />
        <result column="PAY_METHOD" property="payMethod" jdbcType="INTEGER" />
        <result column="IS_PAY_RECEIVED" property="isPayReceived" jdbcType="INTEGER" />
        <result column="MONTH_PAY" property="monthPay" jdbcType="INTEGER"/>
    </resultMap>
    <select id="selectOrderCheckStateDTO"  resultMap="checkDTO"  >
    select ORDER_NO,ORDER_ID,STATE,PAY_STATE,SUB_STATE,ORDER_TYPE,HIS_STATE,IS_DELETE,PAY_METHOD,
    IS_PAY_RECEIVED,MONTH_PAY
    from ORDER_BASE_INFO
    where ORDER_ID = #{orderId}
  </select>


    <update id="updateStateByLock" >
        UPDATE ORDER_BASE_INFO set  STATE = #{newState}
        <if test="closeCode != null">
            ,CLOSE_CODE= #{closeCode}
        </if>
        <if test="closeReason != null">
            ,CLOSE_REASON= #{closeReason}
        </if>
        <if test="hisState != null">
            ,HIS_STATE= #{hisState}
        </if>
        <if test="stateDate != null">
            ,STATE_DATE= #{stateDate}
        </if>
        <if test="isRead != null">
            ,IS_READ= #{isRead}
        </if>

        <if test="deleteFlg != null">
            ,IS_DELETE= #{newDelete}
        </if>
        <if test="payFlg != null">
            ,PAY_STATE= #{newPayState}
        </if>
        <if test="isPayReceivedFlg != null">
            ,IS_PAY_RECEIVED= #{newReceived}
        </if>
        <if test="monthPayFlg != null">
            ,MONTH_PAY= #{newMonthPay}
        </if>
        where ORDER_ID = #{orderId} and  STATE= #{oldState}
        <if test="deleteFlg != null">
            AND IS_DELETE=#{oldDelete}
        </if>
        <if test="payFlg != null">
            AND PAY_STATE= #{oldPayState}
        </if>
        <if test="isPayReceivedFlg != null">
            AND IS_PAY_RECEIVED= #{oldReceived}
        </if>
        <if test="monthPayFlg != null">
            AND MONTH_PAY= #{oldMonthPay}
        </if>
    </update>



    <resultMap id="person" type="com.sgs.ecom.member.dto.custom.SysPersonDTO" >
        <result column="CS_EMAIL" property="csEmail" jdbcType="VARCHAR" />
        <result column="CS_CODE" property="csCode" jdbcType="VARCHAR" />
    </resultMap>
    <select id="qryPersonByLineId"   resultMap="person">
        select CS_CODE,CS_EMAIL from ORDER_BASE_INFO   where 1=1
        <if test="userId != null">
            AND USER_ID=#{userId}
        </if>
        <if test="orderType != null">
            AND ORDER_TYPE=#{orderType}
        </if>
        <if test="lineId != null">
            AND LINE_ID=#{lineId}
        </if>
        order by ORDER_ID desc limit 1
  </select>


    <resultMap id="baseOrder" type="com.sgs.ecom.member.dto.base.BaseOrderDTO" >
        <result column="ORDER_ID" property="orderId" jdbcType="BIGINT" />
        <result column="QUESTION_ID" property="questionId" jdbcType="BIGINT" />
        <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
        <result column="STATE" property="state" jdbcType="INTEGER" />
        <result column="ORDER_TYPE" property="orderType" jdbcType="VARCHAR" />
        <result column="USER_ID" property="userId" jdbcType="BIGINT" />
        <result column="USER_NAME" property="userName" jdbcType="VARCHAR" />
        <result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR" />
        <result column="USER_PHONE" property="userPhone" jdbcType="VARCHAR" />
        <result column="USER_EMAIL" property="userEmail" jdbcType="VARCHAR" />
        <result column="IS_TEST" property="isTest" jdbcType="INTEGER" />
        <result column="RELATE_ORDER_NO" property="relateOrderNo" jdbcType="VARCHAR" />
        <result column="GROUP_NO" property="groupNo" jdbcType="VARCHAR" />
        <result column="TMP_GROUP_NO" property="tmpGroupNo" jdbcType="VARCHAR" />
        <result column="CATAGORY_ID" property="categoryId" jdbcType="BIGINT" />
        <result column="CATEGORY_PATH" property="categoryPath" jdbcType="VARCHAR" />
        <result column="BU" property="bu" jdbcType="VARCHAR" />
        <result column="CREATE_DATE" property="createDate" jdbcType="VARCHAR" />
        <result column="IS_PAY_RECEIVED" property="isPayReceived" jdbcType="INTEGER" />
        <result column="REAL_AMOUNT" property="realAmount" jdbcType="DECIMAL" />
        <result column="CS_CODE" property="csCode" jdbcType="VARCHAR" />
        <result column="LAB_NAME" property="labName" jdbcType="VARCHAR" />
        <result column="FROM_SOURCE" property="fromSource" jdbcType="VARCHAR" />
        <result column="PLATFORM_ORDER" property="platformOrder" jdbcType="VARCHAR" />
        <result column="IS_URGENT" property="isUrgent" jdbcType="INTEGER" />
        <result column="LAB_ID" property="labId" jdbcType="INTEGER" />
        <result column="CS_EMAIL" property="csEmail" jdbcType="VARCHAR" />
        <result column="BUSINESS_PERSON_EMAIL" property="businessPersonEmail" jdbcType="VARCHAR" />
        <result column="CUST_ID" property="custId" jdbcType="BIGINT" />
        <result column="MONTH_PAY" property="monthPay" jdbcType="INTEGER" />
        <result column="OPERATOR_CODE" property="operatorCode" jdbcType="VARCHAR" />
        <result column="IS_ELECTRON" property="isElectron" jdbcType="INTEGER"/>
        <result column="HIS_STATE" property="hisState" jdbcType="INTEGER" />
        <result column="SUB_STATE" property="subState" jdbcType="INTEGER" />
        <result column="REPORT_FORM" property="reportForm" jdbcType="VARCHAR" />
        <result column="REPORT_FORM_CODE" property="reportFormCode" jdbcType="VARCHAR" />
        <result column="REPORT_LUA" property="reportLua" jdbcType="VARCHAR" />
        <result column="REPORT_LUA_CODE" property="reportLuaCode" jdbcType="VARCHAR" />
        <result column="PAY_STATE" property="payState" jdbcType="INTEGER" />
        <result column="PAY_METHOD" property="payMethod" jdbcType="VARCHAR" />
        <result column="PLATFORM_AMOUNT" property="platformAmount" jdbcType="DECIMAL" />
        <result column="COMPANY_NAME_EN" property="companyNameEn" jdbcType="VARCHAR" />
        <result column="COMPANY_ADDRESS_CN" property="companyAddressCn" jdbcType="VARCHAR" />
        <result column="COMPANY_ADDRESS_EN" property="companyAddressEn" jdbcType="VARCHAR" />
        <result column="RECOMMEND_REASON" property="recommendReason" jdbcType="VARCHAR" />
        <result column="CURRENCY" property="currency" jdbcType="VARCHAR" />
        <result column="LINE_ID" property="lineId" jdbcType="VARCHAR" />
        <result column="SAMPLE_REQUIREMENTS" property="sampleRequirements" jdbcType="VARCHAR" />
        <result column="BOSS_NO" property="bossNo" jdbcType="VARCHAR" />
        <result column="SALES_CODE" property="salesCode" jdbcType="VARCHAR" />
        <result column="SALES_PHONE" property="salesPhone" jdbcType="VARCHAR" />
        <result column="ORDER_AMOUNT" property="orderAmount" jdbcType="DECIMAL" />
        <result column="DISCOUNT_AMOUNT" property="discountAmount" jdbcType="DECIMAL" />
        <result column="REFUND_STATE" property="refundState" jdbcType="INTEGER" />
        <result column="DEADLINE_TIME" property="deadlineTime" jdbcType="VARCHAR"/>
        <result column="APPLY_SUBMIT_DATE" property="applySubmitDate" jdbcType="VARCHAR"/>
        <result column="SERVICE_AMOUNT" property="serviceAmount" jdbcType="DECIMAL" />
        <result column="URGENT_AMOUNT" property="urgentAmount" jdbcType="DECIMAL" />
        <result column="SALES_EMAIL" property="salesEmail" jdbcType="VARCHAR"/>
        <result column="CS_PHONE" property="csPhone" jdbcType="VARCHAR"/>
        <result column="CS_NAME" property="csName" jdbcType="VARCHAR"/>
        <result column="CS_NAME_EN" property="csNameEn" jdbcType="VARCHAR"/>
        <result column="OUT_ORDER_NO" property="outOrderNo" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="baseSql" >
        ORDER_ID,QUESTION_ID,ORDER_NO,STATE,ORDER_TYPE,IS_PAY_RECEIVED,REAL_AMOUNT,USER_NAME,COMPANY_NAME,USER_PHONE,
        USER_EMAIL,IS_TEST, USER_ID,RELATE_ORDER_NO,GROUP_NO,TMP_GROUP_NO,BU,CATAGORY_ID,CATEGORY_PATH,
        CREATE_DATE,CS_CODE,LAB_NAME,FROM_SOURCE,PLATFORM_ORDER,IS_URGENT,LAB_ID,CS_EMAIL,BUSINESS_PERSON_EMAIL,CUST_ID,MONTH_PAY,IS_ELECTRON,
        OPERATOR_CODE,HIS_STATE,SUB_STATE,REPORT_FORM,REPORT_FORM_CODE,REPORT_LUA,REPORT_LUA_CODE,PAY_STATE,PAY_METHOD,
        PLATFORM_AMOUNT,COMPANY_NAME_EN,COMPANY_ADDRESS_CN,COMPANY_ADDRESS_EN,RECOMMEND_REASON,CURRENCY,SAMPLE_REQUIREMENTS,LINE_ID,
        BOSS_NO,SALES_CODE,SALES_PHONE,REFUND_STATE,DEADLINE_TIME,APPLY_SUBMIT_DATE,ORDER_AMOUNT,DISCOUNT_AMOUNT,
        SERVICE_AMOUNT,URGENT_AMOUNT,SALES_EMAIL,CS_PHONE,CS_NAME,CS_NAME_EN,OUT_ORDER_NO
    </sql>
    <select id="selectBaseByOrderNo"  resultMap="baseOrder"  >
        select
        <include refid="baseSql" />
        from ORDER_BASE_INFO
        where 1=1
        <if test="orderNo != null">
            AND ORDER_NO=#{orderNo}
        </if>
        <if test="orderType != null">
            AND ORDER_TYPE=#{orderType}
        </if>
        <if test="userId != null">
            AND USER_ID=#{userId}
        </if>
        <if test="notState != null">
            AND STATE!=#{notState} and state!=91
        </if>
        <if test="relateOrderNo != null">
            AND RELATE_ORDER_NO=#{relateOrderNo}
        </if>

        <if test="orderBy != null">
            order by ORDER_ID asc limit 1
        </if>
        <if test="orderBy == null">
            order by ORDER_ID desc limit 1
        </if>

    </select>
    <select id="qryOrderBasePayDTOList"   resultMap="orderBasePay"  >
        select
        <include refid="orderBasePaySql"/>
        from ORDER_BASE_INFO
        where ORDER_NO in
        <foreach collection="orderNoList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by ORDER_ID
    </select>

    <sql id="orderBasePaySql">
    ORDER_ID,ORDER_NO,LAB_ID,RELATE_ORDER_NO,PAY_METHOD,MONTH_PAY,BU,
    USER_ID,USER_NAME,ORDER_TYPE,PAY_STATE,REAL_AMOUNT,REPORT_LUA,REPORT_FORM,CATEGORY_PATH,SUB_STATE,REFUND_STATE,
    STATE,GROUP_NO,IS_PAY_RECEIVED,OPERATOR_CODE, CREATE_DATE,CURRENCY,ORDER_AMOUNT,RECOMMEND_REASON,SERVICE_AMOUNT,CS_DISCOUNT_AMOUNT,SHOP_DIS_AMOUNT,DISCOUNT_AMOUNT
    </sql>

</mapper>