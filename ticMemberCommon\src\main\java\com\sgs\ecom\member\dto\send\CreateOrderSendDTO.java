package com.sgs.ecom.member.dto.send;

import com.platform.util.ValidationUtil;
import com.sgs.ecom.member.dto.OrderDetailDTO;
import com.sgs.ecom.member.dto.order.OrderProductDTO;
import com.sgs.ecom.member.vo.VOOrderApplicationForm;
import com.sgs.ecom.member.vo.VOOrderBaseInfo;
import com.sgs.ecom.member.vo.VOOrderProduct;

import java.util.List;

public class CreateOrderSendDTO {
	private VOOrderBaseInfo voOrderBaseInfo;
	private VOOrderApplicationForm voOrderApplicationForm;
	private List<VOOrderProduct> voOrderProductList;
	private Long prodId;
	private List<OrderProductDTO> listProduct;
	private List<OrderDetailDTO> listDetail;
	private String sendMailGroup;

	public CreateOrderSendDTO() {
	}

	public CreateOrderSendDTO(VOOrderBaseInfo voOrderBaseInfo, VOOrderApplicationForm voOrderApplicationForm, List<VOOrderProduct> voOrderProductList) {
		this.voOrderBaseInfo = voOrderBaseInfo;
		this.voOrderApplicationForm = voOrderApplicationForm;
		this.voOrderProductList = voOrderProductList;
		if(!ValidationUtil.isEmpty(voOrderProductList)){
			this.prodId=voOrderProductList.get(0).getProdId().longValue();
		}
	}

	public CreateOrderSendDTO(VOOrderBaseInfo voOrderBaseInfo, VOOrderApplicationForm voOrderApplicationForm, List<VOOrderProduct> voOrderProductList, List<OrderProductDTO> listProduct, List<OrderDetailDTO> listDetail, String sendMailGroup) {
		this.voOrderBaseInfo = voOrderBaseInfo;
		this.voOrderApplicationForm = voOrderApplicationForm;
		this.voOrderProductList = voOrderProductList;
		if(!ValidationUtil.isEmpty(voOrderProductList)){
			this.prodId=voOrderProductList.get(0).getProdId().longValue();
		}
		this.listProduct = listProduct;
		this.listDetail = listDetail;
		this.sendMailGroup = sendMailGroup;
	}

	public List<OrderProductDTO> getListProduct() {
		return listProduct;
	}

	public void setListProduct(List<OrderProductDTO> listProduct) {
		this.listProduct = listProduct;
	}

	public List<OrderDetailDTO> getListDetail() {
		return listDetail;
	}

	public void setListDetail(List<OrderDetailDTO> listDetail) {
		this.listDetail = listDetail;
	}

	public VOOrderBaseInfo getVoOrderBaseInfo() {
		return voOrderBaseInfo;
	}

	public void setVoOrderBaseInfo(VOOrderBaseInfo voOrderBaseInfo) {
		this.voOrderBaseInfo = voOrderBaseInfo;
	}

	public VOOrderApplicationForm getVoOrderApplicationForm() {
		return voOrderApplicationForm;
	}

	public void setVoOrderApplicationForm(VOOrderApplicationForm voOrderApplicationForm) {
		this.voOrderApplicationForm = voOrderApplicationForm;
	}

	public List<VOOrderProduct> getVoOrderProductList() {
		return voOrderProductList;
	}

	public void setVoOrderProductList(List<VOOrderProduct> voOrderProductList) {
		this.voOrderProductList = voOrderProductList;
	}

	public Long getProdId() {
		return prodId;
	}

	public void setProdId(Long prodId) {
		this.prodId = prodId;
	}

	public String getSendMailGroup() {
		return sendMailGroup;
	}

	public void setSendMailGroup(String sendMailGroup) {
		this.sendMailGroup = sendMailGroup;
	}
}
