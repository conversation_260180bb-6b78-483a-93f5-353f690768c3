package com.sgs.ecom.member.domain.order;

import com.sgs.ecom.member.dto.user.UserDTO;
import com.sgs.ecom.member.entity.order.OrderBuryingPointRecord;
import com.sgs.ecom.member.enumtool.order.BuryingActionEnum;
import com.sgs.ecom.member.util.order.UseDateUtil;

import java.util.Date;


public class OrderBuryingPointRecordDo extends OrderBuryingPointRecord {

    public OrderBuryingPointRecord getEntityByAction(String orderNo, BuryingActionEnum buryingActionEnum, UserDTO userDTO,String frontUrl){
        OrderBuryingPointRecord orderBuryingPointRecord=new OrderBuryingPointRecord();
        orderBuryingPointRecord.setOrderNo(orderNo);
        orderBuryingPointRecord.setCreateDate(UseDateUtil.getDateString(new Date()));
        orderBuryingPointRecord.setBuryingType(buryingActionEnum.getBuryingType());
        orderBuryingPointRecord.setBuryingAction(buryingActionEnum.getBuryingAction());
        orderBuryingPointRecord.setAccountType(1L);
        orderBuryingPointRecord.setAccountId(userDTO.getUserId());
        orderBuryingPointRecord.setBuryingUrl(frontUrl);
        return orderBuryingPointRecord;

    }
}
