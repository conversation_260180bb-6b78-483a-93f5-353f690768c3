package com.sgs.ecom.member.dto.rpc;

import java.util.List;

public class QuestionRelateDTO {

    public static final String QUESTION_ID="questionId";
    public static final String PART_NO="partNo";
    public static final String RELATE_TYPE="relateType";

    private String subjectNo;

    private String partNo;

    private String optionNo;

    private String unionType;

    private List<RelateItem> relateItem;

    public String getSubjectNo() {
        return subjectNo;
    }

    public void setSubjectNo(String subjectNo) {
        this.subjectNo = subjectNo;
    }

    public String getPartNo() {
        return partNo;
    }

    public void setPartNo(String partNo) {
        this.partNo = partNo;
    }

    public String getOptionNo() {
        return optionNo;
    }

    public void setOptionNo(String optionNo) {
        this.optionNo = optionNo;
    }



    public List<RelateItem> getRelateItem() {
        return relateItem;
    }

    public void setRelateItem(List<RelateItem> relateItem) {
        this.relateItem = relateItem;
    }


    public String getUnionType() {
        return unionType;
    }

    public void setUnionType(String unionType) {
        this.unionType = unionType;
    }
}
