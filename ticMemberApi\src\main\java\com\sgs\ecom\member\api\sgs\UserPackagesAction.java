package com.sgs.ecom.member.api.sgs;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.sgs.ecom.member.util.ResultBody;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.platform.annotation.AuthRequired;
import com.platform.util.CollectionService;
import com.platform.util.ReturnTools;
import com.platform.util.tools.LogUtil;
import com.platform.web.BaseAction;
import com.sgs.ecom.member.user.service.interfaces.IUserPackagesSV;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ResultCode;

@RestController
@RequestMapping("/openapi/api.v3.sgs/packapi")
public class UserPackagesAction extends BaseAction {

	@Autowired
	private ReturnTools returnTools = new ReturnTools();
	@Resource
	private IUserPackagesSV userPackagesSV;

	
    /**   
	* @Function: qryPackagesInfo
	* @Description: 查询包裹信息
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2018-10-26
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2018-10-26  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "NULL", sign = true)
    @RequestMapping(value = "qryPackage", method = { RequestMethod.POST })
    public void qryUserPackages(@RequestBody String data,HttpServletResponse res) throws Exception {
    	/*String result = null;
		
		try{
			result = userPackagesSV.qryUserPackages(data);
		} catch (BusinessException e) {
			LogUtil.writeErrorLog(e.getDesc(), e);
			result = returnTools.getResult(e.getResultCode(), e.getDesc(), null);
		} catch (Exception e){
			LogUtil.writeErrorLog("系统异常", e);
			result = returnTools.getResult(ResultCode.SYSTEM_IS_ERROR, ResultCode.getDescription(ResultCode.SYSTEM_IS_ERROR));
		} finally {
			res.getWriter().write(result);
		}*/
		res.setContentType("text/html;charset=UTF-8");
		JSONObject jsonObject=userPackagesSV.qryUserPackages(data);
		String str=returnTools.getResult(ResultCode.SUCCESS, ResultCode.getDescription(ResultCode.SUCCESS), jsonObject);
		res.getWriter().write(str);
	}

}
