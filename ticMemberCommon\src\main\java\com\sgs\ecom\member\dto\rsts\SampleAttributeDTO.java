package com.sgs.ecom.member.dto.rsts;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;

public class SampleAttributeDTO {
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String attrValue;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String attrName;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private int isDefault;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String attrExtend;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String attrCode;

	private Long userId;
	private String sampleNo;

	private String orderNo;

	public SampleAttributeDTO() {
	}

	public SampleAttributeDTO(Long userId, String sampleNo, String attrValue) {
		this.userId = userId;
		this.sampleNo = sampleNo;
		this.attrValue = attrValue;
	}

	public String getAttrValue() {
		return attrValue;
	}

	public void setAttrValue(String attrValue) {
		this.attrValue = attrValue;
	}

	public String getAttrName() {
		return attrName;
	}

	public void setAttrName(String attrName) {
		this.attrName = attrName;
	}

	public int getIsDefault() {
		return isDefault;
	}

	public void setIsDefault(int isDefault) {
		this.isDefault = isDefault;
	}

	public String getAttrExtend() {
		return attrExtend;
	}

	public void setAttrExtend(String attrExtend) {
		this.attrExtend = attrExtend;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getSampleNo() {
		return sampleNo;
	}

	public void setSampleNo(String sampleNo) {
		this.sampleNo = sampleNo;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getAttrCode() {
		return attrCode;
	}

	public void setAttrCode(String attrCode) {
		this.attrCode = attrCode;
	}
}
