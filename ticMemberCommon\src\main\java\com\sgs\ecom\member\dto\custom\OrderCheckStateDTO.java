package com.sgs.ecom.member.dto.custom;

public class OrderCheckStateDTO {
    private Long orderId;
    private String orderNo;
    private int orderType;
    private int state;
    private int subState;
    private int payState;
    private int hisState;
    private int isDelete;
    private String payMethod;
    private int isPayReceived;
    private int monthPay;

    public int getMonthPay() {
        return monthPay;
    }

    public void setMonthPay(int monthPay) {
        this.monthPay = monthPay;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public int getOrderType() {
        return orderType;
    }

    public void setOrderType(int orderType) {
        this.orderType = orderType;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public int getSubState() {
        return subState;
    }

    public void setSubState(int subState) {
        this.subState = subState;
    }

    public int getPayState() {
        return payState;
    }

    public void setPayState(int payState) {
        this.payState = payState;
    }

    public int getHisState() {
        return hisState;
    }

    public void setHisState(int hisState) {
        this.hisState = hisState;
    }

    public int getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(int isDelete) {
        this.isDelete = isDelete;
    }

    public String getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(String payMethod) {
        this.payMethod = payMethod;
    }

    public int getIsPayReceived() {
        return isPayReceived;
    }

    public void setIsPayReceived(int isPayReceived) {
        this.isPayReceived = isPayReceived;
    }
}
