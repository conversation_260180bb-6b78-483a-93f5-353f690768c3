package com.sgs.ecom.member.enumtool.pay;

public enum OrderPayRefundEnum {
    PAY_MAORE("payMore", 0, "金额多付"),
    PAY_REPEAT("payRepart", 1, "重复付款"),
    CANCEL_PART("cancelPart", 2, "取消部分项目"),
    CANCEL_ALL("cancelAll", 3, "整单取消"),
    WRONG_ORDER("wrongOrder", 4, "拍错单，将另下一单"),
    WRONG_INVOICE_TITLE("WrongInvoiceTitle", 5, "付款方和申请发票的抬头不一致，将另下一单"),
    OTHER_COMMON("otherCommon", 6, "其他"),
    SERVICE_REFUND("serviceRefund", 91, "客服退款标识"),

    ;
    OrderPayRefundEnum(String name, int index, String nameCh) {
        this.name = name;
        this.index = index;
        this.nameCh = nameCh;
    }

    public static String getName(int index) {
        for (OrderPayRefundEnum c : OrderPayRefundEnum.values()) {
            if (c.getIndex()==index) {
                return c.getName();
            }
        }
        return null;
    }

    public static OrderPayRefundEnum getEnum(int index) {
        for (OrderPayRefundEnum c : OrderPayRefundEnum.values()) {
            if (c.getIndex()==index) {
                return c;
            }
        }
        return null;
    }

    public static String getNameCh(int index) {
        for (OrderPayRefundEnum c : OrderPayRefundEnum.values()) {
            if (c.getIndex()==index) {
                return c.getNameCh();
            }
        }
        return null;
    }





    private String name;
    private int index;
    private String nameCh;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }
}
