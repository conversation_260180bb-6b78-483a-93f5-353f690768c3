package com.sgs.ecom.member.dto.pdf;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.dto.OrderAttributeDTO;
import com.sgs.ecom.member.dto.OrderBaseOtherDTO;
import com.sgs.ecom.member.dto.OrderDetailDTO;
import com.sgs.ecom.member.enumtool.pay.OiqCurrencyEnum;
import com.sgs.ecom.member.util.serializer.PriceNumFormatSerializer;
import com.sgs.ecom.member.util.time.TimeCalendarUtil;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class OrderSharePDFDTO {

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private  Long orderId;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String orderNo;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String groupNo;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String stateDate;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String orderType;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String businessLine;


    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String csCode;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String userName;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int userSex;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String categoryPath;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int state;

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String offerDate;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String orderExpDate;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String companyName;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String sampleRequirements;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String recommendReason;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String csName;

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BigDecimal testCycle;
    @JsonSerialize(using = PriceNumFormatSerializer.class)
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BigDecimal realAmount;
    @JsonSerialize(using = PriceNumFormatSerializer.class)
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BigDecimal orderAmount;
    @JsonSerialize(using = PriceNumFormatSerializer.class)
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BigDecimal serviceAmount;
    @JsonSerialize(using = PriceNumFormatSerializer.class)
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BigDecimal discountAmount;
    @JsonSerialize(using = PriceNumFormatSerializer.class)
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BigDecimal urgentAmount;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private List<OrderDetailDTO> orderDetailDTOList;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private List<OrderDetailDTO> orderDetailOptionalList;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private OrderBaseOtherDTO orderBaseOtherDTO;


    @JsonSerialize(using =PriceNumFormatSerializer.class)
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BigDecimal amount;

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private Integer isDetermine=0;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String determine;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int waitFlg=0;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int priceType;

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private List<OrderAttributeDTO> labList=new ArrayList<>();
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private List<OrderAttributeDTO> reportFormList=new ArrayList<>();
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private List<OrderAttributeDTO> reportLuaList=new ArrayList<>();
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private List<OrderAttributeDTO> testCycleList=new ArrayList<>();
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private List<OrderAttributeDTO> urgentAmountNum=new ArrayList<>();
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private List<OrderAttributeDTO> discountAmountNum=new ArrayList<>();
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private List<OrderAttributeDTO> specialNote=new ArrayList<>();
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String reportLuaValue;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String reportFormValue;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String labNameValue;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String reportMemo;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int newTemplate=1;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String currency;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String currencyMark;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BigDecimal taxRates;



    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getCsCode() {
        return csCode;
    }

    public void setCsCode(String csCode) {
        this.csCode = csCode;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }



    public String getCategoryPath() {
        if(StringUtils.isBlank(categoryPath)){
            return "";
        }
        if(!categoryPath.contains("/")){
            return categoryPath;
        }
        return categoryPath.substring(categoryPath.lastIndexOf("/"));
    }

    public void setCategoryPath(String categoryPath) {
        this.categoryPath = categoryPath;
    }

    public String getOfferDate() {
        if(StringUtils.isBlank(offerDate)){
            return "";
        }
        Date date=TimeCalendarUtil.getStringToDate(offerDate);
        String time=TimeCalendarUtil.getDateToDateString(date);
        return time;
    }

    public void setOfferDate(String offerDate) {
        this.offerDate = offerDate;
    }

    public String getOrderExpDate() {
        return orderExpDate;
    }

    public void setOrderExpDate(String orderExpDate) {
        this.orderExpDate = orderExpDate;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getSampleRequirements() {
        return sampleRequirements;
    }

    public void setSampleRequirements(String sampleRequirements) {
        this.sampleRequirements = sampleRequirements;
    }

    public String getRecommendReason() {
        return recommendReason;
    }

    public void setRecommendReason(String recommendReason) {
        this.recommendReason = recommendReason;
    }

    public String getCsName() {
        return csName;
    }

    public void setCsName(String csName) {
        this.csName = csName;
    }

    public BigDecimal getTestCycle() {
        return testCycle;
    }

    public void setTestCycle(BigDecimal testCycle) {
        this.testCycle = testCycle;
    }

    public BigDecimal getRealAmount() {
        return realAmount;
    }

    public void setRealAmount(BigDecimal realAmount) {
        this.realAmount = realAmount;
    }

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    public BigDecimal getServiceAmount() {
        return serviceAmount;
    }

    public void setServiceAmount(BigDecimal serviceAmount) {
        this.serviceAmount = serviceAmount;
    }

    public BigDecimal getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount;
    }

    public BigDecimal getUrgentAmount() {
        return urgentAmount;
    }

    public void setUrgentAmount(BigDecimal urgentAmount) {
        this.urgentAmount = urgentAmount;
    }

    public String getGroupNo() {
        return groupNo;
    }

    public void setGroupNo(String groupNo) {
        this.groupNo = groupNo;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public List<OrderDetailDTO> getOrderDetailDTOList() {
        return orderDetailDTOList;
    }

    public void setOrderDetailDTOList(List<OrderDetailDTO> orderDetailDTOList) {
        this.orderDetailDTOList = orderDetailDTOList;
    }

    public OrderBaseOtherDTO getOrderBaseOtherDTO() {
        return orderBaseOtherDTO;
    }

    public void setOrderBaseOtherDTO(OrderBaseOtherDTO orderBaseOtherDTO) {
        this.orderBaseOtherDTO = orderBaseOtherDTO;
    }



    public BigDecimal getAmount() {
       return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Integer getIsDetermine() {
        return isDetermine;
    }

    public void setIsDetermine(Integer isDetermine) {
        this.isDetermine = isDetermine;
    }

    public String getDetermine() {
        return determine;
    }

    public void setDetermine(String determine) {
        this.determine = determine;
    }

    public int getWaitFlg() {
        return waitFlg;
    }

    public void setWaitFlg(int waitFlg) {
        this.waitFlg = waitFlg;
    }

    public List<OrderDetailDTO> getOrderDetailOptionalList() {
        return orderDetailOptionalList;
    }

    public void setOrderDetailOptionalList(List<OrderDetailDTO> orderDetailOptionalList) {
        this.orderDetailOptionalList = orderDetailOptionalList;
    }

    public List<OrderAttributeDTO> getLabList() {
        return labList;
    }

    public void setLabList(List<OrderAttributeDTO> labList) {
        this.labList = labList;
    }

    public List<OrderAttributeDTO> getReportFormList() {
        return reportFormList;
    }

    public void setReportFormList(List<OrderAttributeDTO> reportFormList) {
        this.reportFormList = reportFormList;
    }

    public List<OrderAttributeDTO> getReportLuaList() {
        return reportLuaList;
    }

    public void setReportLuaList(List<OrderAttributeDTO> reportLuaList) {
        this.reportLuaList = reportLuaList;
    }

    public List<OrderAttributeDTO> getTestCycleList() {
        return testCycleList;
    }

    public void setTestCycleList(List<OrderAttributeDTO> testCycleList) {
        this.testCycleList = testCycleList;
    }

    public List<OrderAttributeDTO> getUrgentAmountNum() {
        return urgentAmountNum;
    }

    public void setUrgentAmountNum(List<OrderAttributeDTO> urgentAmountNum) {
        this.urgentAmountNum = urgentAmountNum;
    }

    public List<OrderAttributeDTO> getDiscountAmountNum() {
        return discountAmountNum;
    }

    public void setDiscountAmountNum(List<OrderAttributeDTO> discountAmountNum) {
        this.discountAmountNum = discountAmountNum;
    }

    public List<OrderAttributeDTO> getSpecialNote() {
        return specialNote;
    }

    public void setSpecialNote(List<OrderAttributeDTO> specialNote) {
        this.specialNote = specialNote;
    }

    public int getUserSex() {
        return userSex;
    }

    public void setUserSex(int userSex) {
        this.userSex = userSex;
    }

    public String getReportLuaValue() {
        return reportLuaValue;
    }

    public void setReportLuaValue(String reportLuaValue) {
        this.reportLuaValue = reportLuaValue;
    }

    public String getReportFormValue() {
        return reportFormValue;
    }

    public void setReportFormValue(String reportFormValue) {
        this.reportFormValue = reportFormValue;
    }

    public String getLabNameValue() {
        return labNameValue;
    }

    public void setLabNameValue(String labNameValue) {
        this.labNameValue = labNameValue;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public String getBusinessLine() {
        return businessLine;
    }

    public void setBusinessLine(String businessLine) {
        this.businessLine = businessLine;
    }

    public String getReportMemo() {
        return reportMemo;
    }

    public void setReportMemo(String reportMemo) {
        this.reportMemo = reportMemo;
    }

    public int getPriceType() {
        return priceType;
    }

    public void setPriceType(int priceType) {
        this.priceType = priceType;
    }

    public int getNewTemplate() {
        return newTemplate;
    }

    public void setNewTemplate(int newTemplate) {
        this.newTemplate = newTemplate;
    }

    public String getStateDate() {
        return stateDate;
    }

    public void setStateDate(String stateDate) {
        this.stateDate = stateDate;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getCurrencyMark() {
        return OiqCurrencyEnum.getKeyShow(currency);
    }

    public void setCurrencyMark(String currencyMark) {
        this.currencyMark = currencyMark;
    }

    public BigDecimal getTaxRates() {
        return taxRates;
    }

    public void setTaxRates(BigDecimal taxRates) {
        this.taxRates = taxRates;
    }
}
