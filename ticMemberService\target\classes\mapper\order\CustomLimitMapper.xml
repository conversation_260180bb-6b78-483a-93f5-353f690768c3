<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.order.dao.CustomLimitMapper" >
  <resultMap id="indexItem" type="com.sgs.ecom.member.dto.custom.DetailItemNameDTO" >
    <result column="DETAIL_ID" property="detailId" jdbcType="BIGINT" />
    <result column="ITEM_NAME" property="itemName" jdbcType="VARCHAR" />
  </resultMap>

  <select id="selectFirstDetail"  resultMap="indexItem" >
   select ITEM_NAME,DETAIL_ID from ORDER_DETAIL
   where  ORDER_NO=#{orderNo} and  GROUP_NO=#{groupNo}
   limit 1;
  </select>

    <resultMap id="resultSample" type="com.sgs.ecom.member.dto.custom.OrderSampleNameDTO" >
        <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
        <result column="SAMPLE_NAME" property="orderSampleName" jdbcType="VARCHAR" />
    </resultMap>

    <select id="selectFirstSample"  resultMap="resultSample" >
        select oa.ORDER_NO ,oa.SAMPLE_NAME from ORDER_SAMPLE_RELATE osr,ORDER_SAMPLE oa
        where  osr.GROUP_NO=oa.GROUP_NO
        and osr.SAMPLE_NO=oa.SAMPLE_NO and oa.STATE=1
        and osr.DETAIL_ID=#{detailId}
        and oa.ORDER_NO=#{orderNo}
        limit 1
    </select>

    <select id="getLastSendCc"  resultType="java.lang.String" >
    select oaf.REPORT_SEND_CC from
    ORDER_APPLICATION_FORM oaf,ORDER_BASE_INFO obi
    where obi.ORDER_NO=oaf.ORDER_NO
    AND obi.BU=#{bu}
    AND obi.USER_ID=#{userId}
    AND obi.ORDER_TYPE=100000
    AND oaf.STATE=1
    order by oaf.CREATE_DATE desc LIMIT 1
  </select>

    <select id="qryRstsSampleStr"  resultType="java.lang.String" >
        select GROUP_CONCAT(c separator ';' ) from
         ( select "sample" sample,GROUP_CONCAT(SAMPLE_VALUE separator '|' ) as c from order_sample_from
            where ORDER_NO=#{orderNo} and SAMPLE_KEY in("sampleName","sampleNameEn") and state=1 and SAMPLE_VALUE!=''
           group by SAMPLE_NO
          ) t group by sample
    </select>

  <select id="qryRstsProductInfoStr"  resultType="java.lang.String" >
    select GROUP_CONCAT(c separator ';' ) from
      ( select "productInfo" productInfo,GROUP_CONCAT(SAMPLE_VALUE separator '|' ) as c from order_sample_from
        where ORDER_NO=#{orderNo} and SAMPLE_KEY in("productInfo","productInfoEn") and state=1 and SAMPLE_VALUE!=''
        group by SAMPLE_NO
      ) t group by productInfo
  </select>

  <resultMap id="resultProductNum" type="com.sgs.ecom.member.dto.custom.ProductNumDTO" >
    <result column="PROD_ID" property="productId" jdbcType="BIGINT" />
    <result column="NUM" property="num" jdbcType="VARCHAR" />
    <result column="CREATE_DATE" property="payDate" jdbcType="VARCHAR" />
  </resultMap>

  <select id="qryProductNum"  resultMap="resultProductNum"  >
    select PROD_ID,NUM,CREATE_DATE from (
                    select op.PROD_ID,count(1) as NUM,MAX(oPay.CREATE_DATE) as CREATE_DATE
                    from ORDER_PRODUCT op,ORDER_BASE_INFO obi,
                         (select ORDER_NO,MAX(CREATE_DATE) as  CREATE_DATE from order_pay  where STATE=1 and USER_ID=#{userId} GROUP by ORDER_NO ) oPay
                    where op.ORDER_NO=obi.ORDER_NO and obi.USER_ID=#{userId} and obi.ORDER_TYPE=100000
                      and obi.ORDER_NO=oPay.ORDER_NO
                      and obi.STATE!=91 and obi.bu not in ("INS")
                      and obi.PAY_STATE in (1,2)
                     and op.state = 1
                    group by op.PROD_ID
                  ) t1
    order by PROD_ID
  </select>


    <resultMap id="leadsInfo" type="com.sgs.ecom.member.dto.rpc.LeadsInfoDTO" >
        <result column="ORDER_ID" property="orderId" jdbcType="BIGINT" />
        <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
        <result column="CREATE_DATE" property="createDate" jdbcType="VARCHAR"/>
        <result column="STATE_DATE" property="stateDate" jdbcType="VARCHAR"/>
        <result column="STATE" property="state" jdbcType="INTEGER"/>
        <result column="ORDER_TYPE" property="orderType" jdbcType="INTEGER"/>
        <result column="REAL_AMOUNT" property="amount" jdbcType="INTEGER"/>

    </resultMap>

    <select id="qryLeadsInfoList"  resultMap="leadsInfo" >
        select CREATE_DATE,STATE_DATE,STATE,ORDER_TYPE,ORDER_NO,REAL_AMOUNT,

        (CASE WHEN ORDER_TYPE=210000 THEN  obi.CATEGORY_PATH
        when ORDER_TYPE=100000 THEN  (select PRODUCT_NAME from ORDER_PRODUCT op where op.ORDER_NO=obi.ORDER_NO and op.state = 1)
        ELSE  '' end) AS INFO
        from ORDER_BASE_INFO obi
        <include refid="baseLeadsWhere"/>
        order by obi.STATE_DATE desc
    </select>


    <sql id="baseLeadsWhere">
         where obi.ORDER_TYPE in (100000,210000)
            <if test="orderId != null">
                AND obi.ORDER_ID=#{orderId}
            </if>
            <if test="userId != null">
                AND obi.USER_ID=#{userId}
            </if>
    </sql>

    <select id="qryOrderByOiqRepurchase" resultType="java.lang.Integer">
        select count(1) from

            (
                select min(CREATE_DATE) as a,max(CREATE_DATE) as b from ORDER_BASE_INFO
                where ORDER_TYPE=#{orderType} and USER_ID =#{userId} and state!=91
                GROUP BY USER_ID
                HAVING date_add(a,interval 1 day) &lt; b
            ) t


    </select>

    <select id="qryOrderByTicRepurchase" resultType="java.lang.Integer">
        select count(1) from

            (
                select  (select min(CREATE_DATE) from ORDER_PAY op where op.STATE=1 and op.order_no=obi.ORDER_NO) as a,
                        max(CREATE_DATE) as b from ORDER_BASE_INFO obi
                where ORDER_TYPE=100000 and USER_ID =#{userId} and state!=91
                GROUP BY USER_ID
                HAVING date_add(a,interval 1 day) &lt; b
            ) t

    </select>


    <select id="selectRepurchaseCount"  resultType="java.lang.Long" >
        select
        count(1)
        from ORDER_BASE_INFO
        where USER_ID=#{userId}
        AND ORDER_TYPE in (200000,200001)
        AND DATE_ADD(CREATE_DATE,INTERVAL 1 DAY) &lt; now()
    </select>

    <select id="qryLastPayDate"  resultType="java.lang.String" >
    select date_add(IFNULL(min(t.c),now()),interval 1 day)  from (
    select obi.ORDER_NO,(select min(CREATE_DATE)  from ORDER_PAY op
    where op.ORDER_NO=obi.ORDER_NO and op.state=1) as c from ORDER_BASE_INFO obi where obi.ORDER_TYPE=100000
    and obi.STATE!=91 and obi.PAY_STATE in (1,2)
    and obi.USER_ID =#{userId} ) t
    </select>


    <select id="qryPortalTotalNum" resultType="java.lang.Integer">
        select count(1) from ORDER_BASE_INFO obi where obi.USER_ID =#{userId} and SUB_STATE!=98 and obi.ORDER_TYPE=210001
    </select>

    <select id="qryRoStageTime" resultType="java.lang.String">
       select  IFNULL(MAX(ol.OPERATOR_DATE),obi.CREATE_DATE) as time
       from ORDER_BASE_INFO obi left join ORDER_OPERATOR_LOG ol on obi.ORDER_NO=ol.ORDER_NO and
       ol.ORDER_TYPE=300000 and ol.OPERATOR_TYPE in (300,310) where obi.ORDER_NO =#{orderNo}
    </select>

</mapper>