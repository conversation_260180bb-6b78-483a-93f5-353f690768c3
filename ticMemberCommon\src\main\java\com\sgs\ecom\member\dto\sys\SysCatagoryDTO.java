package com.sgs.ecom.member.dto.sys;
 
import java.sql.Timestamp;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.json.TimeFormatSerializer;
import com.sgs.base.BaseQryFilter; 

public class SysCatagoryDTO extends BaseQryFilter {

 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="QUESTION_NAME", getName="getQuestionName", setName="setQuestionName")
 	private String questionName;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="BU", getName="getBu", setName="setBu")
 	private String bu;
 	@ApiAnno(groups={Default.class,QueryList.class,QuerySummary.class})
 	@BeanAnno(value="CATAGORY_NAME", getName="getCatagoryName", setName="setCatagoryName")
 	private String catagoryName;
 	@ApiAnno(groups={Default.class,QueryList.class,QuerySummary.class})
 	@BeanAnno(value="CATAGORY_LEVEL", getName="getCatagoryLevel", setName="setCatagoryLevel")
 	private int catagoryLevel;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private Timestamp stateDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="QUESTION_ID", getName="getQuestionId", setName="setQuestionId")
 	private long questionId;
 	@ApiAnno(groups={Default.class,QueryList.class,Insert.class,QuerySummary.class})
 	@BeanAnno(value="CATAGORY_ID", getName="getCatagoryId", setName="setCatagoryId")
 	private long catagoryId;
 	@ApiAnno(groups={Default.class,QueryList.class,QuerySummary.class})
 	@BeanAnno(value="CATAGORY_PATH", getName="getCatagoryPath", setName="setCatagoryPath")
 	private String catagoryPath;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="PARENT_ID", getName="getParentId", setName="setParentId")
 	private long parentId;
 	@ApiAnno(groups={Default.class,QueryList.class,QuerySummary.class})
 	@BeanAnno(value="IS_END", getName="getIsEnd", setName="setIsEnd")
 	private int isEnd;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="SORT_SHOW", getName="getSortShow", setName="setSortShow")
 	private int sortShow;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="ID_PATH", getName="getIdPath", setName="setIdPath")
 	private String idPath;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="BUSI_CODE", getName="getBusiCode", setName="setBusiCode")
 	private String busiCode;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="IS_HIDE", getName="getIsHide", setName="setIsHide")
 	private int isHide;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="CUST_ID", getName="getCustId", setName="setCustId")
 	private int custId;

 	public void setQuestionName(String questionName){
 		 this.questionName=questionName;
 	}
 	public String getQuestionName(){
 		 return this.questionName;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setBu(String bu){
 		 this.bu=bu;
 	}
 	public String getBu(){
 		 return this.bu;
 	}
 
 	 
 	public void setCatagoryName(String catagoryName){
 		 this.catagoryName=catagoryName;
 	}
 	public String getCatagoryName(){
 		 return this.catagoryName;
 	}
 
 	 
 	public void setCatagoryLevel(int catagoryLevel){
 		 this.catagoryLevel=catagoryLevel;
 	}
 	public int getCatagoryLevel(){
 		 return this.catagoryLevel;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setQuestionId(long questionId){
 		 this.questionId=questionId;
 	}
 	public long getQuestionId(){
 		 return this.questionId;
 	}
 
 	 
 	public void setCatagoryId(long catagoryId){
 		 this.catagoryId=catagoryId;
 	}
 	public long getCatagoryId(){
 		 return this.catagoryId;
 	}
 
 	 
 	public void setCatagoryPath(String catagoryPath){
 		 this.catagoryPath=catagoryPath;
 	}
 	public String getCatagoryPath(){
 		 return this.catagoryPath;
 	}
 
 	 
 	public void setParentId(long parentId){
 		 this.parentId=parentId;
 	}
 	public long getParentId(){
 		 return this.parentId;
 	}
	public int getIsEnd() {
		return isEnd;
	}
	public void setIsEnd(int isEnd) {
		this.isEnd = isEnd;
	}
	public int getSortShow() {
		return sortShow;
	}
	public void setSortShow(int sortShow) {
		this.sortShow = sortShow;
	}
	public String getIdPath() {
		return idPath;
	}
	public void setIdPath(String idPath) {
		this.idPath = idPath;
	}
	public String getBusiCode() {
		return busiCode;
	}
	public void setBusiCode(String busiCode) {
		this.busiCode = busiCode;
	}
	public int getIsHide() {
		return isHide;
	}
	public void setIsHide(int isHide) {
		this.isHide = isHide;
	}
	public int getCustId() {
		return custId;
	}
	public void setCustId(int custId) {
		this.custId = custId;
	}
 
 	 
}