package com.sgs.ecom.member.dto.rpc;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.json.TimeFormatSerializer;
import com.sgs.base.BaseQryFilter;
import java.sql.Timestamp;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.sf.json.JSONArray;

@EqualsAndHashCode(callSuper = true)
@Data
public class QuestionPartDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select CREATE_DATE,PART_NAME,PART_NO,STATE_DATE,QUESTION_ID,STATE,SORT_SHOW,PART_SCORE,PART_ID,PART_NAME_EN from QUESTION_PART"; 
 
 
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(groups={Default.class,Insert.class,QueryList.class})
 	@BeanAnno(value="PART_NAME", getName="getPartName", setName="setPartName")
 	private String partName;
 	@ApiAnno(groups={Default.class,Insert.class,QueryList.class})
 	@BeanAnno(value="PART_NO", getName="getPartNo", setName="setPartNo")
 	private String partNo;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private Timestamp stateDate;
 	@ApiAnno(groups={Default.class,Insert.class})
 	@BeanAnno(value="QUESTION_ID", getName="getQuestionId", setName="setQuestionId")
 	private long questionId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="SORT_SHOW", getName="getSortShow", setName="setSortShow")
 	private int sortShow;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="PART_SCORE", getName="getPartScore", setName="setPartScore")
 	private int partScore;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="PART_ID", getName="getPartId", setName="setPartId")
 	private long partId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="PART_NAME_EN", getName="getPartNameEn", setName="setPartNameEn")
 	private String partNameEn;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="POSITION", getName="getPosition", setName="setPosition")
 	private String position;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="SUBJECT_ITEM", getName="getSubjectItem", setName="setSubjectItem")
 	private String subjectItem;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="REPLY_NO", getName="getReplyNo", setName="setReplyNo")
 	private String replyNo;

	/**
	 * 问卷章节所属业务
	 */
	@ApiAnno(groups={Default.class,QueryList.class})
	@BeanAnno(value="BUSINESS_TYPE", getName="getBusinessType", setName="setBusinessType")
	private String businessType;

	/**
	 * 所属业务对应CODE或ID
	 */
	@ApiAnno(groups={Default.class,QueryList.class})
	@BeanAnno(value="BUSINESS_CODE", getName="getBusinessCode", setName="setBusinessCode")
	private String businessCode;

	/**
	 * 章节下问题列表
	 */
	@ApiAnno(groups={Default.class,QueryList.class})
	private List<QuestionSubjectDTO> subjects;

	/**
	 * 章节下关系列表
	 */
	@ApiAnno(groups={Default.class,QueryList.class})
	private JSONArray relates;
 	
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setPartName(String partName){
 		 this.partName=partName;
 	}
 	public String getPartName(){
 		 return this.partName;
 	}
 
 	 
 	public void setPartNo(String partNo){
 		 this.partNo=partNo;
 	}
 	public String getPartNo(){
 		 return this.partNo;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setQuestionId(long questionId){
 		 this.questionId=questionId;
 	}
 	public long getQuestionId(){
 		 return this.questionId;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setSortShow(int sortShow){
 		 this.sortShow=sortShow;
 	}
 	public int getSortShow(){
 		 return this.sortShow;
 	}
 
 	 
 	public void setPartScore(int partScore){
 		 this.partScore=partScore;
 	}
 	public int getPartScore(){
 		 return this.partScore;
 	}
 
 	 
 	public void setPartId(long partId){
 		 this.partId=partId;
 	}
 	public long getPartId(){
 		 return this.partId;
 	}
 
 	 
 	public void setPartNameEn(String partNameEn){
 		 this.partNameEn=partNameEn;
 	}
 	public String getPartNameEn(){
 		 return this.partNameEn;
 	}
	public String getSubjectItem() {
		return subjectItem;
	}
	public void setSubjectItem(String subjectItem) {
		this.subjectItem = subjectItem;
	}
	public String getPosition() {
		return position;
	}
	public void setPosition(String position) {
		this.position = position;
	}
	public String getReplyNo() {
		return replyNo;
	}
	public void setReplyNo(String replyNo) {
		this.replyNo = replyNo;
	}

}
