package com.sgs.ecom.member.domain.user;

import com.sgs.base.BaseCopyObj;
import com.sgs.ecom.member.dto.detail.OrderExpressDTO;
import com.sgs.ecom.member.dto.user.UserAddressDTO;
import com.sgs.ecom.member.dto.user.UserInvoiceDTO;
import com.sgs.ecom.member.entity.user.UserAddress;
import com.sgs.ecom.member.entity.user.UserInvoice;
import com.sgs.ecom.member.request.oiq.OiqUserAddressReq;
import com.sgs.ecom.member.util.order.OrderUtil;
import com.sgs.ecom.member.util.order.UseDateUtil;

import java.util.Date;

public class UserAddressDO extends UserAddress {

    private BaseCopyObj baseCopy = new BaseCopyObj();

    public UserAddress getUserAddressByDTO(OiqUserAddressReq userAddressDTO, Long userId){
        UserAddress userAddress=new UserAddress();
        baseCopy.copyWithNull(userAddress,userAddressDTO);
        userAddress.setState(1);
        userAddress.setUserId(userId);
        String dateStr= UseDateUtil.getDateString(new Date());
        userAddress.setCreateDate(dateStr);
        userAddress.setStateDate(dateStr);
        userAddress.setAddressUuid(OrderUtil.UUID());
        return userAddress;
    }


    public static UserAddressDTO expressToAddress(OrderExpressDTO orderExpressDTO) {
        UserAddressDTO userAddressDTO=new UserAddressDTO();
        userAddressDTO.setAddressId(orderExpressDTO.getAddressId());
        userAddressDTO.setCity(orderExpressDTO.getReceiptCity());
        userAddressDTO.setProvince(orderExpressDTO.getReceiptProvice());
        userAddressDTO.setTown(orderExpressDTO.getReceiptTown());
        userAddressDTO.setUserName(orderExpressDTO.getReceiptPerson());
        userAddressDTO.setUserPhone(orderExpressDTO.getReceiptPhone());
        userAddressDTO.setCompanyAddress(orderExpressDTO.getReceiptAddr());
        userAddressDTO.setCompanyName(orderExpressDTO.getReceiptCompany());
        return userAddressDTO;
    }

    public UserAddressDTO entityToDTO(UserAddress userAddress){
        UserAddressDTO userAddressDTO=new UserAddressDTO();
        baseCopy.copyWithNull(userAddressDTO,userAddress);
        return userAddressDTO;
    }

    public OiqUserAddressReq entityToReq(UserAddress userAddress){
        OiqUserAddressReq userAddressDTO=new OiqUserAddressReq();
        baseCopy.copyWithNull(userAddressDTO,userAddress);
        return userAddressDTO;
    }




}
