package com.sgs.ecom.member.dto.order;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.ValidationUtil;
import com.platform.util.json.TimeFormatSerializer;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.dto.OrderDetailDTO;
import com.sgs.ecom.member.dto.bbc.BbcSkuAttrDTO;
import com.sgs.ecom.member.dto.detail.OrderAttachmentDTO;
import com.sgs.ecom.member.util.attr.AttrUtil;
import com.sgs.ecom.member.util.collection.StrUtil;
import com.sgs.ecom.member.util.serializer.PriceNumFormatSerializer;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Data
public class OrderProductDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select PRICE,QUANTITY,PRODUCT_NUMS,STATE_DATE,ORDER_NO,PRODUCT_ID,PRODUCT_NAME,CREATE_DATE,SUB_TITLE,STORE_NAME,TOTAL_PRICE,STORE_ID,PROD_ID from ORDER_PRODUCT";

	@JsonSerialize(using = PriceNumFormatSerializer.class)
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="PRICE", getName="getPrice", setName="setPrice")
 	private BigDecimal price;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="QUANTITY", getName="getQuantity", setName="setQuantity")
 	private int quantity;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private Timestamp stateDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="ORDER_NO", getName="getOrderNo", setName="setOrderNo")
 	private String orderNo;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="PRODUCT_ID", getName="getProductId", setName="setProductId")
 	private Long productId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="PRODUCT_NAME", getName="getProductName", setName="setProductName")
 	private String productName;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="SUB_TITLE", getName="getSubTitle", setName="setSubTitle")
 	private String subTitle;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STORE_NAME", getName="getStoreName", setName="setStoreName")
 	private String storeName;
 	@ApiAnno(groups={Default.class})
	@JsonSerialize(using = PriceNumFormatSerializer.class)
 	@BeanAnno(value="TOTAL_PRICE", getName="getTotalPrice", setName="setTotalPrice")
 	private BigDecimal totalPrice;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STORE_ID", getName="getStoreId", setName="setStoreId")
 	private String storeId;
 	@ApiAnno(groups={Default.class,QuerySummary.class})
 	@BeanAnno(value="PROD_ID", getName="getProdId", setName="setProdId")
 	private int prodId;
	@ApiAnno(groups={Default.class})
	private String skuAttr;
	@ApiAnno(groups={Default.class})
	private String sku;
	@ApiAnno(groups={Default.class})
	private String hotelSku;
	@ApiAnno(groups={Default.class})
	private String testItem;
	@ApiAnno(groups={Default.class})
	private String picPath;
	@ApiAnno(groups={Default.class})
	private String productMemo;
	@ApiAnno(groups={Default.class})
	private Map<String,String> skuMap=new HashMap<>();
	@ApiAnno(groups={Default.class})
	private List<BbcSkuAttrDTO> skuAttrList=new ArrayList<>();

	@ApiAnno(groups={Default.class})
	@BeanAnno(value="SUB_BU_CODE", getName="getSubBuCode", setName="setSubBuCode")
	private String subBuCode;
	@ApiAnno(groups={Default.class})
	private String customType;
	@ApiAnno(groups={QuerySummary.class})
	private int count;//商品下单数

	@ApiAnno(groups={Default.class})
	private String packageName;//套餐/自选名称
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	@ApiAnno(groups={Default.class})
	private BigDecimal realPrice;//每个SKU实际支付金额
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	@ApiAnno(groups={Default.class})
	private BigDecimal realTotalPrice;//实际总金额

	private Integer state;
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	@ApiAnno(groups={Default.class})
	private BigDecimal shopDisAmount;
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	@ApiAnno(groups={Default.class})
	private BigDecimal subDiscountAmount;
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	@ApiAnno(groups={Default.class})
	private BigDecimal subCsDiscountAmount;
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	@ApiAnno(groups={Default.class})
	private BigDecimal subServiceAmount;

	/**
	 * 最低售价
	 */
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	@ApiAnno(groups={Default.class})
	private BigDecimal lowestPrice;

	/**
	 * SKU费用
	 */
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	@ApiAnno(groups={Default.class})
	private BigDecimal skuPrice;

	/**
	 * 最低售价补收差额
	 */
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	@ApiAnno(groups={Default.class})
	private BigDecimal lowestPriceMargin;

	/**
	 * 触发最低售价
	 */
	@ApiAnno(groups={Default.class})
	private boolean triggerLowestPrice;

	/**
	 * 触发最低售价描述
	 */
	@ApiAnno(groups={Default.class})
	private String triggerLowestPriceStr;

	@ApiAnno(groups={Default.class})
	private String memo;
	@ApiAnno(groups={Default.class})
	private String itemNameStr;//打印报价单/申请表使用
	@ApiAnno(groups={Default.class})
	private String standardCodeStr;//打印报价单/申请表使用
	@ApiAnno(groups={Default.class})
	private int nums;//打印报价单/申请表使用 套餐数量

	@ApiAnno(groups={Default.class})
	private Long detailId;//对应的套餐id
	@ApiAnno(groups={Default.class})
	private List<OrderAttachmentDTO> attachments = new ArrayList<>();

	@ApiAnno(groups={Default.class})
	private List<OrderDetailDTO> detailDTOList = new ArrayList<>();//处理关于单项的信息

	@ApiAnno(groups={Default.class})
	private String sampleNameInfo;

	@ApiAnno(groups={Default.class})
	private String projectName;//项目明细

	@ApiAnno(groups={Default.class})
	private Integer rowNum;//报价单排序

	@ApiAnno(groups={Default.class})
	private String packageFileNames;//套餐的文件名

	@ApiAnno(groups={Default.class})
	private String pointsNum;//点位数

	/**
	 * 出货数量
	 */
	private String testNum;

	/**
	 * 抽样水平
	 */
	private String samplingName;

	/**
	 * 检验省份
	 */
	private String province;

	/**
	 * 检验城市
	 */
	private String city;


	public String getPackageFileNames() {
		return packageFileNames;
	}

	public void setPackageFileNames(String packageFileNames) {
		this.packageFileNames = packageFileNames;
	}

	public Integer getRowNum() {
		return rowNum;
	}

	public void setRowNum(Integer rowNum) {
		this.rowNum = rowNum;
	}

	public String getProjectName() {
		return projectName;
	}

	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}

	public String getSampleNameInfo() {
		return sampleNameInfo;
	}

	public void setSampleNameInfo(String sampleNameInfo) {
		this.sampleNameInfo = sampleNameInfo;
	}

	public List<OrderDetailDTO> getDetailDTOList() {
		return detailDTOList;
	}

	public void setDetailDTOList(List<OrderDetailDTO> detailDTOList) {
		this.detailDTOList = detailDTOList;
	}

	public List<OrderAttachmentDTO> getAttachments() {
		return attachments;
	}

	public void setAttachments(List<OrderAttachmentDTO> attachments) {
		this.attachments = attachments;
	}

	public Long getDetailId() {
		return detailId;
	}

	public void setDetailId(Long detailId) {
		this.detailId = detailId;
	}

	public int getNums() {
		return nums;
	}

	public void setNums(int nums) {
		this.nums = nums;
	}

	public String getStandardCodeStr() {
		return standardCodeStr;
	}

	public void setStandardCodeStr(String standardCodeStr) {
		this.standardCodeStr = standardCodeStr;
	}

	public String getItemNameStr() {
		return itemNameStr;
	}

	public void setItemNameStr(String itemNameStr) {
		this.itemNameStr = itemNameStr;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public BigDecimal getSubDiscountAmount() {
		return subDiscountAmount;
	}

	public void setSubDiscountAmount(BigDecimal subDiscountAmount) {
		this.subDiscountAmount = subDiscountAmount;
	}

	public BigDecimal getSubCsDiscountAmount() {
		return subCsDiscountAmount;
	}

	public void setSubCsDiscountAmount(BigDecimal subCsDiscountAmount) {
		this.subCsDiscountAmount = subCsDiscountAmount;
	}

	public BigDecimal getSubServiceAmount() {
		return subServiceAmount;
	}

	public void setSubServiceAmount(BigDecimal subServiceAmount) {
		this.subServiceAmount = subServiceAmount;
	}

	public BigDecimal getShopDisAmount() {
		return shopDisAmount;
	}

	public void setShopDisAmount(BigDecimal shopDisAmount) {
		this.shopDisAmount = shopDisAmount;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public BigDecimal getRealTotalPrice() {
		return realTotalPrice;
	}

	public void setRealTotalPrice(BigDecimal realTotalPrice) {
		this.realTotalPrice = realTotalPrice;
	}

	public BigDecimal getRealPrice() {
		return realPrice;
	}

	public void setRealPrice(BigDecimal realPrice) {
		this.realPrice = realPrice;
	}

	public String getPackageName() {
		return packageName;
	}

	public void setPackageName(String packageName) {
		this.packageName = packageName;
	}

	public int getCount() {
		return count;
	}

	public void setCount(int count) {
		this.count = count;
	}

	public String getCustomType() {
		return customType;
	}

	public void setCustomType(String customType) {
		this.customType = customType;
	}

	public String getSubBuCode() {
		return subBuCode;
	}

	public void setSubBuCode(String subBuCode) {
		this.subBuCode = subBuCode;
	}

	public void setQuantity(int quantity){
 		 this.quantity=quantity;
 	}
 	public int getQuantity(){
 		 return this.quantity;
 	}
 

 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setOrderNo(String orderNo){
 		 this.orderNo=orderNo;
 	}
 	public String getOrderNo(){
 		 return this.orderNo;
 	}
 
 	 
 	public void setProductId(long productId){
 		 this.productId=productId;
 	}
 	public long getProductId(){
 		 return this.productId;
 	}
 
 	 
 	public void setProductName(String productName){
 		 this.productName=productName;
 	}
 	public String getProductName(){
 		 return this.productName;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setSubTitle(String subTitle){
 		 this.subTitle=subTitle;
 	}
 	public String getSubTitle(){
 		 return this.subTitle;
 	}
 
 	 
 	public void setStoreName(String storeName){
 		 this.storeName=storeName;
 	}
 	public String getStoreName(){
 		 return this.storeName;
 	}


	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public BigDecimal getTotalPrice() {
		return totalPrice;
	}

	public void setTotalPrice(BigDecimal totalPrice) {
		this.totalPrice = totalPrice;
	}


 
 	 
 	public void setProdId(int prodId){
 		 this.prodId=prodId;
 	}
 	public int getProdId(){
 		 return this.prodId;
 	}

	public String getSkuAttr() {
		return skuAttr;
	}

	public void setSkuAttr(String skuAttr) {
		this.skuAttr = skuAttr;
	}


	public String getStoreId() {
		return storeId;
	}

	public void setStoreId(String storeId) {
		this.storeId = storeId;
	}

	public String getSku() {
 		if(StringUtils.isBlank(skuAttr)){
 			return "";
		}

		List<BbcSkuAttrDTO> list=JSON.parseArray(skuAttr, BbcSkuAttrDTO.class);
		return AttrUtil.skuListToSkuStr(list);
	}

	public String getHotelSku() {
		if(StringUtils.isBlank(skuAttr)){
			return "";
		}

		List<BbcSkuAttrDTO> list=JSON.parseArray(skuAttr, BbcSkuAttrDTO.class);
		Collections.reverse(list);
		Map<String,BbcSkuAttrDTO> map=list.stream().filter(a->a.getShow()).
						collect(Collectors.toMap(BbcSkuAttrDTO::getAttrKey, Function.identity(), (key1, key2) -> key1, LinkedHashMap::new));
		StringBuilder stringBuilder=new StringBuilder();
		for(String key:map.keySet()){
			stringBuilder.append(map.get(key).getAttrValue()+"、");
		}
		return StrUtil.subLastChar(stringBuilder);
	}



	public Map<String,String> getSkuMap() {
 		if(StringUtils.isBlank(skuAttr) ){
 			return new HashMap();
		}
		if(!skuAttr.contains("attrKey") ){
			return new HashMap();
		}

		List<BbcSkuAttrDTO> list=JSON.parseArray(skuAttr, BbcSkuAttrDTO.class);
		list=list.stream().filter(a->StringUtils.isNotBlank(a.getAttrKey())).collect(Collectors.toList());
		if(ValidationUtil.isEmpty(list)){
			return new HashMap();
		}
		return list.stream().collect(Collectors.toMap(BbcSkuAttrDTO::getAttrKey, BbcSkuAttrDTO::getAttrValue, (key1, key2) -> key2));
	}

	public void setSkuMap(Map skuMap) {
		this.skuMap = skuMap;
	}

	public void setSku(String sku) {
		this.sku = sku;
	}

	public String getTestItem() {
		return testItem;
	}

	public void setTestItem(String testItem) {
		this.testItem = testItem;
	}

	public String getProductMemo() {
		return productMemo;
	}

	public void setProductMemo(String productMemo) {
		this.productMemo = productMemo;
	}

	public String getPicPath() {
		return picPath;
	}

	public void setPicPath(String picPath) {
		this.picPath = picPath;
	}

	public String getPointsNum() {
		return pointsNum;
	}

	public void setPointsNum(String pointsNum) {
		this.pointsNum = pointsNum;
	}
}