package com.sgs.ecom.member.base;

import com.sgs.ecom.member.dto.send.OiqOtherDTO;
import com.sgs.ecom.member.enumtool.OiqSmsEnum;

import java.util.HashMap;
import java.util.Map;

public class BaseSmsSend {

	private String orderNo;
	private OiqSmsEnum oiqSmsEnum;
	private String orderType;
	private OiqOtherDTO oiqOtherDTO;

	public BaseSmsSend() {
	}
	public BaseSmsSend(String orderNo, String orderType, OiqSmsEnum oiqSmsEnum){
		this.orderNo = orderNo;
		this.oiqSmsEnum = oiqSmsEnum;
		this.orderType=orderType;
	}
	public BaseSmsSend(String orderNo, String orderType, OiqSmsEnum oiqSmsEnum,OiqOtherDTO oiqOtherDTO){
		this.orderNo = orderNo;
		this.oiqSmsEnum = oiqSmsEnum;
		this.orderType=orderType;
		this.oiqOtherDTO=oiqOtherDTO;
	}

	public String getOrderType() {
		return orderType;
	}

	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}


	public OiqSmsEnum getOiqSmsEnum() {
		return oiqSmsEnum;
	}

	public void setOiqSmsEnum(OiqSmsEnum oiqSmsEnum) {
		this.oiqSmsEnum = oiqSmsEnum;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public OiqOtherDTO getOiqOtherDTO() {
		return oiqOtherDTO;
	}

	public void setOiqOtherDTO(OiqOtherDTO oiqOtherDTO) {
		this.oiqOtherDTO = oiqOtherDTO;
	}
}
