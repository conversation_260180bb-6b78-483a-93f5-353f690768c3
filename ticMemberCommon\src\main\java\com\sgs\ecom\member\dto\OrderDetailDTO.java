package com.sgs.ecom.member.dto; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.dto.order.OrderSampleDTO;
import com.sgs.ecom.member.dto.order.OrderSampleMoreDTO;
import com.sgs.ecom.member.enumtool.aplication.TestCycleEnum;
import com.sgs.ecom.member.util.NumUtil;
import com.sgs.ecom.member.util.serializer.PriceNumFormatSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class OrderDetailDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select UNIT,SAMPLE_REQUIREMENTS,PRICE,TEST_MEMO,TEST_NAME,TEST_DAYS,BUY_NUMS,MEMO_EXPLAIN,OTHER_EXPLAIN,ITEM_NAME from ORDER_DETAIL";
	@ApiAnno(groups={Default.class})
	@BeanAnno(value="DETAIL_ID", getName="getDetailId", setName="setDetailId")
	private Long detailId;
 
 	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqOrderDetail.class})
 	@BeanAnno(value="UNIT", getName="getUnit", setName="setUnit")
 	private String unit;
 	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqOrderDetail.class})
 	@BeanAnno(value="SAMPLE_REQUIREMENTS", getName="getSampleRequirements", setName="setSampleRequirements")
 	private String sampleRequirements;

 	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqOrderDetail.class,BaseOrderFilter.WordQuotation.class,BaseOrderFilter.WordForm.class})
 	@BeanAnno(value="TEST_MEMO", getName="getTestMemo", setName="setTestMemo")
 	private String testMemo;
 	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqOrderDetail.class,BaseOrderFilter.WordQuotation.class,BaseOrderFilter.WordForm.class})
 	@BeanAnno(value="TEST_NAME", getName="getTestName", setName="setTestName")
 	private String testName;
 	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqOrderDetail.class})
 	@BeanAnno(value="TEST_DAYS", getName="getTestDays", setName="setTestDays")
 	private BigDecimal testDays;
 	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqOrderDetail.class,BaseOrderFilter.WordQuotation.class,BaseOrderFilter.WordForm.class})
 	@BeanAnno(value="BUY_NUMS", getName="getBuyNums", setName="setBuyNums")
 	private Integer buyNums;
 	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqOrderDetail.class})
 	@BeanAnno(value="MEMO_EXPLAIN", getName="getMemoExplain", setName="setMemoExplain")
 	private String memoExplain;
 	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqOrderDetail.class})
 	@BeanAnno(value="OTHER_EXPLAIN", getName="getOtherExplain", setName="setOtherExplain")
 	private String otherExplain;
 	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqOrderDetail.class,BaseOrderFilter.WordQuotation.class,BaseOrderFilter.WordForm.class})
 	@BeanAnno(value="ITEM_NAME", getName="getItemName", setName="setItemName")
 	private String itemName;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="PRODUCT_ID", getName="getProductId", setName="setProductId")
 	private Long productId;

 	private Long itemId;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqOrderDetail.class,BaseOrderFilter.WordQuotation.class,BaseOrderFilter.WordForm.class})
	private String standardCode;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqOrderDetail.class})
 	private Integer isGive=0;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqOrderDetail.class})
	private List<OrderSampleDTO> orderSampleDTOList=new ArrayList<>();
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqOrderDetail.class})
	private List<OrderSampleMoreDTO> orderSampleMoreDTOList=new ArrayList<>();

	@ApiAnno(groups={Default.class})
	private String orderNo;

	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqOrderDetail.class})
	private String memo;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqOrderDetail.class})
	private String businessLine;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqOrderDetail.class})
	private String labName;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqOrderDetail.class})
	private String cnasLab;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqOrderDetail.class})
	private String cmaLab;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqOrderDetail.class})
	private String bu;
	@ApiAnno(groups={Default.class})
	private String detailNo;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqOrderDetail.class})
	private Integer state=1;
	@ApiAnno(groups={Default.class})
	private String groupNo;



	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqOrderDetail.class,BaseOrderFilter.WordQuotation.class,BaseOrderFilter.WordForm.class})
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	private BigDecimal price;
	private String priceShow;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqOrderDetail.class})
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	private BigDecimal originalPrice;
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	private BigDecimal originalTotalPrice;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqOrderDetail.class})
	private BigDecimal priceNum;

	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqOrderDetail.class,BaseOrderFilter.WordQuotation.class,BaseOrderFilter.WordForm.class})
	@JsonSerialize(using = PriceNumFormatSerializer.class)
 	private BigDecimal totalPrice;
	private String totalPriceShow;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqOrderDetail.class})
	private Integer isOptional=0;//是否可选，1是0否
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqOrderDetail.class})
	private String labelName;//标签
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqOrderDetail.class})
	private Integer isDefault=0;

	@ApiAnno(groups={Default.class})
	private String categoryPath;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqOrderDetail.class})
	private String itemNameShow;

	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqOrderDetail.class})
	private List<OrderDetailDTO> subOrderDetailDTO;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqOrderDetail.class})
	private String itemAlias;
	private Integer itemType;
	//补差价使用
	@ApiAnno(groups={Default.class})
	private String sampleName;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqOrderDetail.class})
	private Integer isDetermine;//0,1
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqOrderDetail.class})
	private Integer urgentType;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqOrderDetail.class})
	private String urgentTypeShow;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqOrderDetail.class})
	private BigDecimal urgentAmount;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqOrderDetail.class})
	private String extendName;//检测项目分组名称

	@ApiAnno(groups={Default.class})
	private Integer parentDetailId=0;//
	@ApiAnno(groups={Default.class})
	private Integer detailState=0;//
	@ApiAnno(groups={Default.class})
	private BigDecimal baseOriginalPrice;

	@ApiAnno(groups={Default.class})
	private Integer isCs;
	@ApiAnno(groups={Default.class,BaseOrderFilter.WordQuotation.class,BaseOrderFilter.WordForm.class})
	private Integer rowNum;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqOrderDetail.class})
	private String standardCodeStr;

	@ApiAnno(groups={Default.class})
	private String itemNameGroup;
	@ApiAnno(groups={Default.class})
	private String packageType;
	private String testLineId;


	@ApiAnno(groups={Default.class})
	private String pointsNum;

	/**
	 * 最低售价
	 */
	@ApiAnno(groups={Default.class})
	private BigDecimal lowestPrice;

	/**
	 * SKU价格
	 */
	@ApiAnno(groups={Default.class})
	private BigDecimal skuPrice;

	/**
	 * 最低售价补收差额
	 */
	@ApiAnno(groups={Default.class})
	private BigDecimal lowestPriceMargin;

	public String getPackageType() {
		return packageType;
	}

	public void setPackageType(String packageType) {
		this.packageType = packageType;
	}

	public String getItemNameGroup() {
		return itemNameGroup;
	}

	public void setItemNameGroup(String itemNameGroup) {
		this.itemNameGroup = itemNameGroup;
	}

	public String getStandardCodeStr() {
		return standardCodeStr=standardCode;
	}

	public Integer getRowNum() {
		return rowNum;
	}

	public void setRowNum(Integer rowNum) {
		this.rowNum = rowNum;
	}

	public Integer getIsCs() {
		return isCs;
	}

	public void setIsCs(Integer isCs) {
		this.isCs = isCs;
	}

	public String getExtendName() {
		return extendName;
	}

	public void setExtendName(String extendName) {
		this.extendName = extendName;
	}

	public Integer getItemType() {
		return itemType;
	}

	public void setItemType(Integer itemType) {
		this.itemType = itemType;
	}

	public Long getProductId() {
		return productId;
	}

	public void setProductId(Long productId) {
		this.productId = productId;
	}

	public void setUnit(String unit){
 		 this.unit=unit;
 	}
 	public String getUnit(){
 		 return this.unit;
 	}
 
 	 
 	public void setSampleRequirements(String sampleRequirements){
 		 this.sampleRequirements=sampleRequirements;
 	}
 	public String getSampleRequirements(){
 		 return this.sampleRequirements;
 	}

 	 
 	public void setTestMemo(String testMemo){
 		 this.testMemo=testMemo;
 	}
 	public String getTestMemo(){
 		 return this.testMemo;
 	}
 
 	 
 	public void setTestName(String testName){
 		 this.testName=testName;
 	}
 	public String getTestName(){
 		 return this.testName;
 	}


	public BigDecimal getTestDays() {
		return testDays;
	}

	public void setTestDays(BigDecimal testDays) {
		this.testDays = testDays;
	}

	public Integer getBuyNums() {
		return buyNums;
	}

	public void setBuyNums(Integer buyNums) {
		this.buyNums = buyNums;
	}

	public void setMemoExplain(String memoExplain){
 		 this.memoExplain=memoExplain;
 	}
 	public String getMemoExplain(){
 		 return this.memoExplain;
 	}
 
 	 
 	public void setOtherExplain(String otherExplain){
 		 this.otherExplain=otherExplain;
 	}
 	public String getOtherExplain(){
 		 return this.otherExplain;
 	}
 
 	 
 	public void setItemName(String itemName){
 		 this.itemName=itemName;
 	}
 	public String getItemName(){
 		 return this.itemName;
 	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public Long getItemId() {
		return itemId;
	}

	public void setItemId(Long itemId) {
		this.itemId = itemId;
	}

	public Integer getIsGive() {
 		Integer isGivePrice=0;
 		if(price!=null &&  price.compareTo(BigDecimal.ZERO)==0){
			isGivePrice=1;
		}
		return isGivePrice;
	}



	public BigDecimal getTotalPrice() {
		return totalPrice;
	}

	public void setTotalPrice(BigDecimal totalPrice) {
		this.totalPrice = totalPrice;
	}

	public String getStandardCode() {
		return standardCode;
	}

	public void setStandardCode(String standardCode) {
		this.standardCode = standardCode;
	}

	public Long getDetailId() {
		return detailId;
	}

	public void setDetailId(Long detailId) {
		this.detailId = detailId;
	}

	public List<OrderSampleDTO> getOrderSampleDTOList() {
		return orderSampleDTOList;
	}

	public void setOrderSampleDTOList(List<OrderSampleDTO> orderSampleDTOList) {
		this.orderSampleDTOList = orderSampleDTOList;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getBusinessLine() {
		return businessLine;
	}

	public void setBusinessLine(String businessLine) {
		this.businessLine = businessLine;
	}

	public String getLabName() {
		return labName;
	}

	public void setLabName(String labName) {
		this.labName = labName;
	}

	public String getCnasLab() {
		return cnasLab;
	}

	public void setCnasLab(String cnasLab) {
		this.cnasLab = cnasLab;
	}

	public String getCmaLab() {
		return cmaLab;
	}

	public void setCmaLab(String cmaLab) {
		this.cmaLab = cmaLab;
	}

	public String getBu() {
		return bu;
	}

	public void setBu(String bu) {
		this.bu = bu;
	}

	public static String getCreateSql() {
		return CREATE_SQL;
	}

	public String getDetailNo() {
		return detailNo;
	}

	public void setDetailNo(String detailNo) {
		this.detailNo = detailNo;
	}



	public List<OrderDetailDTO> getSubOrderDetailDTO() {
		return subOrderDetailDTO;
	}

	public void setSubOrderDetailDTO(List<OrderDetailDTO> subOrderDetailDTO) {
		this.subOrderDetailDTO = subOrderDetailDTO;
	}


	public String getLabelName() {
		return labelName;
	}

	public void setLabelName(String labelName) {
		this.labelName = labelName;
	}



	public String getCategoryPath() {
		return categoryPath;
	}

	public void setCategoryPath(String categoryPath) {
		this.categoryPath = categoryPath;
	}

	public String getItemNameShow() {
		if(StringUtils.isNotBlank(categoryPath)){
			return categoryPath+"/"+itemName;
		}
		return itemName;
	}

	public void setItemNameShow(String itemNameShow) {
		this.itemNameShow = itemNameShow;
	}

	public String getItemAlias() {
		return itemAlias;
	}

	public void setItemAlias(String itemAlias) {
		this.itemAlias = itemAlias;
	}

	public BigDecimal getOriginalPrice() {
		return originalPrice;
	}

	public void setOriginalPrice(BigDecimal originalPrice) {
		this.originalPrice = originalPrice;
	}

	public BigDecimal getPriceNum() {
 		if(originalPrice==null||price==null){
 			return new BigDecimal("100");
		}
 		if(originalPrice.doubleValue()==0 || price.doubleValue()==0){
			return new BigDecimal("100");
		}

		return price.divide(originalPrice,4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100"));
	}

	public void setPriceNum(BigDecimal priceNum) {
		this.priceNum = priceNum;
	}

	public BigDecimal getOriginalTotalPrice() {
 		if(originalPrice==null){
 			return null;
		}

		return originalPrice.multiply(new BigDecimal(buyNums).setScale(4, BigDecimal.ROUND_HALF_UP));
	}

	public void setOriginalTotalPrice(BigDecimal originalTotalPrice) {
		this.originalTotalPrice = originalTotalPrice;
	}

	public String getSampleName() {
		return sampleName;
	}

	public void setSampleName(String sampleName) {
		this.sampleName = sampleName;
	}

	public Integer getIsDetermine() {
		return isDetermine;
	}

	public void setIsDetermine(Integer isDetermine) {
		this.isDetermine = isDetermine;
	}

	public Integer getUrgentType() {
		return urgentType;
	}

	public void setUrgentType(Integer urgentType) {
		this.urgentType = urgentType;
	}

	public String getUrgentTypeShow() {
		return urgentType==null?"": TestCycleEnum.getNameCh(urgentType.toString());
	}

	public void setUrgentTypeShow(String urgentTypeShow) {
		this.urgentTypeShow = urgentTypeShow;
	}

	public BigDecimal getUrgentAmount() {
		return urgentAmount;
	}

	public void setUrgentAmount(BigDecimal urgentAmount) {
		this.urgentAmount = urgentAmount;
	}

	public String getGroupNo() {
		return groupNo;
	}

	public void setGroupNo(String groupNo) {
		this.groupNo = groupNo;
	}

	public String getPriceShow() {
		return NumUtil.toFormat(price);
	}

	public String getTotalPriceShow() {
		return NumUtil.toFormat(totalPrice);
	}

	public List<OrderSampleMoreDTO> getOrderSampleMoreDTOList() {
		return orderSampleMoreDTOList;
	}

	public void setOrderSampleMoreDTOList(List<OrderSampleMoreDTO> orderSampleMoreDTOList) {
		this.orderSampleMoreDTOList = orderSampleMoreDTOList;
	}

	public BigDecimal getBaseOriginalPrice() {
		return baseOriginalPrice;
	}

	public void setBaseOriginalPrice(BigDecimal baseOriginalPrice) {
		this.baseOriginalPrice = baseOriginalPrice;
	}

	public void setIsGive(Integer isGive) {
		this.isGive = isGive;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public void setPriceShow(String priceShow) {
		this.priceShow = priceShow;
	}

	public void setTotalPriceShow(String totalPriceShow) {
		this.totalPriceShow = totalPriceShow;
	}

	public Integer getIsOptional() {
		return isOptional;
	}

	public void setIsOptional(Integer isOptional) {
		this.isOptional = isOptional;
	}

	public Integer getIsDefault() {
		return isDefault;
	}

	public void setIsDefault(Integer isDefault) {
		this.isDefault = isDefault;
	}

	public Integer getParentDetailId() {
		return parentDetailId;
	}

	public void setParentDetailId(Integer parentDetailId) {
		this.parentDetailId = parentDetailId;
	}

	public Integer getDetailState() {
		return detailState;
	}

	public void setDetailState(Integer detailState) {
		this.detailState = detailState;
	}

	public String getPointsNum() {
		if(ValidationUtil.isEmpty(labelName))
			return "";

		return labelName;
	}

	public String getTestLineId() {
		return testLineId;
	}

	public void setTestLineId(String testLineId) {
		this.testLineId = testLineId;
	}
}