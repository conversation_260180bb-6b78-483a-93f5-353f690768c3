package com.sgs.ecom.member.domain.util;

import com.sgs.ecom.member.util.NumUtil;
import com.sgs.ecom.member.util.select.SelectMapUtil;

import java.math.BigDecimal;
import java.util.Map;

public class RoDO {

    public static String getToPayAmountByOrder(Map<String, Integer> amountMap, String orderNo,BigDecimal realAmount, BigDecimal platformAmount){
        if(amountMap.containsKey(orderNo)){
            return "";
        }
        if(platformAmount==null){
            return  NumUtil.toFormat(realAmount);
        }
        return  NumUtil.toFormat(platformAmount);
    }
    //看能否出支付按钮
    public static Boolean  checkToPayAmount(int monthPay,int payState,String currency,int state){
        if(state==11 || state==91){
            return false;
        }
        if(monthPay==0 && payState==0 && SelectMapUtil.CNY.equals(currency) ){
            return true;
        }
        return false;
    }


}
