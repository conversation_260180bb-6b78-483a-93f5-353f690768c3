package com.sgs.ecom.member.entity.order;

public class OrderSampleRelate {


    private Long relateId;

    private String createDate;

    private Long detailId;

    private String groupNo;

    private String stateDate;

    private Integer state;

    private String orderNo;

    private String sampleNo;
    private int buyNums;

    public int getBuyNums() {
        return buyNums;
    }

    public void setBuyNums(int buyNums) {
        this.buyNums = buyNums;
    }

    public void setRelateId(Long relateId){
        this.relateId=relateId;
    }
    public Long getRelateId(){
        return this.relateId;
    }


    public void setCreateDate(String createDate){
        this.createDate=createDate;
    }
    public String getCreateDate(){
        return this.createDate;
    }


    public void setDetailId(Long detailId){
        this.detailId=detailId;
    }
    public Long getDetailId(){
        return this.detailId;
    }


    public void setGroupNo(String groupNo){
        this.groupNo=groupNo;
    }
    public String getGroupNo(){
        return this.groupNo;
    }


    public void setStateDate(String stateDate){
        this.stateDate=stateDate;
    }
    public String getStateDate(){
        return this.stateDate;
    }


    public void setState(Integer state){
        this.state=state;
    }
    public Integer getState(){
        return this.state;
    }


    public void setOrderNo(String orderNo){
        this.orderNo=orderNo;
    }
    public String getOrderNo(){
        return this.orderNo;
    }


    public String getSampleNo() {
        return sampleNo;
    }

    public void setSampleNo(String sampleNo) {
        this.sampleNo = sampleNo;
    }
}
