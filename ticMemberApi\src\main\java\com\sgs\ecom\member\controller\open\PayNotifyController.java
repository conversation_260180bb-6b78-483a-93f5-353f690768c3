package com.sgs.ecom.member.controller.open;

import com.alibaba.fastjson.JSON;
import com.platform.util.SystemConfig;
import com.platform.util.ValidationUtil;
import com.sgs.ecom.member.domain.order.service.interfaces.IOrderPayDomainService;
import com.sgs.ecom.member.domain.util.RoDO;
import com.sgs.ecom.member.dto.base.BaseOrderDTO;
import com.sgs.ecom.member.dto.pay.OrderBasePayDTO;
import com.sgs.ecom.member.enumtool.event.EventEnum;
import com.sgs.ecom.member.event.EventApiUtil;
import com.sgs.ecom.member.service.order.interfaces.IOrderBaseInfoCustomService;
import com.sgs.ecom.member.service.order.interfaces.IOrderPayService;
import com.sgs.ecom.member.service.rsts.interfaces.IRSTSPayService;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.select.SelectListUtil;
import com.sgs.ecom.payment.tools.SgspaySignature;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;

@RestController
@RequestMapping("/openapi/api.v1.payment/pay")
public class PayNotifyController extends ControllerUtil {

    Logger logger = LoggerFactory.getLogger(PayNotifyController.class);
    @Autowired
    private IOrderPayService orderPayService;
    @Autowired
    private IOrderBaseInfoCustomService orderBaseInfoCustomService;
    @Autowired
    private EventApiUtil eventApiUtil;
    @Autowired
    private IOrderPayDomainService orderPayDomainService;
    @Autowired
    private IRSTSPayService irstsPayService;

    /**
     * 支付回调公有key
     */
    @Value("${payment.public_key}")
    private String publicKey;


    private SgspaySignature sgspaySignature = new SgspaySignature();


    @RequestMapping(value = "notify", method = {RequestMethod.POST, RequestMethod.GET})
    public String notify(HttpServletRequest request) throws Exception {
        String returnFlg = "fail";

        Map<String, String> params = new HashMap<>();
        Map<String, String[]> requestParams = request.getParameterMap();
        for (Iterator<?> iter = requestParams.keySet().iterator(); iter.hasNext(); ) {
            String name = (String) iter.next();
            String[] values = requestParams.get(name);
            String valueStr = "";
            for (int i = 0; i < values.length; i++) {
                valueStr = (i == values.length - 1) ? valueStr + values[i] : valueStr + values[i] + ",";
            }
            params.put(name, valueStr);
        }
        logger.info("params======3====" + JSON.toJSONString(params));
        Boolean amountFlag = false;
        try {
            String transNo = params.get("merOrderNo");
            if (ValidationUtil.isEmpty(transNo)) {
                logger.info("params======支付订单号不存在====" + returnFlg);
                return returnFlg;
            }
            List<String> orderNoList = Arrays.asList(transNo.split(","));
            Map<String, Object> qryMap = new HashMap<>();
            qryMap.put(SelectListUtil.ORDER_NO_LIST, orderNoList);
            List<OrderBasePayDTO> orderBasePayDTOList = orderBaseInfoCustomService.qryOrderBasePayDTOList(qryMap);
            if (ValidationUtil.isEmpty(orderBasePayDTOList)) {
                logger.info("params======订单不存在====" + returnFlg);
                return returnFlg;
            }
            if (!sgspaySignature.rsaCheck(params, publicKey, "1.8")) {
                logger.info("params======5====" + JSON.toJSONString(params));
                return returnFlg;
            }

            String amount = params.get("amount");
            logger.info("params======amount====" + amount);
            BigDecimal reduce = orderBasePayDTOList.stream()
                    .filter(orderBasePayDTO -> !ValidationUtil.isEmpty(orderBasePayDTO.getRealAmount()))
                    .map(OrderBasePayDTO::getRealAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            logger.info("params======reduce1====" + reduce);
            logger.info("params======reduce2====" + String.valueOf(reduce.multiply(new BigDecimal("100")).intValue()));
            if (!String.valueOf(reduce.multiply(new BigDecimal("100")).intValue()).equals(amount)) {
                logger.info("params======订单总金额不一致====" + JSON.toJSONString(params));
                amountFlag = true;
            }
            logger.info("params======orderBasePayDTOList====" + JSON.toJSONString(orderBasePayDTOList));
            returnFlg = orderPayService.payNotify(params, orderBasePayDTOList,amountFlag);
            if(params.containsKey("isSub")){
                eventApiUtil.saveEvent(params.get("isSub").toString(), EventEnum.SAVE_ORDER_TO_END);
            }
            if(params.containsKey("payToLeads")){
                eventApiUtil.saveEvent(params.get("payToLeads").toString(), EventEnum.PAY_TO_LEADS);
            }


        } catch (Exception e) {
            logger.info("params======6====", e);
            return returnFlg;
        }
        return returnFlg;

    }


    @RequestMapping(value = "roNotify", method = {RequestMethod.POST, RequestMethod.GET})
    public String roNotify(HttpServletRequest request) throws Exception {
        String returnFlg = "fail";

        Map<String, String> params = new HashMap<>();
        Map<String, String[]> requestParams = request.getParameterMap();
        for (Iterator<?> iter = requestParams.keySet().iterator(); iter.hasNext(); ) {
            String name = (String) iter.next();
            String[] values = requestParams.get(name);
            String valueStr = "";
            for (int i = 0; i < values.length; i++) {
                valueStr = (i == values.length - 1) ? valueStr + values[i] : valueStr + values[i] + ",";
            }
            params.put(name, valueStr);
        }
        logger.info("params======3====" + JSON.toJSONString(params));
        Boolean amountSame = true;
        try {
            String transNo = params.get("merOrderNo");
            if (ValidationUtil.isEmpty(transNo)) {
                logger.info("params======支付订单号不存在====" + returnFlg);
                return returnFlg;
            }
            List<String> orderNoList = Arrays.asList(transNo.split(","));
            String orderNo=orderNoList.get(0);
            BaseOrderDTO baseOrderDTO=orderBaseInfoCustomService.selectBaseByOrderNo(orderNo);
            if (ValidationUtil.isEmpty(baseOrderDTO)) {
                logger.info("params======订单不存在====" + returnFlg);
                return returnFlg;
            }

            if (!sgspaySignature.rsaCheck(params, publicKey, "1.8")) {
                logger.info("params======5====" + JSON.toJSONString(params));
                return returnFlg;
            }

            String amount = params.get("amount");
            Map<String, Integer> amountMap=orderPayDomainService.qryPayAmount(Arrays.asList(baseOrderDTO.getOrderNo()));
            String reduce= RoDO.getToPayAmountByOrder(amountMap,baseOrderDTO.getOrderNo(),baseOrderDTO.getRealAmount(),baseOrderDTO.getPlatformAmount());
            if(StringUtils.isBlank(reduce)){
                reduce="0";
            }
            logger.info("params======reduce2===="+amount+"--"+reduce.toString());
            if (!String.valueOf(new BigDecimal(reduce).multiply(new BigDecimal("100")).intValue()).equals(amount)) {
                logger.info("params======订单总金额不一致====" + JSON.toJSONString(params));
                amountSame = false;
            }

            returnFlg = irstsPayService.roNotify(params, baseOrderDTO,amountSame);

        } catch (Exception e) {
            logger.info("params======6====", e);
            return returnFlg;
        }
        return returnFlg;

    }


}
