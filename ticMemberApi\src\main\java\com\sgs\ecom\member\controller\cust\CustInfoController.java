package com.sgs.ecom.member.controller.cust;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.base.BaseBean;
import com.sgs.ecom.member.request.cust.CheckTaxNoReq;
import com.sgs.ecom.member.request.cust.CustInfoReq;
import com.sgs.ecom.member.service.custom.interfaces.ICustInfoSV;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.cust/info")
public class CustInfoController extends ControllerUtil {

	@Autowired
	private ICustInfoSV custInfoSV;

	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "qryByPayCode", method = { RequestMethod.POST })
	public ResultBody qryCustByPayCode(
		@RequestHeader(value="accessToken") String token,
		@RequestBody CustInfoReq custInfoReq) throws Exception {
		return ResultBody.newInstance(custInfoSV.qryCustInfoByPayCode(custInfoReq,getUserDTO(token)));
	}

	/**
	* @params [token]
	* @return com.sgs.ecom.member.util.ResultBody
	* @description 获取用户关联的企业数据
	* <AUTHOR> || created at 2024/5/8 14:56
	*/
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "qryUserCust", method = { RequestMethod.POST })
	public ResultBody qryUserCust(
			@RequestHeader(value="accessToken") String token) throws Exception {
		return ResultBody.newInstance(custInfoSV.qryUserCust(getUserDTO(token)));
	}

	@AuthRequired(login = "NULL", sign = true)
	@RequestMapping(value = "search", method = { RequestMethod.POST })
	public ResultBody search(
			@RequestBody JSONObject jsonObject) throws Exception {
		return ResultBody.newInstance(custInfoSV.search(jsonObject));
	}

	/**
	 * 查找公司基本信息
	 *
	 * @param jsonObject
	 * @return
	 * @throws Exception
	 */
	@AuthRequired(login = "NULL", sign = true)
	@RequestMapping(value = "searchBaseInfo", method = { RequestMethod.POST })
	public ResultBody searchBaseInfo(
		@RequestBody JSONObject jsonObject) throws Exception {
		return ResultBody.newInstance(custInfoSV.searchBaseInfo(jsonObject));
	}

	@AuthRequired(login = "NULL", sign = true)
	@RequestMapping(value = "checkCompany", method = { RequestMethod.POST })
	public ResultBody checkCompany(
			@Validated(BaseBean.Query.class)
			@RequestBody CheckTaxNoReq checkTaxNoReq) throws Exception {
		custInfoSV.checkCompany(checkTaxNoReq);
		return ResultBody.success();
	}

}
