package com.sgs.ecom.member.dto.order;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.util.serializer.PriceNumFormatSerializer;
import com.sgs.ecom.member.util.serializer.TimeStringFormatSerializer;

import java.math.BigDecimal;

public class SubOrderInfoDTO {
    @ApiAnno(groups={BaseQryFilter.Default.class, BaseOrderFilter.OrderList.class})
    private int num;
    @JsonSerialize(using = PriceNumFormatSerializer.class)
    @ApiAnno(groups={BaseQryFilter.Default.class, BaseOrderFilter.OrderList.class})
    private BigDecimal price;
    @ApiAnno(groups={BaseQryFilter.Default.class, BaseOrderFilter.OrderList.class})
    private String orderNo;

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }
}
