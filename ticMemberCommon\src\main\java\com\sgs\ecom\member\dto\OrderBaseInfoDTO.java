package com.sgs.ecom.member.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.dto.detail.OrderAttachmentDTO;
import com.sgs.ecom.member.dto.detail.OrderReportDTO;
import com.sgs.ecom.member.dto.order.OrderProductDTO;
import com.sgs.ecom.member.dto.order.OrderSampleDTO;
import com.sgs.ecom.member.dto.order.OrderSampleStrDTO;
import com.sgs.ecom.member.dto.order.SubOrderDTO;
import com.sgs.ecom.member.dto.order.SubOrderInfoDTO;
import com.sgs.ecom.member.dto.portal.PortalInquiryBaseDTO;
import com.sgs.ecom.member.dto.question.UserAnswerDTO;
import com.sgs.ecom.member.enumtool.OrderTypeEnum;
import com.sgs.ecom.member.enumtool.bbc.DownReportEnum;
import com.sgs.ecom.member.enumtool.bbc.StoreEnum;
import com.sgs.ecom.member.enumtool.order.OrderRefundStateEnum;
import com.sgs.ecom.member.enumtool.pay.PayMethodEnum;
import com.sgs.ecom.member.util.order.UseDateUtil;
import com.sgs.ecom.member.util.serializer.PriceNumFormatSerializer;
import com.sgs.ecom.member.util.serializer.TimeStringFormatSerializer;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


public class OrderBaseInfoDTO extends OrderBaseInfoCheckDTO {

	public static final String STATE_SHOW="stateShow";
	public static final String TIME_DATE_SHOW="timeDateShow";


	@ApiAnno(groups={Default.class,OrderList.class,InquiryList.class,RstsList.class,TicList.class,QuerySummary.class,OrderCustomerList.class})
	@JsonSerialize(using = TimeStringFormatSerializer.class)
	private String createDate;


	@ApiAnno(groups={Default.class,OrderList.class,InquiryList.class})
	private String labName;

	@ApiAnno(groups={Default.class,OrderList.class,InquiryList.class})
	private String businessLine;

	@ApiAnno(groups={Default.class,OrderList.class,InquiryList.class})
	private String categoryPath;

	@ApiAnno(groups={Default.class,OrderList.class,InquiryList.class})
	private int isRead=1;
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	@ApiAnno(groups={Default.class,OrderList.class,InquiryList.class,RstsList.class,TicList.class,QuerySummary.class})
	private BigDecimal orderAmount;
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	@ApiAnno(groups={Default.class,OrderList.class,InquiryList.class,RstsList.class,TicList.class,QuerySummary.class})
	private BigDecimal shopDisAmount;

	@ApiAnno(groups={Default.class,InquiryList.class})
	private String sampleNameStr;
	@ApiAnno(groups={Default.class,InquiryList.class})
	private String itemName;
	@ApiAnno(groups={RstsList.class,RstsDetail.class})
	private int industryField;
	@ApiAnno(groups={OrderList.class})
	private int hisState;

	//不吐给用户

	private String productName;


	//询价单单独
	@ApiAnno(groups={Default.class,InquiryList.class})
	private List<UserAnswerDTO> userAnswerDTOList;
	@ApiAnno(groups={Default.class,InquiryList.class,RstsList.class})
	private OrderOperatorLogDTO orderOperatorLogDTO;
	@ApiAnno(groups={Default.class,InquiryList.class})
	private String csEmail;
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	@ApiAnno(groups={Default.class,InquiryList.class,TicList.class,QuerySummary.class})
	private BigDecimal discountAmount;
	@ApiAnno(groups={Default.class,InquiryList.class})
	private String toOrder;
	@ApiAnno(groups={Default.class,InquiryList.class})
	private int leadsState;
	@ApiAnno(groups={Default.class,InquiryList.class})
	private String ticketType;
	@ApiAnno(groups={Default.class,InquiryList.class})
	private String ticketSource;
	@ApiAnno(groups={Default.class,InquiryList.class})
	private String ticketSourceName;
	@ApiAnno(groups={Default.class,InquiryList.class})
	private int customerReminderNum;
	@ApiAnno(groups={Default.class,InquiryList.class})
	private String kf5Desc;
	@ApiAnno(groups={Default.class,InquiryList.class})
	private List<Map> receiptLog;
	@ApiAnno(groups={Default.class,InquiryList.class})
	private int waitFlg=0;
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	@ApiAnno(groups={RstsList.class,RstsDetail.class})
	private BigDecimal platformAmount;
	//复购和复询相关功能
	@ApiAnno(groups={Default.class,InquiryList.class,OrderList.class})
	private int userLabelFlg;


	//订单单独
	@ApiAnno(groups={Default.class,OrderList.class})
	private int isInvoice;
	@ApiAnno(groups={Default.class,OrderList.class})
	private int invoiceType=-1;
	@ApiAnno(groups={Default.class,OrderList.class,TicList.class,OrderCustomerList.class})
	private int isPayReceived;
	@ApiAnno(groups={Default.class,OrderList.class,TicList.class})
	private int subState;
	@ApiAnno(groups={Default.class,OrderList.class})
	private int totalNums;//列表数量 oiq为1
	@ApiAnno(groups={Default.class,OrderList.class,RstsList.class,TicList.class,QuerySummary.class,OrderCustomerList.class})
	private int payState;
	@ApiAnno(groups={Default.class,OrderList.class,RstsList.class,TicList.class})
	private int proState;
	@ApiAnno(groups={Default.class,OrderList.class,TicList.class,RstsList.class})
	private Integer payMethod;
	@JsonSerialize(using = TimeStringFormatSerializer.class)
	@ApiAnno(groups={Default.class,OrderList.class,RstsList.class})
	private String deadlineTime;
	@ApiAnno(groups={Default.class,OrderList.class,TicList.class,RstsList.class})
	private String deadlineTimeShow;
	@ApiAnno(groups={Default.class,OrderList.class,TicList.class})
	private String optDeadlineTime;//操作报告日期的时间
	@ApiAnno(groups={Default.class,OrderList.class,TicList.class})
	private String  applySubmitDate;
	@ApiAnno(groups={Default.class,OrderList.class})
	private String productImg;
	@ApiAnno(groups={Default.class,OrderList.class,TicList.class,QuerySummary.class,OrderCustomerList.class})
	private String relateOrderNo;
	@ApiAnno(groups={Default.class,OrderList.class})
	private String reportForm;
	@ApiAnno(groups={Default.class,OrderList.class})
	private String reportLua;
	@ApiAnno(groups={Default.class,OrderList.class,OrderCustomerList.class})
	private List<OrderAttachmentDTO> reportFileList=new ArrayList<>() ;
	@ApiAnno(groups={Default.class,OrderList.class})
	private List<OrderAttachmentDTO> invoiceFileList=new ArrayList<>() ;
	@ApiAnno(groups={Default.class,OrderList.class,TicList.class})
	private int isDelete;
	@ApiAnno(groups={Default.class,OrderList.class,TicList.class,QuerySummary.class})
	private int refundState;
	@ApiAnno(groups={Default.class,OrderList.class,TicList.class})
	private  PortalInquiryBaseDTO inquiryBaseDTO;
	@ApiAnno(groups={Default.class,OrderList.class,RstsList.class,RstsDetail.class,TicList.class})
	private List<SubOrderDTO> subOrderDTOList = new ArrayList<>();
	@ApiAnno(groups={OrderList.class})
	private SubOrderInfoDTO subOrderInfoDTO=new SubOrderInfoDTO();

	@ApiAnno(groups={RstsList.class})
	private List<OrderDetailDTO> orderDetailDTOList;
	@ApiAnno(groups={OrderList.class,OrderCustomerList.class})
	private String orderDetailStr;
	@ApiAnno(groups={OrderList.class,OrderCustomerList.class})
	private int orderDetailSize;
	@ApiAnno(groups={OrderList.class})
	private OrderReportDTO report;

	@ApiAnno(groups={OrderList.class,OrderCustomerList.class})
	private List<OrderSampleStrDTO> orderSampleStrDTOList=new ArrayList<>();
	@ApiAnno(groups={RstsList.class})
	private List<OrderSampleDTO> orderSampleDTOList;
	@ApiAnno(groups={RstsList.class})
	@JsonSerialize(using = TimeStringFormatSerializer.class)
	private String confirmOrderDate;
	@ApiAnno(groups={RstsList.class,InquiryList.class,OrderList.class})
	private String currency;
	@ApiAnno(groups={InquiryList.class,OrderList.class})
	private String currencyMark;
	@ApiAnno(groups={RstsList.class})
	private String sampleCode;
	@ApiAnno(groups={RstsList.class})
	private String sampleId;
	@ApiAnno(groups={RstsList.class})
	private String toPayAmount;


	@ApiAnno(groups={TicList.class})
	private OrderProductDTO orderProductDTO;
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	@ApiAnno(groups={RstsList.class,RstsDetail.class})
	private BigDecimal rstsAmount;
	@ApiAnno(groups={TicList.class})
	private List<OrderProductDTO> orderProductDTOList;
	@ApiAnno(groups={RstsList.class,TicList.class})
	private int monthPay;
	@ApiAnno(groups={TicList.class})
	private String storeId;
	@ApiAnno(groups={TicList.class})
	private String storeName;
	@ApiAnno(groups={TicList.class})
	private String serviceItems;
	@ApiAnno(groups={TicList.class})
	private String monthPayShow;
	@ApiAnno(groups={TicList.class})
	private String bbcOrderId;
	@ApiAnno(groups={TicList.class})
	private int useBbc;//1是bbc的数据 0是member
	@ApiAnno(groups={TicList.class})
	private int downLoad;
	@ApiAnno(groups={TicList.class})
	private Integer downInvoiceLoad;//发票下载标记 0-未出票 1-已出票未下载 2-已出票已下载
	@ApiAnno(groups={TicList.class})
	private String downLoadShow;

	@ApiAnno(groups={OrderList.class})
	private List<OrderAttachmentDTO> quotationFileList=new ArrayList<>();
	@ApiAnno(groups={OrderList.class})
	private int haveForm;

	private String stateDate;


	//用户满意度
	@ApiAnno(groups={InquiryList.class})
	private Integer userAgree;
	@ApiAnno(groups={InquiryList.class})
	private Integer isEvaluate;

	@ApiAnno(groups={InquiryList.class})
	private String ticketIdFeed;

	@ApiAnno(groups={InquiryList.class})
	private String userProd;
	@ApiAnno(groups={InquiryList.class})
	private String requirementsInfo;

	@ApiAnno(groups={InquiryList.class})
	private Integer receiptState;
	@ApiAnno(groups={TicList.class,InquiryList.class})
	private String companyAddressEn;
	@ApiAnno(groups={TicList.class,InquiryList.class})
	private String companyAddressCn;
	@ApiAnno(groups={TicList.class,InquiryList.class})
	private String companyNameEn;
	@ApiAnno(groups={TicList.class,InquiryList.class})
	private String town;
	@ApiAnno(groups={Default.class,TicList.class,InquiryList.class})
	private Integer isElectron; //是否是上海开票显示电子 0-否 1-是

	@JsonSerialize(using = PriceNumFormatSerializer.class)
	@ApiAnno(groups={Default.class,RstsList.class,TicList.class})
	private BigDecimal serviceAmount;
	@ApiAnno(groups={Default.class,RstsList.class})
	private BigDecimal urgentAmount;

	@ApiAnno(groups={BaseQryFilter.Default.class,TicList.class})
	private String model;//返回是否时新拆单规则

	@JsonSerialize(using = PriceNumFormatSerializer.class)
	@ApiAnno(groups={OrderDetail.class,Default.class,TicDetail.class,TicList.class})
	private BigDecimal csDiscountAmount;//客服优惠金额
	@ApiAnno(groups={Default.class})
	private String submitApplicationDate;

	@ApiAnno(groups={Default.class,OrderList.class,TicList.class,RstsList.class})
	private String payMethodName;

	@ApiAnno(groups={Default.class,OrderList.class,TicList.class,RstsList.class})
	private String msdsFileNames;

	@ApiAnno(groups={Default.class,QuerySummary.class})
	private String recommendReason;

	@ApiAnno(groups={Default.class,OrderList.class,TicList.class,RstsList.class})
	private String suppDetectItemNames;//补充检测项目数据

	@JsonSerialize(using = PriceNumFormatSerializer.class)
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private BigDecimal subOrderPrice;

	@JsonSerialize(using = PriceNumFormatSerializer.class)
	@ApiAnno(groups={OrderDetail.class,Default.class,TicDetail.class,TicList.class})
	private BigDecimal realCsAfterAmount;//客服优惠后的金额
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	@ApiAnno(groups={Default.class,OrderDetail.class,TicDetail.class,TicList.class,OrderList.class,InquiryList.class,InquiryDetail.class,RstsDetail.class,RstsList.class})
	private BigDecimal realAmount;
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	@ApiAnno(groups={OrderDetail.class,Default.class,TicDetail.class,TicList.class})
	private BigDecimal realTotalAmount;//主单应付金额+补单金额


	private String clauseFileUrlStr;//打印申请表的条款图片


	private String barCodeUrl; //条形码的url地址

	/**
	 * 当前页码
	 */
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String fileNo;

	/**
	 * 订单的样品申请表类型标识
	 */
	@ApiAnno(groups={BaseQryFilter.Default.class, TicList.class})
	private boolean multiType;


	/**申请方名称 有中文取中文没有取英文*/
	@ApiAnno(groups={BaseOrderFilter.OrderCustomerList.class})
	private String applicationCompanyNameStr;
	/**报告名称 有中文取中文没有取英文*/
	@ApiAnno(groups={BaseOrderFilter.OrderCustomerList.class})
	private String reportCompanyNameStr;
	/**
	 * 跟踪单号
	 */
	@ApiAnno(groups={OrderList.class,OrderCustomerList.class})
	private String outOrderNo;
	/**
	 * 提单号
	 */
	@ApiAnno(groups={OrderList.class,OrderCustomerList.class})
	private String ladingNo;
	/**
	 * 发票
	 */
	@ApiAnno(groups={OrderList.class})
	private String invoiceTitle;
	/**
	 * tfs订单流水号
	 */
	@ApiAnno(groups={OrderList.class,OrderCustomerList.class})
	private String platformOrder;


	public String getClauseFileUrlStr() {
		return clauseFileUrlStr;
	}

	public void setClauseFileUrlStr(String clauseFileUrlStr) {
		this.clauseFileUrlStr = clauseFileUrlStr;
	}

	public BigDecimal getRealTotalAmount() {
		return realTotalAmount;
	}

	public void setRealTotalAmount(BigDecimal realTotalAmount) {
		this.realTotalAmount = realTotalAmount;
	}

	public BigDecimal getRealAmount() {
		return realAmount;
	}

	public void setRealAmount(BigDecimal realAmount) {
		this.realAmount = realAmount;
	}

	public BigDecimal getRealCsAfterAmount() {
		BigDecimal realDecimal = !ValidationUtil.isEmpty(realAmount) ? realAmount : BigDecimal.ZERO;
		BigDecimal serviceDecimal = !ValidationUtil.isEmpty(serviceAmount) ? serviceAmount : BigDecimal.ZERO;
		return realDecimal.subtract(serviceDecimal);
	}
	public BigDecimal getSubOrderPrice() {
		return subOrderPrice;
	}

	public void setSubOrderPrice(BigDecimal subOrderPrice) {
		this.subOrderPrice = subOrderPrice;
	}

	public String getSuppDetectItemNames() {
		return suppDetectItemNames;
	}

	public void setSuppDetectItemNames(String suppDetectItemNames) {
		this.suppDetectItemNames = suppDetectItemNames;
	}

	public String getMsdsFileNames() {
		return msdsFileNames;
	}

	public void setMsdsFileNames(String msdsFileNames) {
		this.msdsFileNames = msdsFileNames;
	}

	public String getPayMethodName() {
		return PayMethodEnum.getNameCh(String.valueOf(payMethod));
	}


	public String getSubmitApplicationDate() {
		return submitApplicationDate;
	}

	public void setSubmitApplicationDate(String submitApplicationDate) {
		this.submitApplicationDate = submitApplicationDate;
	}

	public BigDecimal getCsDiscountAmount() {
		return csDiscountAmount;
	}

	public void setCsDiscountAmount(BigDecimal csDiscountAmount) {
		this.csDiscountAmount = csDiscountAmount;
	}

	public BigDecimal getShopDisAmount() {
		return shopDisAmount;
	}

	public void setShopDisAmount(BigDecimal shopDisAmount) {
		this.shopDisAmount = shopDisAmount;
	}

	public String getModel() {
		return model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	private Long itemId;
	private int saleNum;
	private int itemType;

	@ApiAnno(groups={InquiryList.class})
	private String ticketNo;

	public Integer getDownInvoiceLoad() {
		return downInvoiceLoad;
	}

	public void setDownInvoiceLoad(Integer downInvoiceLoad) {
		this.downInvoiceLoad = downInvoiceLoad;
	}

	public int getItemType() {
		return itemType;
	}

	public void setItemType(int itemType) {
		this.itemType = itemType;
	}

	public int getSaleNum() {
		return saleNum;
	}

	public void setSaleNum(int saleNum) {
		this.saleNum = saleNum;
	}

	public Long getItemId() {
		return itemId;
	}

	public void setItemId(Long itemId) {
		this.itemId = itemId;
	}

	public BigDecimal getServiceAmount() {
		return serviceAmount;
	}

	public void setServiceAmount(BigDecimal serviceAmount) {
		this.serviceAmount = serviceAmount;
	}

	public BigDecimal getUrgentAmount() {
		return urgentAmount;
	}

	public void setUrgentAmount(BigDecimal urgentAmount) {
		this.urgentAmount = urgentAmount;
	}

	public String getOptDeadlineTime() {
		return optDeadlineTime;
	}

	public void setOptDeadlineTime(String optDeadlineTime) {
		this.optDeadlineTime = optDeadlineTime;
	}

	public String getApplySubmitDate() {
		return applySubmitDate;
	}

	public void setApplySubmitDate(String applySubmitDate) {
		this.applySubmitDate = applySubmitDate;
	}

	public String getDeadlineTimeShow() {
		if(ValidationUtil.isEmpty(deadlineTime))
			return "";

		return UseDateUtil.getDeadTimeShow(deadlineTime);
	}

	public Integer getIsElectron() {
		return isElectron;
	}

	public void setIsElectron(Integer isElectron) {
		this.isElectron = isElectron;
	}

	public String getCompanyAddressEn() {
		return companyAddressEn;
	}

	public void setCompanyAddressEn(String companyAddressEn) {
		this.companyAddressEn = companyAddressEn;
	}

	public String getCompanyAddressCn() {
		return companyAddressCn;
	}

	public void setCompanyAddressCn(String companyAddressCn) {
		this.companyAddressCn = companyAddressCn;
	}

	public String getCompanyNameEn() {
		return companyNameEn;
	}

	public void setCompanyNameEn(String companyNameEn) {
		this.companyNameEn = companyNameEn;
	}

	public String getTown() {
		return town;
	}

	public void setTown(String town) {
		this.town = town;
	}

	public int getProState() {
		return proState;
	}

	public void setProState(int proState) {
		this.proState = proState;
	}

	public Integer getReceiptState() {
		return receiptState;
	}

	public void setReceiptState(Integer receiptState) {
		this.receiptState = receiptState;
	}

	public String getTicketIdFeed() {
		return ticketIdFeed;
	}

	public void setTicketIdFeed(String ticketIdFeed) {
		this.ticketIdFeed = ticketIdFeed;
	}

	public Integer getUserAgree() {
		return userAgree;
	}

	public void setUserAgree(Integer userAgree) {
		this.userAgree = userAgree;
	}


	public int getIsRead() {
		return isRead;
	}

	public void setIsRead(int isRead) {
		this.isRead = isRead;
	}


	public void setLabName(String labName){
		this.labName=labName;
	}
	public String getLabName(){
		return this.labName;
	}


	public BigDecimal getDiscountAmount() {
		return discountAmount;
	}

	public void setDiscountAmount(BigDecimal discountAmount) {
		this.discountAmount = discountAmount;
	}



	public String getCategoryPath() {
		return categoryPath;
	}

	public void setCategoryPath(String categoryPath) {
		this.categoryPath = categoryPath;
	}

	public List<UserAnswerDTO> getUserAnswerDTOList() {
		return userAnswerDTOList;
	}

	public void setUserAnswerDTOList(List<UserAnswerDTO> userAnswerDTOList) {
		this.userAnswerDTOList = userAnswerDTOList;
	}





	public int getHisState() {
		return hisState;
	}

	public void setHisState(int hisState) {
		this.hisState = hisState;
	}

	public void setBusinessLine(String businessLine) {
		this.businessLine = businessLine;
	}

	public String getBusinessLine() {
		return businessLine;
	}

	public OrderOperatorLogDTO getOrderOperatorLogDTO() {
		return orderOperatorLogDTO;
	}

	public void setOrderOperatorLogDTO(OrderOperatorLogDTO orderOperatorLogDTO) {
		this.orderOperatorLogDTO = orderOperatorLogDTO;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public String getRelateOrderNo() {
		return relateOrderNo;
	}

	public void setRelateOrderNo(String relateOrderNo) {
		this.relateOrderNo = relateOrderNo;
	}

	public int getTotalNums() {
		return totalNums;
	}

	public void setTotalNums(int totalNums) {
		this.totalNums = totalNums;
	}

	public int getSubState() {
		return subState;
	}

	public void setSubState(int subState) {
		this.subState = subState;
	}

	public String getProductImg() {
		return productImg;
	}

	public void setProductImg(String productImg) {
		this.productImg = productImg;
	}

	public String getReportForm() {
		return reportForm;
	}

	public void setReportForm(String reportForm) {
		this.reportForm = reportForm;
	}

	public String getReportLua() {
		return reportLua;
	}

	public void setReportLua(String reportLua) {
		this.reportLua = reportLua;
	}

	public int getPayState() {
		return payState;
	}

	public void setPayState(int payState) {
		this.payState = payState;
	}

	public BigDecimal getOrderAmount() {
		return orderAmount;
	}

	public void setOrderAmount(BigDecimal orderAmount) {
		this.orderAmount = orderAmount;
	}

	public List<OrderAttachmentDTO> getReportFileList() {
		return reportFileList;
	}

	public void setReportFileList(List<OrderAttachmentDTO> reportFileList) {
		this.reportFileList = reportFileList;
	}

	public int getIsDelete() {
		return isDelete;
	}

	public void setIsDelete(int isDelete) {
		this.isDelete = isDelete;
	}

	public int getIsPayReceived() {
		return isPayReceived;
	}

	public void setIsPayReceived(int isPayReceived) {
		this.isPayReceived = isPayReceived;
	}

	public int getIsInvoice() {
		return isInvoice;
	}

	public void setIsInvoice(int isInvoice) {
		this.isInvoice = isInvoice;
	}

	public int getCustomerReminderNum() {
		return customerReminderNum;
	}

	public void setCustomerReminderNum(int customerReminderNum) {
		this.customerReminderNum = customerReminderNum;
	}

	public String getKf5Desc() {
		return kf5Desc;
	}

	public void setKf5Desc(String kf5Desc) {
		this.kf5Desc = kf5Desc;
	}

	public List<Map> getReceiptLog() {
		return receiptLog;
	}

	public void setReceiptLog(List<Map> receiptLog) {
		this.receiptLog = receiptLog;
	}

	public String getDeadlineTime() {
		return deadlineTime;
	}

	public void setDeadlineTime(String deadlineTime) {
		this.deadlineTime = deadlineTime;
	}

	public String getSampleNameStr() {
		return sampleNameStr;
	}

	public void setSampleNameStr(String sampleNameStr) {
		this.sampleNameStr = sampleNameStr;
	}

	public String getItemName() {
		return itemName;
	}

	public void setItemName(String itemName) {
		this.itemName = itemName;
	}

	public String getToOrder() {
		return toOrder;
	}

	public void setToOrder(String toOrder) {
		this.toOrder = toOrder;
	}

	public Integer getPayMethod() {
		return payMethod;
	}

	public void setPayMethod(Integer payMethod) {
		this.payMethod = payMethod;
	}

	public int getRefundState() {
		return refundState;
	}

	public void setRefundState(int refundState) {
		this.refundState = refundState;
	}

	public String getCreateDate() {
		return createDate;
	}

	public void setCreateDate(String createDate) {
		this.createDate = createDate;
	}


	public String getCsEmail() {
		return csEmail;
	}

	public void setCsEmail(String csEmail) {
		this.csEmail = csEmail;
	}

	public int getLeadsState() {
		return leadsState;
	}

	public void setLeadsState(int leadsState) {
		this.leadsState = leadsState;
	}

	public int getWaitFlg() {
		return waitFlg;
	}

	public void setWaitFlg(int waitFlg) {
		this.waitFlg = waitFlg;
	}

	public String getTicketSource() {
		String orderType=getOrderType();
		if(OrderTypeEnum.OIQINQUIRY.getIndex().equals(orderType)){
			return "6";
		}

		return ticketSource;
	}

	public void setTicketSource(String ticketSource) {
		this.ticketSource = ticketSource;
	}

	public String getTicketSourceName() {
		String orderType=getOrderType();
		if(OrderTypeEnum.OIQINQUIRY.getIndex().equals(orderType)||OrderTypeEnum.OIQ_PORTAL_INQUIRY.getIndex().equals(orderType)){
			return "快速报价";
		}
		if("1".equals(ticketSource)){
			return "咨询报价";
		}
		return ticketSourceName;
	}

	public void setTicketSourceName(String ticketSourceName) {
		this.ticketSourceName = ticketSourceName;
	}

	@ApiAnno(groups={Default.class,OrderList.class,OrderDetail.class,TicList.class})
	private String refundStateShow;

	public String getRefundStateShow() {
		return OrderRefundStateEnum.getNameCh(refundState);
	}

	public List<SubOrderDTO> getSubOrderDTOList() {
		return subOrderDTOList;
	}

	public void setSubOrderDTOList(List<SubOrderDTO> subOrderDTOList) {
		this.subOrderDTOList = subOrderDTOList;
	}

	public List<OrderDetailDTO> getOrderDetailDTOList() {
		return orderDetailDTOList;
	}

	public void setOrderDetailDTOList(List<OrderDetailDTO> orderDetailDTOList) {
		this.orderDetailDTOList = orderDetailDTOList;
	}

	public List<OrderSampleDTO> getOrderSampleDTOList() {
		return orderSampleDTOList;
	}

	public void setOrderSampleDTOList(List<OrderSampleDTO> orderSampleDTOList) {
		this.orderSampleDTOList = orderSampleDTOList;
	}

	public BigDecimal getPlatformAmount() {
		return platformAmount;
	}

	public void setPlatformAmount(BigDecimal platformAmount) {
		this.platformAmount = platformAmount;
	}

	public String getConfirmOrderDate() {
		return confirmOrderDate;
	}

	public void setConfirmOrderDate(String confirmOrderDate) {
		this.confirmOrderDate = confirmOrderDate;
	}

	public int getInvoiceType() {
		return invoiceType;
	}

	public void setInvoiceType(int invoiceType) {
		this.invoiceType = invoiceType;
	}



	public int getMonthPay() {
		return monthPay;
	}

	public void setMonthPay(int monthPay) {
		this.monthPay = monthPay;
	}

	public String getStoreId() {
		return storeId;
	}

	public void setStoreId(String storeId) {
		this.storeId = storeId;
	}

	public String getStoreName() {
		return StoreEnum.getNameCh(storeId);
	}

	public void setStoreName(String storeName) {
		this.storeName = storeName;
	}

	public String getServiceItems() {
		return serviceItems;
	}

	public void setServiceItems(String serviceItems) {
		this.serviceItems = serviceItems;
	}

	public List<OrderProductDTO> getOrderProductDTOList() {
		return orderProductDTOList;
	}

	public void setOrderProductDTOList(List<OrderProductDTO> orderProductDTOList) {
		this.orderProductDTOList = orderProductDTOList;
	}

	public int getUseBbc() {
		return useBbc;
	}

	public void setUseBbc(int useBbc) {
		this.useBbc = useBbc;
	}

	public String getMonthPayShow() {
		if(monthPay==1){
			return "月结 ：已付款";
		}
		if(monthPay==3){
			return "月结：待付款";
		}
		return "";
	}

	public String getBbcOrderId() {
		return bbcOrderId;
	}

	public void setBbcOrderId(String bbcOrderId) {
		this.bbcOrderId = bbcOrderId;
	}

	public String getStateDate() {
		return stateDate;
	}

	public void setStateDate(String stateDate) {
		this.stateDate = stateDate;
	}

	public int getDownLoad() {
		return downLoad;
	}

	public void setDownLoad(int downLoad) {
		this.downLoad = downLoad;
	}

	public String getDownLoadShow() {
		return DownReportEnum.getNameCh(downLoad);
	}

	public void setDownLoadShow(String downLoadShow) {
		this.downLoadShow = downLoadShow;
	}

	public BigDecimal getRstsAmount() {
		return rstsAmount;
	}

	public void setRstsAmount(BigDecimal rstsAmount) {
		this.rstsAmount = rstsAmount;
	}

	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public int getUserLabelFlg() {
		return userLabelFlg;
	}

	public void setUserLabelFlg(int userLabelFlg) {
		this.userLabelFlg = userLabelFlg;
	}

	public int getIndustryField() {
		return industryField;
	}

	public void setIndustryField(int industryField) {
		this.industryField = industryField;
	}

	public String getSampleCode() {
		return sampleCode;
	}

	public void setSampleCode(String sampleCode) {
		this.sampleCode = sampleCode;
	}

	public String getSampleId() {
		return sampleId;
	}

	public void setSampleId(String sampleId) {
		this.sampleId = sampleId;
	}

	public List<OrderSampleStrDTO> getOrderSampleStrDTOList() {
		return orderSampleStrDTOList;
	}

	public void setOrderSampleStrDTOList(List<OrderSampleStrDTO> orderSampleStrDTOList) {
		this.orderSampleStrDTOList = orderSampleStrDTOList;
	}

	public OrderProductDTO getOrderProductDTO() {
		return orderProductDTO;
	}

	public void setOrderProductDTO(OrderProductDTO orderProductDTO) {
		this.orderProductDTO = orderProductDTO;
	}

	public String getOrderDetailStr() {
		return orderDetailStr;
	}

	public void setOrderDetailStr(String orderDetailStr) {
		this.orderDetailStr = orderDetailStr;
	}

	public PortalInquiryBaseDTO getInquiryBaseDTO() {
		return inquiryBaseDTO;
	}

	public void setInquiryBaseDTO(PortalInquiryBaseDTO inquiryBaseDTO) {
		this.inquiryBaseDTO = inquiryBaseDTO;
	}

	public List<OrderAttachmentDTO> getQuotationFileList() {
		return quotationFileList;
	}

	public void setQuotationFileList(List<OrderAttachmentDTO> quotationFileList) {
		this.quotationFileList = quotationFileList;
	}

	public List<OrderAttachmentDTO> getInvoiceFileList() {
		return invoiceFileList;
	}

	public void setInvoiceFileList(List<OrderAttachmentDTO> invoiceFileList) {
		this.invoiceFileList = invoiceFileList;
	}

	public String getToPayAmount() {
		return toPayAmount;
	}

	public void setToPayAmount(String toPayAmount) {
		this.toPayAmount = toPayAmount;
	}

	public String getUserProd() {
		return userProd;
	}

	public void setUserProd(String userProd) {
		this.userProd = userProd;
	}

	public String getRequirementsInfo() {
		return requirementsInfo;
	}

	public void setRequirementsInfo(String requirementsInfo) {
		this.requirementsInfo = requirementsInfo;
	}

	public int getHaveForm() {
		return haveForm;
	}

	public void setHaveForm(int haveForm) {
		this.haveForm = haveForm;
	}

	public Integer getIsEvaluate() {
		return isEvaluate;
	}

	public void setIsEvaluate(Integer isEvaluate) {
		this.isEvaluate = isEvaluate;
	}

	public String getCurrencyMark() {
		return currencyMark;
	}

	public void setCurrencyMark(String currencyMark) {
		this.currencyMark = currencyMark;
	}

	public int getOrderDetailSize() {
		return orderDetailSize;
	}

	public void setOrderDetailSize(int orderDetailSize) {
		this.orderDetailSize = orderDetailSize;
	}

	public SubOrderInfoDTO getSubOrderInfoDTO() {
		return subOrderInfoDTO;
	}

	public void setSubOrderInfoDTO(SubOrderInfoDTO subOrderInfoDTO) {
		this.subOrderInfoDTO = subOrderInfoDTO;
	}

	public String getRecommendReason() {
		return recommendReason;
	}

	public void setRecommendReason(String recommendReason) {
		this.recommendReason = recommendReason;
	}

	public OrderReportDTO getReport() {
		return report;
	}

	public void setReport(OrderReportDTO report) {
		this.report = report;
	}

	public String getTicketNo() {
		return ticketNo;
	}

	public void setTicketNo(String ticketNo) {
		this.ticketNo = ticketNo;
	}


	public String getTicketType() {
		return ticketType;
	}

	public void setTicketType(String ticketType) {
		this.ticketType = ticketType;
	}

	public String getBarCodeUrl() {
		return barCodeUrl;
	}

	public void setBarCodeUrl(String barCodeUrl) {
		this.barCodeUrl = barCodeUrl;
	}

	public String getFileNo() {
		return fileNo;
	}

	public void setFileNo(String fileNo) {
		this.fileNo = fileNo;
	}

	public boolean isMultiType() {
		return multiType;
	}

	public void setMultiType(boolean multiType) {
		this.multiType = multiType;
	}

	public String getApplicationCompanyNameStr() {
		return applicationCompanyNameStr;
	}

	public void setApplicationCompanyNameStr(String applicationCompanyNameStr) {
		this.applicationCompanyNameStr = applicationCompanyNameStr;
	}

	public String getReportCompanyNameStr() {
		return reportCompanyNameStr;
	}

	public void setReportCompanyNameStr(String reportCompanyNameStr) {
		this.reportCompanyNameStr = reportCompanyNameStr;
	}

	public String getOutOrderNo() {
		return outOrderNo;
	}

	public void setOutOrderNo(String outOrderNo) {
		this.outOrderNo = outOrderNo;
	}

	public String getLadingNo() {
		return ladingNo;
	}

	public void setLadingNo(String ladingNo) {
		this.ladingNo = ladingNo;
	}

	public String getInvoiceTitle() {
		return invoiceTitle;
	}

	public void setInvoiceTitle(String invoiceTitle) {
		this.invoiceTitle = invoiceTitle;
	}

	public String getPlatformOrder() {
		return platformOrder;
	}

	public void setPlatformOrder(String platformOrder) {
		this.platformOrder = platformOrder;
	}
}