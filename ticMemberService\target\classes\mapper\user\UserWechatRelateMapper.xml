<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.user.dao.UserWechatRelateMapper" >

    <resultMap id="BaseResultMap" type="com.sgs.ecom.member.dto.user.UserWechatRelateDTO" >
        <result column="OPEN_ID" property="openId" jdbcType="VARCHAR" />
    </resultMap>

    <select id="selectLastByReq" resultMap="BaseResultMap"  >
        select
        OPEN_ID
        from USER_WECHAT_RELATE
        where USER_ID=#{userId} and APP_ID=#{appId} limit 1
    </select>

</mapper>