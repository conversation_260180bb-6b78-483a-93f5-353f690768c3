package com.sgs.ecom.member.controller.user;

import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.sgs.base.BaseBean;
import com.sgs.ecom.member.request.base.ImpReq;
import com.sgs.ecom.member.request.detail.OrderSampleReq;
import com.sgs.ecom.member.request.rsts.userSample.VOUserSampleBasic;
import com.sgs.ecom.member.request.user.UserSampleListReq;
import com.sgs.ecom.member.service.user.interfaces.IUserSampleService;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/business/api.v2.user/sample")
public class UserSampleController  extends ControllerUtil {

    @Autowired
    private IUserSampleService userSampleService;

    /**
    *@Function: impUserSample
    *@Description 导入样品
    *@param: [impReq, token]
    *@author: Xiwei_Qiu @date: 2021/10/11 @version:
    **/
    //@HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "impUserSample", method = { RequestMethod.POST })
    public ResultBody impUserSample(
            @RequestBody ImpReq impReq,
            @RequestHeader(value="accessToken") String token) throws Exception {
        userSampleService.impUserSample(impReq,getUserDTO(token));
        return ResultBody.success();
    }

    /**
     * @Description : 查询样品库列表数据-rsts-new
     * <AUTHOR> Zhang
     * @Date  2023/10/12
     * @param userSampleListReq:
     * @param token:
     * @return: com.sgs.ecom.member.util.ResultBody
     **/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "qryUserSampleListNew", method = { RequestMethod.POST })
    public ResultBody qryUserSampleListNew(
            @RequestBody UserSampleListReq userSampleListReq,
            @RequestHeader(value="accessToken") String token) throws Exception {
        return ResultBody.newInstance(userSampleService.qryUserSampleListNew(userSampleListReq,getUserDTO(token)));
    }




    /**
     * @Description :新增用户样品信息-new-rsts
     * <AUTHOR> Zhang
     * @Date  2023/10/12
     * @param token:
     * @param voUserSampleBasic:
     * @return: com.sgs.ecom.member.util.ResultBody
     **/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "addUserSample", method = { RequestMethod.POST })
    public ResultBody addUserSample(
            @RequestHeader(value="accessToken") String token,
            @Validated(BaseBean.Insert.class)
            @RequestBody VOUserSampleBasic voUserSampleBasic) throws Exception {
        voUserSampleBasic.setUserId(getUserDTO(token).getUserId());
        userSampleService.addUserSample(voUserSampleBasic);
        return ResultBody.success();
    }



    /**
    *@Function: modSample
    *@Description 修改样品
    *@param: [token, orderSampleReq]
    *@author: Xiwei_Qiu @date: 2021/10/11 @version:
    **/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "modSample", method = { RequestMethod.POST })
    public ResultBody modSample(
            @RequestHeader(value="accessToken") String token,
            @Validated(BaseBean.Insert.class)
            @RequestBody OrderSampleReq orderSampleReq) throws Exception {
        userSampleService.modSample(orderSampleReq,getUserDTO(token).getUserId());
        return ResultBody.success();
    }


    /**
    *@Function: delUserSample
    *@Description 删除用户样品
    *@param: [orderSampleReq, token]
    *@author: Xiwei_Qiu @date: 2021/10/11 @version:
    **/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "delUserSample", method = { RequestMethod.POST })
    public ResultBody delUserSample(
            @RequestBody OrderSampleReq orderSampleReq,
            @RequestHeader(value="accessToken") String token) throws Exception {
            userSampleService.delUserSample(orderSampleReq.getSampleId(),getUserDTO(token).getUserId());
        return ResultBody.success();
    }


}
