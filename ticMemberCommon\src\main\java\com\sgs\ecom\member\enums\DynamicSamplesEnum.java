package com.sgs.ecom.member.enums;
/**
 * @Description :查询动态样品配置的参数枚举
 * <AUTHOR>
 * @Date  2023/12/14
 **/
public enum DynamicSamplesEnum {

	LVTHREE_CPCH_EFFICACY("sampleFormDto", "CPCH-EFFICACY");

    // 成员变量
    private String name;
    private String index;
    // 构造方法
    private DynamicSamplesEnum(String name, String index) {
        this.name = name;  
        this.index = index;  
    }  

    // 普通方法  
    public static String getName(String index) {  
        for (DynamicSamplesEnum c : DynamicSamplesEnum.values()) {
            if (c.getIndex().equals(index)) {  
                return c.name;  
            }  
        }  
        return null;  
    }  
    // get set 方法  
    public String getName() {  
        return name;  
    }  

    public String getIndex() {  
        return index;  
    }  
}
