package com.sgs.ecom.member.dto.export;

import com.platform.annotation.ApiAnno;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.enumtool.OrderStateEnum;
import com.sgs.ecom.member.enumtool.pay.MonthPayEnum;
import org.apache.commons.lang.StringUtils;

public class ExpRSTSOrderDTO {
	private Long orderId;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String orderNo;				//订单号
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String createDate;			//创建时间
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String confirmOrderDate;	//提交时间
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String sampleInfo;			//样品
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String testItem;			//测试项目
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String orderState;			//订单状态
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String billingType;			//结算类型
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String payState	;		//支付状态
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String companyName;			//公司名称
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String currency;			//币种
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String orderAmount;			//订单金额
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String mainOrderNo;			//原订单号
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String sendSampleArea;		//送样地区
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String formPerson;			//申请方联系人
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String formPhone;			//申请方手机号
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String formMail;			//申请方邮箱

	public Long getOrderId() {
		return orderId;
	}

	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getCreateDate() {
		if(ValidationUtil.isEmpty(createDate))
			return "";

		String[] split = createDate.split("\\.");
		return split[0];

	}

	public void setCreateDate(String createDate) {
		this.createDate = createDate;
	}

	public String getConfirmOrderDate() {
		if(ValidationUtil.isEmpty(confirmOrderDate))
			return "";

		String[] split = confirmOrderDate.split("\\.");
		return split[0];
	}

	public void setConfirmOrderDate(String confirmOrderDate) {
		this.confirmOrderDate = confirmOrderDate;
	}

	public String getSampleInfo() {
		return sampleInfo;
	}

	public void setSampleInfo(String sampleInfo) {
		this.sampleInfo = sampleInfo;
	}

	public String getTestItem() {
		return testItem;
	}

	public void setTestItem(String testItem) {
		this.testItem = testItem;
	}

	public String getOrderState() {
		if(ValidationUtil.isEmpty(orderState))
			return "";


		return OrderStateEnum.getNameCh(orderState);
	}

	public void setOrderState(String orderState) {
		this.orderState = orderState;
	}

	public String getBillingType() {
		if(ValidationUtil.isEmpty(billingType))
			return "";


		return MonthPayEnum.getNameChByIndex(billingType);

	}

	public void setBillingType(String billingType) {
		this.billingType = billingType;
	}

	public String getPayState() {
		if(StringUtils.isBlank(payState)){
			return "未支付";
		}
		return "已支付";
	}

	public void setPayState(String payState) {
		this.payState = payState;
	}

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public String getCurrency() {
		if(ValidationUtil.isEmpty(currency))
			return "";

		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public String getOrderAmount() {
		return orderAmount;
	}

	public void setOrderAmount(String orderAmount) {
		this.orderAmount = orderAmount;
	}

	public String getMainOrderNo() {
		return mainOrderNo;
	}

	public void setMainOrderNo(String mainOrderNo) {
		this.mainOrderNo = mainOrderNo;
	}

	public String getSendSampleArea() {
		return sendSampleArea;
	}

	public void setSendSampleArea(String sendSampleArea) {
		this.sendSampleArea = sendSampleArea;
	}

	public String getFormPerson() {
		return formPerson;
	}

	public void setFormPerson(String formPerson) {
		this.formPerson = formPerson;
	}

	public String getFormPhone() {
		return formPhone;
	}

	public void setFormPhone(String formPhone) {
		this.formPhone = formPhone;
	}

	public String getFormMail() {
		return formMail;
	}

	public void setFormMail(String formMail) {
		this.formMail = formMail;
	}
}
