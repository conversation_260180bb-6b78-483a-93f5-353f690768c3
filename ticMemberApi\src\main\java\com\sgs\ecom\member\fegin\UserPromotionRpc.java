package com.sgs.ecom.member.fegin;

import com.alibaba.fastjson.JSON;
import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.ecom.member.service.user.interfaces.IUserPromotionSV;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import com.sgs.ecom.member.vo.VOUserInfo;
import com.sgs.ecom.member.vo.VOUserPromotion;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/business/rpc.v2.user/promotion")
@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class,SysException.class})
public class UserPromotionRpc extends ControllerUtil {

	@Autowired
	private IUserPromotionSV userPromotionSV;
	
	/**   
	* @Function: upgrade
	* @Description: 活动升级
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2018-11-23
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2018-11-23 shenyi    v1.0                 新增
	*/

	@HystrixCommand
    @AuthRequired(login = "SSO", sign = true)	
    @RequestMapping(value = "upgrade", method = { RequestMethod.POST })
    public ResultBody upgrade(@RequestBody VOUserPromotion userPromotion,
    	@RequestHeader(value="appId") String appId,
    	@RequestHeader(value="accessToken") String token) throws Exception {
		String userStr = getUser(token);
		
    	VOUserInfo user = JSON.parseObject(userStr, VOUserInfo.class);
    	userPromotion.setUserId(user.getUserId());
    	userPromotion.setUserPhone(user.getUserPhone());
		userPromotionSV.upgrade(userPromotion);
		return ResultBody.success();
	}
	
	/**   
	* @Function: upgradeByPhone
	* @Description: 活动升级
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2018-11-23
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2018-11-23 shenyi    v1.0                 新增
	*/

	@HystrixCommand
    @AuthRequired(login = "NULL", sign = true)	
    @RequestMapping(value = "uplevel", method = { RequestMethod.POST })
    public ResultBody upgradeByPhone(@RequestBody VOUserPromotion userPromotion) throws Exception {
		userPromotionSV.upgrade(userPromotion);
		return ResultBody.success();
	}





	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "qryPro", method = { RequestMethod.POST })
	public ResultBody qryPro(@RequestHeader(value="accessToken") String token) throws Exception {

		return ResultBody.newInstance(userPromotionSV.qryPro(getUserDTO(token)));
	}



}
