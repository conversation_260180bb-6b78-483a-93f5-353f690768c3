<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.order.dao.CustomExpMapper">

    <resultMap id="ticExp" type="com.sgs.ecom.member.dto.export.ExpTicOrderDTO">
        <result column="NOW_TIME" property="nowTime" jdbcType="INTEGER"/>
        <result column="STATE" property="state" jdbcType="INTEGER"/>
        <result column="SUB_STATE" property="subState" jdbcType="INTEGER"/>
        <result column="REFUND_STATE" property="refundState" jdbcType="INTEGER"/>
        <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR"/>
        <result column="CREATE_DATE" property="createDate" jdbcType="VARCHAR"/>
        <result column="FINISH_DATE" property="finishDate" jdbcType="VARCHAR"/>
        <result column="PRODUCT_NAME" property="productName" jdbcType="VARCHAR"/>
        <result column="SKU" property="skuAttr" jdbcType="VARCHAR"/>
        <result column="TEST_ITEM" property="testItem" jdbcType="VARCHAR"/>
        <result column="PRODUCT_MEMO" property="productMemo" jdbcType="VARCHAR"/>
        <result column="BU" property="bu" jdbcType="VARCHAR"/>
        <result column="PRICE" property="price" jdbcType="VARCHAR"/>
        <result column="QUANTITY" property="quantity" jdbcType="VARCHAR"/>
        <result column="ORDER_REAL_AMOUNT" property="orderRealAmount" jdbcType="DECIMAL"/>
        <result column="PAY_METHOD" property="payMethod" jdbcType="VARCHAR"/>
        <result column="MONTH_PAY" property="monthPay" jdbcType="VARCHAR"/>
        <result column="SAMPLE_NAME" property="sampleName" jdbcType="VARCHAR"/>
        <result column="NUMBER" property="number" jdbcType="VARCHAR"/>
        <result column="LINK_PERSON" property="formUserName" jdbcType="VARCHAR"/>
        <result column="LINK_PHONE" property="formUserPhone" jdbcType="VARCHAR"/>
        <result column="LINK_EMAIL" property="formUserEmail" jdbcType="VARCHAR"/>
        <result column="company_NAME_CN" property="companyNameCn" jdbcType="VARCHAR"/>
        <result column="company_NAME_EN" property="companyNameEn" jdbcType="VARCHAR"/>
        <result column="COMPANY_ADDRESS_CN" property="companyAddressCn" jdbcType="VARCHAR"/>
        <result column="INVOICE_TYPE" property="invoiceType" jdbcType="VARCHAR"/>
        <result column="INVOICE_TITLE" property="invoiceTitle" jdbcType="VARCHAR"/>
        <result column="TAX_NO" property="taxNo" jdbcType="VARCHAR"/>
        <result column="BANK_ADDR" property="bankAddr" jdbcType="VARCHAR"/>
        <result column="BANK_NUMBER" property="bankNumber" jdbcType="VARCHAR"/>
        <result column="REGISTER_ADDR" property="registerAddr" jdbcType="VARCHAR"/>
        <result column="REGISTER_PHONE" property="registerPhone" jdbcType="VARCHAR"/>
        <result column="DELIVERY_NAME" property="invoiceSendPerson" jdbcType="VARCHAR"/>
        <result column="DELIVERY_PHONE" property="invoiceSendPhone" jdbcType="VARCHAR"/>
        <result column="DELIVER_COMPANY" property="invoiceSendCompanyName" jdbcType="VARCHAR"/>
        <result column="DELIVERY_ADDR" property="invoiceSendAddress" jdbcType="VARCHAR"/>
        <result column="DELIVER_MAIL" property="invoiceSendEmail" jdbcType="VARCHAR"/>
        <result column="FOREIGN_COUNTRY" property="foreignCountry" jdbcType="VARCHAR"/>
        <result column="FOREIGN_ADDR" property="foreignAddr" jdbcType="VARCHAR"/>
        <result column="FOREIGN_CONCAT" property="foreignConcat" jdbcType="VARCHAR"/>
        <result column="PAYER_NAME" property="payerName" jdbcType="VARCHAR" />
        <result column="PAYER_PHONE" property="payerPhone" jdbcType="VARCHAR" />
        <result column="PAYER_EMAIL" property="payerEmail" jdbcType="VARCHAR" />
        <result column="ORDER_AMOUNT" property="orderAmount" jdbcType="DECIMAL" />
        <result column="STORE_AMOUNT" property="storeAmount" jdbcType="DECIMAL" />
        <result column="COUPON_AMOUNT" property="couponAmount" jdbcType="DECIMAL" />
        <result column="URGENT_NAME" property="urgentName" jdbcType="VARCHAR" />
        <result column="SERVICE_AMOUNT" property="serviceAmount" jdbcType="DECIMAL" />
        <result column="REAL_AMOUNT" property="realAmount" jdbcType="DECIMAL" />
    </resultMap>

    <resultMap id="subTicExp" type="com.sgs.ecom.member.dto.export.ExpTicSubOrderDTO">
        <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR"/>
        <result column="RELATE_ORDER_NO" property="relateOrderNo" jdbcType="VARCHAR"/>
        <result column="STATE" property="state" jdbcType="INTEGER"/>
        <result column="PAY_METHOD" property="payMethod" jdbcType="VARCHAR"/>
        <result column="ACCOUNT_NAME" property="accountName" jdbcType="VARCHAR"/>
        <result column="BUYER_INFO" property="buyerInfo" jdbcType="VARCHAR"/>
        <result column="PAYMENT_NO" property="paymentNo" jdbcType="VARCHAR"/>
        <result column="TRANS_NO" property="transNo" jdbcType="VARCHAR"/>
        <result column="PAY_PRICE" property="refundAmount" jdbcType="DECIMAL"/>
        <result column="REFUND_INFO" property="refundReason" jdbcType="VARCHAR"/>
        <result column="PAY_DATE" property="payDate" jdbcType="VARCHAR"/>
        <result column="ORDER_REAL_AMOUNT" property="orderRealAmount" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="baseQueryWhere">
        where 1=1
        <if test="orderId != null">
            AND obi.ORDER_ID=#{orderId}
        </if>
        <if test="isDelete != null and isDelete != -1">
            AND obi.IS_DELETE=#{isDelete}
        </if>
        <if test="orderNo != null">
            AND obi.ORDER_NO like concat('%',#{orderNo},'%')
        </if>
        <if test="relateOrderNo != null">
            AND obi.RELATE_ORDER_NO=#{relateOrderNo}
        </if>
        <if test="isUrgent != null">
            AND obi.IS_URGENT=#{isUrgent}
        </if>
        <if test="userId != null">
            AND obi.USER_ID=#{userId}
        </if>
        <if test="userPhone != null">
            AND obi.USER_PHONE=#{userPhone}
        </if>
        <if test="labId != null">
            AND obi.LAB_ID=#{labId}
        </if>
        <if test="state != null">
            AND obi.STATE=#{state}
        </if>
        <if test="stateNot != null">
            AND obi.STATE!=#{stateNot}
        </if>
        <if test="orderType != null">
            AND obi.ORDER_TYPE=#{orderType}
        </if>
        <if test="orderExpDateEnd != null">
            AND obi.ORDER_EXP_DATE &lt; now()
        </if>
        <if test="orderSendDate != null">
            AND DATE_ADD(obi.CREATE_DATE, INTERVAL 3 HOUR) &lt; now()
        </if>
        <if test="csEmail != null">
            AND obi.CS_EMAIL=#{csEmail}
        </if>
        <if test="csCode != null">
            AND obi.CS_CODE=#{csCode}
        </if>
        <if test="city != null">
            AND obi.CITY=#{city}
        </if>
        <if test="bu != null">
            AND obi.BU=#{bu}
        </if>
        <if test="province != null">
            AND obi.PROVINCE=#{province}
        </if>
        <if test="isRemind != null">
            AND obi.IS_REMIND=#{isRemind}
        </if>
        <if test="isTest != null">
            AND obi.IS_TEST=#{isTest}
        </if>
        <if test="isFinish != null">
            AND obi.IS_FINISH=#{isFinish}
        </if>
        <if test="isPayReceived != null">
            <if test="isPayReceived == 0">
                AND obi.IS_PAY_RECEIVED=0 AND obi.PAY_STATE=1
            </if>
            <if test="isPayReceived == 1">
                AND obi.IS_PAY_RECEIVED=1
            </if>

        </if>
        <if test="orderSource != null">
            AND obi.ORDER_SOURCE=#{orderSource}
        </if>
        <if test="isInvoice != null">
            AND obi.IS_INVOICE=#{isInvoice}
        </if>
        <if test="testLabel != null">
            AND obi.TEST_LABEL=#{testLabel}
        </if>
        <if test="payState != null">
            AND obi.PAY_STATE=#{payState}
        </if>
        <if test="subState != null">
            AND obi.SUB_STATE=#{subState}
        </if>
        <if test="operatorCode != null">
            AND obi.OPERATOR_CODE=#{operatorCode}
        </if>
        <if test="testLabel != null">
            AND obi.TEST_LABEL=#{testLabel}
        </if>
        <if test="closeCode != null">
            AND obi.CLOSE_CODE=#{closeCode}
        </if>
        <if test="expressNo != null">
            AND obi.ORDER_NO in (select ORDER_NO from ORDER_EXPRESS where EXPRESS_NO like concat('%',#{expressNo},'%'))
        </if>
        <if test="monthPay != null">
            AND obi.MONTH_PAY=#{monthPay}
        </if>
        <if test="monthMorePay != null">
            <if test="monthMorePay == 1">
                AND obi.MONTH_PAY in(1,3)
            </if>
            <if test="monthMorePay != 1">
                AND obi.MONTH_PAY=#{monthMorePay}
            </if>

        </if>
        <if test="ots != null">
            AND obi.ORDER_NO in (select ORDER_NO from ORDER_RELATE_EXTERNAL where EXTERNAL_NO like
            concat('%',#{ots},'%'))
        </if>
        <if test="transNo != null">
            AND obi.ORDER_NO in (select ORDER_NO from ORDER_PAY where TRANS_NO like concat('%',#{transNo},'%'))
        </if>
        <if test="paymentNo != null">
            AND obi.ORDER_NO in (select ORDER_NO from ORDER_PAY where PAYMENT_NO like concat('%',#{paymentNo},'%'))
        </if>
        <if test="itemName != null">
            AND obi.ORDER_NO in
            (select obit.ORDER_NO from ORDER_BASE_INFO obit,ORDER_DETAIL odt
            where obit.ORDER_NO=odt.ORDER_NO and obit.GROUP_NO=odt.GROUP_NO
            and ITEM_NAME like concat('%',#{itemName},'%') group by obit.ORDER_NO
            )
        </if>
        <if test="sampleName != null">
            AND obi.ORDER_NO in
            (select DISTINCT ORDER_NO from ORDER_SAMPLE where SAMPLE_NAME like concat('%',#{sampleName},'%') or
            SAMPLE_NAME_CN like concat('%',#{sampleName},'%') or SAMPLE_NAME_EN like concat('%',#{sampleName},'%'))
        </if>
        <if test="productName != null">
            AND obi.ORDER_NO in
            (
            select DISTINCT ORDER_NO from ORDER_PRODUCT where PRODUCT_NAME like concat('%',#{productName},'%')  and state = 1
            )
        </if>

        <if test="businessLineList != null ">
            AND obi.LINE_ID in
            <foreach collection="businessLineList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="useBusinessLineList != null ">
            AND obi.LINE_ID in
            <foreach collection="useBusinessLineList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="useProdList != null ">
            AND obi.ORDER_NO in (
            select DISTINCT ORDER_NO from ORDER_PRODUCT where PROD_ID in
            <foreach collection="useProdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and state = 1
            )
        </if>

        <if test="stateList != null ">
            AND obi.STATE in
            <foreach collection="stateList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="stateOrList != null ">
            AND
            <foreach collection="stateOrList" index="index" item="item" open="(" separator=" or " close=")">

                <if test="item == 51">
                    obi.PAY_STATE=2
                </if>
                <if test="item == 52">
                    obi.SUB_STATE=70
                </if>
                <if test="item == 53">
                    obi.REFUND_STATE=1
                </if>
                <if test="item == 54">
                    obi.REFUND_STATE=2
                </if>
                <if test="item != 51 and item != 52 and item != 53 and item != 54 ">
                    obi.STATE=#{item}
                </if>
            </foreach>
        </if>


        <if test="labIdList != null ">
            AND obi.LAB_ID in
            <foreach collection="labIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="usePrivilegeLevel != null">
            AND (obi.STATE=1 or obi.CS_CODE=#{usePrivilegeLevel})
        </if>

        <include refid="likeKey"/>
        <include refid="betweenKey"/>
    </sql>
    <sql id="likeKey">
        <if test="likeKey != null">
            <if test="likeKey.companyName!=null">
                AND obi.COMPANY_NAME like concat('%',#{likeKey.companyName},'%')
            </if>
            <if test="likeKey.userName!=null">
                AND obi.USER_NAME like concat('%',#{likeKey.userName},'%')
            </if>
            <if test="likeKey.csCodeLike!=null">
                AND obi.CS_CODE like concat('%',#{likeKey.csCodeLike},'%')
            </if>
            <if test="likeKey.userPhone!=null">
                AND obi.USER_PHONE like concat('%',#{likeKey.userPhone},'%')
            </if>
            <if test="likeKey.csEmail!=null">
                AND obi.CS_EMAIL like concat('%',#{likeKey.csEmail},'%')
            </if>
            <if test="likeKey.orderNo!=null">
                AND obi.ORDER_NO like concat('%',#{likeKey.orderNo},'%')
            </if>
            <if test="likeKey.orderSubNo!=null">
                AND
                (obi.ORDER_NO like concat('%',#{likeKey.orderSubNo},'%')
                or
                obi.ORDER_NO in (select RELATE_ORDER_NO FROM ORDER_BASE_INFO where ORDER_TYPE=211000 and ORDER_NO like
                concat('%',#{likeKey.orderSubNo},'%') )
                )
            </if>
            <if test="likeKey.ticSubNo!=null">
                AND
                (obi.ORDER_NO like concat('%',#{likeKey.ticSubNo},'%')
                or
                obi.ORDER_NO in (select RELATE_ORDER_NO FROM ORDER_BASE_INFO where ORDER_TYPE=101000 and ORDER_NO like
                concat('%',#{likeKey.ticSubNo},'%') )
                )
            </if>
            <if test="likeKey.auditCode!=null">
                AND obi.AUDIT_CODE like concat('%',#{likeKey.auditCode},'%')
            </if>
            <if test="likeKey.orderSourceFrom!=null">
                AND obi.ORDER_SOURCE_FROM like concat('%',#{likeKey.orderSourceFrom},'%')
            </if>
            <if test="likeKey.operatorCode != null">
                AND obi.OPERATOR_CODE like concat('%',#{likeKey.operatorCode},'%')
            </if>
            <if test="likeKey.categoryPath!=null">
                AND obi.CATEGORY_PATH like concat('%',#{likeKey.categoryPath},'%')
            </if>
            <if test="likeKey.fileName!=null">
                AND obi.ORDER_NO in (select DISTINCT ORDER_NO from ORDER_ATTACHMENT where (FILE_NAME like
                concat('%',#{likeKey.fileName},'%') ) )
            </if>
        </if>
    </sql>
    <sql id="betweenKey">
        <if test="betweenKey != null">
            <if test="betweenKey.createDateStart!=null and betweenKey.createDateEnd!=null">
                AND obi.CREATE_DATE between #{betweenKey.createDateStart} and #{betweenKey.createDateEnd}
            </if>
            <if test="betweenKey.realAmountStart!=null and betweenKey.realAmountEnd!=null">
                AND obi.REAL_AMOUNT between #{betweenKey.realAmountStart} and #{betweenKey.realAmountEnd}
            </if>
            <if test="betweenKey.stateDateStart!=null and betweenKey.stateDateEnd!=null">
                AND obi.STATE=4 AND obi.STATE_DATE between #{betweenKey.stateDateStart} and #{betweenKey.stateDateEnd}
            </if>
            <if test="betweenKey.offerDateStart!=null and betweenKey.offerDateEnd!=null">
                AND obi.OFFER_DATE between #{betweenKey.offerDateStart} and #{betweenKey.offerDateEnd}
            </if>
            <if test="betweenKey.applySubmitDateStart!=null and betweenKey.applySubmitDateEnd!=null">
                AND obi.APPLY_SUBMIT_DATE between #{betweenKey.applySubmitDateStart} and
                #{betweenKey.applySubmitDateEnd}
            </if>
            <if test="betweenKey.deadlineTimeStart!=null and betweenKey.deadlineTimeEnd!=null">
                AND obi.DEADLINE_TIME between #{betweenKey.deadlineTimeStart} and #{betweenKey.deadlineTimeEnd}
            </if>


        </if>
    </sql>

    <select id="selectTicOrderExp" resultMap="ticExp">
        select @i:= @i+ 1 AS `row` , ttt.* from
        (SELECT
        distinct
        oi.PAYER_NAME,
        oi.PAYER_PHONE,
        oi.PAYER_EMAIL,
        now( ) AS NOW_TIME,
        obi.STATE,
        obi.SUB_STATE,
        obi.ORDER_NO,
        obi.CREATE_DATE,
        obi.MONTH_PAY,
        obi.BU,
        IF
        ( obi.STATE = 80, obi.STATE_DATE, "" ) FINISH_DATE,
        op.PRODUCT_NAME,
        op.SKU,
        null as 'PRICE',
        null as 'QUANTITY',
        op.PRODUCT_MEMO,
        obi.REAL_AMOUNT AS ORDER_REAL_AMOUNT,
        obi.REAL_AMOUNT,
        IF(oaf.FORM_ID is null ,obi.USER_NAME,oaf.LINK_PERSON) LINK_PERSON,
        IF(oaf.FORM_ID is null ,obi.USER_PHONE,oaf.LINK_PHONE) LINK_PHONE,
        IF(oaf.FORM_ID is null ,obi.USER_EMAIL,oaf.LINK_EMAIL) LINK_EMAIL,
        IF(oaf.FORM_ID is null ,obi.COMPANY_NAME,oaf.COMPANY_NAME_CN) COMPANY_NAME_CN,
        IF(oaf.FORM_ID is null ,CONCAT_WS("",obi.PROVINCE,obi.CITY,obi.TOWN,obi.COMPANY_ADDRESS_CN),CONCAT_WS("",oaf.PROVINCE,oaf.CITY,oaf.TOWN,oaf.COMPANY_ADDRESS_CN)) COMPANY_ADDRESS_CN,
        IF(oaf.FORM_ID is null ,obi.COMPANY_ADDRESS_EN,oaf.COMPANY_ADDRESS_EN) COMPANY_ADDRESS_EN,
        IF(oaf.FORM_ID is null ,obi.company_NAME_EN,oaf.company_NAME_EN) COMPANY_NAME_EN,
        oi.INVOICE_TYPE,
        oi.INVOICE_TITLE,
        pay.PAY_METHOD,
        IF
        ( oi.INVOICE_TYPE != 2, oi.TAX_NO, "" ) TAX_NO,
        oi.BANK_ADDR,
        oi.BANK_NUMBER,
        oi.REGISTER_ADDR,
        IF
        ( oi.INVOICE_TYPE != 2, oi.REGISTER_PHONE, "" ) REGISTER_PHONE,
        oi.DELIVER_COMPANY,
        oi.DELIVERY_NAME,
        oi.DELIVERY_PHONE,
        oi.DELIVERY_ADDR,
        (CASE WHEN obi.ORDER_TYPE = 'INS' THEN  ''  ELSE  osfa.SAMPLE_VALUE  END) AS SAMPLE_NAME,
        (CASE WHEN obi.ORDER_TYPE = 'INS' THEN oaak2.ATTR_VALUE  ELSE  osfb.SAMPLE_VALUE  END) AS NUMBER,
        oi.DELIVER_MAIL,
        obi.ORDER_AMOUNT,
        obi.SHOP_DIS_AMOUNT as STORE_AMOUNT,obi.DISCOUNT_AMOUNT as  COUPON_AMOUNT,
        IFNULL(obi.URGENT_NAME,'普通') as 'URGENT_NAME',
        obi.SERVICE_AMOUNT
        FROM
        ORDER_BASE_INFO obi
        LEFT JOIN ORDER_INVOICE oi ON obi.ORDER_NO = oi.ORDER_NO AND oi.STATE = 1
        LEFT JOIN ORDER_APPLICATION_FORM oaf ON obi.ORDER_NO = oaf.ORDER_NO AND oaf.STATE = 1
        LEFT JOIN (	SELECT oaak.ATTR_VALUE,oaak.ORDER_NO FROM  ORDER_APPLICATION_ATTR oaak  where
        oaak.STATE = 1
        AND oaak.AREA_CODE = "inspectionInformation"
        AND oaak.ATTR_CODE = "number" GROUP BY oaak.ORDER_NO) oaak2 ON oaak2.ORDER_NO = obi.ORDER_NO
        LEFT JOIN (select osf.ORDER_NO,GROUP_CONCAT(osf.SAMPLE_VALUE )  as SAMPLE_VALUE from  ORDER_SAMPLE_FROM osf  where
        osf.STATE = 1
        AND (osf.SAMPLE_KEY ='sampleName' or osf.SAMPLE_KEY ='sampleDescription' or osf.SAMPLE_KEY ='sampleDescriptionEn' or osf.SAMPLE_KEY ='modelName' or osf.SAMPLE_KEY ='modelNameEn' OR osf.SAMPLE_KEY = 'sampleNameEn') GROUP BY osf.ORDER_NO)osfa ON osfa.ORDER_NO =  obi.ORDER_NO
        LEFT JOIN (select osf.ORDER_NO,GROUP_CONCAT(osf.SAMPLE_VALUE) as  SAMPLE_VALUE from  ORDER_SAMPLE_FROM osf  where
        osf.STATE = 1
        and (osf.SAMPLE_KEY = 'styleNo'  or osf.SAMPLE_KEY ='styleNoEn')   GROUP BY osf.ORDER_NO)osfb ON osfb.ORDER_NO =  obi.ORDER_NO
        LEFT JOIN ( SELECT ORDER_NO, PAY_METHOD, PAYMENT_NO, TRANS_NO, ACCOUNT_NAME, BUYER_INFO FROM ORDER_PAY WHERE state = 1 AND PAY_TYPE = 1 GROUP BY order_no ) pay ON pay.ORDER_NO = obi.ORDER_NO
        LEFT JOIN (
        select
        ORDER_NO,
        PRODUCT_NAME  as PRODUCT_NAME,
        SKU_ATTR as SKU,
        null  as PRICE,
        null  as QUANTITY,
        STATE,
        PRODUCT_MEMO  as PRODUCT_MEMO
        from
        ORDER_PRODUCT
        where STATE =1
        group by
        ORDER_NO  ) op ON obi.ORDER_NO = op.ORDER_NO and op.STATE =1

        <include refid="com.sgs.ecom.member.domain.order.dao.OrderBaseInfoMapper.baseQueryWhere"/>
        order by obi.ORDER_ID desc ) ttt ,(SELECT @i:=0) as ii
    </select>


    <select id="selectTicSubOrderExp" resultMap="subTicExp">
        SELECT obi.ORDER_NO,
               obi.RELATE_ORDER_NO,
               obi.STATE,
               pay.PAY_METHOD,
               pay.ACCOUNT_NAME,
               pay.BUYER_INFO,
               pay.PAYMENT_NO,
               pay.TRANS_NO,
               refundPay.PAY_PRICE,
               refundPay.REFUND_INFO,
               obi.PAY_DATE,
               obi.REAL_AMOUNT as ORDER_REAL_AMOUNT
        from ORDER_BASE_INFO obi
                 left join (select ORDER_NO, PAY_METHOD, PAYMENT_NO, TRANS_NO, ACCOUNT_NAME, BUYER_INFO
                            from ORDER_PAY
                            where state = 1
                            group by order_no) pay
                           on pay.ORDER_NO = obi.ORDER_NO
                 left join ORDER_PAY refundPay on obi.ORDER_NO = refundPay.ORDER_NO and refundPay.PAYMENT_ID in
                                                                                        (
                                                                                            select (CASE REFUND_INFO
                                                                                                        when REFUND_INFO = 91
                                                                                                            then payt2.maxId
                                                                                                        else payt1.minId end) as ID
                                                                                            from (select Min(PAYMENT_ID) minId, ORDER_NO, REFUND_INFO
                                                                                                  from ORDER_PAY
                                                                                                  where PAY_TYPE = 3
                                                                                                    and STATE = 1
                                                                                                  group by ORDER_NO) payt1
                                                                                                     left join
                                                                                                 (select ORDER_NO, MAX(PAYMENT_ID) maxId
                                                                                                  from ORDER_PAY
                                                                                                  where PAY_TYPE = 3
                                                                                                    and STATE = 1
                                                                                                    and REFUND_INFO = 91
                                                                                                  GROUP BY ORDER_NO) payt2
                                                                                                 on payt1.ORDER_NO = payt2.ORDER_NO
                                                                                        )
        where ORDER_TYPE = 101000
        <if test="userId != null">
            AND obi.USER_ID=#{userId}
        </if>
        order by obi.ORDER_ID desc
    </select>


    <resultMap id="customerExp" type="com.sgs.ecom.member.dto.export.ExpCustomerOrderDTO">
        <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR"/>
        <result column="BU" property="bu" jdbcType="VARCHAR"/>
        <result column="applicationCompanyNameCn" property="applicationCompanyNameCn" jdbcType="VARCHAR"/>
        <result column="applicationCompanyNameEn" property="applicationCompanyNameEn" jdbcType="VARCHAR"/>
        <result column="reportCompanyNameCn" property="reportCompanyNameCn" jdbcType="VARCHAR"/>
        <result column="reportCompanyNameEn" property="reportCompanyNameEn" jdbcType="VARCHAR"/>
        <result column="itemStr" property="orderDetailStr" jdbcType="VARCHAR"/>
        <result column="orderSampleStr" property="orderSampleStr" jdbcType="VARCHAR"/>
        <result column="STATE" property="state" jdbcType="INTEGER"/>
        <result column="reportNoStr" property="reportNoStr" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="selectCustomerOrderExp" resultMap="customerExp">
        select obi.ORDER_NO,obi.BU,
        (select COMPANY_NAME_CN from ORDER_APPLICATION_FORM oaf where oaf.order_no=obi.order_no limit 1) as applicationCompanyNameCn,
        (select COMPANY_NAME_En from ORDER_APPLICATION_FORM oaf where oaf.order_no=obi.order_no limit 1) as applicationCompanyNameEn,
        (select REPORT_COMPANY_NAME_CN from ORDER_REPORT oaf where oaf.order_no=obi.order_no limit 1) as reportCompanyNameCn,
        (select REPORT_COMPANY_NAME_EN from ORDER_REPORT oaf where oaf.order_no=obi.order_no limit 1) as reportCompanyNameEn,
        items.itemNameAll as itemStr,
        (select
        group_concat( case obii.c when "CHI" then
        if(bu=1900,(select max( CASE osff.SAMPLE_KEY WHEN 'SAMPLE_NUMBER' THEN osff.SAMPLE_VALUE ELSE '' end) sampleName
        from ORDER_SAMPLE_FROM osff where osff.state=1 and os.sample_no=osff.sample_no and osff.group_no=obi.group_no),os.sample_name_cn)
        when "EN" then
        if(bu=1900,(select max( CASE osff.SAMPLE_KEY WHEN 'SAMPLE_NUMBER_EN' THEN osff.SAMPLE_VALUE ELSE '' end) sampleNameEN
        from ORDER_SAMPLE_FROM osff where osff.state=1 and os.sample_no=osff.sample_no and osff.group_no=obi.group_no),os.sample_name_en)
        else concat( os.sample_name_cn,os.sample_name_en) end separator ';')
        from order_sample os ,
        (select case REPORT_LUA_CODE when "ZN" then "CHI" when "EN" then "EN" else "LUA" end as c,ORDER_NO,GROUP_NO  from order_Base_info ) as  obii
        where
        os.ORDER_NO=obii.ORDER_NO and os.GROUP_NO=obii.GROUP_NO
        and os.STATE=1
        AND obii.ORDER_NO=obi.order_no
        group by os.ORDER_NO
        order by os.SAMPLE_ID asc
        ) as orderSampleStr,
        obi.state,
        (select group_concat(oa.file_name separator ';' )  from order_attachment oa ,order_base_info obii where oa.order_no=obii.order_no and obii.pay_state=1 and obii.IS_PAY_RECEIVED=1 and
        oa.ATT_TYPE=10 and oa.order_no=obi.order_no and obii.order_type in (210001,210000)
        ) as reportNoStr
        from ORDER_BASE_INFO obi
        left join (
        <![CDATA[
        select obi.ORDER_NO,REGEXP_REPLACE( GROUP_CONCAT(ITEM_NAME separator ";"), '<[^>]+>', '' ) as itemNameAll
        ]]>
        from ORDER_DETAIL od,ORDER_BASE_INFO obi where od.GROUP_NO=obi.GROUP_NO
        and od.order_no=obi.ORDER_NO and obi.ORDER_TYPE in (210001,210000) and od.STATE = 1 and obi.bu!=2700
        <if test="useCustomerUserId != null">
            and obi.order_no in (
            select distinct ORDER_NO from (
            select order_no from ORDER_CUSTOMER oc where (OBJECT_TYPE,OBJECT_ID) in (SELECT "CUST",cust_id FROM tb_cust_apply_relate tcar
            where tcar.user_id=#{useCustomerUserId} and tcar.bu="901" and tcar.state=1) and oc.state=1
            union all
            select ORDER_NO  from order_customer oc where oc.OBJECT_TYPE="user" and oc.OBJECT_ID=#{useCustomerUserId} and oc.state=1) t
            ) and ((obi.state!=11 and obi.state!=91) or (obi.state=91 and obi.his_state!=11))
        </if>
        group by obi.ORDER_NO
        union all
        <![CDATA[
        select obi.ORDER_NO,REGEXP_REPLACE( GROUP_CONCAT(ITEM_NAME separator ";"), '<[^>]+>', '' ) as itemNameAll
         ]]>
        from order_base_info obi,order_base_info obi1,order_detail od
        where obi.bu=2700 and obi1.bu=2700 and obi.relate_order_no=obi1.order_no
        and obi1.order_no=od.order_no and obi1.group_no=od.group_no and obi.order_type=210001
        <if test="useCustomerUserId != null">
            and obi.order_no in (
            select distinct ORDER_NO from (
            select order_no from ORDER_CUSTOMER oc where (OBJECT_TYPE,OBJECT_ID) in (SELECT "CUST",cust_id FROM tb_cust_apply_relate tcar
            where tcar.user_id=#{useCustomerUserId} and tcar.bu="901" and tcar.state=1) and oc.state=1
            union all
            select ORDER_NO  from order_customer oc where oc.OBJECT_TYPE="user" and oc.OBJECT_ID=#{useCustomerUserId} and oc.state=1) t
            ) and ((obi.state!=11 and obi.state!=91) or (obi.state=91 and obi.his_state!=11))
        </if>
        group by obi.ORDER_NO
        ) items
        on obi.ORDER_NO=items.ORDER_NO
        <include refid="com.sgs.ecom.member.domain.order.dao.OrderBaseInfoMapper.baseNewQueryWhere"/>
        <include refid="com.sgs.ecom.member.domain.order.dao.OrderBaseInfoMapper.orderQueryWhere"/>
    </select>
</mapper>