package com.sgs.ecom.member.enumtool.bbc;

public enum StoreEnum {
	INSPECTION("INS", "消费品检测服务"),
	AFL("AFL", "食品与农产品服务"),
	CBE("CBE", "培训服务"),
	SL("SL", "纺织服装鞋包服务"),
	CPCH("CPCH", "美妆与日化服务"),
	NGO("NGO", "NGO服务"),
	HL("HL", "家居与百货服务"),
	EHS("EHS", "环境健康服务"),
	AKC("AKC", "爱库存商家品控服务"),
	MIN("MIN", "矿产检测服务"),
	EEC("EEC", "电子电气产品服务"),
	RSTS("RSTS", "限用物质测试服务"),
	TEST("TST", "测试"),
	EC("EC", "电商服务"),
	;

	StoreEnum(String name,  String nameCh) {
		this.name = name;
		this.nameCh = nameCh;
	}

	public static String getNameCh(String name) {
		for (StoreEnum c : StoreEnum.values()) {
			if (c.getName().equals(name)) {
				return c.getNameCh();
			}
		}
		return null;
	}

	public static StoreEnum getEnum(String index) {
		for (StoreEnum c : StoreEnum.values()) {
			if (c.getName().equals(index)) {
				return c;
			}
		}
		return null;
	}

	private String name;
	private String nameCh;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}


	public String getNameCh() {
		return nameCh;
	}

	public void setNameCh(String nameCh) {
		this.nameCh = nameCh;
	}


}
