package com.sgs.ecom.member.enums;

/**
 * 用户小工具字段类型枚举
 */
public enum UserToolKitAttType {

    /**
     * <PERSON><PERSON>(kilian_shen) 4-27 14:24:02
     * 产品名称 PRODUCT_NAME
     * 商标 TRADE_MARK
     * 号型规格 MODEL_TYPE
     * 款号 STYLE_NUM
     * 颜色 COLOR
     * 成分 COMPONENT
     * 产品标准编号 PRODUCT_STANDARD
     * 产品质量等级 PRODUCT_LEVEL
     * 安全技术要求类别 SECURITY_TYPE
     * 检验合格证明 QUALIFIED_CERT
     * 公司名称 COMPANY_NAME
     * 地址 COMPANY_ADDR
     * 产地 PRODUCTING_AREA @冯鹏程
     *
     * <PERSON><PERSON>(kilian_shen) 4-27 14:37:53
     * 洗涤图标 ICON
     * 洗涤提醒 NOTES
     */
    //小工具类型对应工具包编号
    MODEL_TYPE("号型规格", "MODEL_TYPE"),
    COMPONENT("成分", "COMPONENT"),
    PRODUCT_NAME("产品名称", "PRODUCT_NAME"),
    ICON("洗涤图标", "ICON");

    // 成员变量
    private String name;
    private String index;
    // 构造方法
    private UserToolKitAttType(String name, String index) {
        this.name = name;  
        this.index = index;  
    }  

    // 普通方法  
    public static String getName(String index) {  
        for (UserToolKitAttType c : UserToolKitAttType.values()) {
            if (c.getIndex().equals(index)) {  
                return c.name;  
            }  
        }  
        return null;  
    }  
    // get set 方法  
    public String getName() {  
        return name;  
    }  

    public String getIndex() {  
        return index;  
    }  
}
