package com.sgs.ecom.member.dto.question;

import com.sgs.ecom.member.dto.rpc.QuestionQryDtlDTO;
import com.sgs.ecom.member.request.OrderBaseAddReq;

public class SaveQuestionDTO {
	//String questionId,String replyNo, String catagoryId,
	// String orderNo, Long userId,int isOriginal,String categoryPath
	private String questionId;
	private String replyNo;
	private String categoryId;
	private String orderNo;
	private Long lineId;
	private String businessLine;
	private Long userId;
	private int isOriginal;
	private String categoryPath;

	public SaveQuestionDTO() {
	}

	public SaveQuestionDTO(OrderBaseAddReq orderBaseAddReq, QuestionQryDtlDTO questionQryDtlDTO, String orderNo, int isOriginal, Long userId) {
		this.questionId = orderBaseAddReq.getQuestionId();
		this.replyNo = orderBaseAddReq.getReplyNo();
		this.categoryId = orderBaseAddReq.getCatagoryId();
		this.orderNo = orderNo;
		this.userId = userId;
		this.isOriginal = isOriginal;
		this.categoryPath = orderBaseAddReq.getCategoryPath();
		this.lineId=Long.parseLong(questionQryDtlDTO.getItemId());
		this.businessLine=questionQryDtlDTO.getItemName();
	}

	public String getQuestionId() {
		return questionId;
	}

	public void setQuestionId(String questionId) {
		this.questionId = questionId;
	}

	public String getReplyNo() {
		return replyNo;
	}

	public void setReplyNo(String replyNo) {
		this.replyNo = replyNo;
	}

	public String getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(String categoryId) {
		this.categoryId = categoryId;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public int getIsOriginal() {
		return isOriginal;
	}

	public void setIsOriginal(int isOriginal) {
		this.isOriginal = isOriginal;
	}

	public String getCategoryPath() {
		return categoryPath;
	}

	public void setCategoryPath(String categoryPath) {
		this.categoryPath = categoryPath;
	}

	public Long getLineId() {
		return lineId;
	}

	public void setLineId(Long lineId) {
		this.lineId = lineId;
	}

	public String getBusinessLine() {
		return businessLine;
	}

	public void setBusinessLine(String businessLine) {
		this.businessLine = businessLine;
	}
}
