package com.sgs.ecom.member.enumtool.bbc.StoreSon;

public enum StoreSLEnum {
	DEFAULT("SL-DEFAULT", "SL-DEFAULT"),
	QD("SL-QD", "SL-QD"),
	SH_KZ("SL-SH-KZ", "SL-SH-KZ"),
	HZ_REGULAR("SL-HZ-WB-REGULAR", "SL-HZ-WB-REGULAR"),
	PX("SL-PX", "SL-PX"),
	PPE("SL-PPE", "SL-PPE"),
	CHMICAL("SL-HZ-WB-CHMICAL", "SL-HZ-WB-CHMICAL"),
	<PERSON>("CBE-DEFAULT", "CBE-DEFAULT"),
	CBE_REGULAR("CBE-REGULAR", "CBE-REGULAR"),
	HOTEL_EHS("EHS-HOTEL", "EHS-HOTEL"),
	EHS_JINJIANGSTAR("EHS-JINJIANGSTAR", "EHS-JINJIANGSTAR"),
	RSTS_CERTIFICATE("RSTS-CERTIFICATE", "RSTS-CERTIFICATE"),
	RSTS_SDS("RSTS-SDS", "RSTS-SDS"),
	EHS_REGULAR("EHS-REGULAR", "EHS-REGULAR"),
	EC_PX("EC-PX", "EC-PX"),
	ALL("ALL", "ALL"),//默认
	;

	StoreSLEnum(String name, String nameCh) {
		this.name = name;
		this.nameCh = nameCh;
	}

	public static String getNameCh(String name) {
		for (StoreSLEnum c : StoreSLEnum.values()) {
			if (c.getName().equals(name)) {
				return c.getNameCh();
			}
		}
		return null;
	}

	public static StoreSLEnum getEnum(String index) {
		for (StoreSLEnum c : StoreSLEnum.values()) {
			if (c.getName().equals(index)) {
				return c;
			}
		}
		return DEFAULT;
	}

	private String name;
	private String nameCh;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}


	public String getNameCh() {
		return nameCh;
	}

	public void setNameCh(String nameCh) {
		this.nameCh = nameCh;
	}


}
