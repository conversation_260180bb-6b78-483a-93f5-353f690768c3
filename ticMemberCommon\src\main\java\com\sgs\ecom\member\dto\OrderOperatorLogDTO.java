package com.sgs.ecom.member.dto; 
 
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.StringJoiner;

import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno; 
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.enumtool.OrderOperatorTypeEnum;
import com.sgs.ecom.member.enumtool.OrderTypeEnum;
import com.sgs.ecom.member.enumtool.pay.OiqCurrencyEnum;
import org.apache.commons.lang.StringUtils;

public class OrderOperatorLogDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select CS_CODE,LOG_ID,OPERATOR_DATE,MEMO from ORDER_OPERATOR_LOG"; 
 
 
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="CS_CODE", getName="getCsCode", setName="setCsCode")
 	private String csCode;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="LOG_ID", getName="getLogId", setName="setLogId")
 	private long logId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="OPERATOR_DATE", getName="getOperatorDate", setName="setOperatorDate")
 	private String operatorDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="MEMO", getName="getMemo", setName="setMemo")
 	private String memo;
	@ApiAnno(groups={Default.class})
 	private String operatorType;
	@ApiAnno(groups={Default.class})
	private String orderType;
	@ApiAnno(groups={Default.class})
	private String operatorTypeShow;
	@ApiAnno(groups={Default.class})
	private String operatorText;
	@ApiAnno(groups={Default.class})
	private int flag;


	@ApiAnno(groups={Default.class})
	private BigDecimal originalPrice;
	@ApiAnno(groups={Default.class})
	private BigDecimal currentPrice;
	@ApiAnno(groups={Default.class})
	private String differPrice;
	@ApiAnno(groups={Default.class})
	private String isShow;
	@ApiAnno(groups={Default.class})
	private String  fileUrl;
	@ApiAnno(groups={Default.class})
	private int isComfirm;
	@ApiAnno(groups={Default.class})
	private String currency;

	private String orderNo;
	private String detailNo;




	public String getIsShow() {
		return isShow;
	}

	public void setIsShow(String isShow) {
		this.isShow = isShow;
	}

	public void setCsCode(String csCode){
 		 this.csCode=csCode;
 	}
 	public String getCsCode(){
 		 return this.csCode;
 	}
 
 	 
 	public void setLogId(long logId){
 		 this.logId=logId;
 	}
 	public long getLogId(){
 		 return this.logId;
 	}


	public String getOperatorDate() {
		String[] str = operatorDate.split("\\.");
		String orderCreateDate=str[0];
		return orderCreateDate;
	}

	public void setOperatorDate(String operatorDate) {
		this.operatorDate = operatorDate;
	}

	public void setMemo(String memo){
 		 this.memo=memo;
 	}
 	public String getMemo(){
 		 return this.memo;
 	}

	public String getOperatorType() {
		return operatorType;
	}

	public void setOperatorType(String operatorType) {
		this.operatorType = operatorType;
	}



	public void setOperatorText(String operatorText) {
		this.operatorText = operatorText;
	}

	public BigDecimal getOriginalPrice() {
		return originalPrice;
	}

	public void setOriginalPrice(BigDecimal originalPrice) {
		this.originalPrice = originalPrice;
	}

	public BigDecimal getCurrentPrice() {
		return currentPrice;
	}

	public void setCurrentPrice(BigDecimal currentPrice) {
		this.currentPrice = currentPrice;
	}

	public String getFileUrl() {
		return fileUrl;
	}

	public void setFileUrl(String fileUrl) {
		this.fileUrl = fileUrl;
	}

	public String getDifferPrice() {
		String str="";
		if(OrderTypeEnum.isOiqOrder(orderType)){
			if(originalPrice!=null && currentPrice!=null){
				DecimalFormat decimalFormat = new DecimalFormat("0.00");
				StringJoiner s =new StringJoiner("");
				Boolean add=originalPrice.doubleValue()< currentPrice.doubleValue();
				s.add(add?"+":"-");
				s.add(OiqCurrencyEnum.getKeyShow(currency));
				s.add(add?decimalFormat.format(currentPrice.subtract(originalPrice)):decimalFormat.format(originalPrice.subtract(currentPrice)));
				str=s.toString();
			}
			return str;

		}
		//旧代码
		BigDecimal  differPrice=null;
		if(originalPrice!=null && currentPrice!=null){
			differPrice=currentPrice.subtract(originalPrice);
			DecimalFormat decimalFormat = new DecimalFormat("0.00");
			str = decimalFormat.format(differPrice);
		}
		return str;
	}

	public int getFlag() {
		return flag;
	}

	public void setFlag(int flag) {
		this.flag = flag;
	}

	public String getOperatorTypeShow() {
		return OrderOperatorTypeEnum.getNameCh(operatorType);
	}

	public String getOperatorText() {
		if(flag==1){
			return operatorText;
		}
		String addStr= OrderOperatorTypeEnum.getOperatorAdd(operatorType);
		if(StringUtils.isNotBlank(addStr)){
			if(StringUtils.isNotBlank(operatorText)){
				return addStr+operatorText;
			}
			return addStr;
		}
		return operatorText;
	}

	@ApiAnno(groups={Default.class})
	private String operatorTextMore;

	public String getOperatorTextMore() {
		this.operatorTextMore= operatorText;
		return operatorTextMore;
	}

	public int getIsComfirm() {
		return isComfirm;
	}

	public void setIsComfirm(int isComfirm) {
		this.isComfirm = isComfirm;
	}

	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getDetailNo() {
		return detailNo;
	}

	public void setDetailNo(String detailNo) {
		this.detailNo = detailNo;
	}

	public String getOrderType() {
		return orderType;
	}

	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}
}