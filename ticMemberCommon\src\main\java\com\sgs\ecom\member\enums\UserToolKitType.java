package com.sgs.ecom.member.enums;

/**
 * 用户小工具类型枚举
 */
public enum UserToolKitType {

    //小工具类型对应工具包编号
    CERTIFICATE("合格证", "SL_TOOLKIT001"),
    CARE_LABEL("水洗标", "SL_TOOLKIT002"),
    EU_CARE_LABEL("欧标水洗标", "SL_TOOLKIT005"),
    NA_CARE_LABEL("美标水洗标", "SL_TOOLKIT006"),
    REPORT_AUTHENTICITY("报告验真", "SL_TOOLKIT007");
    // 成员变量
    private String name;
    private String index;
    // 构造方法
    private UserToolKitType(String name, String index) {
        this.name = name;  
        this.index = index;  
    }  

    // 普通方法  
    public static String getName(String index) {  
        for (UserToolKitType c : UserToolKitType.values()) {
            if (c.getIndex().equals(index)) {  
                return c.name;  
            }  
        }  
        return null;  
    }  
    // get set 方法  
    public String getName() {  
        return name;  
    }  

    public String getIndex() {  
        return index;  
    }  
}
