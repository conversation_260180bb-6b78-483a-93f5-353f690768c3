<?xml version="1.0" encoding="UTF-8"?><project-modules id="moduleCoreId" project-version="1.5.0">
    <wb-module deploy-name="ticMember">
        <wb-resource deploy-path="/" source-path="/target/m2e-wtp/web-resources"/>
        <wb-resource deploy-path="/" source-path="/src/main/webapp" tag="defaultRootSource"/>
        <wb-resource deploy-path="/WEB-INF/classes" source-path="/src/main/java"/>
        <wb-resource deploy-path="/WEB-INF/classes" source-path="/src/main/resources"/>
        <dependent-module archiveName="ticMemberSys.jar" deploy-path="/WEB-INF/lib" handle="module:/resource/ticMemberSys/ticMemberSys">
            <dependency-type>uses</dependency-type>
        </dependent-module>
        <dependent-module archiveName="ticMemberCommon.jar" deploy-path="/WEB-INF/lib" handle="module:/resource/ticMemberCommon/ticMemberCommon">
            <dependency-type>uses</dependency-type>
        </dependent-module>
        <dependent-module archiveName="ticMemberApi.war" deploy-path="/WEB-INF/lib" handle="module:/resource/ticMemberApi/ticMemberApi">
            <dependency-type>uses</dependency-type>
        </dependent-module>
        <property name="context-root" value="ticMember"/>
        <property name="java-output-path" value="/ticMember/target/classes"/>
    </wb-module>
</project-modules>
