package com.sgs.ecom.member.dto; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import java.util.Date; 
import com.platform.util.json.DateFormatSerializer; 
import com.platform.util.json.PriceFormatSerializer; 
import com.platform.annotation.ApiAnno; 
import com.platform.annotation.BeanAnno;
import com.platform.annotation.IsNullAnno; 

public class AcctBalanceDTO{
 
 	public static final String CREATE_SQL = "select AREA_ID,EXP_DATE,FREEZE_AMOUNT,STATE_DATE,USER_ID,STATE,BALANCE,CREATE_DATE,BALANCE_TYPE,BALANCE_ID,SKU_ID,USER_TYPE,STORE_ID,EFF_DATE from ACCT_BALANCE"; 
 
 
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="AREA_ID", getName="getAreaId", setName="setAreaId")
 	private long areaId;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="EXP_DATE", getName="getExpDate", setName="setExpDate")
 	private Date expDate;
 	@ApiAnno(serviceName={"qryAcctBalanceByType"})
 	@BeanAnno(value="FREEZE_AMOUNT", getName="getFreezeAmount", setName="setFreezeAmount")
 	private long freezeAmount;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private Timestamp stateDate;
 	@IsNullAnno(serviceName={"qryAcctBalanceByType"})
 	@ApiAnno(serviceName={"qryAcctBalanceByType"})
 	@BeanAnno(value="USER_ID", getName="getUserId", setName="setUserId")
 	private long userId;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private long state;
 	@ApiAnno(serviceName={"qryAcctBalanceByType"})
 	@BeanAnno(value="BALANCE", getName="getBalance", setName="setBalance")
 	private long balance;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@IsNullAnno(serviceName={"qryAcctBalanceByType"})
 	@ApiAnno(serviceName={"qryAcctBalanceByType"})
 	@BeanAnno(value="BALANCE_TYPE", getName="getBalanceType", setName="setBalanceType")
 	private long balanceType;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="BALANCE_ID", getName="getBalanceId", setName="setBalanceId")
 	private long balanceId;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="SKU_ID", getName="getSkuId", setName="setSkuId")
 	private long skuId;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="USER_TYPE", getName="getUserType", setName="setUserType")
 	private int userType;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STORE_ID", getName="getStoreId", setName="setStoreId")
 	private long storeId;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="EFF_DATE", getName="getEffDate", setName="setEffDate")
 	private Date effDate;

 	public void setAreaId(long areaId){
 		 this.areaId=areaId;
 	}
 	public long getAreaId(){
 		 return this.areaId;
 	}
 
 	 
 	public void setExpDate(Date expDate){
 		 this.expDate=expDate;
 	}
 	@JsonSerialize(using = DateFormatSerializer.class) 
 	public Date getExpDate(){
 		 return this.expDate;
 	}
 
 	 
 	public void setFreezeAmount(long freezeAmount){
 		 this.freezeAmount=freezeAmount;
 	}
 	@JsonSerialize(using = PriceFormatSerializer.class) 
 	public long getFreezeAmount(){
 		 return this.freezeAmount;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setUserId(long userId){
 		 this.userId=userId;
 	}
 	public long getUserId(){
 		 return this.userId;
 	}
 
 	 
 	public void setState(long state){
 		 this.state=state;
 	}
 	public long getState(){
 		 return this.state;
 	}
 
 	 
 	public void setBalance(long balance){
 		 this.balance=balance;
 	}
 	public long getBalance(){
 		 return this.balance;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setBalanceType(long balanceType){
 		 this.balanceType=balanceType;
 	}
 	public long getBalanceType(){
 		 return this.balanceType;
 	}
 
 	 
 	public void setBalanceId(long balanceId){
 		 this.balanceId=balanceId;
 	}
 	public long getBalanceId(){
 		 return this.balanceId;
 	}
 
 	 
 	public void setSkuId(long skuId){
 		 this.skuId=skuId;
 	}
 	public long getSkuId(){
 		 return this.skuId;
 	}
 
 	 
 	public void setUserType(int userType){
 		 this.userType=userType;
 	}
 	public int getUserType(){
 		 return this.userType;
 	}
 
 	 
 	public void setStoreId(long storeId){
 		 this.storeId=storeId;
 	}
 	public long getStoreId(){
 		 return this.storeId;
 	}
 
 	 
 	public void setEffDate(Date effDate){
 		 this.effDate=effDate;
 	}
 	@JsonSerialize(using = DateFormatSerializer.class) 
 	public Date getEffDate(){
 		 return this.effDate;
 	}
 
 	 
}