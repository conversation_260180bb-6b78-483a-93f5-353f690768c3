package com.sgs.ecom.member.dto.send;

import com.sgs.ecom.member.base.MailBaseSend;
import com.sgs.ecom.member.dto.OrderBaseInfoDTO;
import com.sgs.ecom.member.dto.base.BaseOrderDTO;

import java.util.Map;

public class BaseSendDTO {
    private String projectName;
    private Long businessType;
    private String buType;//All
    private Long mailType;//2
    private String orderNo;
    private String sendMail;
    private String sendFrom;
    private String sendCc;//不需要
    private String sendPhone;
    private Map item;

    private Long annexType;//不需要
    private String annexName;//不需要
    private String groupNo;
    private Long labId;//实验室id

    private Long productId;

    private int isTest;

    private Long userId;

    public Long getLabId() {
        return labId;
    }

    public void setLabId(Long labId) {
        this.labId = labId;
    }

    public int getIsTest() {
        return isTest;
    }

    public void setIsTest(int isTest) {
        this.isTest = isTest;
    }

    public String getSendFrom() {
        return sendFrom;
    }

    public void setSendFrom(String sendFrom) {
        this.sendFrom = sendFrom;
    }

    public BaseSendDTO() {
    }

    public BaseSendDTO(Map map,String orderNo, MailBaseSend mailBaseSend) {
        this.projectName = "TIC_TEMPLATE";
        this.businessType =  Long.parseLong(mailBaseSend.getMailEnum());
        this.buType = "ALL";
        this.mailType = mailBaseSend.getMailType();
        this.orderNo = orderNo;
        this.sendCc = mailBaseSend.getSendCC();
        this.item = map;
        this.groupNo = "";

    }

    public String getSendPhone() {
        return sendPhone;
    }

    public void setSendPhone(String sendPhone) {
        this.sendPhone = sendPhone;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public Long getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Long businessType) {
        this.businessType = businessType;
    }

    public String getBuType() {
        return buType;
    }

    public void setBuType(String buType) {
        this.buType = buType;
    }

    public Long getMailType() {
        return mailType;
    }

    public void setMailType(Long mailType) {
        this.mailType = mailType;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getSendMail() {
        return sendMail;
    }

    public void setSendMail(String sendMail) {
        this.sendMail = sendMail;
    }

    public String getSendCc() {
        return sendCc;
    }

    public void setSendCc(String sendCc) {
        this.sendCc = sendCc;
    }

    public Long getAnnexType() {
        return annexType;
    }

    public void setAnnexType(Long annexType) {
        this.annexType = annexType;
    }

    public String getAnnexName() {
        return annexName;
    }

    public void setAnnexName(String annexName) {
        this.annexName = annexName;
    }

    public Map getItem() {
        return item;
    }

    public void setItem(Map item) {
        this.item = item;
    }

    public String getGroupNo() {
        return groupNo;
    }

    public void setGroupNo(String groupNo) {
        this.groupNo = groupNo;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
}
