package com.sgs.ecom.member.entity.user;

import lombok.Data;

import javax.persistence.Id;

/**
 * @ClassName UserMessageNoticeConfig
 * @<PERSON> <PERSON><PERSON>_<PERSON>
 * @Date 2025/7/25 14:59
 */
@Data
public class UserMessageNoticeConfig {

    @Id
    private String configId;

    private Long userId;

    private String sendChannel;

    private String noticeType;

    private String businessGroup;

    private Integer businessType;

    private Integer isClose;

    private Integer state;

    private String createDate;

    private String stateDate;
}
