package com.sgs.ecom.member.dto.bbc;

import com.sgs.ecom.member.request.tic.InsOrderReq;

public class InsProductDTO {
	public static final String CHECK_DATE="checkDate";
	public static final String SAMPLING_NAME="samplingName";
	public static final String PROVINCE="province";
	public static final String CITY="city";
	public static final String TEST_NUM="testNum";


	private String checkDate;//时间
	private int sampling;//规则ID(根据枚举或规则)
	private String samplingName;//规则ID(根据枚举或规则)
	private String province;//省
	private String city;//市
	private int testNum;//出货数量



	public InsProductDTO(InsOrderReq insOrderReq) {
		this.checkDate=insOrderReq.getCheckDate();
		this.city=insOrderReq.getCity();
		this.province=insOrderReq.getProvince();
		this.testNum=insOrderReq.getQuantity();
		this.sampling=insOrderReq.getSampling();
		this.samplingName=insOrderReq.getSamplingName();
	}



	public InsProductDTO() {
	}

	public String getCheckDate() {
		return checkDate;
	}

	public void setCheckDate(String checkDate) {
		this.checkDate = checkDate;
	}

	public int getSampling() {
		return sampling;
	}

	public void setSampling(int sampling) {
		this.sampling = sampling;
	}

	public String getSamplingName() {
		return samplingName;
	}

	public void setSamplingName(String samplingName) {
		this.samplingName = samplingName;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public int getTestNum() {
		return testNum;
	}

	public void setTestNum(int testNum) {
		this.testNum = testNum;
	}
}
