package com.sgs.ecom.member.dto.user;

import com.alibaba.fastjson.JSON;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.dto.detail.OrderAttachmentDTO;
import com.sgs.ecom.member.enumtool.user.UserInvoiceEnum;
import com.sgs.ecom.member.request.FileReq;
import com.sgs.ecom.member.request.base.BaseFileReq;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class UserInvoiceDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select TAX_NO,BANK_NUMBER,STATE_DATE,USER_ID,STATE,INVOICE_ID,CREATE_DATE,REG_ADDRESS,INVOICE_TYPE,INVOICE_TITLE,IS_DEFAULT,REG_PHONE,BANK_NAME from USER_INVOICE"; 
 
 
 	@ApiAnno(groups={Default.class, BaseOrderFilter.OiqFormInfo.class})
 	@BeanAnno(value="TAX_NO", getName="getTaxNo", setName="setTaxNo")
 	private String taxNo;
 	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
 	@BeanAnno(value="BANK_NUMBER", getName="getBankNumber", setName="setBankNumber")
 	private String bankNumber;

 	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
 	@BeanAnno(value="INVOICE_ID", getName="getInvoiceId", setName="setInvoiceId")
 	private Long invoiceId;
 	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
 	@BeanAnno(value="REG_ADDRESS", getName="getRegAddress", setName="setRegAddress")
 	private String regAddress;
 	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
 	@BeanAnno(value="INVOICE_TYPE", getName="getInvoiceType", setName="setInvoiceType")
 	private int invoiceType;
 	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
 	@BeanAnno(value="INVOICE_TITLE", getName="getInvoiceTitle", setName="setInvoiceTitle")
 	private String invoiceTitle;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="IS_DEFAULT", getName="getIsDefault", setName="setIsDefault")
 	private int isDefault;
 	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
 	@BeanAnno(value="REG_PHONE", getName="getRegPhone", setName="setRegPhone")
 	private String regPhone;
 	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
 	@BeanAnno(value="BANK_NAME", getName="getBankName", setName="setBankName")
 	private String bankName;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private String backImg;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private String frontImg;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private String linkPhone;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private String linkPerson;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private String linkEmail;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private String invoiceUuid;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private String bossNo;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private List<BaseFileReq> otherFileList=new ArrayList<>();
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private String bossTag;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private int bossFlg;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private int isForeign;

	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private List<OrderAttachmentDTO> invoiceFileList=new ArrayList<>();

	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private String country;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private String foreignCity;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private String postCode;

	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private String contact;

	@ApiAnno(groups={Default.class})
	private String detailNo;


	private int state;
	private Long userId;
	private String monthCompanyName;
	private String monthCompanyNameEn;
	private String monthAddress;
	private String monthAddressEn;
	private String otherFileListStr;
	private String extendFilesStr;

	@ApiAnno(groups={Default.class})
	private String payerName;
	@ApiAnno(groups={Default.class})
	private String payerPhone;
	@ApiAnno(groups={Default.class})
	private String payerEmail;

	public String getPayerName() {
		return payerName;
	}

	public void setPayerName(String payerName) {
		this.payerName = payerName;
	}

	public String getPayerPhone() {
		return payerPhone;
	}

	public void setPayerPhone(String payerPhone) {
		this.payerPhone = payerPhone;
	}

	public String getPayerEmail() {
		return payerEmail;
	}

	public void setPayerEmail(String payerEmail) {
		this.payerEmail = payerEmail;
	}

	public String getDetailNo() {
		return detailNo;
	}

	public void setDetailNo(String detailNo) {
		this.detailNo = detailNo;
	}

	public String getForeignCity() {
		return foreignCity;
	}

	public void setForeignCity(String foreignCity) {
		this.foreignCity = foreignCity;
	}

	public String getContact() {
		return contact;
	}

	public void setContact(String contact) {
		this.contact = contact;
	}

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}


	public String getPostCode() {
		return postCode;
	}

	public void setPostCode(String postCode) {
		this.postCode = postCode;
	}

	public void setTaxNo(String taxNo){
 		 this.taxNo=taxNo;
 	}
 	public String getTaxNo(){
 		 return this.taxNo;
 	}

	public List<OrderAttachmentDTO> getInvoiceFileList() {
		return invoiceFileList;
	}

	public void setInvoiceFileList(List<OrderAttachmentDTO> invoiceFileList) {
		this.invoiceFileList = invoiceFileList;
	}

	public void setBankNumber(String bankNumber){
 		 this.bankNumber=bankNumber;
 	}
 	public String getBankNumber(){
 		 return this.bankNumber;
 	}


	public Long getInvoiceId() {
		return invoiceId;
	}

	public void setInvoiceId(Long invoiceId) {
		this.invoiceId = invoiceId;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public void setRegAddress(String regAddress){
 		 this.regAddress=regAddress;
 	}
 	public String getRegAddress(){
 		 return this.regAddress;
 	}
 
 	 
 	public void setInvoiceType(int invoiceType){
 		 this.invoiceType=invoiceType;
 	}
 	public int getInvoiceType(){
 		 return this.invoiceType;
 	}
 
 	 
 	public void setInvoiceTitle(String invoiceTitle){
 		 this.invoiceTitle=invoiceTitle;
 	}
 	public String getInvoiceTitle(){
 		 return this.invoiceTitle;
 	}
 
 	 
 	public void setIsDefault(int isDefault){
 		 this.isDefault=isDefault;
 	}
 	public int getIsDefault(){
 		 return this.isDefault;
 	}
 
 	 
 	public void setRegPhone(String regPhone){
 		 this.regPhone=regPhone;
 	}
 	public String getRegPhone(){
 		 return this.regPhone;
 	}
 
 	 
 	public void setBankName(String bankName){
 		 this.bankName=bankName;
 	}
 	public String getBankName(){
 		 return this.bankName;
 	}

	public String getBackImg() {
		return backImg;
	}

	public void setBackImg(String backImg) {
		this.backImg = backImg;
	}

	public String getFrontImg() {
		return frontImg;
	}

	public void setFrontImg(String frontImg) {
		this.frontImg = frontImg;
	}

	public int getState() {
		return state;
	}

	public void setState(int state) {
		this.state = state;
	}

	public String invoiceTypeShow;

	public String getInvoiceTypeShow() {
		return UserInvoiceEnum.getNameCh(invoiceType);
	}

	public String getLinkPhone() {
		return linkPhone;
	}

	public void setLinkPhone(String linkPhone) {
		this.linkPhone = linkPhone;
	}

	public String getLinkPerson() {
		return linkPerson;
	}

	public void setLinkPerson(String linkPerson) {
		this.linkPerson = linkPerson;
	}

	public String getLinkEmail() {
		return linkEmail;
	}

	public void setLinkEmail(String linkEmail) {
		this.linkEmail = linkEmail;
	}

	public String getInvoiceUuid() {
		return invoiceUuid;
	}

	public void setInvoiceUuid(String invoiceUuid) {
		this.invoiceUuid = invoiceUuid;
	}

	public String getBossNo() {
		return bossNo;
	}

	public void setBossNo(String bossNo) {
		this.bossNo = bossNo;
	}


	public void setInvoiceTypeShow(String invoiceTypeShow) {
		this.invoiceTypeShow = invoiceTypeShow;
	}

	public String getBossTag() {
		return bossTag;
	}

	public void setBossTag(String bossTag) {
		this.bossTag = bossTag;
	}

	public int getBossFlg() {
		return StringUtils.isBlank(bossTag)?0:1;
	}

	public void setBossFlg(int bossFlg) {
		this.bossFlg = bossFlg;
	}

	public String getMonthCompanyName() {
		return monthCompanyName;
	}

	public void setMonthCompanyName(String monthCompanyName) {
		this.monthCompanyName = monthCompanyName;
	}

	public String getMonthCompanyNameEn() {
		return monthCompanyNameEn;
	}

	public void setMonthCompanyNameEn(String monthCompanyNameEn) {
		this.monthCompanyNameEn = monthCompanyNameEn;
	}

	public String getMonthAddress() {
		return monthAddress;
	}

	public void setMonthAddress(String monthAddress) {
		this.monthAddress = monthAddress;
	}

	public String getMonthAddressEn() {
		return monthAddressEn;
	}

	public void setMonthAddressEn(String monthAddressEn) {
		this.monthAddressEn = monthAddressEn;
	}

	public int getIsForeign() {
		return isForeign;
	}

	public void setIsForeign(int isForeign) {
		this.isForeign = isForeign;
	}

	public List<BaseFileReq> getOtherFileList() {
		List<BaseFileReq> list=new ArrayList<>();
		if(StringUtils.isNotBlank(otherFileListStr)){
			list= JSON.parseArray(otherFileListStr,BaseFileReq.class);
		}

		return list;
	}

	public String getOtherFileListStr() {
		return otherFileListStr;
	}

	public void setOtherFileListStr(String otherFileListStr) {
		this.otherFileListStr = otherFileListStr;
	}

	public void setOtherFileList(List<BaseFileReq> otherFileList) {
		this.otherFileList = otherFileList;
	}

	public String getExtendFilesStr() {
		return extendFilesStr;
	}

	public void setExtendFilesStr(String extendFilesStr) {
		this.extendFilesStr = extendFilesStr;
	}
}