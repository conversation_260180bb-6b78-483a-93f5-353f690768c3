package com.sgs.ecom.member.enumtool.dml;

import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 */

public enum BusinessCodeEnum {


    IND_ML("IND-ML", "matal","DML"),
    IND_PL("IND-PL", "polymer","DML"),
    IND_CCM("IND-CCM", "construction","DML"),
    IND_PC ("IND-PC", "Polymer Chemical","DML"),
    MIN_EMS ("MIN-EMS", "MIN-EMS","MIN"),
    MIN_EMS_KJ ("MIN-EMS-KJ", "MIN-EMS-KJ","MIN"),
    TFS_ACI ("TFS-ACI", "TFS-ACI","TFS"),
    ;
    //外部
    private String name;
    private String nameCh;
    private String systemCode;

     BusinessCodeEnum(String name, String nameCh,String systemCode) {
        this.name = name;
        this.systemCode = systemCode;
         this.nameCh = nameCh;
    }

    public static String getNameCh(String name) {
        for (BusinessCodeEnum c : BusinessCodeEnum.values()) {
            if (c.getName().equals(name)) {
                return c.getNameCh();
            }
        }
        return "";
    }

    public static Boolean toDml(String name) {
        for (BusinessCodeEnum c : BusinessCodeEnum.values()) {
            if (c.getName().equals(name) && c.getSystemCode().equals("DML")) {
                return true;
            }
        }
        return false;
    }

    public static Boolean toMin(String name) {
         if(StringUtils.isBlank(name)){
             return false;
         }

        for (BusinessCodeEnum c : BusinessCodeEnum.values()) {
            if (c.getName().equals(name) && c.getSystemCode().equals("MIN")) {
                return true;
            }
        }
        return false;
    }

    public static String toOther(String name) {
        for (BusinessCodeEnum c : BusinessCodeEnum.values()) {
            if (c.getName().equals(name)) {
                return c.getSystemCode();
            }
        }
        return "";
    }

    public String getSystemCode() {
        return systemCode;
    }

    public void setSystemCode(String systemCode) {
        this.systemCode = systemCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }
}
