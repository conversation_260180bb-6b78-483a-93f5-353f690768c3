package com.sgs.ecom.member.controller.eureka;

import com.platform.annotation.AuthRequired;
import com.sgs.base.BaseBean;
import com.sgs.ecom.member.dto.rpc.CheckBossDTO;
import com.sgs.ecom.member.request.rpc.BbcCouponListReq;
import com.sgs.ecom.member.request.rpc.BbcCouponReq;
import com.sgs.ecom.member.request.rpc.BbcUserLabelReq;
import com.sgs.ecom.member.request.rpc.BbcUuidReq;
import com.sgs.ecom.member.service.util.interfaces.IBbcService;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/business/rpc.v2.member/bbc")
public class BbcController extends ControllerUtil {

    @Autowired
    private IBbcService bbcService;
    /**
    *@Function: getAddress
    *@Description 通过uuid获取地址详情
    *@param: [bbcUuidReq]
    *@author: Xiwei_Qiu @date: 2021/9/16 @version:
    **/
    @RequestMapping(value = "getAddress", method = { RequestMethod.POST })
    public ResultBody getAddress (
            @RequestBody BbcUuidReq bbcUuidReq)throws Exception{
        return ResultBody.newInstance(bbcService.getAddress(bbcUuidReq.getUuid()));
    }

    /**
    *@Function: getInvoice
    *@Description 通过uuid获取发票详情
    *@param: [bbcUuidReq]
    *@author: Xiwei_Qiu @date: 2021/9/16 @version:
    **/
    @RequestMapping(value = "getInvoice", method = { RequestMethod.POST })
    public ResultBody getInvoice (
            @RequestBody BbcUuidReq bbcUuidReq)throws Exception{
        return ResultBody.newInstance(bbcService.getInvoice(bbcUuidReq.getUuid()));
    }

    /**
    *@Function: checkBoss
    *@Description 当参数一样的时候返回
    *@param: [bbcUuidReq]
    *@author: Xiwei_Qiu @date: 2022/7/2 @version:
    **/
    @RequestMapping(value = "checkBoss", method = { RequestMethod.POST })
    public ResultBody checkBoss (
        @RequestBody CheckBossDTO checkBossDTO)throws Exception{
        return ResultBody.newInstance(bbcService.checkBoss(checkBossDTO));
    }

    @RequestMapping(value = "saveInvoice", method = { RequestMethod.POST })
    public ResultBody saveInvoice (
        @RequestBody BbcUuidReq bbcUuidReq)throws Exception{
        bbcService.saveInvoice(bbcUuidReq.getUuid());
        return ResultBody.success();
    }

    /**
    *@Function: useCoupon
    *@Description bbc 使用优惠券
    *@param: [bbcCouponReq]
    *@author: Xiwei_Qiu @date: 2021/12/20 @version:
    **/
    @AuthRequired(login = "SSO", sign = false)
    @RequestMapping(value = "useCoupon", method = { RequestMethod.POST })
    public ResultBody useCoupon (
            @RequestHeader(value="accessToken") String token,
            @Validated(BaseBean.Insert.class)
            @RequestBody BbcCouponReq bbcCouponReq)throws Exception{
        return ResultBody.newInstance(bbcService.useCoupon(bbcCouponReq,getUserDTO(token)));
    }

    /**
     *@Function: bbc
     *@Description bbc 查询优惠券
     *@param: [bbcCouponReq]
     *@author: Xiwei_Qiu @date: 2021/12/20 @version:
     **/
    @AuthRequired(login = "SSO", sign = false)
    @RequestMapping(value = "qryCoupon", method = { RequestMethod.POST })
    public ResultBody qryCoupon (
        @RequestHeader(value="accessToken") String token,
        @Validated(BaseBean.Insert.class)
        @RequestBody BbcCouponListReq bbcCouponListReq)throws Exception{
        return ResultBody.newInstance(bbcService.qryCoupon(bbcCouponListReq,getUserDTO(token)));
    }

    @AuthRequired(login = "SSO", sign = false)
    @RequestMapping(value = "saveUserLabel", method = { RequestMethod.POST })
    public ResultBody saveUserLabel (
        @RequestHeader(value="accessToken") String token,
        @RequestBody BbcUserLabelReq bbcUserLabelReq)throws Exception{
        bbcService.saveUserLabel(bbcUserLabelReq,getUserDTO(token));
        return ResultBody.success();
    }

    @AuthRequired(login = "SSO", sign = false)
    @RequestMapping(value = "updateUserLabel", method = { RequestMethod.POST })
    public ResultBody updateUserLabel (
        @RequestHeader(value="accessToken") String token,
        @RequestBody BbcUserLabelReq bbcUserLabelReq)throws Exception{
        bbcService.updateUserLabel(bbcUserLabelReq,getUserDTO(token));
        return ResultBody.success();
    }


}
