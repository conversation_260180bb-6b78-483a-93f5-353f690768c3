<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.mapper.custom.CustInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.bo.CustInfo" >
    <id column="CUST_ID" property="custId" jdbcType="BIGINT" />
    <result column="CUST_CODE" property="custCode" jdbcType="VARCHAR" />
    <result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR" />
    <result column="COMPANY_NAME_EN" property="companyNameEn" jdbcType="VARCHAR" />
    <result column="COUNTRY" property="country" jdbcType="VARCHAR" />
    <result column="PROVICE" property="provice" jdbcType="VARCHAR" />
    <result column="CITY" property="city" jdbcType="VARCHAR" />
    <result column="TOWN" property="town" jdbcType="VARCHAR" />
    <result column="ADDRESS" property="address" jdbcType="VARCHAR" />
    <result column="address_en" property="addressEn" jdbcType="VARCHAR" />
    <result column="CREDIT_AMOUNT" property="creditAmount" jdbcType="DECIMAL" />
    <result column="STATE" property="state" jdbcType="TINYINT" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
    <result column="IS_FREEZE" property="isFreeze" jdbcType="TINYINT" />
    <result column="memo" property="memo" jdbcType="VARCHAR" />
    <result column="BOSS_NO" property="bossNo" jdbcType="VARCHAR" />
    <result column="VBA_ACCOUNT" property="vbaAccount" jdbcType="VARCHAR" />
    <result column="SETTLE_TYPE" property="settleType" jdbcType="INTEGER" />
    <result column="BELONG_AREA" property="belongArea" jdbcType="VARCHAR" />
    <result column="BUSI_CODE" jdbcType="VARCHAR" property="busiCode" />
    <result column="REPORT_PRICE" jdbcType="DECIMAL" property="reportPrice" />
    <result column="BELONG_LAB_CODE" jdbcType="VARCHAR" property="belongLabCode" />
    <result column="IS_DELETE" jdbcType="TINYINT" property="isDelete" />
  </resultMap>
  <resultMap id="QryResultMap" type="com.sgs.ecom.member.dto.CustInfoDTO" >
    <id column="CUST_ID" property="custId" jdbcType="BIGINT" />
    <result column="CUST_CODE" property="custCode" jdbcType="VARCHAR" />
    <result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR" />
    <result column="COMPANY_NAME_EN" property="companyNameEn" jdbcType="VARCHAR" />
    <result column="COUNTRY" property="country" jdbcType="VARCHAR" />
    <result column="PROVICE" property="provice" jdbcType="VARCHAR" />
    <result column="CITY" property="city" jdbcType="VARCHAR" />
    <result column="TOWN" property="town" jdbcType="VARCHAR" />
    <result column="ADDRESS" property="address" jdbcType="VARCHAR" />
    <result column="address_en" property="addressEn" jdbcType="VARCHAR" />
    <result column="CREDIT_AMOUNT" property="creditAmount" jdbcType="DECIMAL" />
    <result column="STATE" property="state" jdbcType="TINYINT" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
    <result column="IS_FREEZE" property="isFreeze" jdbcType="TINYINT" />
    <result column="memo" property="memo" jdbcType="VARCHAR" />
    <result column="BOSS_NO" property="bossNo" jdbcType="VARCHAR" />
    <result column="VBA_ACCOUNT" property="vbaAccount" jdbcType="VARCHAR" />
    <result column="SETTLE_TYPE" property="settleType" jdbcType="INTEGER" />
    <result column="BELONG_AREA" property="belongArea" jdbcType="VARCHAR" />
    <result column="BUSI_CODE" jdbcType="VARCHAR" property="busiCode" />
    <result column="REPORT_PRICE" jdbcType="DECIMAL" property="reportPrice" />
    <result column="BELONG_LAB_CODE" jdbcType="VARCHAR" property="belongLabCode" />
    <result column="IS_DELETE" jdbcType="TINYINT" property="isDelete" />
    <result column="TAX_NO" property="taxNo" jdbcType="VARCHAR" />
    <result column="REG_PHONE" property="regPhone" jdbcType="VARCHAR" />
    <result column="BANK_NAME" property="bankName" jdbcType="VARCHAR" />
    <result column="BANK_NUMBER" property="bankNumber" jdbcType="VARCHAR" />
    <result column="INVOICE_TITLE" property="invoiceTitle" jdbcType="VARCHAR" />
    <result column="REG_ADDRESS" property="regAddress" jdbcType="VARCHAR" />
    <result column="CURRENCY" jdbcType="VARCHAR" property="currency" />
  </resultMap>
  <sql id="Base_Column_List" >
    CUST_ID, CUST_CODE, COMPANY_NAME, COMPANY_NAME_EN, COUNTRY, PROVICE, CITY, TOWN, 
    ADDRESS, address_en, CREDIT_AMOUNT, STATE, CREATE_DATE, STATE_DATE, IS_FREEZE, memo, 
    BOSS_NO, VBA_ACCOUNT, SETTLE_TYPE, BELONG_AREA, BUSI_CODE, REPORT_PRICE, BELONG_LAB_CODE, 
    IS_DELETE, CURRENCY
  </sql>
  <select id="qryCustInfo" resultMap="QryResultMap" parameterType="java.lang.Long" >
    select ci.COMPANY_NAME, ci.COMPANY_NAME_EN, ci.ADDRESS, ci.address_en,
    ci.CUST_CODE, ci.REPORT_PRICE, ci.BELONG_LAB_CODE,ci.BOSS_NO,ci.CUST_ID,
    cin.TAX_NO,cin.REG_PHONE,cin.BANK_NAME,cin.BANK_NUMBER,ci.VBA_ACCOUNT,
    cin.INVOICE_TITLE,cin.REG_ADDRESS, ci.CURRENCY,ci.SETTLE_TYPE
    from TB_CUST_INFO ci
    left join TB_CUST_INVOICE cin on ci.CUST_ID = cin.CUST_ID and cin.state=1
    where ci.CUST_ID = #{custId,jdbcType=BIGINT}
    limit 1
  </select>
  <select id="qryCustInfoByBossNo" resultMap="QryResultMap" parameterType="java.lang.String" >
    select ci.COMPANY_NAME, ci.COMPANY_NAME_EN, ci.ADDRESS, ci.address_en,
           ci.CUST_CODE, ci.REPORT_PRICE, ci.BELONG_LAB_CODE,ci.BOSS_NO,ci.CUST_ID,
           cin.TAX_NO,cin.REG_PHONE,cin.BANK_NAME,cin.BANK_NUMBER,ci.VBA_ACCOUNT,
           cin.INVOICE_TITLE,cin.REG_ADDRESS, ci.CURRENCY
    from TB_CUST_INFO ci
           left join TB_CUST_INVOICE cin on ci.CUST_ID = cin.CUST_ID
    where ci.BOSS_NO=#{bossNo} and ci.STATE=1 and ci.IS_DELETE = 0
      limit 1
  </select>

  <select id="qryCustByCode" resultMap="QryResultMap" parameterType="java.lang.String" >
    select ci.COMPANY_NAME, ci.COMPANY_NAME_EN, ci.ADDRESS, ci.address_en,ci.BOSS_NO,
    ci.CUST_CODE, ci.REPORT_PRICE, ci.BELONG_LAB_CODE, ci.CUST_ID, ci.CURRENCY
    from TB_CUST_INFO ci
    where ci.CUST_CODE = #{custCode,jdbcType=VARCHAR}
    limit 1
  </select>

  <resultMap id="QryResultMoreMap" type="com.sgs.ecom.member.dto.acct.MonthCustMoreDTO" >
    <id column="CUST_ID" property="custId" jdbcType="BIGINT" />
    <result column="ADDRESS" property="address" jdbcType="VARCHAR" />
    <result column="address_en" property="addressEn" jdbcType="VARCHAR" />
    <result column="BANK_NAME" property="bankName" jdbcType="VARCHAR" />
    <result column="BANK_NUMBER" property="bankNumber" jdbcType="VARCHAR" />
    <result column="CUST_CODE" property="custCode" jdbcType="VARCHAR" />
    <result column="TAX_NO" property="taxNo" jdbcType="VARCHAR" />
    <result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR" />
    <result column="COMPANY_NAME_EN" property="companyNameEn" jdbcType="VARCHAR" />
    <result column="INVOICE_TITLE" property="invoiceTitle" jdbcType="VARCHAR" />
    <result column="REG_ADDRESS" property="regAddress" jdbcType="VARCHAR" />
    <result column="REG_PHONE" property="regPhone" jdbcType="VARCHAR" />
    <result column="PROVICE" property="province" jdbcType="VARCHAR" />
    <result column="CITY" property="city" jdbcType="VARCHAR" />
    <result column="TOWN" property="town" jdbcType="VARCHAR" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="USER_PHONE" property="userPhone" jdbcType="VARCHAR" />
    <result column="USER_EMAIL" property="userEmail" jdbcType="VARCHAR" />
    <result column="CREDIT_AMOUNT" property="creditAmount" jdbcType="DECIMAL" />
    <result column="IS_FREEZE" property="isFreeze" jdbcType="TINYINT" />
  </resultMap>


  <select id="qryCustMoreByMap" resultMap="QryResultMoreMap"  >
  select d.CUST_ID,d.ADDRESS,d.address_en,b.BANK_NAME,b.BANK_NUMBER,d.CUST_CODE,
  d.COMPANY_NAME, d.COMPANY_NAME_EN,b.CITY,b.TOWN,b.PROVICE,b.TAX_NO,b.INVOICE_TITLE,
  b.REG_ADDRESS,b.REG_PHONE,c.USER_ID,c.USER_PHONE,c.USER_EMAIL,d.CREDIT_AMOUNT,
  d.IS_FREEZE
  from TB_CUST_APPLY_RELATE a,TB_CUST_INVOICE b,USER_INFO c,TB_CUST_INFO d
  where d.CUST_ID=b.CUST_ID and d.CUST_ID=a.CUST_ID  and a.USER_ID=c.USER_ID and d.BUSI_CODE='MONTH'
   <if test="paymentCode != null">
      AND b.PAYMENT_CODE=#{paymentCode}
   </if>
    <if test="userId != null">
      AND c.USER_ID=#{userId}
    </if>

 </select>


  <select id="qryCustByBusi" parameterType="com.sgs.ecom.member.vo.VOCustInfo" resultMap="QryResultMap">
    select ci.CUST_ID, ci.CUST_CODE, ci.COMPANY_NAME, ci.COMPANY_NAME_EN, ci.COUNTRY,
    ci.PROVICE, ci.CITY, ci.TOWN, ci.ADDRESS, ci.address_en,
    ci.CREDIT_AMOUNT, ci.STATE, ci.CREATE_DATE, ci.STATE_DATE, ci.IS_FREEZE,
    ci.memo, ci.BOSS_NO, ci.VBA_ACCOUNT, cs.PERSON_CODE,
    ci.BUSI_CODE, ci.REPORT_PRICE, ci.BELONG_LAB_CODE
    from TB_CUST_INFO ci,TB_CUST_SERVICE cs
    where ci.CUST_ID = cs.CUST_ID
    and cs.STATE = 1
    and ci.IS_DELETE = 0
    <if test="custIdList != null ">
      AND ci.CUST_ID in
      <foreach collection="custIdList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>

    order by ci.CUST_ID desc
  </select>


  <resultMap id="markItem" type="com.sgs.ecom.member.dto.cust.CustMarkDTO" >
    <result column="CUST_ID" property="custId" jdbcType="BIGINT" />
    <result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR" />
  </resultMap>

  <select id="qryCustInfoMartList" parameterType="com.sgs.ecom.member.vo.VOCustInfo" resultMap="markItem">
    select tci.CUST_ID,tci.COMPANY_NAME
    from tb_cust_info tci ,tb_cust_label cl
    where tci.cust_id=cl.cust_id and tci.state=1 and cl.state=1 and    cl.label_value=1
    <if test="busiCode != null">
      and tci.BUSI_CODE = #{busiCode,jdbcType=VARCHAR}
    </if>
    <if test="labelCode != null">
      and cl.LABEL_CODE= #{labelCode,jdbcType=VARCHAR}
    </if>
    <if test="custIdList != null  and custIdList.size()>0">
      AND tci.CUST_ID in
      <foreach collection="custIdList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    order by cl.label_id desc
  </select>
  
</mapper>