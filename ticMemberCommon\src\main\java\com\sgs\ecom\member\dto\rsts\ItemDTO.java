package com.sgs.ecom.member.dto.rsts;

import java.math.BigDecimal;

public class ItemDTO {
	public Long itemId;
	public String itemName;
	private BigDecimal testDays;
	public String unit;
	private String categoryPath;
	private String categoryMemo;
	private Long categoryId;

	public String testMethod;
	public String salesPrice;
	private String price;
	private String sampleRequirements;
	private String testMemo;
	private String memo;
	private String applyFormCode;
	private String labIds;


	public BigDecimal getTestDays() {
		return testDays;
	}

	public void setTestDays(BigDecimal testDays) {
		this.testDays = testDays;
	}

	public Long getItemId() {
		return itemId;
	}

	public void setItemId(Long itemId) {
		this.itemId = itemId;
	}

	public String getItemName() {
		return itemName;
	}

	public void setItemName(String itemName) {
		this.itemName = itemName;
	}

	public String getSalesPrice() {
		return salesPrice;
	}

	public void setSalesPrice(String salesPrice) {
		this.salesPrice = salesPrice;
	}

	public String getUnit() {
		return unit;
	}

	public void setUnit(String unit) {
		this.unit = unit;
	}

	public String getTestMethod() {
		return testMethod;
	}

	public void setTestMethod(String testMethod) {
		this.testMethod = testMethod;
	}

	public String getPrice() {
		return price;
	}

	public void setPrice(String price) {
		this.price = price;
	}

	public String getCategoryPath() {
		return categoryPath;
	}

	public void setCategoryPath(String categoryPath) {
		this.categoryPath = categoryPath;
	}

	public Long getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Long categoryId) {
		this.categoryId = categoryId;
	}

	public String getSampleRequirements() {
		return sampleRequirements;
	}

	public void setSampleRequirements(String sampleRequirements) {
		this.sampleRequirements = sampleRequirements;
	}

	public String getTestMemo() {
		return testMemo;
	}

	public void setTestMemo(String testMemo) {
		this.testMemo = testMemo;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getApplyFormCode() {
		return applyFormCode;
	}

	public void setApplyFormCode(String applyFormCode) {
		this.applyFormCode = applyFormCode;
	}

	public String getLabIds() {
		return labIds;
	}

	public void setLabIds(String labIds) {
		this.labIds = labIds;
	}

	public String getCategoryMemo() {
		return categoryMemo;
	}

	public void setCategoryMemo(String categoryMemo) {
		this.categoryMemo = categoryMemo;
	}


}
