package com.sgs.ecom.member.enumtool.order;

import com.sgs.ecom.member.enumtool.OrderTypeEnum;

public enum OrderTotalEnum {
	TIC("tic", "100000", "TIC购买订单"),
	OIQ_INQUIRY("oiqInquiry", "200000", "OIQ询价单"),
	OIQ_ORDER("oiqOrder", "210000", "OIQ订单"),
	RSTS("rsts", "300000", "RSTS订单"),
	;





	OrderTotalEnum(String name, String index, String nameCh) {
		this.name = name;
		this.index = index;
		this.nameCh = nameCh;
	}

	private String name;
	private String index;
	private String nameCh;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getIndex() {
		return index;
	}

	public void setIndex(String index) {
		this.index = index;
	}

	public String getNameCh() {
		return nameCh;
	}

	public void setNameCh(String nameCh) {
		this.nameCh = nameCh;
	}
}
