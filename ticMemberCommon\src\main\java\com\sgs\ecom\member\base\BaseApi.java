package com.sgs.ecom.member.base;

import com.platform.bo.BOSysPerson;
import com.sgs.ecom.member.dto.send.WechatOtherApiDTO;
import com.sgs.ecom.member.enumtool.event.EventEnum;
import com.sgs.ecom.member.enumtool.send.WechatEnum;
import com.sgs.ecom.member.util.send.ApiOtherDTO;

public class BaseApi {

	private String orderNo;
	private EventEnum eventEnum;
	private ApiOtherDTO apiOtherDTO=new ApiOtherDTO();
	private WechatEnum wechatEnum;
	private WechatOtherApiDTO wechatOtherApiDTO;
	private BOSysPerson sysPerson;

	public BaseApi() {
	}


	public BaseApi(String orderNo, EventEnum eventEnum) {
		this.orderNo = orderNo;
		this.eventEnum = eventEnum;
	}
	public BaseApi(String orderNo, EventEnum eventEnum,ApiOtherDTO apiOtherDTO) {
		this.orderNo = orderNo;
		this.eventEnum = eventEnum;
		this.apiOtherDTO=apiOtherDTO;
	}

	public WechatOtherApiDTO getWechatOtherApiDTO() {
		return wechatOtherApiDTO;
	}

	public void setWechatOtherApiDTO(WechatOtherApiDTO wechatOtherApiDTO) {
		this.wechatOtherApiDTO = wechatOtherApiDTO;
	}

	public EventEnum getEventEnum() {
		return eventEnum;
	}

	public void setEventEnum(EventEnum eventEnum) {
		this.eventEnum = eventEnum;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public ApiOtherDTO getApiOtherDTO() {
		return apiOtherDTO;
	}

	public void setApiOtherDTO(ApiOtherDTO apiOtherDTO) {
		this.apiOtherDTO = apiOtherDTO;
	}

	public WechatEnum getWechatEnum() {
		return wechatEnum;
	}

	public void setWechatEnum(WechatEnum wechatEnum) {
		this.wechatEnum = wechatEnum;
	}

	public BOSysPerson getSysPerson() {
		return sysPerson;
	}

	public void setSysPerson(BOSysPerson sysPerson) {
		this.sysPerson = sysPerson;
	}
}
