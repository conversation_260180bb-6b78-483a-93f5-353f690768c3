package com.sgs.ecom.member.domain.user;

import com.sgs.base.BaseCopyObj;
import com.sgs.ecom.member.entity.UserLabel;
import com.sgs.ecom.member.vo.VOUserLabel;

public class UserLabelDO extends UserLabel {
	
	private UserLabel userLabel = new UserLabel();
	private BaseCopyObj baseCopy = new BaseCopyObj();
	
	public UserLabel init(VOUserLabel voUserLabel) {
		baseCopy.copy(this.userLabel, voUserLabel);
		active();
		return this.userLabel;
	}

	private void active() {
		this.userLabel.setState(1);
 	}
}
