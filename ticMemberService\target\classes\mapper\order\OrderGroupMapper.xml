<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.order.dao.OrderGroupMapper" >

  <insert id="insert" parameterType="com.sgs.ecom.member.vo.VOOrderGroup" >
    insert into ORDER_GROUP (GROUP_ID, ORDER_NO, GROUP_NO, 
      OPERATOR_TYPE, IS_TEMP, CREATE_DATE
      )
    values (#{groupId,jdbcType=BIGINT}, #{orderNo,jdbcType=VARCHAR}, #{groupNo,jdbcType=VARCHAR}, 
      #{operatorType,jdbcType=INTEGER}, #{isTemp,jdbcType=INTEGER}, #{createDate,jdbcType=TIMESTAMP}
      )
  </insert>


  <select id="qryGroupTimeByGroup"  resultType="java.lang.String" >
    select CREATE_DATE from ORDER_GROUP
    where ORDER_NO=#{orderNo} and GROUP_NO=#{groupNo}
  </select>



</mapper>