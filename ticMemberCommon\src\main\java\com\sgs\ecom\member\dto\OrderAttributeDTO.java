package com.sgs.ecom.member.dto; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.math.BigDecimal;
import java.sql.Timestamp;
import com.platform.util.json.TimeFormatSerializer; 
import com.platform.util.json.ScienceFormatSerializer; 
import com.platform.annotation.ApiAnno; 
import com.platform.annotation.BeanAnno; 
import com.sgs.base.BaseQryFilter; 

public class OrderAttributeDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select ATTR_VALUE,CREATE_DATE,ATTR_NAME,GROUP_NO,ORDER_NO,ATTR_AMOUNT,ID,ATTR_ID,IS_DEFAULT from ORDER_ATTRIBUTE"; 
 
 
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="ATTR_VALUE", getName="getAttrValue", setName="setAttrValue")
 	private String attrValue;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="ATTR_NAME", getName="getAttrName", setName="setAttrName")
 	private String attrName;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="ATTR_AMOUNT", getName="getAttrAmount", setName="setAttrAmount")
 	private BigDecimal attrAmount;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="ATTR_ID", getName="getAttrId", setName="setAttrId")
 	private long attrId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="IS_DEFAULT", getName="getIsDefault", setName="setIsDefault")
 	private int isDefault;
	@ApiAnno(groups={Default.class})
 	private Long id;
	@ApiAnno(groups={Default.class})
	private String attrCode;
	@ApiAnno(groups={Default.class})
	private String attrExtend;

	private String createDate;
	private String orderNo;
	private String groupNo;




 	public void setAttrValue(String attrValue){
 		 this.attrValue=attrValue;
 	}
 	public String getAttrValue(){
 		 return this.attrValue;
 	}
 
 	 

 	 
 	public void setAttrName(String attrName){
 		 this.attrName=attrName;
 	}
 	public String getAttrName(){
 		 return this.attrName;
 	}


	public BigDecimal getAttrAmount() {
		return attrAmount;
	}

	public void setAttrAmount(BigDecimal attrAmount) {
		this.attrAmount = attrAmount;
	}

	public void setAttrId(long attrId){
 		 this.attrId=attrId;
 	}
 	public long getAttrId(){
 		 return this.attrId;
 	}
 
 	 
 	public void setIsDefault(int isDefault){
 		 this.isDefault=isDefault;
 	}
 	public int getIsDefault(){
 		 return this.isDefault;
 	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getAttrCode() {
		return attrCode;
	}

	public void setAttrCode(String attrCode) {
		this.attrCode = attrCode;
	}

	public String getAttrExtend() {
		return attrExtend;
	}

	public void setAttrExtend(String attrExtend) {
		this.attrExtend = attrExtend;
	}

	public String getCreateDate() {
		return createDate;
	}

	public void setCreateDate(String createDate) {
		this.createDate = createDate;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getGroupNo() {
		return groupNo;
	}

	public void setGroupNo(String groupNo) {
		this.groupNo = groupNo;
	}
}