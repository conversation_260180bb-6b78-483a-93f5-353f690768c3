<?xml version="1.0" encoding="gb2312"?>
<defaults>

    <center>
    </center>
    <proxy invoke="pojo" type="primitive">
        <clazz name="com.platform.complex.service.impl.LocalServiceInvokeImpl"/>
    </proxy>

    <transaction type="Required">
        <clazz name="com.platform.complex.transaction.impl.LocalMutilTransactionImpl"/>
        <mapping>

            <property name="com.platform.complex.self.service.base" value="comframe"/>
            <property name="com.platform.complex.self.service.check" value="comframe"/>

        </mapping>
    </transaction>

    <datasource>
        <clazz name="com.platform.complex.datasource.impl.LocalMutilDataSourceImpl">
            <property name="tableName" value="cfg_db_acct"/>
        </clazz>

        <pool name="center" primary="true">
          <property name="driverClassName" value="com.mysql.jdbc.Driver"/>
          <!-- <property name="url" value="****************************************"/> -->
          <property name="url" value="**************************************"/>
          <!--<property name="url" value="****************************************"/>-->
          <property name="username" value="center"/>
          <property name="password" value="r9GjeTT1@m1zdofa"/>
          <property name="initialSize" value="0"/>
          <property name="maxActive" value="100"/>
          <property name="maxIdle" value="2"/>
          <property name="maxWait" value="2000"/>
        </pool>

        <pool name="memberread" init="true">
            <property name="url" value="**************************************"/>
            <property name="username" value="member"/>
            <property name="password" value="My9h1!cahPqa"/>
            <property name="initialSize" value="0"/>
            <property name="maxActive" value="100"/>
            <property name="maxIdle" value="2"/>
            <property name="maxWait" value="2000"/>
        </pool>

        <pool name="memberwrite" init="true">
            <property name="url" value="**************************************"/>
            <property name="username" value="member"/>
            <property name="password" value="My9h1!cahPqa"/>
            <property name="initialSize" value="0"/>
            <property name="maxActive" value="100"/>
            <property name="maxIdle" value="2"/>
            <property name="maxWait" value="2000"/>
        </pool>

        <mapping>
            <property name="com.platform.complex.self.dao.base" value="center"/>
            <property name="com.platform.complex.self.service.check" value="center"/>

            <property name="com.platform.busi.dao" value="center"/>

            <property name="com.sgs.ecom.member.acct.dao" value="member"/>
            <property name="com.sgs.ecom.member.user.dao" value="member"/>
            <property name="com.sgs.ecom.member.sys.dao" value="center"/>
            <property name="com.sgs.ecom.member.question.dao" value="center"/>

        </mapping>
    </datasource>

</defaults>
