package com.sgs.ecom.member.question.controller;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.platform.annotation.AuthRequired;
import com.platform.util.CollectionService;
import com.platform.web.BaseAction;
import com.sgs.ecom.member.question.service.interfaces.IUserQuPartSV;
import com.sgs.ecom.member.util.ResultBody;

@RestController
@RequestMapping("/business/api.v2.question/qupart")
public class UserQuPartController extends BaseAction {

	private static IUserQuPartSV userQuPartSV = CollectionService.getService(IUserQuPartSV.class);
	
    /**   
	* @Function: userAnswer
	* @Description: 用户答卷
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2019-12-12
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2019-12-12  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "NULL", sign = false)
    @RequestMapping(value = "submitAnswer", method = { RequestMethod.POST })
    public ResultBody submitAnswer(@RequestBody String data) throws Exception {
		return ResultBody.newInstance(userQuPartSV.submitAnswer(data));
	}
    
    /**   
	* @Function: qryAnswerByPart
	* @Description: 用户答卷查询
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2019-12-12
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2019-12-12  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "NULL", sign = false)
    @RequestMapping(value = "qryAnswer", method = { RequestMethod.POST })
    public ResultBody qryAnswerByPart(@RequestBody String data) throws Exception {
		return ResultBody.newInstance(userQuPartSV.qryAnswerByPart(data));
	}
}
