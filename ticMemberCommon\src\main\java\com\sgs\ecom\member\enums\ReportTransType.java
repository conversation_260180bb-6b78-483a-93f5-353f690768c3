package com.sgs.ecom.member.enums;

/**
 * 报告验真中文注释和code转换表
 */
public enum ReportTransType {

    qrCode("qrCode", "qrCode","qrCode"),
    qryType("返回结果", "qryType","返回结果 0-数字验签返回 1-二维码返回 2-人工"),
    reportType("文件类型", "reportType","文件类型 0-pdf 1-二维码 2-压缩吧"),
    isSuccess("是否成功", "isSuccess","是否成功1-否 2-是"),
    reportNo("报告编号", "reportNo","reportNo"),
    failType("失败类型", "failType","失败类型 1-报告失效 2-二维码失效 3-包含黑名单域名"),
    url("上传文件地址", "url","上传文件地址"),
    userName("用户名称", "userName","用户名称"),
    userPhone("用户手机号", "userPhone","用户手机号"),
    userEmail("用户邮箱", "userEmail","用户邮箱"),
    companyName("公司名称", "companyName","公司名称"),
    fileName("文件名称", "fileName","文件名称"),
    frontUrl("来源网站", "frontUrl","来源网站"),
    isArtificial("是否走人工", "isArtificial","是否走人工 0-否  1-是"),
    qrCodePass("二维码验真通过", "qrCodePass","二维码验真通过是；否；N/A 二维码验证步骤结果通过为是，其他情况为否；N/A为不记录 1-是 2-否"),
    dssSignatureVerificationTime("dss验签耗时", "dssSignatureVerificationTime","dss验签耗时"),
    qrCodeVerificationTime("二维码验证耗时", "qrCodeVerificationTime","二维码验证耗时"),
    getQrCodeTime("获取qrCode耗时", "getQrCodeTime","获取qrCode耗时"),
    dssFlag("dss验证是否成功", "dssFlag","dss验证是否成功 null 历史数据/非pdf的   1-dss验真通过  2-dss验真不通过"),
    javaDssFlag("dss验证是否成功(JAVA)", "javaDssFlag","java dss验证是否成功 null 历史数据/非pdf的   1-dss验真通过  2-dss验真不通过"),
    netDssFlag("dss验证是否成功(.NET)", "netDssFlag",".net dss验证是否成功 null 历史数据/非pdf的   1-dss验真通过  2-dss验真不通过"),
    reportVerificationFlag("（二维码）报告是否失效", "reportVerificationFlag","（二维码）报告失效 1-是；2-否 二维码验证步骤中结果为报告失效的是，其他为否"),
    qrCodeVerificationFlag("二维码是否失效", "qrCodeVerificationFlag","二维码失效 1-是；2-否；N/A 二维码验证步骤中在结果中未匹配到原始文件的结果中二维码失效的为是，其他为否；N/A为不记录"),
    transTimes("transTimes", "transTimes","transTimes"),
    firstQrCode("二维码URL", "firstQrCode","二维码URL"),
    scanTimes("scanTimes", "scanTimes","scanTimes"),
    blacklist("blacklist", "blacklist","blacklist"),
    blacklistStr("blacklistStr", "blacklistStr","blacklistStr"),
    fileConfirm("fileConfirm", "fileConfirm","fileConfirm"),
    recordId("recordId", "recordId","recordId"),



    ;
    // 成员变量
    private String name;
    private String index;
    private String remark;
    // 构造方法
    private ReportTransType(String name, String index,String remark) {
        this.name = name;  
        this.index = index;  
        this.remark = remark;
    }

    // 普通方法  
    public static String getName(String index) {  
        for (ReportTransType c : ReportTransType.values()) {
            if (c.getIndex().equals(index)) {  
                return c.name;  
            }  
        }  
        return null;  
    }  
    // get set 方法  
    public String getName() {  
        return name;  
    }  

    public String getIndex() {  
        return index;  
    }  
}
