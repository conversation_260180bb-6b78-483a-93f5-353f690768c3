package com.sgs.ecom.member.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.enumtool.BaseOrderStateEnum;
import com.sgs.ecom.member.enumtool.OrderTypeEnum;
import com.sgs.ecom.member.enumtool.TfsStateEnum;
import com.sgs.ecom.member.enumtool.bbc.BbcStateExpEnum;
import com.sgs.ecom.member.util.serializer.PriceNumFormatSerializer;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;

public class OrderBaseInfoCheckDTO extends BaseOrderFilter {
    @ApiAnno(groups={Default.class,OrderList.class,OrderDetail.class,InquiryList.class,InquiryDetail.class,RstsDetail.class,RstsList.class,OrderCustomerList.class})
    private Long orderId;
    @ApiAnno(groups={Default.class,OrderList.class,OrderDetail.class,InquiryList.class,InquiryDetail.class,RstsDetail.class,RstsList.class,OrderStateNumList.class,OrderCustomerList.class})
    private int state;
    @ApiAnno(groups={Default.class,OrderList.class,OrderDetail.class,InquiryList.class,InquiryDetail.class,RstsDetail.class,RstsList.class})
    private String groupNo;
    @ApiAnno(groups={Default.class,OrderList.class,OrderDetail.class,InquiryList.class,InquiryDetail.class,RstsDetail.class,RstsList.class,OrderCustomerList.class})
    private String orderNo;
    @ApiAnno(groups={Default.class,OrderList.class,OrderDetail.class,InquiryList.class,InquiryDetail.class,RstsDetail.class,RstsList.class})
    private Long userId;
    @ApiAnno(groups={Default.class,OrderList.class,OrderDetail.class,InquiryList.class,InquiryDetail.class,RstsDetail.class,RstsList.class})
    private Long labId;
    @ApiAnno(groups={Default.class,OrderList.class,OrderDetail.class,InquiryList.class,InquiryDetail.class,RstsDetail.class,RstsList.class,OrderCustomerList.class})
    private String orderType;
    @ApiAnno(groups={Default.class,OrderList.class,OrderDetail.class,InquiryList.class,InquiryDetail.class,RstsDetail.class,RstsList.class,OrderCustomerList.class})
    private String bu;
    @ApiAnno(groups={Default.class,OrderList.class,OrderDetail.class,InquiryList.class,InquiryDetail.class,RstsDetail.class,RstsList.class})
    private String csCode;
    @ApiAnno(groups={Default.class,OrderList.class,OrderDetail.class,InquiryList.class,InquiryDetail.class,RstsDetail.class,RstsList.class})
    private BigDecimal realAmount;
    @ApiAnno(groups={Default.class,OrderList.class})
    private String salesCode;
   @ApiAnno(groups={Default.class,OrderList.class})
    private String salesPhone;
    @ApiAnno(groups={Default.class,OrderList.class})
    private String promoInfo;
    private String bossNo;
    private Long custId;

    @ApiAnno(groups={Default.class,OrderList.class,OrderDetail.class,InquiryList.class,InquiryDetail.class,RstsDetail.class,RstsList.class,TicDetail.class})
    private String userPhone;
    @ApiAnno(groups={Default.class,OrderList.class,OrderDetail.class,InquiryList.class,InquiryDetail.class,RstsDetail.class,RstsList.class,TicDetail.class})
    private String userName;
    @ApiAnno(groups={Default.class,OrderList.class,OrderDetail.class,InquiryList.class,InquiryDetail.class,RstsDetail.class,RstsList.class,TicDetail.class})
    private String userEmail;
    @ApiAnno(groups={Default.class,OrderList.class,OrderDetail.class,InquiryList.class,InquiryDetail.class,RstsDetail.class,RstsList.class,TicDetail.class})
    private String companyName;
    @ApiAnno(groups={Default.class,OrderList.class,OrderDetail.class,InquiryList.class,InquiryDetail.class,RstsDetail.class,RstsList.class,TicDetail.class})
    private String province;
    @ApiAnno(groups={Default.class,OrderList.class,OrderDetail.class,InquiryList.class,InquiryDetail.class,RstsDetail.class,RstsList.class,TicDetail.class})
    private String city;
    @ApiAnno(groups={Default.class,OrderList.class,OrderDetail.class,InquiryList.class,InquiryDetail.class,RstsDetail.class,RstsList.class,TicDetail.class})
    private String companyNameEn;
    @ApiAnno(groups={Default.class,OrderList.class,OrderDetail.class,InquiryList.class,InquiryDetail.class,RstsDetail.class,RstsList.class,TicDetail.class})
    private String companyAddressCn;
    @ApiAnno(groups={Default.class,OrderList.class,OrderDetail.class,InquiryList.class,InquiryDetail.class,RstsDetail.class,RstsList.class,TicDetail.class})
    private String companyAddressEn;
    @ApiAnno(groups={Default.class,OrderList.class,OrderDetail.class,InquiryList.class,InquiryDetail.class,RstsDetail.class,RstsList.class,TicDetail.class})
    private String town;
    @ApiAnno(groups={Default.class,OrderList.class,OrderDetail.class,InquiryList.class,InquiryDetail.class,RstsDetail.class,RstsList.class,TicDetail.class})
    private int subState;

    public int getSubState() {
        return subState;
    }

    public void setSubState(int subState) {
        this.subState = subState;
    }
    @ApiAnno(groups={Default.class,OrderList.class,OrderDetail.class,InquiryList.class,InquiryDetail.class,RstsDetail.class,RstsList.class,TicDetail.class})
    private Integer isElectron; //是否是上海开票显示电子 0-否 1-是




    public String getUserPhone() {
        return userPhone;
    }

    public void setUserPhone(String userPhone) {
        this.userPhone = userPhone;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCompanyNameEn() {
        return companyNameEn;
    }

    public void setCompanyNameEn(String companyNameEn) {
        this.companyNameEn = companyNameEn;
    }

    public String getCompanyAddressCn() {
        return companyAddressCn;
    }

    public void setCompanyAddressCn(String companyAddressCn) {
        this.companyAddressCn = companyAddressCn;
    }

    public String getCompanyAddressEn() {
        return companyAddressEn;
    }

    public void setCompanyAddressEn(String companyAddressEn) {
        this.companyAddressEn = companyAddressEn;
    }

    public String getTown() {
        return town;
    }

    public void setTown(String town) {
        this.town = town;
    }

    private Boolean bbcFlg=false;//0 是member库 1是bbc库

	public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public String getGroupNo() {
        return groupNo;
    }

    public void setGroupNo(String groupNo) {
        this.groupNo = groupNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }




    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }


    public Long getLabId() {
        return labId;
    }

    public void setLabId(Long labId) {
        this.labId = labId;
    }

    public String getBu() {
        return bu;
    }

    public void setBu(String bu) {
        this.bu = bu;
    }

    public String getCsCode() {
        return csCode;
    }

    public void setCsCode(String csCode) {
        this.csCode = csCode;
    }
    @JsonSerialize(using = PriceNumFormatSerializer.class)
    public BigDecimal getRealAmount() {
        return realAmount;
    }

    public void setRealAmount(BigDecimal realAmount) {
        this.realAmount = realAmount;
    }

    @ApiAnno(groups={Default.class,OrderCustomerList.class,OrderList.class})
    private String stateShow;



    public String getStateShow() {
        if(StringUtils.isNotBlank(bu) && "2700".equals(bu)){
            stateShow= TfsStateEnum.getNameCh(state);
            return stateShow;
        }

        //leads 取自己的stateShow
        if(OrderTypeEnum.OIQINQUIRY.getIndex().equals(orderType) ||OrderTypeEnum.OIQORDER.getIndex().equals(orderType)){
            stateShow= BaseOrderStateEnum.getNameCh(state);
        }
        if(OrderTypeEnum.OIQ_PORTAL_INQUIRY.getIndex().equals(orderType) ||OrderTypeEnum.OIQ_PORTAL_ORDER.getIndex().equals(orderType) ){
            stateShow= BaseOrderStateEnum.getNameCh(state);
        }
        if(OrderTypeEnum.TIC.getIndex().equals(orderType)){
            stateShow= BbcStateExpEnum.getNameCh(state);
        }

        if(stateShow==null){
            stateShow="";
        }
        return stateShow;
    }

    public void setStateShow(String stateShow) {
        this.stateShow = stateShow;
    }

    public OrderBaseInfoCheckDTO() {
    }

    public OrderBaseInfoCheckDTO(String orderNo, Long userId) {
        this.orderNo = orderNo;
        this.userId = userId;
    }

    public Boolean getBbcFlg() {
        return bbcFlg;
    }

    public void setBbcFlg(Boolean bbcFlg) {
        this.bbcFlg = bbcFlg;
    }

    public String getSalesCode() {
        return salesCode;
    }

    public void setSalesCode(String salesCode) {
        this.salesCode = salesCode;
    }

    public String getSalesPhone() {
        return salesPhone;
    }

    public void setSalesPhone(String salesPhone) {
        this.salesPhone = salesPhone;
    }

    public String getPromoInfo() {
        return promoInfo;
    }

    public void setPromoInfo(String promoInfo) {
        this.promoInfo = promoInfo;
    }

    public String getBossNo() {
        return bossNo;
    }

    public void setBossNo(String bossNo) {
        this.bossNo = bossNo;
    }

    public Long getCustId() {
        return custId;
    }

    public void setCustId(Long custId) {
        this.custId = custId;
    }







}
