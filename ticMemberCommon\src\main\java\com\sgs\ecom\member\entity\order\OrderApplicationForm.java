package com.sgs.ecom.member.entity.order;

public class OrderApplicationForm  {


 	private String stateDate;

 	private Integer state;

 	private String linkPhone;

 	private String formTemplate;

 	private String createDate;

 	private String linkPerson;

 	private String companyAddressEn;

 	private Integer testCycle;

 	private String companyNameCn;

 	private String companyAddressCn;

 	private String linkEmail;

 	private Long formId;

 	private String companyNameEn;

 	private String orderNo;

 	private String reportSendCc;

 	private Integer isRefundSample;
	private int reportMethod;

 	private String testMemo;

	private String receiveName;
	private String receivePhone;
	private String receiveEmail;


	private String province;
	private String city;
	private String town;

	//税号
	private String taxNo;
	//公司电话
	private String regPhone;
	//开户银行
	private String bankName;

	//银行账户
	private String bankNumber;

	private int isTransfer;

	private String confirmSource;

	public OrderApplicationForm() {
	}

	public OrderApplicationForm(String orderNo, String dateStr) {
		this.orderNo=orderNo;
		this.createDate=dateStr;
		this.stateDate=dateStr;
		this.state=1;
	}

	public void setStateDate(String stateDate){
 		 this.stateDate=stateDate;
 	}
 	public String getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setState(Integer state){
 		 this.state=state;
 	}
 	public Integer getState(){
 		 return this.state;
 	}
 
 	 
 	public void setLinkPhone(String linkPhone){
 		 this.linkPhone=linkPhone;
 	}
 	public String getLinkPhone(){
 		 return this.linkPhone;
 	}
 
 	 
 	public void setFormTemplate(String formTemplate){
 		 this.formTemplate=formTemplate;
 	}
 	public String getFormTemplate(){
 		 return this.formTemplate;
 	}
 
 	 
 	public void setCreateDate(String createDate){
 		 this.createDate=createDate;
 	}
 	public String getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setLinkPerson(String linkPerson){
 		 this.linkPerson=linkPerson;
 	}
 	public String getLinkPerson(){
 		 return this.linkPerson;
 	}
 
 	 
 	public void setCompanyAddressEn(String companyAddressEn){
 		 this.companyAddressEn=companyAddressEn;
 	}
 	public String getCompanyAddressEn(){
 		 return this.companyAddressEn;
 	}
 
 	 
 	public void setTestCycle(Integer testCycle){
 		 this.testCycle=testCycle;
 	}
 	public Integer getTestCycle(){
 		 return this.testCycle;
 	}
 
 	 
 	public void setCompanyNameCn(String companyNameCn){
 		 this.companyNameCn=companyNameCn;
 	}
 	public String getCompanyNameCn(){
 		 return this.companyNameCn;
 	}
 
 	 
 	public void setCompanyAddressCn(String companyAddressCn){
 		 this.companyAddressCn=companyAddressCn;
 	}
 	public String getCompanyAddressCn(){
 		 return this.companyAddressCn;
 	}
 
 	 
 	public void setLinkEmail(String linkEmail){
 		 this.linkEmail=linkEmail;
 	}
 	public String getLinkEmail(){
 		 return this.linkEmail;
 	}
 
 	 
 	public void setFormId(Long formId){
 		 this.formId=formId;
 	}
 	public Long getFormId(){
 		 return this.formId;
 	}
 
 	 
 	public void setCompanyNameEn(String companyNameEn){
 		 this.companyNameEn=companyNameEn;
 	}
 	public String getCompanyNameEn(){
 		 return this.companyNameEn;
 	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getReportSendCc() {
		return reportSendCc;
	}

	public void setReportSendCc(String reportSendCc) {
		this.reportSendCc = reportSendCc;
	}

	public Integer getIsRefundSample() {
		return isRefundSample;
	}

	public void setIsRefundSample(Integer isRefundSample) {
		this.isRefundSample = isRefundSample;
	}

	public String getTestMemo() {
		return testMemo;
	}

	public void setTestMemo(String testMemo) {
		this.testMemo = testMemo;
	}

	public String getReceiveName() {
		return receiveName;
	}

	public void setReceiveName(String receiveName) {
		this.receiveName = receiveName;
	}

	public String getReceivePhone() {
		return receivePhone;
	}

	public void setReceivePhone(String receivePhone) {
		this.receivePhone = receivePhone;
	}

	public String getReceiveEmail() {
		return receiveEmail;
	}

	public void setReceiveEmail(String receiveEmail) {
		this.receiveEmail = receiveEmail;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getTown() {
		return town;
	}

	public void setTown(String town) {
		this.town = town;
	}

	public int getReportMethod() {
		return reportMethod;
	}

	public void setReportMethod(int reportMethod) {
		this.reportMethod = reportMethod;
	}

	public String getTaxNo() {
		return taxNo;
	}

	public void setTaxNo(String taxNo) {
		this.taxNo = taxNo;
	}

	public String getRegPhone() {
		return regPhone;
	}

	public void setRegPhone(String regPhone) {
		this.regPhone = regPhone;
	}

	public String getBankName() {
		return bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}

	public String getBankNumber() {
		return bankNumber;
	}

	public void setBankNumber(String bankNumber) {
		this.bankNumber = bankNumber;
	}

	public int getIsTransfer() {
		return isTransfer;
	}

	public void setIsTransfer(int isTransfer) {
		this.isTransfer = isTransfer;
	}

	public String getConfirmSource() {
		return confirmSource;
	}

	public void setConfirmSource(String confirmSource) {
		this.confirmSource = confirmSource;
	}
}