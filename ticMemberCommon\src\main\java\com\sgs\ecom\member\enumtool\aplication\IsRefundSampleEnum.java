package com.sgs.ecom.member.enumtool.aplication;

public enum IsRefundSampleEnum {

    //是否需要退回样品1是0否
    YES("yes", 1, "需要退回"),
    NO("no", 0, "不需要退回")
    ;

    IsRefundSampleEnum(String name, int index, String nameCh) {
        this.name = name;
        this.index = index;
        this.nameCh = nameCh;
    }

    public static String getName(int index) {
        for (IsRefundSampleEnum c : IsRefundSampleEnum.values()) {
            if (c.getIndex()==index) {
                return c.getName();
            }
        }
        return null;
    }

    public static IsRefundSampleEnum getEnum(int index) {
        for (IsRefundSampleEnum c : IsRefundSampleEnum.values()) {
            if (c.getIndex()==index) {
                return c;
            }
        }
        return null;
    }

    public static String getNameCh(int index) {
        for (IsRefundSampleEnum c : IsRefundSampleEnum.values()) {
            if (c.getIndex()==index) {
                return c.getNameCh();
            }
        }
        return null;
    }





    private String name;
    private int index;
    private String nameCh;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }



    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }
}
