<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.order.dao.OrderShippingMapper" >

    <resultMap id="resultMap" type="com.sgs.ecom.member.dto.oiq.applicationInfo.OrderShippingDTO" >
        <result column="SHIPPING_ID" property="shippingId" jdbcType="BIGINT" />
        <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
        <result column="ARRIVAL_DATE" property="arrivalDate" jdbcType="VARCHAR" />
        <result column="SHIPMENT_DATE" property="shipmentDate" jdbcType="VARCHAR" />
        <result column="ISSUE_DATE" property="issueDate" jdbcType="VARCHAR" />
        <result column="ISSUE_PLACE" property="issuePlace" jdbcType="VARCHAR" />
        <result column="SHIPPING_AGENT" property="shippingAgent" jdbcType="VARCHAR" />
        <result column="LADING_NO" property="ladingNo" jdbcType="VARCHAR" />
        <result column="VESSEL_NAME" property="vesselName" jdbcType="VARCHAR" />
        <result column="VESSEL_NUMBER" property="vesselNumber" jdbcType="VARCHAR" />
        <result column="SHIPPING_LINE" property="shippingLine" jdbcType="VARCHAR" />
        <result column="ORIGINAL_COUNTRY" property="originalCountry" jdbcType="VARCHAR" />
        <result column="LOADING_PORT" property="loadingPort" jdbcType="VARCHAR" />
        <result column="DISCHARGE_PORT" property="dischargePort" jdbcType="VARCHAR" />
        <result column="DELIVERY_COUNTRY" property="deliveryCountry" jdbcType="VARCHAR" />
        <result column="DELIVERY_CITY" property="deliveryCity" jdbcType="VARCHAR" />
        <result column="SHIPPING_PAYMENT_METHOD" property="shippingPaymentMethod" jdbcType="VARCHAR" />
        <result column="COUNTRY_OF_ORIGIN" property="countryOfOrigin" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="sqlListDTO" >
        SHIPPING_ID,ORDER_NO,ARRIVAL_DATE,SHIPMENT_DATE,ISSUE_DATE,ISSUE_PLACE,SHIPPING_AGENT,LADING_NO,VESSEL_NAME,
        VESSEL_NUMBER,SHIPPING_LINE,ORIGINAL_COUNTRY,LOADING_PORT,DISCHARGE_PORT,DELIVERY_COUNTRY,
        DELIVERY_CITY,shipping_payment_method
    </sql>

    <sql id="baseQueryWhere">
        where 1=1
        <if test="orderNo != null">
            AND ORDER_NO=#{orderNo}
        </if>
        <if test="state != null">
            AND STATE=#{state}
        </if>
        <if test="orderNoList != null ">
            AND ORDER_NO in
            <foreach collection="orderNoList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <select id="qryOrderShippingByOrder" resultMap ="resultMap">
        select
        SHIPPING_ID,ORDER_NO,ARRIVAL_DATE,SHIPMENT_DATE,ISSUE_DATE,ISSUE_PLACE,SHIPPING_AGENT,LADING_NO,VESSEL_NAME,
        VESSEL_NUMBER,SHIPPING_LINE,ORIGINAL_COUNTRY,LOADING_PORT,DISCHARGE_PORT,DELIVERY_COUNTRY,
        DELIVERY_CITY,shipping_payment_method,COUNTRY_OF_ORIGIN
        from ORDER_SHIPPING
        where ORDER_NO=#{orderNo} and STATE=1
    </select>

    <select id="selectListByMap" resultMap="resultMap" >
        select
        <include refid="sqlListDTO" />
        from ORDER_SHIPPING
        <include refid="baseQueryWhere"/>
    </select>



</mapper>