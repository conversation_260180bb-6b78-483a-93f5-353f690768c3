package com.sgs.ecom.member.dto.rpc;

import com.sgs.base.BaseBean;
import java.util.List;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * VOQuestionLabel
 *
 * @version: 1.0
 * @author: 标签VO
 * @throws Exception
 * @date: 2025/5/13
 * <p>
 * Modification History: Date         Author          Version            Description
 * ---------------------------------------------------------* 修改时间         修改人         版本 * 修改原因
 * 2025/5/13      标签VO         v1.0               新增
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class VOQuestionLabel extends BaseBean {

    private static final long serialVersionUID = 1L;

    /**
     * 标签ID
     */
    @NotNull(message = "标签ID不可为空", groups = {Delete.class, Update.class})
    private Long labelId;

    /**
     * 所属业务
     */
    @Size(max = 20, message = "所属业务参数长度超出范围", groups = {Insert.class,
        Update.class})
    private String businessType;

    /**
     * 业务编码
     */
    @Size(max = 20, message = "业务编码参数长度超出范围", groups = {Insert.class,
        Update.class})
    private String businessCode;

    /**
     * 标签编码
     */
    @NotNull(message = "标签编码不可为空", groups = {Update.class})
    @Size(max = 20, message = "标签编码参数长度超出范围", groups = {Insert.class,
        Update.class})
    private String labelCode;

    /**
     * 标签名称
     */
    @NotNull(message = "标签名称不可为空", groups = {Insert.class, Update.class})
    @Size(max = 50, message = "标签名称参数长度超出范围", groups = {Insert.class,
        Update.class})
    private String labelName;

    /**
     * 标签描述
     */
    @Size(max = 255, message = "标签描述参数长度超出范围", groups = {Insert.class, Update.class})
    private String labelDesc;

    /**
     * 提示文案
     */
    @Size(max = 20, message = "提示文案参数长度超出范围", groups = {Insert.class, Update.class})
    private String fillPrompt;

    /**
     * 是否可编辑( 1：是 0：否)
     */
    private int isEdit;

    /**
     * 标签选项
     */
    List<VOQuestionLabelOption> labelOptionList;
}
