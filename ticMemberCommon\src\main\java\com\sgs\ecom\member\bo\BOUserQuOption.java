package com.sgs.ecom.member.bo; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import com.platform.annotation.CharacterVaild; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import com.platform.annotation.BeanAnno; 

public class BOUserQuOption{
 
 	public static final String SEQUENCE = "ANSWER_ID"; 
  
 	public static final String BO_SQL = "TB_USER_QU_OPTION"; 
 
 	public static final String OWNER ="member";

 	public static final String PART_NO="partNo";
 	public static final String OPTION_INFO_EN="optionInfoEn";
 	public static final String STATE_DATE="stateDate";
 	public static final String STATE="state";
 	public static final String OPTION_VAL="optionVal";
 	public static final String OPTION_INFO="optionInfo";
 	public static final String SUBJECT_NO="subjectNo";
 	public static final String FINAL_SCORE="finalScore";
 	public static final String OPTION_TEXT="optionText";
 	public static final String CREATE_DATE="createDate";
 	public static final String ANSWER_ID="answerId";
 	public static final String QUESTION_ID="questionId";
 	public static final String REPLY_ID="replyId";
 	public static final String SORT_SHOW="sortShow";
 	public static final String OPTION_NO="optionNo";
 	public static final String STANDARD_CODE="standardCode";
 	public static final String OPTION_SCORE="optionScore";

 	@BeanAnno("PART_NO")
 	private String partNo;
 	@BeanAnno("OPTION_INFO_EN")
 	private String optionInfoEn;
 	@BeanAnno("STATE_DATE")
 	private Timestamp stateDate;
 	@BeanAnno("STATE")
 	private int state;
 	@BeanAnno("OPTION_VAL")
 	private String optionVal;
 	@BeanAnno("OPTION_INFO")
 	private String optionInfo;
 	@BeanAnno("SUBJECT_NO")
 	private String subjectNo;
 	@BeanAnno("FINAL_SCORE")
 	private long finalScore;
 	@BeanAnno("OPTION_TEXT")
 	private String optionText;
 	@BeanAnno("CREATE_DATE")
 	private Timestamp createDate;
 	@BeanAnno("ANSWER_ID")
 	private long answerId;
 	@BeanAnno("QUESTION_ID")
 	private long questionId;
 	@BeanAnno("REPLY_ID")
 	private long replyId;
 	@BeanAnno("SORT_SHOW")
 	private long sortShow;
 	@BeanAnno("OPTION_NO")
 	private String optionNo;
 	@BeanAnno("STANDARD_CODE")
 	private String standardCode;
 	@BeanAnno("OPTION_SCORE")
 	private long optionScore;

 	@CharacterVaild(len = 10) 
 	public void setPartNo(String partNo){
 		 this.partNo=partNo;
 	}
 	public String getPartNo(){
 		 return this.partNo;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setOptionInfoEn(String optionInfoEn){
 		 this.optionInfoEn=optionInfoEn;
 	}
 	public String getOptionInfoEn(){
 		 return this.optionInfoEn;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	@CharacterVaild(len = 1000) 
 	public void setOptionVal(String optionVal){
 		 this.optionVal=optionVal;
 	}
 	public String getOptionVal(){
 		 return this.optionVal;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setOptionInfo(String optionInfo){
 		 this.optionInfo=optionInfo;
 	}
 	public String getOptionInfo(){
 		 return this.optionInfo;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setSubjectNo(String subjectNo){
 		 this.subjectNo=subjectNo;
 	}
 	public String getSubjectNo(){
 		 return this.subjectNo;
 	}
 
 	 
 	public void setFinalScore(long finalScore){
 		 this.finalScore=finalScore;
 	}
 	public long getFinalScore(){
 		 return this.finalScore;
 	}
 
 	 
 	@CharacterVaild(len = 1000) 
 	public void setOptionText(String optionText){
 		 this.optionText=optionText;
 	}
 	public String getOptionText(){
 		 return this.optionText;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setAnswerId(long answerId){
 		 this.answerId=answerId;
 	}
 	public long getAnswerId(){
 		 return this.answerId;
 	}
 
 	 
 	public void setQuestionId(long questionId){
 		 this.questionId=questionId;
 	}
 	public long getQuestionId(){
 		 return this.questionId;
 	}
 
 	 
 	public void setReplyId(long replyId){
 		 this.replyId=replyId;
 	}
 	public long getReplyId(){
 		 return this.replyId;
 	}
 
 	 
 	public void setSortShow(long sortShow){
 		 this.sortShow=sortShow;
 	}
 	public long getSortShow(){
 		 return this.sortShow;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setOptionNo(String optionNo){
 		 this.optionNo=optionNo;
 	}
 	public String getOptionNo(){
 		 return this.optionNo;
 	}
 
 	 
 	@CharacterVaild(len = 20) 
 	public void setStandardCode(String standardCode){
 		 this.standardCode=standardCode;
 	}
 	public String getStandardCode(){
 		 return this.standardCode;
 	}
 
 	 
 	public void setOptionScore(long optionScore){
 		 this.optionScore=optionScore;
 	}
 	public long getOptionScore(){
 		 return this.optionScore;
 	}
 
 	 
}