package com.sgs.ecom.member.enumtool.oiq;

/**
 * <AUTHOR>
 */

public enum OiqReportMethodEnum {
    ONE("one", 1, "一份申请对应一份报告"),
    <PERSON><PERSON><PERSON>("more", 2, "一个样品对应一份报告"),
    OTH<PERSON>("other", 3, "其他"),
    ;

    OiqReportMethodEnum(String name, int index, String nameCh) {
        this.name = name;
        this.index = index;
        this.nameCh = nameCh;
    }

    public static String getName(int index) {
        for (OiqReportMethodEnum c : OiqReportMethodEnum.values()) {
            if (c.getIndex()==index) {
                return c.getName();
            }
        }
        return "";
    }

    public static String getNameCh(int index) {
        for (OiqReportMethodEnum c : OiqReportMethodEnum.values()) {
            if (c.getIndex()==index) {
                return c.getNameCh();
            }
        }
        return "";
    }


    private String name;
    private int index;
    private String nameCh;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }
}
