<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.user.dao.UserAddressMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.vo.VOUserAddress" >
    <id column="ADDRESS_ID" property="addressId" jdbcType="BIGINT" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="USER_NAME" property="userName" jdbcType="VARCHAR" />
    <result column="USER_PHONE" property="userPhone" jdbcType="VARCHAR" />
    <result column="USER_MAIL" property="userMail" jdbcType="VARCHAR" />
    <result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR" />
    <result column="COMPANY_ADDRESS" property="companyAddress" jdbcType="VARCHAR" />
    <result column="PROVINCE" property="province" jdbcType="VARCHAR" />
    <result column="CITY" property="city" jdbcType="VARCHAR" />
    <result column="TOWN" property="town" jdbcType="VARCHAR" />
    <result column="IS_DEFAULT" property="isDefault" jdbcType="TINYINT" />
    <result column="STATE" property="state" jdbcType="TINYINT" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />


  </resultMap>
  <sql id="Base_Column_List" >
    ADDRESS_ID, USER_ID, USER_NAME, USER_PHONE, USER_MAIL, COMPANY_NAME, COMPANY_ADDRESS, 
    PROVINCE, CITY, TOWN, IS_DEFAULT, STATE, CREATE_DATE, STATE_DATE
  </sql>
  <select id="selectVOByPrimaryKey" resultMap="BaseResultMap"  >
    select 
    <include refid="Base_Column_List" />
    from USER_ADDRESS
    where ADDRESS_ID = #{addressId}
  </select>

  <insert id="insertVOSelective" parameterType="com.sgs.ecom.member.vo.VOUserAddress" >
    <selectKey resultType="java.lang.Long" keyProperty="addressId" order="AFTER">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into USER_ADDRESS
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="addressId != null" >
        ADDRESS_ID,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
      <if test="userName != null" >
        USER_NAME,
      </if>
      <if test="userPhone != null" >
        USER_PHONE,
      </if>
      <if test="userMail != null" >
        USER_MAIL,
      </if>
      <if test="companyName != null" >
        COMPANY_NAME,
      </if>
      <if test="companyAddress != null" >
        COMPANY_ADDRESS,
      </if>
      <if test="province != null" >
        PROVINCE,
      </if>
      <if test="city != null" >
        CITY,
      </if>
      <if test="town != null" >
        TOWN,
      </if>
      <if test="isDefault != null" >
        IS_DEFAULT,
      </if>
      <if test="state != null" >
        STATE,
      </if>
      <if test="createDate != null" >
        CREATE_DATE,
      </if>
      <if test="stateDate != null" >
        STATE_DATE,
      </if>
      <if test="addressUuid != null" >
        ADDRESS_UUID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="addressId != null" >
        #{addressId,jdbcType=BIGINT},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="userName != null" >
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="userPhone != null" >
        #{userPhone,jdbcType=VARCHAR},
      </if>
      <if test="userMail != null" >
        #{userMail,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null" >
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="companyAddress != null" >
        #{companyAddress,jdbcType=VARCHAR},
      </if>
      <if test="province != null" >
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null" >
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="town != null" >
        #{town,jdbcType=VARCHAR},
      </if>
      <if test="isDefault != null" >
        #{isDefault,jdbcType=TINYINT},
      </if>
      <if test="state != null" >
        #{state,jdbcType=TINYINT},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        #{stateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="addressUuid != null" >
        #{addressUuid,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>


  <insert id="insertSelectiveEntity" parameterType="com.sgs.ecom.member.entity.user.UserAddress" >
    <selectKey resultType="java.lang.Long" keyProperty="addressId" order="AFTER">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into USER_ADDRESS
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="addressId != null" >
        ADDRESS_ID,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
      <if test="userName != null" >
        USER_NAME,
      </if>
      <if test="userPhone != null" >
        USER_PHONE,
      </if>
      <if test="userMail != null" >
        USER_MAIL,
      </if>
      <if test="companyName != null" >
        COMPANY_NAME,
      </if>
      <if test="companyAddress != null" >
        COMPANY_ADDRESS,
      </if>
      <if test="province != null" >
        PROVINCE,
      </if>
      <if test="city != null" >
        CITY,
      </if>
      <if test="town != null" >
        TOWN,
      </if>
      <if test="isDefault != null" >
        IS_DEFAULT,
      </if>
      <if test="state != null" >
        STATE,
      </if>
      <if test="createDate != null" >
        CREATE_DATE,
      </if>
      <if test="stateDate != null" >
        STATE_DATE,
      </if>
      <if test="addressUuid != null" >
        ADDRESS_UUID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="addressId != null" >
        #{addressId,jdbcType=BIGINT},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="userName != null" >
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="userPhone != null" >
        #{userPhone,jdbcType=VARCHAR},
      </if>
      <if test="userMail != null" >
        #{userMail,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null" >
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="companyAddress != null" >
        #{companyAddress,jdbcType=VARCHAR},
      </if>
      <if test="province != null" >
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null" >
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="town != null" >
        #{town,jdbcType=VARCHAR},
      </if>
      <if test="isDefault != null" >
        #{isDefault,jdbcType=TINYINT},
      </if>
      <if test="state != null" >
        #{state,jdbcType=TINYINT},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        #{stateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="addressUuid != null" >
        #{addressUuid,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>




  <update id="updateVOByPrimaryKeySelective" parameterType="com.sgs.ecom.member.vo.VOUserAddress" >
    update USER_ADDRESS
    <set >
      <if test="userId != null" >
        USER_ID = #{userId,jdbcType=BIGINT},
      </if>
      <if test="userName != null" >
        USER_NAME = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="userPhone != null" >
        USER_PHONE = #{userPhone,jdbcType=VARCHAR},
      </if>
      <if test="userMail != null" >
        USER_MAIL = #{userMail,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null" >
        COMPANY_NAME = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="companyAddress != null" >
        COMPANY_ADDRESS = #{companyAddress,jdbcType=VARCHAR},
      </if>
      <if test="province != null" >
        PROVINCE = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null" >
        CITY = #{city,jdbcType=VARCHAR},
      </if>
      <if test="town != null" >
        TOWN = #{town,jdbcType=VARCHAR},
      </if>
      <if test="isDefault != null" >
        IS_DEFAULT = #{isDefault,jdbcType=TINYINT},
      </if>
      <if test="state != null" >
        STATE = #{state,jdbcType=TINYINT},
      </if>
      <if test="createDate != null" >
        CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        STATE_DATE = #{stateDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ADDRESS_ID = #{addressId,jdbcType=BIGINT}
  </update>


  <!--自定义 -->
  <sql id="sqlListDTO" >
    ADDRESS_ID, USER_ID, USER_NAME, USER_PHONE, USER_MAIL, COMPANY_NAME, COMPANY_ADDRESS,
    PROVINCE, CITY, TOWN, IS_DEFAULT, STATE,ADDRESS_UUID
  </sql>

  <resultMap id="resultDTO" type="com.sgs.ecom.member.dto.user.UserAddressDTO" >
    <id column="ADDRESS_ID" property="addressId" jdbcType="BIGINT" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="USER_NAME" property="userName" jdbcType="VARCHAR" />
    <result column="USER_PHONE" property="userPhone" jdbcType="VARCHAR" />
    <result column="USER_MAIL" property="userMail" jdbcType="VARCHAR" />
    <result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR" />
    <result column="COMPANY_ADDRESS" property="companyAddress" jdbcType="VARCHAR" />
    <result column="PROVINCE" property="province" jdbcType="VARCHAR" />
    <result column="CITY" property="city" jdbcType="VARCHAR" />
    <result column="TOWN" property="town" jdbcType="VARCHAR" />
    <result column="IS_DEFAULT" property="isDefault" jdbcType="TINYINT" />
    <result column="STATE" property="state" jdbcType="TINYINT" />
    <result column="ADDRESS_UUID" property="addressUuid" jdbcType="VARCHAR" />
  </resultMap>


  <select id="selectListByMap" resultMap="resultDTO" >
    select
    <include refid="sqlListDTO" />
    from USER_ADDRESS
    <include refid="baseQueryWhere"/>
    <include refid="orderBy"/>
  </select>

  <sql id="baseQueryWhere">
    where 1=1
    <if test="userId != null">
      AND USER_ID=#{userId}
    </if>
    <if test="state != null">
      AND STATE=#{state}
    </if>
    <if test="addressId != null">
      AND ADDRESS_ID=#{addressId}
    </if>
  </sql>
  <select id="updateDefault" resultMap="resultDTO" >
   update USER_ADDRESS set IS_DEFAULT=0 where IS_DEFAULT=1 and USER_ID=#{userId}
  </select>



  <select id="selectByPrimaryKeyDTO" resultMap="resultDTO"  >
    select
    <include refid="sqlListDTO" />
    from USER_ADDRESS
    where ADDRESS_ID = #{addressId}
  </select>

  <select id="selectByUuidDTO" resultMap="resultDTO"  >
    select
    <include refid="sqlListDTO" />
    from USER_ADDRESS
    where ADDRESS_UUID = #{uuid}
    limit 1
  </select>

  <select id="selectNumByAddress"  resultType="int" >
    select count(1) from USER_ADDRESS where  USER_ID=#{userId}  and STATE=1
  </select>
  <select id="selectByPrimaryKeyAndState" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from USER_ADDRESS
    where ADDRESS_ID = #{addressId} AND STATE =1
  </select>

  <sql id="orderBy">
    <if test="orderBy != null ">
      order by ${orderBy}
    </if>
  </sql>


</mapper>