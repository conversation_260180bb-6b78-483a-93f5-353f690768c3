package com.sgs.ecom.member.dto.send;

import com.sgs.ecom.member.base.BaseSmsSend;

import java.util.Map;

public class BaseSmsSendDTO {

    private String projectName;
    private Long businessType;
    private String buType;//All
    private String orderNo;
    private String sendPhone;
    private Map item;
    private String group;
    private Long userId;

    public BaseSmsSendDTO() {
    }

    public BaseSmsSendDTO(BaseSmsSend baseSmsSend) {
        this.projectName = "TIC_TEMPLATE";
        this.businessType =Long.parseLong(baseSmsSend.getOiqSmsEnum().getIndex());
        this.buType = "ALL";
        this.orderNo = baseSmsSend.getOrderNo();
        this.group = "";
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public Long getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Long businessType) {
        this.businessType = businessType;
    }

    public String getBuType() {
        return buType;
    }

    public void setBuType(String buType) {
        this.buType = buType;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getSendPhone() {
        return sendPhone;
    }

    public void setSendPhone(String sendPhone) {
        this.sendPhone = sendPhone;
    }

    public Map getItem() {
        return item;
    }

    public void setItem(Map item) {
        this.item = item;
    }

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
}
