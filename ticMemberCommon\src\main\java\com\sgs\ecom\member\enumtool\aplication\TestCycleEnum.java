package com.sgs.ecom.member.enumtool.aplication;

import org.apache.commons.lang.StringUtils;

public enum TestCycleEnum {
    REGULAR("Regular", "1", "普通"),
    EXPRESS("Express", "2", "加急"),
    EMERGENCY("Emergency", "3", "紧急"),
    DOUBLE_EXPRESS("Double Express", "4", "特急"),//特级2

    DOUBLE_EXPRESS_II("Double Express", "5", "特急"),//特级1



    ;

    TestCycleEnum(String name, String index, String nameCh) {
        this.name = name;
        this.index = index;
        this.nameCh = nameCh;
    }

    public static String getName(String index) {
        if(StringUtils.isBlank(index)){
            return "";
        }
        for (TestCycleEnum c : TestCycleEnum.values()) {
            if (c.getIndex().equals(index)) {
                return c.getName();
            }
        }
        return "";
    }

    public static TestCycleEnum getEnum(String index) {
        for (TestCycleEnum c : TestCycleEnum.values()) {
            if (c.getIndex().equals(index)) {
                return c;
            }
        }
        return null;
    }

    public static String getNameCh(String index) {
        if(StringUtils.isBlank(index)){
            return "";
        }

        for (TestCycleEnum c : TestCycleEnum.values()) {
            if (c.getIndex().equals(index)) {
                return c.getNameCh();
            }
        }
        //当不存在枚举的时候返回code 相当于其他
        return index;
    }





    private String name;
    private String index;
    private String nameCh;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }
}
