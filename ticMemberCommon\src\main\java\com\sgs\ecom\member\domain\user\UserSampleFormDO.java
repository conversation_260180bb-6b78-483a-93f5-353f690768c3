package com.sgs.ecom.member.domain.user;

import com.alibaba.fastjson.JSON;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseCopyObj;
import com.sgs.ecom.member.dto.order.OrderSampleFromDTO;
import com.sgs.ecom.member.entity.UserLabel;
import com.sgs.ecom.member.entity.user.UserSampleFrom;
import com.sgs.ecom.member.vo.VOUserLabel;

import java.util.ArrayList;
import java.util.List;

public class UserSampleFormDO extends UserSampleFrom {
	
	private BaseCopyObj baseCopyObj = new BaseCopyObj();


	public List<UserSampleFrom> initList(List<OrderSampleFromDTO> sampleFromDTOList, String sampleNo, Long userId) {
		List<UserSampleFrom> list=new ArrayList<>();

		for(OrderSampleFromDTO orderSampleFromDTO:sampleFromDTOList){
			UserSampleFrom userSampleFrom=new UserSampleFrom();
			baseCopyObj.copyWithNull(userSampleFrom,orderSampleFromDTO);
			userSampleFrom.setUserId(userId);
			userSampleFrom.setSampleNo(sampleNo);
			userSampleFrom.setState(1);
			if("annex".equals(orderSampleFromDTO.getSampleKey()) && !ValidationUtil.isEmpty(orderSampleFromDTO.getFileReqList())){
				userSampleFrom.setSampleValue(JSON.toJSONString(orderSampleFromDTO.getFileReqList()));
			}
			list.add(userSampleFrom);
		}
		return list;
	}
}
