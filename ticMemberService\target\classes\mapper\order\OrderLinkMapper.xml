<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.order.dao.OrderLinkMapper" >



  <insert id="insertForeach" parameterType="com.sgs.ecom.member.entity.order.OrderLink" >
    insert into ORDER_LINK (
    ORDER_NO, BUSI_TYPE, LINK_PHONE,
    LINK_EMAIL, LINK_NAME,  LINK_NAME_EN,
     DEPARTMENT, POSITION,  CREATE_DATE)
    values
    <foreach collection ="list" item="item" index= "index" separator =",">
      (
      #{item.orderNo,jdbcType=VARCHAR}, #{item.busiType,jdbcType=TINYINT},#{item.linkPhone,jdbcType=VARCHAR},
      #{item.linkEmail,jdbcType=VARCHAR}, #{item.linkName,jdbcType=VARCHAR}, #{item.linkNameEn,jdbcType=VARCHAR},
       #{item.department,jdbcType=VARCHAR}, #{item.position,jdbcType=VARCHAR}, #{item.createDate,jdbcType=TIMESTAMP}
      )
    </foreach>
  </insert>



  <!--自定义 -->
  <resultMap id="resultDTO" type="com.sgs.ecom.member.dto.order.OrderLinkDTO" >
    <id column="LINK_ID" property="linkId" jdbcType="BIGINT" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="BUSI_TYPE" property="busiType" jdbcType="TINYINT" />
    <result column="LINK_PHONE" property="linkPhone" jdbcType="VARCHAR" />
    <result column="LINK_EMAIL" property="linkEmail" jdbcType="VARCHAR" />
    <result column="LINK_NAME" property="linkName" jdbcType="VARCHAR" />
    <result column="LINK_NAME_EN" property="linkNameEn" jdbcType="VARCHAR" />
    <result column="DEPARTMENT" property="department" jdbcType="VARCHAR" />
    <result column="POSITION" property="position" jdbcType="VARCHAR" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
  </resultMap>

  <sql id="sqlListDTO" >
    LINK_ID, ORDER_NO, BUSI_TYPE, LINK_PHONE, LINK_EMAIL, LINK_NAME,
    LINK_NAME_EN, DEPARTMENT,  POSITION, CREATE_DATE
  </sql>

  <select id="selectListByMap" resultMap="resultDTO" >
    select
    <include refid="sqlListDTO" />
    from ORDER_LINK
    <include refid="baseQueryWhere"/>
  </select>
  <select id="selectLinkListByOrderNo" resultMap="resultDTO" >
    select
    <include refid="sqlListDTO" />
    from ORDER_LINK
    where ORDER_NO=#{orderNo}
  </select>
  <sql id="baseQueryWhere">
    <where>
      <if test="orderNo != null">
        AND ORDER_NO=#{orderNo}
      </if>
    </where>
  </sql>

  <delete id="delByOrderNo">
    delete from ORDER_LINK where ORDER_NO=#{orderNo}
    <if test="busiType != null">
    AND BUSI_TYPE=#{busiType}
    </if>
  </delete>



</mapper>