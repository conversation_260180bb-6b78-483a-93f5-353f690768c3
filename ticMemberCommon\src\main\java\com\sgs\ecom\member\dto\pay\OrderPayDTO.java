package com.sgs.ecom.member.dto.pay;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.SysCommonConstant;
import com.platform.util.ValidationUtil;
import com.platform.util.date.DateFormat;
import com.platform.util.json.TimeFormatSerializer;
import com.platform.util.tools.LogUtil;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.dto.detail.OrderAttachmentDTO;
import com.sgs.ecom.member.dto.detail.OrderInvoiceDTO;
import com.sgs.ecom.member.enumtool.pay.OrderPayRefundEnum;
import com.sgs.ecom.member.enumtool.pay.OrderRefundCnEnum;
import com.sgs.ecom.member.util.order.UseDateUtil;
import com.sgs.ecom.member.util.serializer.PriceNumFormatSerializer;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

public class OrderPayDTO extends BaseOrderFilter {
 
 	public static final String CREATE_SQL = "select PAY_ACCOUNT,PAY_PRICE,CREATE_DATE,DETAIL_CODE,TRANS_NO,PAY_DATE,PAYMENT_ID,USER_ID,STATE,ORDER_NO,PAY_METHOD,MEMO from ORDER_PAY"; 
 
 
 	@ApiAnno(groups={Default.class,RstsDetail.class})
 	@BeanAnno(value="PAY_ACCOUNT", getName="getPayAccount", setName="setPayAccount")
 	private String payAccount;
 	@ApiAnno(groups={Default.class,RstsDetail.class})
 	@BeanAnno(value="PAY_PRICE", getName="getPayPrice", setName="setPayPrice")
 	private int payPrice;
 	@ApiAnno(groups={Default.class,RstsDetail.class,QueryList.class})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(groups={Default.class,RstsDetail.class})
 	@BeanAnno(value="DETAIL_CODE", getName="getDetailCode", setName="setDetailCode")
 	private String detailCode;
 	@ApiAnno(groups={Default.class,RstsDetail.class, QueryList.class})
 	@BeanAnno(value="TRANS_NO", getName="getTransNo", setName="setTransNo")
 	private String transNo;
 	@ApiAnno(groups={Default.class,RstsDetail.class, QueryList.class})
 	@BeanAnno(value="PAY_DATE", getName="getPayDate", setName="setPayDate")
 	private Timestamp payDate;
 	@ApiAnno(groups={Default.class,RstsDetail.class, QueryList.class})
 	@BeanAnno(value="PAYMENT_ID", getName="getPaymentId", setName="setPaymentId")
 	private long paymentId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="USER_ID", getName="getUserId", setName="setUserId")
 	private long userId;
 	@ApiAnno(groups={Default.class,RstsDetail.class, QueryList.class})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(groups={Default.class,RstsDetail.class, QueryList.class})
 	@BeanAnno(value="ORDER_NO", getName="getOrderNo", setName="setOrderNo")
 	private String orderNo;
 	@ApiAnno(groups={Default.class,RstsDetail.class, QueryList.class})
 	@BeanAnno(value="PAY_METHOD", getName="getPayMethod", setName="setPayMethod")
 	private int payMethod;
 	@ApiAnno(groups={Default.class,RstsDetail.class, QueryList.class})
 	@BeanAnno(value="MEMO", getName="getMemo", setName="setMemo")
 	private String memo;
	@ApiAnno(groups={Default.class, QueryList.class})
	private List<OrderAttachmentDTO> payFileList;

	@ApiAnno(groups={Default.class,RstsDetail.class, QueryList.class})
	private String accountName;
	@ApiAnno(groups={Default.class,RstsDetail.class, QueryList.class})
	private String paymentNo;
	@ApiAnno(groups={Default.class})
	private String bossNo;
	@ApiAnno(groups={Default.class})
	private int isFull;
	@ApiAnno(groups={Default.class,RstsDetail.class})
	private String companyName;
	@ApiAnno(groups={Default.class,QueryList.class})
	private String refundInfo;
	@ApiAnno(groups={Default.class,QueryList.class})
	private String refundInfoShow;

	@JsonSerialize(using = PriceNumFormatSerializer.class)
	@ApiAnno(groups={Default.class,RstsDetail.class,QueryList.class})
	private BigDecimal payPriceShow;
	@ApiAnno(groups={Default.class,RstsDetail.class})
	private int payType;
	
	@ApiAnno(groups={Default.class, QueryList.class})
	private String remark;
	@ApiAnno(groups={Default.class})
	private String refundOperatorShow;
	@ApiAnno(groups={Default.class,QueryList.class})
	private String refundStateShow;
	@ApiAnno(groups={Default.class, QueryList.class})
	private String refundTypeShow;
	@ApiAnno(groups={Default.class})
	private String refundShow;
	@ApiAnno(groups={Default.class, QueryList.class})
	private String buyerInfo;
	@ApiAnno(groups={Default.class})
	private String refundPrice;
	@ApiAnno(groups={Default.class})
	private OrderInvoiceDTO orderInvoiceDTO;
	@ApiAnno(groups={Default.class,QueryList.class})
	private int isCs;

	@ApiAnno(groups={Default.class,QueryList.class})
	private int isSuccessShow =0;

	@ApiAnno(groups={QueryList.class})
	private int isTime;
	@ApiAnno(groups={QueryList.class})
	private String payDateShow;

	@ApiAnno(groups={Default.class,QueryList.class})
	private BankDTO bankDTO;

	@ApiAnno(groups={QueryList.class})
	@BeanAnno(dtocls={OrderPayDTO.class})
	private List<OrderPayDTO> refunds;


	public BankDTO getBankDTO() {
		return bankDTO;
	}

	public void setBankDTO(BankDTO bankDTO) {
		this.bankDTO = bankDTO;
	}

	public int getIsSuccessShow() {
		return isSuccessShow;
	}

	public void setIsSuccessShow(int isSuccessShow) {
		this.isSuccessShow = isSuccessShow;
	}

	public int getIsCs() {
		return isCs;
	}

	public void setIsCs(int isCs) {
		this.isCs = isCs;
	}
 	public String getRefundInfo() {
		return refundInfo;
	}
	public void setRefundInfo(String refundInfo) {
		this.refundInfo = refundInfo;
	}
	public void setPayPrice(int payPrice){
 		 this.payPrice=payPrice;
 	}
 	public int getPayPrice(){
 		 return this.payPrice;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setDetailCode(String detailCode){
 		 this.detailCode=detailCode;
 	}
 	public String getDetailCode(){
 		 return this.detailCode;
 	}
 
 	 
 	public void setTransNo(String transNo){
 		 this.transNo=transNo;
 	}
 	public String getTransNo(){
 		 return this.transNo;
 	}
 
 	 
 	public void setPayDate(Timestamp payDate){
 		 this.payDate=payDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getPayDate(){
 		 return this.payDate;
 	}
 
 	 
 	public void setPaymentId(long paymentId){
 		 this.paymentId=paymentId;
 	}
 	public long getPaymentId(){
 		 return this.paymentId;
 	}
 
 	 
 	public void setUserId(long userId){
 		 this.userId=userId;
 	}
 	public long getUserId(){
 		 return this.userId;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setOrderNo(String orderNo){
 		 this.orderNo=orderNo;
 	}
 	public String getOrderNo(){
 		 return this.orderNo;
 	}
 
 	 
 	public void setPayMethod(int payMethod){
 		 this.payMethod=payMethod;
 	}
 	public int getPayMethod(){
 		 return this.payMethod;
 	}
 
 	 
 	public void setMemo(String memo){
 		 this.memo=memo;
 	}
 	public String getMemo(){
 		 return this.memo;
 	}

	public List<OrderAttachmentDTO> getPayFileList() {
		return payFileList;
	}

	public void setPayFileList(List<OrderAttachmentDTO> payFileList) {
		this.payFileList = payFileList;
	}

	public String getAccountName() {
		return accountName;
	}

	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}

	public String getPaymentNo() {
		return paymentNo;
	}

	public void setPaymentNo(String paymentNo) {
		this.paymentNo = paymentNo;
	}

	public String getPayAccount() {
		return payAccount;
	}

	public void setPayAccount(String payAccount) {
		this.payAccount = payAccount;
	}

	public String getBossNo() {
		return bossNo;
	}

	public void setBossNo(String bossNo) {
		this.bossNo = bossNo;
	}

	public int getIsFull() {
		return isFull;
	}

	public void setIsFull(int isFull) {
		this.isFull = isFull;
	}

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}


	public BigDecimal getPayPriceShow() {
		return new BigDecimal(payPrice).movePointLeft(2);
	}

	public void setPayPriceShow(BigDecimal payPriceShow) {
		this.payPriceShow = payPriceShow;
	}

	public int getPayType() {
		return payType;
	}

	public void setPayType(int payType) {
		this.payType = payType;
	}

	public String getRefundInfoShow() {
		if(StringUtils.isBlank(refundInfo)){
			return "";
		}
		refundInfoShow= OrderPayRefundEnum.getNameCh(Integer.parseInt(refundInfo));
		if(refundInfoShow==null){
			refundInfoShow="";
		}
		return refundInfoShow;
	}
	public void setRefundInfoShow(String refundInfoShow) {
		this.refundInfoShow = refundInfoShow;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	
	public String getRefundOperatorShow() {
		if(payType != 3){
			return "";
		}
		if(ValidationUtil.isEmpty(refundInfo)){
			return "";
		}
		int parseInt = Integer.parseInt(refundInfo);
		if(parseInt==91){
			return "";
		}
		if(StringUtils.isBlank(remark) && state == 0){
			refundOperatorShow= OrderRefundCnEnum.getNameCh(2);
		}else if(!StringUtils.isBlank(remark) && state == 0){
			refundOperatorShow= OrderRefundCnEnum.getNameChC(2);
		}else if(OrderPayRefundEnum.SERVICE_REFUND.getIndex() != parseInt  && state == 1){
			refundOperatorShow= OrderRefundCnEnum.getNameChB(2);
		}else if(state == 2){
			refundOperatorShow= OrderRefundCnEnum.getNameChC(2);
		}
		if(refundOperatorShow==null){
			refundOperatorShow="";
		}
		return refundOperatorShow;
	}
	
	public void setRefundOperatorShow(String refundOperatorShow) {
		this.refundOperatorShow = refundOperatorShow;
	}
	public String getRefundStateShow() {
		if(payType != 3){
			return "";
		}
		if(ValidationUtil.isEmpty(refundInfo)){
			return "";
		}
		int parseInt = Integer.parseInt(refundInfo);
		if(parseInt==91 && isSuccessShow == 0){
			return "";
		}
		if(StringUtils.isBlank(remark) && state == 0){
			refundStateShow= OrderRefundCnEnum.getNameCh(3);
		}else if(!StringUtils.isBlank(remark) && state == 0){
			refundStateShow= UseDateUtil.getDateString(payDate) +" "+ OrderRefundCnEnum.getNameChC(3) +"，"+remark;
		}else if(OrderPayRefundEnum.SERVICE_REFUND.getIndex() != parseInt  && state == 1 && isSuccessShow == 0){
			refundStateShow= UseDateUtil.getDateString(payDate) +" "+ OrderRefundCnEnum.getNameChB(3);
		}else if( state == 1 && isSuccessShow == 1){//30天已过
			refundStateShow= UseDateUtil.getDateString(payDate) +" 退款成功";
		}else if(state == 2){
			refundStateShow= UseDateUtil.getDateString(payDate) +" "+ OrderRefundCnEnum.getNameChC(3) +"，"+remark;
		}
		if(refundStateShow==null){
			refundStateShow="";
		}
		return refundStateShow;
	
	}
	public void setRefundStateShow(String refundStateShow) {
		this.refundStateShow = refundStateShow;
	}
	public String getRefundTypeShow() {
		if(payType != 3){
			return "";
		}
		if(ValidationUtil.isEmpty(refundInfo)){
			return "";
		}
		int parseInt = Integer.parseInt(refundInfo);
		if(OrderPayRefundEnum.SERVICE_REFUND.getIndex() == parseInt){
			this.refundTypeShow = OrderRefundCnEnum.getNameChD(4);
			if(!StringUtils.isBlank(payAccount)  && payMethod == 100000){
				refundTypeShow= OrderRefundCnEnum.getNameCh(5)+payAccount;
			}
			if(StringUtils.isBlank(payAccount) && payMethod == 100000){
				refundTypeShow= OrderRefundCnEnum.getNameCh(5)+buyerInfo;
			}
		}else if(StringUtils.isBlank(remark) && state == 0){
			refundTypeShow= OrderRefundCnEnum.getNameCh(4);
			if(!StringUtils.isBlank(payAccount)  && payMethod == 100000){
				refundTypeShow= OrderRefundCnEnum.getNameCh(5)+payAccount;
			}
			if(StringUtils.isBlank(payAccount) && payMethod == 100000){
				refundTypeShow= OrderRefundCnEnum.getNameCh(5)+buyerInfo;
			}
		}else if(!StringUtils.isBlank(remark) && state == 0){
			this.refundTypeShow= OrderRefundCnEnum.getNameChC(4);
			if(!StringUtils.isBlank(payAccount)  && payMethod == 100000){
				refundTypeShow= OrderRefundCnEnum.getNameCh(5)+payAccount;
			}
			if(StringUtils.isBlank(payAccount) && payMethod == 100000){
				refundTypeShow= OrderRefundCnEnum.getNameCh(5)+buyerInfo;
			}
		}else if(OrderPayRefundEnum.SERVICE_REFUND.getIndex() != parseInt  && state == 1){
			refundTypeShow= OrderRefundCnEnum.getNameChB(4);
			if(!StringUtils.isBlank(payAccount)  && payMethod == 100000){
				refundTypeShow= OrderRefundCnEnum.getNameCh(5)+payAccount;
			}
			if(StringUtils.isBlank(payAccount) && payMethod == 100000){
				refundTypeShow= OrderRefundCnEnum.getNameCh(5)+buyerInfo;
			}
		}else if( state == 2){
			this.refundTypeShow= OrderRefundCnEnum.getNameChC(4);
			if(!StringUtils.isBlank(payAccount)  && payMethod == 100000){
				refundTypeShow= OrderRefundCnEnum.getNameCh(5)+payAccount;
			}
			if(StringUtils.isBlank(payAccount) && payMethod == 100000){
				refundTypeShow= OrderRefundCnEnum.getNameCh(5)+buyerInfo;
			}
		}
		if(refundTypeShow==null){
			refundTypeShow="";
		}
		return refundTypeShow;
	}
	public void setRefundTypeShow(String refundTypeShow) {
		this.refundTypeShow = refundTypeShow;
		
	}
	public String getRefundShow() {
		if(payType != 3){
			return "";
		}
		if(ValidationUtil.isEmpty(refundInfo)){
			return "";
		}
		int parseInt = Integer.parseInt(refundInfo);
		if(OrderPayRefundEnum.SERVICE_REFUND.getIndex() == parseInt){
			refundShow= OrderRefundCnEnum.getNameChD(1);
		}else if(StringUtils.isBlank(remark) && state == 0){
			refundShow= OrderRefundCnEnum.getNameCh(1);
		}else if(!StringUtils.isBlank(remark) && state == 0){
			refundShow= OrderRefundCnEnum.getNameChC(1);
		}else if(OrderPayRefundEnum.SERVICE_REFUND.getIndex() != parseInt  && state == 1){
			refundShow= OrderRefundCnEnum.getNameChB(1);
		}else if(state == 2){
			refundShow= OrderRefundCnEnum.getNameChC(1);
		}
		if(refundShow==null){
			refundShow="";
		}
		return refundShow;
	}
	public void setRefundShow(String refundShow) {
		this.refundShow = refundShow;
	}
	public String getBuyerInfo() {
		return buyerInfo;
	}
	public void setBuyerInfo(String buyerInfo) {
		this.buyerInfo = buyerInfo;
	}
	public String getRefundPrice() {
		return String.valueOf(new BigDecimal(payPrice).movePointLeft(2));
	}
	public void setRefundPrice(String refundPrice) {
		this.refundPrice = refundPrice;
	}

	public OrderInvoiceDTO getOrderInvoiceDTO() {
		return orderInvoiceDTO;
	}

	public void setOrderInvoiceDTO(OrderInvoiceDTO orderInvoiceDTO) {
		this.orderInvoiceDTO = orderInvoiceDTO;
	}

	public List<OrderPayDTO> getRefunds() {
		return refunds;
	}

	public void setRefunds(List<OrderPayDTO> refunds) {
		this.refunds = refunds;
	}

	public int getIsTime() {
		return isTime;
	}

	public void setIsTime(int isTime) {
		this.isTime = isTime;
	}

	public String getPayDateShow() {
		String payDateShow = "待客服确认";
        try {
            payDateShow = DateFormat.dateToString(payDate, SysCommonConstant.DateFormat.DatetimeFormat);
        } catch (Exception e) {
			LogUtil.writeMessage("日期转换异常");
        }
        return payDateShow;
	}

	public void setPayDateShow(String payDateShow) {
		this.payDateShow = payDateShow;
	}
}