package com.sgs.ecom.member.enums;

/**
 * 用户小工具福利类型枚举
 */
public enum UserWelfareStatus {

    TOOL_KIT("工具包", "100"),
    COUPON("优惠券", "200");

    // 成员变量
    private String name;
    private String index;
    // 构造方法
    private UserWelfareStatus(String name, String index) {
        this.name = name;  
        this.index = index;  
    }  

    // 普通方法  
    public static String getName(String index) {  
        for (UserWelfareStatus c : UserWelfareStatus.values()) {
            if (c.getIndex().equals(index)) {  
                return c.name;  
            }  
        }  
        return null;  
    }  
    // get set 方法  
    public String getName() {  
        return name;  
    }  

    public String getIndex() {  
        return index;  
    }  
}
