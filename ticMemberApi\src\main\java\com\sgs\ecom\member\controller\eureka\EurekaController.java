package com.sgs.ecom.member.controller.eureka;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.platform.util.SysException;
import com.sgs.ecom.member.service.util.interfaces.IEurekaService;
import com.sgs.ecom.member.service.util.interfaces.IOrderQuestionService;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ResultBody;
import com.sgs.ecom.member.util.select.RedisKeyUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.Map;

@RestController
@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RequestMapping("/business/rpc.v2.member/eureka")
public class EurekaController {


    @Autowired
    public IOrderQuestionService orderQuestionService;
    @Autowired
    public IEurekaService eurekaService;

    @RequestMapping(value = "getOptionByData", method = { RequestMethod.POST })
    public ResultBody getOptionByData (
            @RequestParam(name = "orderNo", required = true) String orderNo,
            @RequestParam(name = "isOriginal", required = true) int isOriginal
    )throws Exception{
       return ResultBody.newInstance(orderQuestionService.getOptionByData(orderNo, RedisKeyUtil.ORDER_QUESTION,isOriginal));
    }

    @RequestMapping(value = "getDetailByGroupNo", method = { RequestMethod.POST })
    public ResultBody getDetailByGroupNo(
            @RequestParam(name = "orderNo", required = true) String orderNo,
            @RequestParam(name = "groupNo", required = true) String groupNo){
        return ResultBody.newInstance(eurekaService.getDetailByGroupNo(orderNo,groupNo));
    }


    @RequestMapping(value = "getUserAnswerListByRedis", method = { RequestMethod.POST })
    public ResultBody getUserAnswerListByRedis(
            @RequestParam(name = "orderNo", required = true) String orderNo,
            @RequestParam(name = "position", required = true) String position,
            @RequestParam(name = "baseKey", required = true) String baseKey,
            @RequestParam(name = "isOriginal", required = true) int isOriginal
    ){
        return ResultBody.newInstance(orderQuestionService.getUserAnswerListByRedis(baseKey,orderNo,position,RedisKeyUtil.ORDER_QUESTION,isOriginal));
    }

    @RequestMapping(value = "getOrderPDF", method = { RequestMethod.POST })
    public void getOrderPDF(
            @RequestParam(name = "orderNo", required = true) String orderNo,
            @RequestParam(name = "groupNo", required = true) String groupNo,
            @RequestParam(name = "flg", required = true) int flg,
            HttpServletResponse httpServletResponse) throws Exception {
        eurekaService.getOrderPDF(orderNo,groupNo,flg,httpServletResponse);
    }

    @RequestMapping(value = "getPDFFileList", method = { RequestMethod.POST })
    public ResultBody getOrderPDF(
            @RequestParam(name = "orderNo", required = true) String orderNo,
            @RequestParam(name = "groupNo", required = true) String groupNo) throws Exception {
        return ResultBody.newInstance(eurekaService.getPDFFileList(orderNo,groupNo));
    }





    @RequestMapping(value = "getFormByUser", method = { RequestMethod.POST })
    public ResultBody getFormByUser(
            @RequestParam(name = "userId", required = true) String userId){
        return ResultBody.newInstance(eurekaService.getFormByUser(userId));
    }

    @RequestMapping(value = "getFormPDF", method = { RequestMethod.POST })
    public void getFormPDF(
            @RequestParam(name = "orderNo", required = true) String orderNo,
            @RequestParam(name = "groupNo", required = true) String groupNo,
            HttpServletResponse httpServletResponse) throws Exception{
        eurekaService.getFormPDF(orderNo,groupNo,httpServletResponse);
    }






}
