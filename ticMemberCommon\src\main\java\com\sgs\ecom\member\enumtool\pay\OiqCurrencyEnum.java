package com.sgs.ecom.member.enumtool.pay;



public enum OiqCurrencyEnum {
    CNY("CNY","¥","人民币"),
    USD("USD","$","美元"),
    EUR("EUR","€","欧元"),


    ;

    private String key;
    private String keyShow;
    private String name;

    OiqCurrencyEnum(String key, String keyShow, String name) {
        this.key = key;
        this.keyShow = keyShow;
        this.name = name;
    }

    public static String getKeyShow(String key) {
        for (OiqCurrencyEnum c : OiqCurrencyEnum.values()) {
            if (c.getKey().equals(key)) {
                return c.getKeyShow();
            }
        }
        return "";
    }



    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getKeyShow() {
        return keyShow;
    }

    public void setKeyShow(String keyShow) {
        this.keyShow = keyShow;
    }
}
