package com.sgs.ecom.member.dto.export;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.enumtool.aplication.InvoiceTypeEnum;
import com.sgs.ecom.member.enumtool.bbc.BbcStateEnum;
import com.sgs.ecom.member.enumtool.bbc.BbcStateExpEnum;
import com.sgs.ecom.member.enumtool.bbc.StoreEnum;
import com.sgs.ecom.member.enumtool.order.OrderRefundStateEnum;
import com.sgs.ecom.member.enumtool.order.OrderSubStateEnum;
import com.sgs.ecom.member.enumtool.pay.MonthPayEnum;
import com.sgs.ecom.member.enumtool.pay.PayMethodEnum;
import com.sgs.ecom.member.util.attr.AttrUtil;
import com.sgs.ecom.member.util.serializer.TimeStringFormatSerializer;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;

public class ExpTicOrderDTO {


	@ApiAnno(groups={BaseQryFilter.Default.class})
	private int row;//编号
	@JsonSerialize(using = TimeStringFormatSerializer.class)
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String nowTime;//导出时间
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private int state;//当前状态
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String stateShow;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private int subState;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String subStateShow;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private int refundState;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String refundStateShow;

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String orderNo;//订单号
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String subOrderNo;//补差价订单号
	@ApiAnno(groups={BaseQryFilter.Default.class})
	@JsonSerialize(using = TimeStringFormatSerializer.class)
	private String createDate;//下单时间
	@ApiAnno(groups={BaseQryFilter.Default.class})
	@JsonSerialize(using = TimeStringFormatSerializer.class)
	private String finishDate;// 完成时间
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String productName;//服务名称
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String sku;//价格属性
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String skuAttr;//
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String testItem;//检测项目

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String productMemo;//商品备注
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String bu;//服务类型
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private BigDecimal price;//单价
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private Integer quantity;//数量
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private BigDecimal orderRealAmount;//订单总金额 realAmount
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String payMethod;//支付方式

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private int monthPay;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String monthPayShow;//支付方式 0现付（√）
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String monthMorePay;//月结支付状态（√）
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String sampleName;//样品信息（√）
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String number;//款号
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String formUserName;//申请人姓名
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String formUserPhone;//申请人电话
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String formUserEmail;//申请人邮箱
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String companyNameCn;//公司名称
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String companyNameEn;//
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String companyAddressCn;//公司地址

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private int invoiceType;//发票类型
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String invoiceTypeShow;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String invoiceTitle;//公司抬头
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String taxNo;//纳税人识别号，
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String bankAddr;//开户银行
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String bankNumber;//开户行号
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String registerAddr;//注册地址
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String registerPhone;//注册电话
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String invoiceSendPerson;//发票寄送收件人
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String invoiceSendPhone;//发票寄送收件人电话
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String invoiceSendAddress;//发票寄送公司地址
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String invoiceSendCompanyName;//发票邮寄公司名称
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String invoiceSendEmail;//发票寄送收件人邮箱

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private Integer isElectron; //是否是上海开票显示电子 0-否 1-是

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String foreignCountry;

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String foreignAddr;

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String foreignConcat;

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String payerName;//付款方名称
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String payerPhone;//付款方手机号
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String payerEmail;//付款方邮箱

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String customerPackage;//套餐

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private Integer customerPackageNum;//套餐数量

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String customerFlexi;//自选

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private Integer customerFlexiNum;//自选数量

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String csPackage;//客服定制

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private Integer csPackageNum;//客服定制数量

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String csDisCountRate;//客服优惠比例

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String urgentName;//加急选项
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private BigDecimal serviceAmount;//加急费

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private BigDecimal orderAmount;//总计原价 (没有优惠的)
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private BigDecimal storeAmount;//店铺优惠
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private BigDecimal couponAmount;//优惠券金额

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private BigDecimal realAmount;//实付金额 realAmount

	public BigDecimal getOrderAmount() {
		return orderAmount;
	}

	public void setOrderAmount(BigDecimal orderAmount) {
		this.orderAmount = orderAmount;
	}

	public BigDecimal getStoreAmount() {
		return storeAmount;
	}

	public void setStoreAmount(BigDecimal storeAmount) {
		this.storeAmount = storeAmount;
	}

	public BigDecimal getCouponAmount() {
		return couponAmount;
	}

	public void setCouponAmount(BigDecimal couponAmount) {
		this.couponAmount = couponAmount;
	}

	public BigDecimal getRealAmount() {
		return realAmount;
	}

	public void setRealAmount(BigDecimal realAmount) {
		this.realAmount = realAmount;
	}

	public BigDecimal getServiceAmount() {
		return serviceAmount;
	}

	public void setServiceAmount(BigDecimal serviceAmount) {
		this.serviceAmount = serviceAmount;
	}

	public String getCsDisCountRate() {
		BigDecimal orderOldDecimal = !ValidationUtil.isEmpty(orderAmount) ? orderAmount : BigDecimal.ZERO;
		BigDecimal shopDecimal = !ValidationUtil.isEmpty(storeAmount) ? storeAmount : BigDecimal.ZERO;
		BigDecimal disCountDecimal = !ValidationUtil.isEmpty(couponAmount) ? couponAmount : BigDecimal.ZERO;
		BigDecimal realDecimal = !ValidationUtil.isEmpty(realAmount) ? realAmount : BigDecimal.ZERO;
		BigDecimal serviceDecimal = !ValidationUtil.isEmpty(serviceAmount) ? serviceAmount : BigDecimal.ZERO;
		BigDecimal subtract = realDecimal.subtract(serviceDecimal);
		//客服折扣优惠率 = (实付金额-加急费)/(订单实付金额-店铺优惠-优惠券优惠)
		BigDecimal subtract1 = orderOldDecimal.subtract(shopDecimal).subtract(disCountDecimal);
		if(ValidationUtil.isEmpty(subtract1) || subtract1.doubleValue() == 0){
			return  "";
		}else {
			BigDecimal multiply = subtract.divide(orderOldDecimal.subtract(shopDecimal).subtract(disCountDecimal), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
			if(!ValidationUtil.isEmpty(multiply) && multiply.doubleValue() !=0){
				return multiply.toString()+"%";
			}
			return  "";
		}
	}



	public String getUrgentName() {
		return urgentName;
	}

	public void setUrgentName(String urgentName) {
		this.urgentName = urgentName;
	}

	public String getCustomerPackage() {
		return customerPackage;
	}

	public void setCustomerPackage(String customerPackage) {
		this.customerPackage = customerPackage;
	}

	public Integer getCustomerPackageNum() {
		return customerPackageNum;
	}

	public void setCustomerPackageNum(Integer customerPackageNum) {
		this.customerPackageNum = customerPackageNum;
	}

	public String getCustomerFlexi() {
		return customerFlexi;
	}

	public void setCustomerFlexi(String customerFlexi) {
		this.customerFlexi = customerFlexi;
	}

	public Integer getCustomerFlexiNum() {
		return customerFlexiNum;
	}

	public void setCustomerFlexiNum(Integer customerFlexiNum) {
		this.customerFlexiNum = customerFlexiNum;
	}

	public String getCsPackage() {
		return csPackage;
	}

	public void setCsPackage(String csPackage) {
		this.csPackage = csPackage;
	}

	public Integer getCsPackageNum() {
		return csPackageNum;
	}

	public void setCsPackageNum(Integer csPackageNum) {
		this.csPackageNum = csPackageNum;
	}

	public String getPayerName() {
		return payerName;
	}

	public void setPayerName(String payerName) {
		this.payerName = payerName;
	}

	public String getPayerPhone() {
		return payerPhone;
	}

	public void setPayerPhone(String payerPhone) {
		this.payerPhone = payerPhone;
	}

	public String getPayerEmail() {
		return payerEmail;
	}

	public void setPayerEmail(String payerEmail) {
		this.payerEmail = payerEmail;
	}

	public String getForeignCountry() {
		return foreignCountry;
	}

	public void setForeignCountry(String foreignCountry) {
		this.foreignCountry = foreignCountry;
	}

	public String getForeignAddr() {
		return foreignAddr;
	}

	public void setForeignAddr(String foreignAddr) {
		this.foreignAddr = foreignAddr;
	}

	public String getForeignConcat() {
		return foreignConcat;
	}

	public void setForeignConcat(String foreignConcat) {
		this.foreignConcat = foreignConcat;
	}

	public Integer getIsElectron() {
		return isElectron;
	}

	public void setIsElectron(Integer isElectron) {
		this.isElectron = isElectron;
	}

	public String getFinishDate() {
		return finishDate;
	}

	public void setFinishDate(String finishDate) {
		this.finishDate = finishDate;
	}

	public String getNumber() {
		return number;
	}

	public void setNumber(String number) {
		this.number = number;
	}

	public int getRow() {
		return row;
	}

	public void setRow(int row) {
		this.row = row;
	}

	public String getNowTime() {
		return nowTime;
	}

	public void setNowTime(String nowTime) {
		this.nowTime = nowTime;
	}


	public int getState() {
		return state;
	}

	public void setState(int state) {
		this.state = state;
	}

	public String getStateShow() {
		return BbcStateExpEnum.getNameCh(state);
	}

	public void setStateShow(String stateShow) {
		this.stateShow = stateShow;
	}


	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getSubOrderNo() {
		return subOrderNo;
	}

	public void setSubOrderNo(String subOrderNo) {
		this.subOrderNo = subOrderNo;
	}

	public String getCreateDate() {
		return createDate;
	}

	public void setCreateDate(String createDate) {
		this.createDate = createDate;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public String getSkuAttr() {
		return skuAttr;
	}

	public void setSkuAttr(String skuAttr) {
		this.skuAttr = skuAttr;
	}

	public void setSku(String sku) {
		this.sku = sku;
	}

	public String getTestItem() {
		return testItem;
	}

	public void setTestItem(String testItem) {
		this.testItem = testItem;
	}



	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public Integer getQuantity() {
		return quantity;
	}

	public void setQuantity(Integer quantity) {
		this.quantity = quantity;
	}

	public void setCsDisCountRate(String csDisCountRate) {
		this.csDisCountRate = csDisCountRate;
	}

	public String getProductMemo() {
		return productMemo;
	}

	public void setProductMemo(String productMemo) {
		this.productMemo = productMemo;
	}

	public String getPayMethod() {
		return PayMethodEnum.getNameCh(payMethod);
	}

	public void setPayMethod(String payMethod) {
		this.payMethod = payMethod;
	}


	public String getCompanyNameEn() {
		return companyNameEn;
	}

	public void setCompanyNameEn(String companyNameEn) {
		this.companyNameEn = companyNameEn;
	}

	public BigDecimal getOrderRealAmount() {
		return orderRealAmount;
	}

	public void setOrderRealAmount(BigDecimal orderRealAmount) {
		this.orderRealAmount = orderRealAmount;
	}



	public String getBu() {
		return StoreEnum.getNameCh(bu);
	}

	public void setBu(String bu) {
		this.bu = bu;
	}



	public String getFormUserName() {
		return formUserName;
	}

	public void setFormUserName(String formUserName) {
		this.formUserName = formUserName;
	}

	public String getFormUserPhone() {
		return formUserPhone;
	}

	public void setFormUserPhone(String formUserPhone) {
		this.formUserPhone = formUserPhone;
	}

	public String getFormUserEmail() {
		return formUserEmail;
	}

	public void setFormUserEmail(String formUserEmail) {
		this.formUserEmail = formUserEmail;
	}


	public String getCompanyAddressCn() {
		return companyAddressCn;
	}

	public void setCompanyAddressCn(String companyAddressCn) {
		this.companyAddressCn = companyAddressCn;
	}


	public String getCompanyNameCn() {
		return companyNameCn;
	}

	public void setCompanyNameCn(String companyNameCn) {
		this.companyNameCn = companyNameCn;
	}


	public int getInvoiceType() {
		return invoiceType;
	}

	public void setInvoiceType(int invoiceType) {
		this.invoiceType = invoiceType;
	}

	public String getInvoiceTypeShow() {
//		if(!ValidationUtil.isEmpty(isElectron) && isElectron == 1)
//			return InvoiceTypeEnum.COMMON_COMPANY.getNameCh();

		return InvoiceTypeEnum.getNameCh(invoiceType);
	}

	public void setInvoiceTypeShow(String invoiceTypeShow) {
		this.invoiceTypeShow = invoiceTypeShow;
	}

	public String getInvoiceTitle() {
		return invoiceTitle;
	}

	public void setInvoiceTitle(String invoiceTitle) {
		this.invoiceTitle = invoiceTitle;
	}

	public String getTaxNo() {
		return taxNo;
	}

	public void setTaxNo(String taxNo) {
		this.taxNo = taxNo;
	}

	public String getBankAddr() {
		return bankAddr;
	}

	public void setBankAddr(String bankAddr) {
		this.bankAddr = bankAddr;
	}

	public String getBankNumber() {
		return bankNumber;
	}

	public void setBankNumber(String bankNumber) {
		this.bankNumber = bankNumber;
	}

	public String getRegisterAddr() {
		return registerAddr;
	}

	public void setRegisterAddr(String registerAddr) {
		this.registerAddr = registerAddr;
	}

	public String getRegisterPhone() {
		return registerPhone;
	}

	public void setRegisterPhone(String registerPhone) {
		this.registerPhone = registerPhone;
	}



	public String getInvoiceSendCompanyName() {
		return invoiceSendCompanyName;
	}

	public void setInvoiceSendCompanyName(String invoiceSendCompanyName) {
		this.invoiceSendCompanyName = invoiceSendCompanyName;
	}

	public String getInvoiceSendPerson() {
		return invoiceSendPerson;
	}

	public void setInvoiceSendPerson(String invoiceSendPerson) {
		this.invoiceSendPerson = invoiceSendPerson;
	}

	public String getInvoiceSendPhone() {
		return invoiceSendPhone;
	}

	public void setInvoiceSendPhone(String invoiceSendPhone) {
		this.invoiceSendPhone = invoiceSendPhone;
	}

	public String getInvoiceSendAddress() {
		return invoiceSendAddress;
	}

	public void setInvoiceSendAddress(String invoiceSendAddress) {
		this.invoiceSendAddress = invoiceSendAddress;
	}

	public String getInvoiceSendEmail() {
		return invoiceSendEmail;
	}

	public void setInvoiceSendEmail(String invoiceSendEmail) {
		this.invoiceSendEmail = invoiceSendEmail;
	}



	public String getSampleName() {
		return sampleName;
	}

	public void setSampleName(String sampleName) {
		this.sampleName = sampleName;
	}


	public int getMonthPay() {
		return monthPay;
	}

	public void setMonthPay(int monthPay) {
		this.monthPay = monthPay;
	}



	public String getSku() {
		if(StringUtils.isBlank(skuAttr)){
			return "";
		}
		return AttrUtil.skuAttrToSku(skuAttr);
	}


	public String getMonthPayShow() {
		if(monthPay==1 ||monthPay==3){
			return "月结";
		}

		return MonthPayEnum.getNameCh(monthPay);
	}

	public void setMonthPayShow(String monthPayShow) {
		this.monthPayShow = monthPayShow;
	}

	public String getMonthMorePay() {
		if(monthPay==1 ||monthPay==3){
			return MonthPayEnum.getNameCh(monthPay);
		}
		return "";
	}

	public void setMonthMorePay(String monthMorePay) {
		this.monthMorePay = monthMorePay;
	}



	public int getSubState() {
		return subState;
	}

	public void setSubState(int subState) {
		this.subState = subState;
	}

	public String getSubStateShow() {
		if(subState==70){
			return OrderSubStateEnum.getNameCh(subState);
		}
		return "";
	}

	public void setSubStateShow(String subStateShow) {
		this.subStateShow = subStateShow;
	}

	public int getRefundState() {
		return refundState;
	}

	public void setRefundState(int refundState) {
		this.refundState = refundState;
	}

	public String getRefundStateShow() {
		return OrderRefundStateEnum.getNameCh(refundState);
	}

	public void setRefundStateShow(String refundStateShow) {
		this.refundStateShow = refundStateShow;
	}

}
