package com.sgs.ecom.member.base;

import com.sgs.base.BaseQryFilter;

public class BaseOrderFilter extends BaseQryFilter {
    public BaseOrderFilter() {
    }

    public interface OrderDetail {

    }
    public interface OiqOrderDetail {

    }

    public interface InquiryDetail {

    }

    public interface ShareDetail {

    }

    public interface OrderList {

    }

    public interface OrderCustomerList {

    }

    public interface TicList {

    }
    public interface TicDetail {

    }
    public interface InquiryList {

    }
    public interface RstsList {

    }
    public interface RstsDetail {

    }

    public interface PortalOrderDetail {

    }

    public interface OiqFormInfo {

    }




    public interface LabCode {

    }
    public interface OrderApplicationOther {

    }

    public interface SubList {

    }

    public interface OiqSubList {

    }

    public interface OrderStateNumList {

    }
    public interface WordForm {

    }
    public interface WordQuotation {

    }
    public interface OrderToOther {

    }
    public interface ExpressToOther {

    }
    public interface PayToOther {

    }


}
