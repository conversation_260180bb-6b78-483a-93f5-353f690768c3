<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.user.dao.UserMessageNoticeConfigMapper" >
    <resultMap id="BaseResultMap" type="com.sgs.ecom.member.entity.user.UserMessageNoticeConfig" >
        <id column="CONFIG_ID" property="configId" jdbcType="BIGINT" />
        <result column="USER_ID" property="userId" jdbcType="BIGINT" />
        <result column="SEND_CHANNEL" property="sendChannel" jdbcType="VARCHAR" />
        <result column="NOTICE_TYPE" property="noticeType" jdbcType="VARCHAR" />
        <result column="BUSINESS_GROUP" property="businessGroup" jdbcType="VARCHAR" />
        <result column="BUSINESS_TYPE" property="businessType" jdbcType="INTEGER" />
        <result column="IS_CLOSE" property="isClose" jdbcType="INTEGER" />
        <result column="STATE" property="state" jdbcType="INTEGER" />
        <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
        <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
    </resultMap>
    <sql id="Base_Column_List" >
        CONFIG_ID, USER_ID, SEND_CHANNEL, NOTICE_TYPE, BUSINESS_GROUP, BUSINESS_TYPE, IS_CLOSE, STATE, CREATE_DATE, STATE_DATE
    </sql>
    <select id="selectMessageNoticeByUser" resultMap="BaseResultMap" parameterType="java.lang.Long" >
        select
        <include refid="Base_Column_List" />
        from USER_MESSAGE_NOTICE_CONFIG
        where USER_ID = #{userId,jdbcType=BIGINT}
        and STATE=1
    </select>
    <update id="deleteByUser" parameterType="java.lang.Long" >
        update USER_MESSAGE_NOTICE_CONFIG set
        state=0
        where USER_ID = #{userId,jdbcType=BIGINT}
        and STATE=1
    </update>
    <update id="deleteByModel" parameterType="com.sgs.ecom.member.vo.user.VOUserMessageNoticeConfig" >
        update USER_MESSAGE_NOTICE_CONFIG set
        state=0
        where USER_ID = #{userId,jdbcType=BIGINT}
        and STATE=1
        <if test="sendChannel != null and sendChannel != ''">
            and SEND_CHANNEL = #{sendChannel}
        </if>
        <if test="noticeType != null and noticeType != ''">
            and NOTICE_TYPE = #{noticeType}
        </if>
        <if test="businessGroup != null and businessGroup != ''">
            and BUSINESS_GROUP = #{businessGroup}
        </if>
        <if test="businessType != null">
            and BUSINESS_TYPE = #{businessType}
        </if>
    </update>
    <insert id="batchInsert" parameterType="java.util.List">
        insert into USER_MESSAGE_NOTICE_CONFIG (
        USER_ID, SEND_CHANNEL, NOTICE_TYPE,
        BUSINESS_GROUP, BUSINESS_TYPE, IS_CLOSE,
        STATE
        )
        values
        <foreach collection="list" index="index" item="item" separator=",">
            (
            #{item.userId}, #{item.sendChannel}, #{item.noticeType},
            #{item.businessGroup}, #{item.businessType}, #{item.isClose},
            1
            )
        </foreach>
    </insert>
</mapper>