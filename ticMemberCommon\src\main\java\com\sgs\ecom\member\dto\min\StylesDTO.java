package com.sgs.ecom.member.dto.min;

import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.sgs.ecom.member.base.BaseOrderFilter;

public class StylesDTO {
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    @BeanAnno(dtocls = {FormatDTO.class})
    private FormatDTO format;
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private String label;

    public StylesDTO( String label) {
        this.label = label;
        FormatDTO formatDTO=new FormatDTO();
        formatDTO.getStyle().setFont(new Font("#FFFF00"));
        this.format=formatDTO;
    }


    public FormatDTO getFormat() {
        return format;
    }

    public void setFormat(FormatDTO format) {
        this.format = format;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }
}
