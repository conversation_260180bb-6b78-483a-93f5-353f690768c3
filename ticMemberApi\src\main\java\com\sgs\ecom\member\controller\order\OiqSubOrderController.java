package com.sgs.ecom.member.controller.order;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.ecom.member.base.BaseOrderNoReq;
import com.sgs.ecom.member.dto.user.UserDTO;
import com.sgs.ecom.member.enumtool.OiqMailEnum;
import com.sgs.ecom.member.enumtool.OiqSmsEnum;
import com.sgs.ecom.member.enumtool.event.EventEnum;
import com.sgs.ecom.member.event.EventApiUtil;
import com.sgs.ecom.member.infrastructure.event.MailEventUtil;
import com.sgs.ecom.member.infrastructure.event.SmsEventUtil;
import com.sgs.ecom.member.request.OrderNoReq;
import com.sgs.ecom.member.request.detail.ExpressReq;
import com.sgs.ecom.member.request.oiq.OiqOrderReq;
import com.sgs.ecom.member.service.oiq.interfaces.IOiqOrderApplicationService;
import com.sgs.ecom.member.service.oiq.interfaces.IOiqSubOrderService;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.oiq/sub")
public class OiqSubOrderController extends ControllerUtil {

    @Resource
    private IOiqSubOrderService oiqSubOrderService;


    /**
     *@Function:
     *@Description
     *@param: [token, applicationReq]
     *@version:
     *@author: Xiwei_Qiu
     *@date: 2021/3/9
     **/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "qrySubOrderList", method = { RequestMethod.POST })
    public ResultBody qrySubOrderList (
            @RequestHeader(value="accessToken") String token,
            @RequestBody BaseOrderNoReq baseOrderNoReq)throws Exception{
        return ResultBody.newInstance(oiqSubOrderService.qrySubOrderList(baseOrderNoReq.getOrderNo(),getUserDTO(token)));
    }


}
