package com.sgs.ecom.member.dto.detail;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.json.TimeFormatSerializer;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.enumtool.aplication.ExpressCodeEnum;
import com.sgs.ecom.member.enumtool.aplication.ExpressPayEnum;
import com.sgs.ecom.member.enumtool.aplication.ExpressTypeEnum;
import com.sgs.ecom.member.vo.VOOrderExpress;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

public class OrderExpressDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select RECEIPT_PERSON,ORDER_NO,EXPRESS_CODE,PRE_EXPRESS_DATE,RECEIPT_TOWN,LAB_ID,EXPRESS_ID,SEND_PROVICE,LAB_NAME,GOODS_NAME,PACKAGE_NUMS,SEND_TOWN,DELIVER_TYPE,SEND_ADDR,SEND_CITY,STATE_DATE,SEND_PERSON,RECEIPT_PROVICE,EXPRESS_TYPE,CREATE_DATE,GOODS_PRICE,EXPRESS_NO,RECEIPT_PHONE,RECEIPT_ADDR,SEND_PHONE,RECEIPT_CITY from ORDER_EXPRESS";


	@ApiAnno(groups={Default.class,BaseOrderFilter.OrderToOther.class})
	@BeanAnno(value="RECEIPT_PERSON", getName="getReceiptPerson", setName="setReceiptPerson")
	private String receiptPerson;
	@ApiAnno(groups={Default.class,QueryList.class})
	@BeanAnno(value="ORDER_NO", getName="getOrderNo", setName="setOrderNo")
	private String orderNo;
	@ApiAnno(groups={Default.class,QueryList.class,BaseOrderFilter.OrderToOther.class})
	@BeanAnno(value="EXPRESS_CODE", getName="getExpressCode", setName="setExpressCode")
	private String expressCode;
	@ApiAnno(groups={Default.class})
	@BeanAnno(value="PRE_EXPRESS_DATE", getName="getPreExpressDate", setName="setPreExpressDate")
	private Timestamp preExpressDate;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OrderToOther.class})
	@BeanAnno(value="RECEIPT_TOWN", getName="getReceiptTown", setName="setReceiptTown")
	private String receiptTown;
	@ApiAnno(groups={Default.class})
	@BeanAnno(value="LAB_ID", getName="getLabId", setName="setLabId")
	private long labId;
	@ApiAnno(groups={Default.class,QueryList.class})
	@BeanAnno(value="EXPRESS_ID", getName="getExpressId", setName="setExpressId")
	private long expressId;
	@ApiAnno(groups={Default.class})
	@BeanAnno(value="SEND_PROVICE", getName="getSendProvice", setName="setSendProvice")
	private String sendProvice;
	@ApiAnno(groups={Default.class})
	@BeanAnno(value="LAB_NAME", getName="getLabName", setName="setLabName")
	private String labName;
	@ApiAnno(groups={Default.class,QueryList.class})
	@BeanAnno(value="GOODS_NAME", getName="getGoodsName", setName="setGoodsName")
	private String goodsName;
	@ApiAnno(groups={Default.class})
	@BeanAnno(value="PACKAGE_NUMS", getName="getPackageNums", setName="setPackageNums")
	private int packageNums;
	@ApiAnno(groups={Default.class})
	@BeanAnno(value="SEND_TOWN", getName="getSendTown", setName="setSendTown")
	private String sendTown;
	@ApiAnno(groups={Default.class,QueryList.class,BaseOrderFilter.OrderToOther.class,BaseOrderFilter.ExpressToOther.class})
	@BeanAnno(value="DELIVER_TYPE", getName="getDeliverType", setName="setDeliverType")
	private int deliverType;
	@ApiAnno(groups={Default.class})
	@BeanAnno(value="SEND_ADDR", getName="getSendAddr", setName="setSendAddr")
	private String sendAddr;
	@ApiAnno(groups={Default.class})
	@BeanAnno(value="SEND_CITY", getName="getSendCity", setName="setSendCity")
	private String sendCity;
	@ApiAnno(groups={Default.class})
	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
	private Timestamp stateDate;
	@ApiAnno(groups={Default.class})
	@BeanAnno(value="SEND_PERSON", getName="getSendPerson", setName="setSendPerson")
	private String sendPerson;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OrderToOther.class})
	@BeanAnno(value="RECEIPT_PROVICE", getName="getReceiptProvice", setName="setReceiptProvice")
	private String receiptProvice;
	@ApiAnno(groups={Default.class})
	@BeanAnno(value="EXPRESS_TYPE", getName="getExpressType", setName="setExpressType")
	private String expressType;
	@ApiAnno(groups={Default.class})
	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
	private Timestamp createDate;
	@ApiAnno(groups={Default.class})
	@BeanAnno(value="GOODS_PRICE", getName="getGoodsPrice", setName="setGoodsPrice")
	private BigDecimal goodsPrice;
	@ApiAnno(groups={Default.class,QueryList.class,BaseOrderFilter.OrderToOther.class,BaseOrderFilter.ExpressToOther.class})
	@BeanAnno(value="EXPRESS_NO", getName="getExpressNo", setName="setExpressNo")
	private String expressNo;
	@ApiAnno(groups={Default.class,QueryList.class,BaseOrderFilter.OrderToOther.class})
	@BeanAnno(value="RECEIPT_PHONE", getName="getReceiptPhone", setName="setReceiptPhone")
	private String receiptPhone;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OrderToOther.class})
	@BeanAnno(value="RECEIPT_ADDR", getName="getReceiptAddr", setName="setReceiptAddr")
	private String receiptAddr;
	@ApiAnno(groups={Default.class,QueryList.class})
	@BeanAnno(value="SEND_PHONE", getName="getSendPhone", setName="setSendPhone")
	private String sendPhone;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OrderToOther.class})
	@BeanAnno(value="RECEIPT_CITY", getName="getReceiptCity", setName="setReceiptCity")
	private String receiptCity;
	@ApiAnno(groups={Default.class,QueryList.class})
	@BeanAnno(value="GOODS_TYPE", getName="getGoodsType", setName="setGoodsType")
	private Integer goodsType;
	@ApiAnno(groups={Default.class,QueryList.class,BaseOrderFilter.OrderToOther.class})
	private String receiptEmail;


	@ApiAnno(groups={Default.class,QueryList.class})
	private String state;
	@ApiAnno(groups={Default.class,QueryList.class})
	private Long addressId;

	@ApiAnno(groups={Default.class,QueryList.class})
	private int payMethond;
	@ApiAnno(groups={Default.class,QueryList.class})
	private String monthlyCard;
	@ApiAnno(groups={Default.class,QueryList.class})
	private String receiptCompany;
	@ApiAnno(groups={Default.class,QueryList.class})
	private String sendCompany;

	@ApiAnno(groups={Default.class,QueryList.class})
	private List<OrderExpressListDTO> list ;

	@ApiAnno(groups={Default.class,QueryList.class})
	private int isCs;
	@ApiAnno(groups={Default.class,QueryList.class})
	private String memo;

	public OrderExpressDTO() {
	}

	public OrderExpressDTO(VOOrderExpress voOrderExpress) {
		this.expressNo = voOrderExpress.getExpressNo();
		this.expressCode =ExpressCodeEnum.getNameCh(voOrderExpress.getExpressCode());

	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public int getIsCs() {
		return isCs;
	}

	public void setIsCs(int isCs) {
		this.isCs = isCs;
	}

	public List<OrderExpressListDTO> getList() {
		return list;
	}

	public void setList(List<OrderExpressListDTO> list) {
		this.list = list;
	}

	public Integer getGoodsType() {
		return goodsType;
	}

	public void setGoodsType(Integer goodsType) {
		this.goodsType = goodsType;
	}

	public void setReceiptPerson(String receiptPerson){
 		 this.receiptPerson=receiptPerson;
 	}
 	public String getReceiptPerson(){
 		 return this.receiptPerson;
 	}
 
 	 
 	public void setOrderNo(String orderNo){
 		 this.orderNo=orderNo;
 	}
 	public String getOrderNo(){
 		 return this.orderNo;
 	}
 
 	 
 	public void setExpressCode(String expressCode){
 		 this.expressCode=expressCode;
 	}
 	public String getExpressCode(){
 		 return this.expressCode;
 	}
 
 	 
 	public void setPreExpressDate(Timestamp preExpressDate){
 		 this.preExpressDate=preExpressDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getPreExpressDate(){
 		 return this.preExpressDate;
 	}
 
 	 
 	public void setReceiptTown(String receiptTown){
 		 this.receiptTown=receiptTown;
 	}
 	public String getReceiptTown(){
 		 return this.receiptTown;
 	}
 
 	 

 
 	 
 	public void setExpressId(long expressId){
 		 this.expressId=expressId;
 	}
 	public long getExpressId(){
 		 return this.expressId;
 	}
 
 	 
 	public void setSendProvice(String sendProvice){
 		 this.sendProvice=sendProvice;
 	}
 	public String getSendProvice(){
 		 return this.sendProvice;
 	}
 
 	 
 	public void setLabName(String labName){
 		 this.labName=labName;
 	}
 	public String getLabName(){
 		 return this.labName;
 	}
 
 	 
 	public void setGoodsName(String goodsName){
 		 this.goodsName=goodsName;
 	}
 	public String getGoodsName(){
 		 return this.goodsName;
 	}
 
 	 
 	public void setPackageNums(int packageNums){
 		 this.packageNums=packageNums;
 	}
 	public int getPackageNums(){
 		 return this.packageNums;
 	}
 
 	 
 	public void setSendTown(String sendTown){
 		 this.sendTown=sendTown;
 	}
 	public String getSendTown(){
 		 return this.sendTown;
 	}
 
 	 
 	public void setDeliverType(int deliverType){
 		 this.deliverType=deliverType;
 	}
 	public int getDeliverType(){
 		 return this.deliverType;
 	}
 
 	 
 	public void setSendAddr(String sendAddr){
 		 this.sendAddr=sendAddr;
 	}
 	public String getSendAddr(){
 		 return this.sendAddr;
 	}
 
 	 
 	public void setSendCity(String sendCity){
 		 this.sendCity=sendCity;
 	}
 	public String getSendCity(){
 		 return this.sendCity;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setSendPerson(String sendPerson){
 		 this.sendPerson=sendPerson;
 	}
 	public String getSendPerson(){
 		 return this.sendPerson;
 	}
 
 	 
 	public void setReceiptProvice(String receiptProvice){
 		 this.receiptProvice=receiptProvice;
 	}
 	public String getReceiptProvice(){
 		 return this.receiptProvice;
 	}
 
 	 
 	public void setExpressType(String expressType){
 		 this.expressType=expressType;
 	}
 	public String getExpressType(){
 		 return this.expressType;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}


	public BigDecimal getGoodsPrice() {
		return goodsPrice;
	}

	public void setGoodsPrice(BigDecimal goodsPrice) {
		this.goodsPrice = goodsPrice;
	}

	public void setExpressNo(String expressNo){
 		 this.expressNo=expressNo;
 	}
 	public String getExpressNo(){
 		 return this.expressNo;
 	}
 
 	 
 	public void setReceiptPhone(String receiptPhone){
 		 this.receiptPhone=receiptPhone;
 	}
 	public String getReceiptPhone(){
 		 return this.receiptPhone;
 	}
 
 	 
 	public void setReceiptAddr(String receiptAddr){
 		 this.receiptAddr=receiptAddr;
 	}
 	public String getReceiptAddr(){
 		 return this.receiptAddr;
 	}
 
 	 
 	public void setSendPhone(String sendPhone){
 		 this.sendPhone=sendPhone;
 	}
 	public String getSendPhone(){
 		 return this.sendPhone;
 	}
 
 	 
 	public void setReceiptCity(String receiptCity){
 		 this.receiptCity=receiptCity;
 	}
 	public String getReceiptCity(){
 		 return this.receiptCity;
 	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public Long getAddressId() {
		return addressId;
	}

	public void setAddressId(Long addressId) {
		this.addressId = addressId;
	}

	public Long getLabId() {
		return labId;
	}

	public void setLabId(Long labId) {
		this.labId = labId;
	}

	public void setExpressId(Long expressId) {
		this.expressId = expressId;
	}

	public int getPayMethond() {
		return payMethond;
	}

	public void setPayMethond(int payMethond) {
		this.payMethond = payMethond;
	}




	@ApiAnno(groups={Default.class,QueryList.class,BaseOrderFilter.ExpressToOther.class})
	private String expressCodeShow;
	@ApiAnno(groups={Default.class,QueryList.class})
	private String expressTypeShow;
	@ApiAnno(groups={Default.class,QueryList.class})
	private String payMethondShow;

	public String getExpressCodeShow() {
		expressCodeShow= ExpressCodeEnum.getNameCh(expressCode);
		return expressCodeShow==null?"":expressCodeShow;
	}

	public String getExpressTypeShow() {
		expressTypeShow= ExpressTypeEnum.getNameCh(expressType);
		return expressTypeShow==null?"":expressTypeShow;
	}

	public String getPayMethondShow() {
		payMethondShow= ExpressPayEnum.getNameCh(String.valueOf(payMethond));
		return payMethondShow==null?"":payMethondShow;
	}

	public String getMonthlyCard() {
		return monthlyCard;
	}

	public void setMonthlyCard(String monthlyCard) {
		this.monthlyCard = monthlyCard;
	}

	public String getReceiptCompany() {
		return receiptCompany;
	}

	public void setReceiptCompany(String receiptCompany) {
		this.receiptCompany = receiptCompany;
	}

	public String getSendCompany() {
		return sendCompany;
	}

	public void setSendCompany(String sendCompany) {
		this.sendCompany = sendCompany;
	}

	public String getReceiptEmail() {
		return receiptEmail;
	}

	public void setReceiptEmail(String receiptEmail) {
		this.receiptEmail = receiptEmail;
	}
}