package com.sgs.ecom.member.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * @ClassName CacheTimeProperties
 * @<PERSON> Ki<PERSON>_<PERSON>
 * @Date 2024-07-05 16:16
 */
@RefreshScope
@Component
@ConfigurationProperties(prefix = "cachetimes.shop")
public class CacheTimeProperties {

    private Integer applicationTimes;

    public Integer getApplicationTimes() {
        return applicationTimes;
    }

    public void setApplicationTimes(Integer applicationTimes) {
        this.applicationTimes = applicationTimes;
    }
}
