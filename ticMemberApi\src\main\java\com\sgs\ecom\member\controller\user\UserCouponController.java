package com.sgs.ecom.member.controller.user;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.ecom.member.dto.user.UserDTO;
import com.sgs.ecom.member.request.rpc.CenterCouponReq;
import com.sgs.ecom.member.request.user.UserCouponListReq;
import com.sgs.ecom.member.service.user.interfaces.IUserCouponService;
import com.sgs.ecom.member.service.util.interfaces.IUserService;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/business/api.v2.user/coupon")
@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
public class UserCouponController extends ControllerUtil {

    @Autowired
    private IUserService userService;
    @Autowired
    private IUserCouponService userCouponService;


    /**
    *@Function: list
    *@Description 用户优惠券列表
    *@param: [token, userCouponListReq]
    *@author: Xiwei_Qiu @date: 2021/11/29 @version:
    **/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "list", method = { RequestMethod.POST })
    public ResultBody list(
            @RequestHeader(value="accessToken") String token,
            @RequestBody UserCouponListReq userCouponListReq
    ) throws Exception {
        return ResultBody.newInstance(userService.selectCouponList(getUserDTO(token),userCouponListReq));
    }

    @HystrixCommand
    @AuthRequired(login = "NULL", sign = true)
    @RequestMapping(value = "commonList", method = { RequestMethod.POST })
    public ResultBody commonList(
        @RequestHeader(value="accessToken") String token,
        @RequestBody UserCouponListReq userCouponListReq
    ) throws Exception {
        UserDTO userDTO= StringUtils.isBlank(token)?null:getUserDTO(token);
        return ResultBody.newInstance(userCouponService.commonList(userCouponListReq,userDTO));
    }

    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "commonReceive", method = { RequestMethod.POST })
    public ResultBody commonReceive(
        @RequestHeader(value="accessToken") String token,
        @RequestBody UserCouponListReq userCouponListReq
    ) throws Exception {
        userCouponService.commonReceive(userCouponListReq,getUserDTO(token));
        return ResultBody.success();
    }





    /**
     *@Function: list
     *@Description 用户优惠券列表 报告和加急券
     *@param: [token, userCouponListReq]
     *@author: Xiwei_Qiu @date: 2021/11/29 @version:
     **/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "rangeList", method = { RequestMethod.POST })
    public ResultBody rangeList(
        @RequestHeader(value="accessToken") String token,
        @RequestBody UserCouponListReq userCouponListReq
    ) throws Exception {
        return ResultBody.newInstance(userService.rangeList(getUserDTO(token),userCouponListReq));
    }
    /**
    *@Function: orderList
    *@Description 订单的优惠券列表
    *@param: [token, userCouponListReq]
    *@author: Xiwei_Qiu @date: 2021/11/5 @version:
    **/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "orderList", method = { RequestMethod.POST })
    public ResultBody orderList(
            @RequestHeader(value="accessToken") String token,
            @RequestBody UserCouponListReq userCouponListReq
    ) throws Exception {
        return ResultBody.newInstance(userService.selectCouponOrderList(getUserDTO(token),userCouponListReq));
    }


    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "getStateNum", method = { RequestMethod.POST })
    public ResultBody getStateNum(
            @RequestHeader(value="accessToken") String token,
            @RequestBody UserCouponListReq userCouponListReq
    ) throws Exception {
        return ResultBody.newInstance(userService.selectCouponStateNum(getUserDTO(token),userCouponListReq));
    }

    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "getUseNum", method = { RequestMethod.POST })
    public ResultBody getUseNum(
            @RequestHeader(value="accessToken") String token
    ) throws Exception {
        return ResultBody.newInstance(userService.getCouponUseNum(getUserDTO(token)));
    }


    /**
    *@Function: receive
    *@Description 
    *@param: [token]
    *@author: Xiwei_Qiu @date: 2022/7/1 @version: 
    **/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "receive", method = { RequestMethod.POST })
    public ResultBody receive(
            @RequestHeader(value="accessToken") String token
    ) throws Exception {
        userService.receiveCoupon(String.valueOf(getUserDTO(token).getUserId()));
        return ResultBody.success();
    }

}
