package com.sgs.ecom.member.dto.order;

import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.bo.AttrInfo;
import com.sgs.ecom.member.dto.OrderBaseInfoDTO;
import com.sgs.ecom.member.dto.OrderDetailDTO;
import com.sgs.ecom.member.dto.address.ApplicationAddressDTO;
import com.sgs.ecom.member.dto.bbc.TicPdfDTO;
import com.sgs.ecom.member.dto.center.LabDTO;
import com.sgs.ecom.member.dto.detail.OrderApplicationFormDTO;
import com.sgs.ecom.member.dto.detail.OrderInvoiceDTO;
import com.sgs.ecom.member.dto.detail.OrderReportDTO;
import com.sgs.ecom.member.dto.pay.BankDTO;
import com.sgs.ecom.member.entity.order.OrderLink;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description :新的申请表数据结构
 * @date 2024/7/4
 */
public class OrderFormDTO extends BaseOrderFilter {
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private OrderBaseInfoDTO orderBaseInfo;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private LabDTO lab;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private OrderReportDTO orderReport;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private OrderApplicationFormDTO orderApplicationForm;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    @BeanAnno(dtocls = {OrderSampleDTO.class})
    private List<OrderSampleDTO> orderSampleIList = new ArrayList<>();
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private OrderProductDTO orderProduct;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private OrderInvoiceDTO orderInvoice;

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private AttrInfo attrInfo;//认证信息

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private List<OrderBaseInfoDTO> subOrderList = new ArrayList<>();//补差价信息

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private List<OrderDetailDTO> suppOrderDetailList = new ArrayList<>();//补充检测项目查询


    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BankDTO bank;//银行信息

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private TicPdfDTO fromPdfInfo;//

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private List<OrderProductDTO> orderProductList = new ArrayList<>();//


    @ApiAnno(groups={BaseQryFilter.Default.class})
    private List<OrderLinkDTO> orderLinkList = new ArrayList<>();//


    @ApiAnno(groups={BaseQryFilter.Default.class})
    private ApplicationAddressDTO address;

    public List<OrderProductDTO> getOrderProductList() {
        return orderProductList;
    }

    public ApplicationAddressDTO getAddress() {
        return address;
    }

    public void setAddress(ApplicationAddressDTO address) {
        this.address = address;
    }

    public void setOrderProductList(List<OrderProductDTO> orderProductList) {
        this.orderProductList = orderProductList;
    }

    public BankDTO getBank() {
        return bank;
    }

    public void setBank(BankDTO bank) {
        this.bank = bank;
    }

    public TicPdfDTO getFromPdfInfo() {
        return fromPdfInfo;
    }

    public void setFromPdfInfo(TicPdfDTO fromPdfInfo) {
        this.fromPdfInfo = fromPdfInfo;
    }

    public List<OrderDetailDTO> getSuppOrderDetailList() {
        return suppOrderDetailList;
    }

    public void setSuppOrderDetailList(List<OrderDetailDTO> suppOrderDetailList) {
        this.suppOrderDetailList = suppOrderDetailList;
    }

    public List<OrderBaseInfoDTO> getSubOrderList() {
        return subOrderList;
    }

    public void setSubOrderList(List<OrderBaseInfoDTO> subOrderList) {
        this.subOrderList = subOrderList;
    }

    public AttrInfo getAttrInfo() {
        return attrInfo;
    }

    public void setAttrInfo(AttrInfo attrInfo) {
        this.attrInfo = attrInfo;
    }

    public OrderBaseInfoDTO getOrderBaseInfo() {
        return orderBaseInfo;
    }

    public void setOrderBaseInfo(OrderBaseInfoDTO orderBaseInfo) {
        this.orderBaseInfo = orderBaseInfo;
    }

    public LabDTO getLab() {
        return lab;
    }

    public void setLab(LabDTO lab) {
        this.lab = lab;
    }

    public OrderReportDTO getOrderReport() {
        return orderReport;
    }

    public void setOrderReport(OrderReportDTO orderReport) {
        this.orderReport = orderReport;
    }

    public OrderApplicationFormDTO getOrderApplicationForm() {
        return orderApplicationForm;
    }

    public void setOrderApplicationForm(OrderApplicationFormDTO orderApplicationForm) {
        this.orderApplicationForm = orderApplicationForm;
    }

    public List<OrderSampleDTO> getOrderSampleIList() {
        return orderSampleIList;
    }

    public void setOrderSampleIList(List<OrderSampleDTO> orderSampleIList) {
        this.orderSampleIList = orderSampleIList;
    }

    public OrderProductDTO getOrderProduct() {
        return orderProduct;
    }

    public void setOrderProduct(OrderProductDTO orderProduct) {
        this.orderProduct = orderProduct;
    }

    public OrderInvoiceDTO getOrderInvoice() {
        return orderInvoice;
    }

    public void setOrderInvoice(OrderInvoiceDTO orderInvoice) {
        this.orderInvoice = orderInvoice;
    }

    public List<OrderLinkDTO> getOrderLinkList() {
        return orderLinkList;
    }

    public void setOrderLinkList(List<OrderLinkDTO> orderLinkList) {
        this.orderLinkList = orderLinkList;
    }
}
