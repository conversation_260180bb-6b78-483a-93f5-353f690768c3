package com.sgs.ecom.member.controller.cust;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.ecom.member.request.cust.CustReq;
import com.sgs.ecom.member.service.custom.interfaces.ICustInvoiceService;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.cust/invoice")
public class CustInvoiceController extends ControllerUtil {

	@Autowired
	private ICustInvoiceService custInvoiceService;



	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "qryInvoiceListByCustId", method = { RequestMethod.POST })
	public ResultBody qryInvoiceListByCustId(
			@RequestHeader(value="accessToken") String token,
			@RequestBody CustReq custReq) throws Exception {
		return ResultBody.newInstance(custInvoiceService.qryInvoiceListByCustId(custReq,getUserDTO(token)));
	}



}
