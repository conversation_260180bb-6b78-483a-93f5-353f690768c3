package com.sgs.ecom.member.fegin;

import com.alibaba.fastjson.JSON;
import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.ecom.member.service.user.interfaces.IUserMessageNoticeConfigService;
import com.sgs.ecom.member.service.user.interfaces.IUserPromotionSV;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import com.sgs.ecom.member.vo.VOUserInfo;
import com.sgs.ecom.member.vo.VOUserPromotion;
import com.sgs.ecom.member.vo.user.VOUserMessageNoticeConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @ClassName UserMessageNoticeRpc
 * <AUTHOR>
 * @Date 2025/7/25 17:39
 */
@RestController
@RequestMapping("/business/rpc.v2.user/msgNotice")
@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
public class UserMessageNoticeRpc extends ControllerUtil {

    @Autowired
    private IUserMessageNoticeConfigService userMessageNoticeConfigService;

    /**
     * @Function: queryUserCloseNoticeConfig
     * @Description: 查询用户消息设置
     *
     * @param: userMessageNoticeConfig
     * @return: ResultBody
     *
     * @version: 1.0
     * @author: shenyi
     * @date: 2025-07-25
     */

    @HystrixCommand
    @AuthRequired(login = "NULL", sign = false, intAuth = false)
    @RequestMapping(value = "queryClose", method = { RequestMethod.POST })
    public ResultBody queryUserCloseNoticeConfig(@RequestBody VOUserMessageNoticeConfig userMessageNoticeConfig) throws Exception {
        return ResultBody.newInstance(userMessageNoticeConfigService.queryUserCloseNoticeConfig(userMessageNoticeConfig));
    }
}
