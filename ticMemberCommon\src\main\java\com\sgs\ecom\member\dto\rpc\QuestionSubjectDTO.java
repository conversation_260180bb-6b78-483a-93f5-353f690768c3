package com.sgs.ecom.member.dto.rpc;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.json.TimeFormatSerializer;
import com.sgs.base.BaseQryFilter;
import java.sql.Timestamp;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.sf.json.JSONArray;

@EqualsAndHashCode(callSuper = true)
@Data
public class QuestionSubjectDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select QUESTION_TITLE_EN,QUESTION_TYPE,PART_NO,STATE_DATE,STATE,UPLOAD_TYPE,QUESTION_TITLE,SUBJECT_NO,TEXT_SIZE,CREATE_DATE,IS_MUST,SUBJECT_ID,IS_HOT,CHECK_TYPE,SORT_SHOW,IMG_PATH,PARNET_SUBJECT_ID,MEMO from QUESTION_SUBJECT"; 
 
 
 	@ApiAnno(groups={Default.class,QuerySummary.class})
 	@BeanAnno(value="QUESTION_TITLE_EN", getName="getQuestionTitleEn", setName="setQuestionTitleEn")
 	private String questionTitleEn;
 	@ApiAnno(groups={Default.class,QuerySummary.class})
 	@BeanAnno(value="QUESTION_TYPE", getName="getQuestionType", setName="setQuestionType")
 	private int questionType;
 	@ApiAnno(groups={Default.class,Insert.class,QuerySummary.class})
 	@BeanAnno(value="PART_NO", getName="getPartNo", setName="setPartNo")
 	private String partNo;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private Timestamp stateDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(groups={Default.class,QuerySummary.class})
 	@BeanAnno(value="UPLOAD_TYPE", getName="getUploadType", setName="setUploadType")
 	private int uploadType;
 	@ApiAnno(groups={Default.class,QuerySummary.class})
 	@BeanAnno(value="QUESTION_TITLE", getName="getQuestionTitle", setName="setQuestionTitle")
 	private String questionTitle;
 	@ApiAnno(groups={Default.class,Insert.class,QuerySummary.class})
 	@BeanAnno(value="SUBJECT_NO", getName="getSubjectNo", setName="setSubjectNo")
 	private String subjectNo;
 	@ApiAnno(groups={Default.class,QuerySummary.class})
 	@BeanAnno(value="TEXT_SIZE", getName="getTextSize", setName="setTextSize")
 	private int textSize;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(groups={Default.class,QuerySummary.class})
 	@BeanAnno(value="IS_MUST", getName="getIsMust", setName="setIsMust")
 	private int isMust;
 	@ApiAnno(groups={Default.class,Insert.class,QuerySummary.class})
 	@BeanAnno(value="SUBJECT_ID", getName="getSubjectId", setName="setSubjectId")
 	private long subjectId;
 	@ApiAnno(groups={Default.class,QuerySummary.class})
 	@BeanAnno(value="IS_HOT", getName="getIsHot", setName="setIsHot")
 	private int isHot;
 	@ApiAnno(groups={Default.class,QuerySummary.class})
 	@BeanAnno(value="CHECK_TYPE", getName="getCheckType", setName="setCheckType")
 	private int checkType;
 	@ApiAnno(groups={Default.class,QuerySummary.class})
 	@BeanAnno(value="SORT_SHOW", getName="getSortShow", setName="setSortShow")
 	private int sortShow;
 	@ApiAnno(groups={Default.class,QuerySummary.class})
 	@BeanAnno(value="IMG_PATH", getName="getImgPath", setName="setImgPath")
 	private String imgPath;
 	@ApiAnno(groups={Default.class,QuerySummary.class})
 	@BeanAnno(value="PARNET_SUBJECT_ID", getName="getParnetSubjectId", setName="setParnetSubjectId")
 	private long parnetSubjectId;
 	@ApiAnno(groups={Default.class,QuerySummary.class})
 	@BeanAnno(value="MEMO", getName="getMemo", setName="setMemo")
 	private String memo;
 	@ApiAnno(groups={Default.class,QuerySummary.class})
 	@BeanAnno(value="FILL_MEMO", getName="getFillMemo", setName="setFillMemo")
 	private String fillMemo;
 	@ApiAnno(groups={Default.class,QuerySummary.class})
 	@BeanAnno(value="UPLOAD_MEMO_MUST", getName="getUploadMemoMust", setName="setUploadMemoMust")
 	private int uploadMemoMust;
 	@ApiAnno(groups={Default.class,QuerySummary.class})
 	@BeanAnno(value="IS_SHOW_IMG", getName="getIsShowImg", setName="setIsShowImg")
 	private int isShowImg;
 	@ApiAnno(groups={Default.class,QuerySummary.class})
 	@BeanAnno(value="IS_HIDE", getName="getIsHide", setName="setIsHide")
 	private int isHide;
 	@ApiAnno(groups={Default.class,QuerySummary.class})
 	@BeanAnno(value="IS_SHOW_MEMO", getName="getIsShowMemo", setName="setIsShowMemo")
 	private int isShowMemo;
 	@ApiAnno(groups={Default.class,QuerySummary.class})
 	@BeanAnno(value="OPTION_MEMO", getName="getOptionMemo", setName="setOptionMemo")
 	private String optionMemo;

	/**
	 * 是否移动端(1：是，0：否)
	 */
	@ApiAnno(groups={Default.class,QuerySummary.class})
	@BeanAnno(value="IS_SIMPLIFIED_MODE", getName="getIsSimplifiedMode", setName="setIsSimplifiedMode")
	private Integer isSimplifiedMode;

	/**
	 * 是否移动端显示必填(1：是，0：否)
	 */
	@ApiAnno(groups={Default.class,QuerySummary.class})
	@BeanAnno(value="IS_SIMPLIFIED_MUST_FILL", getName="getIsSimplifiedMustFill", setName="setIsSimplifiedMustFill")
	private Integer isSimplifiedMustFill;

	/**
	 * 选项
	 * @return
	 */
	private JSONArray options;

	/**
	 * 标签编码
	 */
	@ApiAnno(groups={Default.class,QueryList.class})
	private String labelCode;

	/**
	 * AI模型
	 */
	@ApiAnno(groups={Default.class,QueryList.class})
	private String aiModel;

	/**
	 * 标签
	 */
	@ApiAnno(groups={Default.class,QueryList.class})
	private List<QuestionLabel> labels;
 	
 	public void setQuestionTitleEn(String questionTitleEn){
 		 this.questionTitleEn=questionTitleEn;
 	}
 	public String getQuestionTitleEn(){
 		 return this.questionTitleEn;
 	}
 
 	 
 	public void setQuestionType(int questionType){
 		 this.questionType=questionType;
 	}
 	public int getQuestionType(){
 		 return this.questionType;
 	}
 
 	 
 	public void setPartNo(String partNo){
 		 this.partNo=partNo;
 	}
 	public String getPartNo(){
 		 return this.partNo;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setUploadType(int uploadType){
 		 this.uploadType=uploadType;
 	}
 	public int getUploadType(){
 		 return this.uploadType;
 	}
 
 	 
 	public void setQuestionTitle(String questionTitle){
 		 this.questionTitle=questionTitle;
 	}
 	public String getQuestionTitle(){
 		 return this.questionTitle;
 	}
 
 	 
 	public void setSubjectNo(String subjectNo){
 		 this.subjectNo=subjectNo;
 	}
 	public String getSubjectNo(){
 		 return this.subjectNo;
 	}
 
 	 
 	public void setTextSize(int textSize){
 		 this.textSize=textSize;
 	}
 	public int getTextSize(){
 		 return this.textSize;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setIsMust(int isMust){
 		 this.isMust=isMust;
 	}
 	public int getIsMust(){
 		 return this.isMust;
 	}
 
 	 
 	public void setSubjectId(long subjectId){
 		 this.subjectId=subjectId;
 	}
 	public long getSubjectId(){
 		 return this.subjectId;
 	}
 
 	 
 	public void setIsHot(int isHot){
 		 this.isHot=isHot;
 	}
 	public int getIsHot(){
 		 return this.isHot;
 	}
 
 	 
 	public void setCheckType(int checkType){
 		 this.checkType=checkType;
 	}
 	public int getCheckType(){
 		 return this.checkType;
 	}
 
 	 
 	public void setSortShow(int sortShow){
 		 this.sortShow=sortShow;
 	}
 	public int getSortShow(){
 		 return this.sortShow;
 	}
 
 	 
 	public void setImgPath(String imgPath){
 		 this.imgPath=imgPath;
 	}
 	public String getImgPath(){
 		 return this.imgPath;
 	}
 
 	 
 	public void setParnetSubjectId(long parnetSubjectId){
 		 this.parnetSubjectId=parnetSubjectId;
 	}
 	public long getParnetSubjectId(){
 		 return this.parnetSubjectId;
 	}
 
 	 
 	public void setMemo(String memo){
 		 this.memo=memo;
 	}
 	public String getMemo(){
 		 return this.memo;
 	}
	public String getFillMemo() {
		return fillMemo;
	}
	public void setFillMemo(String fillMemo) {
		this.fillMemo = fillMemo;
	}
	public int getUploadMemoMust() {
		return uploadMemoMust;
	}
	public void setUploadMemoMust(int uploadMemoMust) {
		this.uploadMemoMust = uploadMemoMust;
	}
	public int getIsShowImg() {
		return isShowImg;
	}
	public void setIsShowImg(int isShowImg) {
		this.isShowImg = isShowImg;
	}
	public int getIsHide() {
		return isHide;
	}
	public void setIsHide(int isHide) {
		this.isHide = isHide;
	}
	public int getIsShowMemo() {
		return isShowMemo;
	}
	public void setIsShowMemo(int isShowMemo) {
		this.isShowMemo = isShowMemo;
	}
	public String getOptionMemo() {
		return optionMemo;
	}
	public void setOptionMemo(String optionMemo) {
		this.optionMemo = optionMemo;
	}

}