package com.sgs.ecom.member.aop;

import com.platform.util.ValidationUtil;
import com.sgs.ecom.member.annotation.CheckUserToolKitPermission;
import com.sgs.ecom.member.bo.SysMemberBenefits;
import com.sgs.ecom.member.dto.user.UserDTO;
import com.sgs.ecom.member.enums.UserWelfareStatus;
import com.sgs.ecom.member.service.user.interfaces.ISysMemberBenefitsService;
import com.sgs.ecom.member.service.user.interfaces.IUserInfoService;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultCode;
import com.sgs.ecom.member.util.select.SelectMapUtil;
import com.sgs.ecom.member.vo.VOUserInfo;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Scope;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> zhang
 * @date 2022年04月28日 13:47
 */
@Aspect
@Scope
@Component
public class CheckUserToolKitPermissionContract {
    @Qualifier("userToolController")
    @Autowired
    private ControllerUtil controllerUtil;
    @Autowired
    private IUserInfoService iUserInfoService;
    @Autowired
    private ISysMemberBenefitsService iSysMemberBenefitsService;

    @Pointcut("@annotation(com.sgs.ecom.member.annotation.CheckUserToolKitPermission)")
    @Order(10)
    public void controllerAspect() {

    }

    /**
     * 前置通知 (在方法执行之前返回)用于拦截Controller层记录用户的操作的开始时间
     *
     * @param joinPoint 切点
     * @throws InterruptedException
     */
    @Before("controllerAspect()")
    public void CheckUserToolKitPermission(JoinPoint joinPoint) throws Exception {
    	String toolCode = null;
    	MethodSignature auth = (MethodSignature) joinPoint.getSignature();
    	Method method = auth.getMethod();
    	Annotation[] annotations = method.getAnnotations();
    	for (Annotation annotation : annotations) {
    		if (annotation.annotationType().equals(CheckUserToolKitPermission.class)) {
    			CheckUserToolKitPermission permission = method.getAnnotation(CheckUserToolKitPermission.class);
	      		if (!ValidationUtil.isEmpty(permission))
                    toolCode = permission.value();
	      		
	      		break;
    		}
    	}
    	
        /**
         * 校验当前用户是否具有工具包权限
         */

            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes())
                    .getRequest();
            String accessToken = request.getHeader("accessToken");
            UserDTO userDTO = controllerUtil.getUserDTO(accessToken);
            if(ValidationUtil.isEmpty(userDTO))
                throw new BusinessException("9978");

            VOUserInfo voUserInfo = iUserInfoService.selectByPrimaryKey(userDTO.getUserId());
            Map<String, Object> qryMap = new HashMap<>();
            qryMap.put(SelectMapUtil.BENEFITS_TYPE, UserWelfareStatus.TOOL_KIT.getIndex());
            qryMap.put(SelectMapUtil.STATE, 1);
            qryMap.put(SelectMapUtil.LEVEL_ID, voUserInfo.getLevelId());
            qryMap.put(SelectMapUtil.USER_ID, userDTO.getUserId());
            List<String> toolCodeList = Arrays.asList(toolCode.split(","));
            qryMap.put(SelectMapUtil.TOOL_CODE_LIST, toolCodeList);
            List<SysMemberBenefits> list = iSysMemberBenefitsService.qryUserToolKitByMap(qryMap);

            if (ValidationUtil.isEmpty(list)) {
                throw new BusinessException(ResultCode.TOOLKIT_IS_NULL);
            }

    }

}
