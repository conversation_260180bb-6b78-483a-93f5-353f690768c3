package com.sgs.ecom.member.enumtool.sl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum TicshkzEnum {
    //type 0-输入框 1-前缀方形勾选框 2-圆形前缀勾选
    //isMust 0-否 1-是
    SAMPLE_NAME("sampleName","样品名称",0,1),
    SAMPLE_NUM("sampleNum","样品数量",0,0),
    SPECIFICATION_MODEL("specificationModel","规格型号",0,0),
    COLOUR("colour","颜色",0,0),
    MANUFACTURER_NAME("manufacturerName","生产商名称",0,0),
    PRODUCED_DATE("producedDate","生产日期",0,0),
    USEFUL_LIFE("usefulLife","有效期信息",0,0),
    FILTRATION_EFFICIENCY_CLASS("filtrationEfficiencyClass","过滤效率等级",2,1),
    PROTECTIVE_EFFECT_LEVEL("protectiveEffectLevel","防护效果级别",2,1),
    IS_STERILIZATION("isSterilization","是否经过环氧乙烷灭菌处理",2,1)
    ;
    private String nameEN;
    private String nameCN;
    private Integer type;
    private Integer isMust;

    TicshkzEnum(String nameEN, String nameCN, Integer type, Integer isMust) {
        this.nameEN = nameEN;
        this.nameCN = nameCN;
        this.type = type;
        this.isMust = isMust;
    }
    /**
     * 转为数据
     * @return 枚举对象数组
     */
    public static List<Map<String, Object>> toList() {
        List<Map<String, Object>> list = new ArrayList<>();
        for (TicshkzEnum item : TicshkzEnum.values()) {
            Map<String, Object> map = new HashMap<>();
            map.put("nameEN", item.getNameEN());
            map.put("nameCN", item.getNameCN());
            map.put("type", item.getType());
            map.put("isMust", item.getIsMust());
            list.add(map);
        }
        return list;
    }

    public String getNameEN() {
        return nameEN;
    }

    public void setNameEN(String nameEN) {
        this.nameEN = nameEN;
    }

    public String getNameCN() {
        return nameCN;
    }

    public void setNameCN(String nameCN) {
        this.nameCN = nameCN;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getIsMust() {
        return isMust;
    }

    public void setIsMust(Integer isMust) {
        this.isMust = isMust;
    }
}
