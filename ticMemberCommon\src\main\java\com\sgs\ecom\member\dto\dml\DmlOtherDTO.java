package com.sgs.ecom.member.dto.dml;

import com.alibaba.fastjson.JSONObject;
import com.platform.annotation.ApiAnno;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.request.oiq.OiqApplicationAttrReq;
import com.sgs.ecom.member.request.oiq.OiqOrderLinkAttrReq;

import java.util.ArrayList;
import java.util.List;

public class DmlOtherDTO {

    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private List<JSONObject> testMemoImg=new ArrayList<>();
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String labCode;

    private int acceptSubcon=1;//isTransfer 样品是否分包 0否 1是
    private Integer reportTitleType;

    private OiqApplicationAttrReq oiqApplicationAttrReq;

    private OiqOrderLinkAttrReq oiqOrderLinkAttrReq;



    public DmlOtherDTO() {
    }



    public int getAcceptSubcon() {
        return acceptSubcon;
    }

    public void setAcceptSubcon(int acceptSubcon) {
        this.acceptSubcon = acceptSubcon;
    }

    public Integer getReportTitleType() {
        return reportTitleType;
    }

    public void setReportTitleType(Integer reportTitleType) {
        this.reportTitleType = reportTitleType;
    }

    public OiqApplicationAttrReq getOiqApplicationAttrReq() {
        return oiqApplicationAttrReq;
    }

    public void setOiqApplicationAttrReq(OiqApplicationAttrReq oiqApplicationAttrReq) {
        this.oiqApplicationAttrReq = oiqApplicationAttrReq;
    }

    public List<JSONObject> getTestMemoImg() {
        return testMemoImg;
    }

    public void setTestMemoImg(List<JSONObject> testMemoImg) {
        this.testMemoImg = testMemoImg;
    }

    public String getLabCode() {
        return labCode;
    }

    public void setLabCode(String labCode) {
        this.labCode = labCode;
    }

    public OiqOrderLinkAttrReq getOiqOrderLinkAttrReq() {
        return oiqOrderLinkAttrReq;
    }

    public void setOiqOrderLinkAttrReq(OiqOrderLinkAttrReq oiqOrderLinkAttrReq) {
        this.oiqOrderLinkAttrReq = oiqOrderLinkAttrReq;
    }
}
