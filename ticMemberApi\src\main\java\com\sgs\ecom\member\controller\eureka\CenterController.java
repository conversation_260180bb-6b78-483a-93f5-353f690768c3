package com.sgs.ecom.member.controller.eureka;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.ecom.member.request.rpc.CenterCouponReq;
import com.sgs.ecom.member.request.rpc.CenterProductReq;
import com.sgs.ecom.member.service.util.interfaces.ICenterService;
import com.sgs.ecom.member.service.util.interfaces.IUserService;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RequestMapping("/business/rpc.v2.member/center")
public class CenterController extends ControllerUtil {
    @Autowired
    private ICenterService centerService;
    @Autowired
    private IUserService userService;


    @RequestMapping(value = "saveCoupon", method = { RequestMethod.POST })
    public ResultBody saveCoupon (
            @RequestBody CenterCouponReq centerCouponReq)throws Exception{
        centerService.saveCoupon(centerCouponReq);
        return ResultBody.success();
    }


    @RequestMapping(value = "saveCouponByUserId", method = { RequestMethod.POST })
    public ResultBody receive(
            @RequestBody CenterCouponReq centerCouponReq) throws Exception {
        userService.receiveCoupon(centerCouponReq.getUserId());
        return ResultBody.success();
    }

    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "qryProductNum", method = { RequestMethod.POST })
    public ResultBody qryProductNum (@RequestHeader(value="accessToken") String accessToken)throws Exception{
        CenterProductReq centerProductReq=new CenterProductReq();
        centerProductReq.setUserId(getUserDTO(accessToken).getUserId());
        return ResultBody.newInstance(centerService.qryProductNum(centerProductReq));
    }

}
