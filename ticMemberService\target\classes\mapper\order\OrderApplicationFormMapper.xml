<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.user.dao.OrderApplicationFormMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.vo.VOOrderApplicationForm" >
    <id column="FORM_ID" property="formId" jdbcType="BIGINT" />
    <result column="FORM_TEMPLATE" property="formTemplate" jdbcType="VARCHAR" />
    <result column="TEST_CYCLE" property="testCycle" jdbcType="INTEGER" />
    <result column="COMPANY_NAME_CN" property="companyNameCn" jdbcType="VARCHAR" />
    <result column="COMPANY_ADDRESS_CN" property="companyAddressCn" jdbcType="VARCHAR" />
    <result column="COMPANY_NAME_EN" property="companyNameEn" jdbcType="VARCHAR" />
    <result column="COMPANY_ADDRESS_EN" property="companyAddressEn" jdbcType="VARCHAR" />
    <result column="REPORT_METHOD" property="reportMethod" jdbcType="VARCHAR" />
    <result column="LINK_PERSON" property="linkPerson" jdbcType="VARCHAR" />
    <result column="LINK_PHONE" property="linkPhone" jdbcType="VARCHAR" />
    <result column="LINK_EMAIL" property="linkEmail" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="TINYINT" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    FORM_ID, FORM_TEMPLATE, TEST_CYCLE, COMPANY_NAME_CN, COMPANY_ADDRESS_CN, COMPANY_NAME_EN, 
    COMPANY_ADDRESS_EN, LINK_PERSON, LINK_PHONE, LINK_EMAIL, STATE, CREATE_DATE, STATE_DATE
  </sql>


  <insert id="insertVOSelective" parameterType="com.sgs.ecom.member.vo.VOOrderApplicationForm" >
    insert into ORDER_APPLICATION_FORM
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="formId != null" >
        FORM_ID,
      </if>
      <if test="formTemplate != null" >
        FORM_TEMPLATE,
      </if>
      <if test="testCycle != null" >
        TEST_CYCLE,
      </if>
      <if test="companyNameCn != null" >
        COMPANY_NAME_CN,
      </if>
      <if test="companyAddressCn != null" >
        COMPANY_ADDRESS_CN,
      </if>
      <if test="companyNameEn != null" >
        COMPANY_NAME_EN,
      </if>
      <if test="companyAddressEn != null" >
        COMPANY_ADDRESS_EN,
      </if>
      <if test="linkPerson != null" >
        LINK_PERSON,
      </if>
      <if test="linkPhone != null" >
        LINK_PHONE,
      </if>
      <if test="linkEmail != null" >
        LINK_EMAIL,
      </if>
      <if test="state != null" >
        STATE,
      </if>
      <if test="createDate != null" >
        CREATE_DATE,
      </if>
      <if test="stateDate != null" >
        STATE_DATE,
      </if>
      <if test="orderNo != null" >
        ORDER_NO,
      </if>
      <if test="isRefundSample != null" >
        IS_REFUND_SAMPLE,
      </if>
      <if test="reportMethod != null" >
        REPORT_METHOD,
      </if>
      <if test="reportSendCc != null" >
        REPORT_SEND_CC,
      </if>
      <if test="testMemo != null" >
        TEST_MEMO,
      </if>
      <if test="receiveName != null" >
        RECEIVE_NAME,
      </if>
      <if test="receivePhone != null" >
        RECEIVE_PHONE,
      </if>
      <if test="receiveEmail != null" >
        RECEIVE_EMAIL,
      </if>
      <if test="province != null" >
        PROVINCE,
      </if>
      <if test="city != null" >
        CITY,
      </if>
      <if test="town != null" >
        TOWN,
      </if>
      <if test="isTransfer != null" >
        IS_TRANSFER,
      </if>
      <if test="taxNo != null" >
        TAX_NO,
      </if>
      <if test="regPhone != null" >
        REG_PHONE,
      </if>
      <if test="bankName != null" >
        BANK_NAME,
      </if>
      <if test="bankNumber != null" >
        BANK_NUMBER,
      </if>
      <if test="confirmSource != null" >
        CONFIRM_SOURCE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="formId != null" >
        #{formId,jdbcType=BIGINT},
      </if>
      <if test="formTemplate != null" >
        #{formTemplate,jdbcType=VARCHAR},
      </if>
      <if test="testCycle != null" >
        #{testCycle,jdbcType=INTEGER},
      </if>
      <if test="companyNameCn != null" >
        #{companyNameCn,jdbcType=VARCHAR},
      </if>
      <if test="companyAddressCn != null" >
        #{companyAddressCn,jdbcType=VARCHAR},
      </if>
      <if test="companyNameEn != null" >
        #{companyNameEn,jdbcType=VARCHAR},
      </if>
      <if test="companyAddressEn != null" >
        #{companyAddressEn,jdbcType=VARCHAR},
      </if>
      <if test="linkPerson != null" >
        #{linkPerson,jdbcType=VARCHAR},
      </if>
      <if test="linkPhone != null" >
        #{linkPhone,jdbcType=VARCHAR},
      </if>
      <if test="linkEmail != null" >
        #{linkEmail,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        #{state,jdbcType=TINYINT},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        #{stateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="isRefundSample != null" >
        #{isRefundSample},
      </if>
      <if test="reportMethod != null" >
        #{reportMethod},
      </if>
      <if test="reportSendCc != null" >
        #{reportSendCc,jdbcType=VARCHAR},
      </if>
      <if test="testMemo != null" >
        #{testMemo,jdbcType=VARCHAR},
      </if>
      <if test="receiveName != null" >
        #{receiveName,jdbcType=VARCHAR},
      </if>
      <if test="receivePhone != null" >
        #{receivePhone,jdbcType=VARCHAR},
      </if>
      <if test="receiveEmail != null" >
        #{receiveEmail,jdbcType=VARCHAR},
      </if>
      <if test="province != null" >
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null" >
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="town != null" >
        #{town,jdbcType=VARCHAR},
      </if>
      <if test="isTransfer != null" >
        #{isTransfer},
      </if>
      <if test="taxNo != null" >
        #{taxNo,jdbcType=VARCHAR},
      </if>
      <if test="regPhone != null" >
        #{regPhone,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null" >
        #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="bankNumber != null" >
        #{bankNumber,jdbcType=VARCHAR},
      </if>
      <if test="confirmSource != null" >
        #{confirmSource,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateVOByPrimaryKeySelective" parameterType="com.sgs.ecom.member.vo.VOOrderApplicationForm" >
    update ORDER_APPLICATION_FORM
    <set >
      <if test="formTemplate != null" >
        FORM_TEMPLATE = #{formTemplate,jdbcType=VARCHAR},
      </if>
      <if test="testCycle != null" >
        TEST_CYCLE = #{testCycle,jdbcType=INTEGER},
      </if>
      <if test="companyNameCn != null" >
        COMPANY_NAME_CN = #{companyNameCn,jdbcType=VARCHAR},
      </if>
      <if test="companyAddressCn != null" >
        COMPANY_ADDRESS_CN = #{companyAddressCn,jdbcType=VARCHAR},
      </if>
      <if test="companyNameEn != null" >
        COMPANY_NAME_EN = #{companyNameEn,jdbcType=VARCHAR},
      </if>
      <if test="companyAddressEn != null" >
        COMPANY_ADDRESS_EN = #{companyAddressEn,jdbcType=VARCHAR},
      </if>
      <if test="linkPerson != null" >
        LINK_PERSON = #{linkPerson,jdbcType=VARCHAR},
      </if>
      <if test="linkPhone != null" >
        LINK_PHONE = #{linkPhone,jdbcType=VARCHAR},
      </if>
      <if test="linkEmail != null" >
        LINK_EMAIL = #{linkEmail,jdbcType=VARCHAR},
      </if>
      <if test="receiveName != null" >
        RECEIVE_NAME = #{receiveName,jdbcType=VARCHAR},
      </if>
      <if test="receivePhone != null" >
        RECEIVE_PHONE = #{receivePhone,jdbcType=VARCHAR},
      </if>
      <if test="receiveEmail != null" >
        RECEIVE_EMAIL = #{receiveEmail,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        STATE = #{state,jdbcType=TINYINT},
      </if>
      <if test="createDate != null" >
        CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        STATE_DATE = #{stateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="isRefundSample != null" >
        IS_REFUND_SAMPLE = #{isRefundSample,jdbcType=INTEGER},
      </if>
      <if test="reportMethod != null" >
        REPORT_METHOD = #{reportMethod},
      </if>
      <if test="reportSendCc != null" >
        REPORT_SEND_CC = #{reportSendCc,jdbcType=VARCHAR},
      </if>
      <if test="testMemo != null" >
        TEST_MEMO = #{testMemo,jdbcType=VARCHAR},
      </if>
      <if test="province != null" >
        PROVINCE = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null" >
        CITY = #{city,jdbcType=VARCHAR},
      </if>
      <if test="town != null" >
        TOWN = #{town,jdbcType=VARCHAR},
      </if>
      <if test="isTransfer != null" >
        IS_TRANSFER = #{isTransfer,jdbcType=INTEGER},
      </if>
      <if test="confirmSource != null" >
        CONFIRM_SOURCE = #{confirmSource},
      </if>
    </set>
    where FORM_ID = #{formId,jdbcType=BIGINT}
  </update>





  <!--自定义 -->
  <resultMap id="resultDTO" type="com.sgs.ecom.member.dto.detail.OrderApplicationFormDTO" >
    <id column="FORM_ID" property="formId" jdbcType="BIGINT" />
    <id column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="FORM_TEMPLATE" property="formTemplate" jdbcType="VARCHAR" />
    <result column="TEST_CYCLE" property="testCycle" jdbcType="INTEGER" />
    <result column="COMPANY_NAME_CN" property="companyNameCn" jdbcType="VARCHAR" />
    <result column="COMPANY_ADDRESS_CN" property="companyAddressCn" jdbcType="VARCHAR" />
    <result column="COMPANY_NAME_EN" property="companyNameEn" jdbcType="VARCHAR" />
    <result column="COMPANY_ADDRESS_EN" property="companyAddressEn" jdbcType="VARCHAR" />
    <result column="LINK_PERSON" property="linkPerson" jdbcType="VARCHAR" />
    <result column="LINK_PHONE" property="linkPhone" jdbcType="VARCHAR" />
    <result column="LINK_EMAIL" property="linkEmail" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="TINYINT" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
    <result column="REPORT_SEND_CC" property="reportSendCc" jdbcType="VARCHAR" />
    <result column="IS_REFUND_SAMPLE" property="isRefundSample" jdbcType="INTEGER" />
    <result column="TEST_MEMO" property="testMemo" jdbcType="VARCHAR" />
    <result column="RECEIVE_NAME" property="receiveName" jdbcType="VARCHAR" />
    <result column="RECEIVE_PHONE" property="receivePhone" jdbcType="VARCHAR" />
    <result column="RECEIVE_EMAIL" property="receiveEmail" jdbcType="VARCHAR" />
    <result column="REPORT_METHOD" property="reportMethod" jdbcType="VARCHAR" />
    <result column="IS_TRANSFER" property="isTransfer" jdbcType="VARCHAR" />
    <result column="TAX_NO" property="taxNo" jdbcType="VARCHAR" />
    <result column="REG_PHONE" property="regPhone" jdbcType="VARCHAR" />
    <result column="BANK_NUMBER" property="bankNumber" jdbcType="VARCHAR" />
    <result column="BANK_NAME" property="bankName" jdbcType="VARCHAR" />
    <result column="PROVINCE" property="province" jdbcType="VARCHAR" />
    <result column="CITY" property="city" jdbcType="VARCHAR" />
    <result column="TOWN" property="town" jdbcType="VARCHAR" />
  </resultMap>

  <resultMap id="resultMap" type="com.sgs.ecom.member.entity.order.OrderApplicationForm" >
    <id column="FORM_ID" property="formId" jdbcType="BIGINT" />
    <id column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="FORM_TEMPLATE" property="formTemplate" jdbcType="VARCHAR" />
    <result column="TEST_CYCLE" property="testCycle" jdbcType="INTEGER" />
    <result column="COMPANY_NAME_CN" property="companyNameCn" jdbcType="VARCHAR" />
    <result column="COMPANY_ADDRESS_CN" property="companyAddressCn" jdbcType="VARCHAR" />
    <result column="COMPANY_NAME_EN" property="companyNameEn" jdbcType="VARCHAR" />
    <result column="COMPANY_ADDRESS_EN" property="companyAddressEn" jdbcType="VARCHAR" />
    <result column="LINK_PERSON" property="linkPerson" jdbcType="VARCHAR" />
    <result column="LINK_PHONE" property="linkPhone" jdbcType="VARCHAR" />
    <result column="LINK_EMAIL" property="linkEmail" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="TINYINT" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
    <result column="REPORT_SEND_CC" property="reportSendCc" jdbcType="VARCHAR" />
    <result column="IS_REFUND_SAMPLE" property="isRefundSample" jdbcType="INTEGER" />
    <result column="TEST_MEMO" property="testMemo" jdbcType="VARCHAR" />
    <result column="RECEIVE_NAME" property="receiveName" jdbcType="VARCHAR" />
    <result column="RECEIVE_PHONE" property="receivePhone" jdbcType="VARCHAR" />
    <result column="RECEIVE_EMAIL" property="receiveEmail" jdbcType="VARCHAR" />
    <result column="REPORT_METHOD" property="reportMethod" jdbcType="VARCHAR" />
    <result column="IS_TRANSFER" property="isTransfer" jdbcType="VARCHAR" />
    <result column="TAX_NO" property="taxNo" jdbcType="VARCHAR" />
    <result column="REG_PHONE" property="regPhone" jdbcType="VARCHAR" />
    <result column="BANK_NUMBER" property="bankNumber" jdbcType="VARCHAR" />
    <result column="BANK_NAME" property="bankName" jdbcType="VARCHAR" />
    <result column="PROVINCE" property="province" jdbcType="VARCHAR" />
    <result column="CITY" property="city" jdbcType="VARCHAR" />
    <result column="TOWN" property="town" jdbcType="VARCHAR" />
  </resultMap>

  <sql id="sqlListDTO" >
    FORM_ID,ORDER_NO, FORM_TEMPLATE, TEST_CYCLE, COMPANY_NAME_CN, COMPANY_ADDRESS_CN, COMPANY_NAME_EN,
    COMPANY_ADDRESS_EN, LINK_PERSON, LINK_PHONE, LINK_EMAIL, STATE, CREATE_DATE, STATE_DATE,
    REPORT_SEND_CC,IS_REFUND_SAMPLE,TEST_MEMO,RECEIVE_NAME,RECEIVE_PHONE,RECEIVE_EMAIL,REPORT_METHOD,
    IS_TRANSFER,TAX_NO,REG_PHONE,BANK_NUMBER,BANK_NAME,PROVINCE,CITY,TOWN

  </sql>

  <select id="selectListByMap" resultMap="resultDTO" >
    select
    <include refid="sqlListDTO" />
    from ORDER_APPLICATION_FORM
    <include refid="baseQueryWhere"/>
  </select>
  <sql id="baseQueryWhere">
    where 1=1
    <if test="orderNo != null">
      AND ORDER_NO=#{orderNo}
    </if>
    <if test="state != null">
      AND STATE=#{state}
    </if>
    <if test="orderNoList != null ">
      AND ORDER_NO in
      <foreach collection="orderNoList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </sql>

  <select id="selectOneByVOBase" resultMap="resultDTO" >
    select
    <include refid="sqlListDTO" />
    from ORDER_APPLICATION_FORM
    <include refid="baseQueryVOWhere"/>
  </select>
  <sql id="baseQueryVOWhere">
    <where>
      <if test="orderNo != null">
        AND ORDER_NO=#{orderNo}
      </if>
      <if test="state != null">
        AND STATE=#{state}
      </if>
    </where>
  </sql>




  <select id="selectLastFormByUserId" resultMap="resultDTO" >
    select oaf.FORM_ID,oaf.RECEIVE_NAME,oaf.RECEIVE_PHONE,oaf.RECEIVE_EMAIL,oaf.COMPANY_ADDRESS_CN,obi.ORDER_NO
    from ORDER_APPLICATION_FORM oaf,ORDER_BASE_INFO obi
    where oaf.ORDER_NO=obi.ORDER_NO
    and obi.state!=11 and obi.state!=91
    and obi.USER_ID=#{userId}
    and obi.ORDER_TYPE=210000 order by oaf.STATE_DATE desc limit 1
    </select>


  <select id="selectLastFormOrderNoByUserLaseVO"  resultType="java.lang.String" >
    select obi.ORDER_NO
    from ORDER_APPLICATION_FORM oaf,ORDER_BASE_INFO obi
    <if test="businessCode != null">
      left join ORDER_APPLICATION_ATTR oaa on oaa.ORDER_NO=obi.ORDER_NO and oaa.ATTR_CODE='sampleBusinessCode'
    </if>
    where oaf.ORDER_NO=obi.ORDER_NO and obi.state!=11 and obi.state!=91
    and obi.USER_ID=#{userId}
    <if test="bu != null" >
      and bu=#{bu}
    </if>
    <if test="businessCode != null" >
      and oaa.ATTR_VALUE=#{businessCode}
    </if>
    <if test="orderType != null" >
      and ORDER_TYPE=#{orderType}
    </if>
     order by oaf.STATE_DATE desc limit 1
  </select>
    <select id="qryOrderApplicationForm" resultMap ="resultMap">
      select
      <include refid="sqlListDTO" />
      from ORDER_APPLICATION_FORM
      <include refid="baseQueryVOWhere"/>

    </select>


    <delete id="delFormByMap" >
    delete from ORDER_APPLICATION_FORM
    where ORDER_NO=#{orderNo}
  </delete>

  <insert id="insertEntity" parameterType="com.sgs.ecom.member.entity.order.OrderApplicationForm" >
    insert into ORDER_APPLICATION_FORM
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="formId != null" >
        FORM_ID,
      </if>
      <if test="formTemplate != null" >
        FORM_TEMPLATE,
      </if>
      <if test="testCycle != null" >
        TEST_CYCLE,
      </if>
      <if test="companyNameCn != null" >
        COMPANY_NAME_CN,
      </if>
      <if test="companyAddressCn != null" >
        COMPANY_ADDRESS_CN,
      </if>
      <if test="companyNameEn != null" >
        COMPANY_NAME_EN,
      </if>
      <if test="companyAddressEn != null" >
        COMPANY_ADDRESS_EN,
      </if>
      <if test="linkPerson != null" >
        LINK_PERSON,
      </if>
      <if test="linkPhone != null" >
        LINK_PHONE,
      </if>
      <if test="linkEmail != null" >
        LINK_EMAIL,
      </if>
      <if test="state != null" >
        STATE,
      </if>
      <if test="createDate != null" >
        CREATE_DATE,
      </if>
      <if test="stateDate != null" >
        STATE_DATE,
      </if>
      <if test="orderNo != null" >
        ORDER_NO,
      </if>
      <if test="isRefundSample != null" >
        IS_REFUND_SAMPLE,
      </if>
      <if test="reportMethod != null" >
        REPORT_METHOD,
      </if>
      <if test="reportSendCc != null" >
        REPORT_SEND_CC,
      </if>
      <if test="testMemo != null" >
        TEST_MEMO,
      </if>
      <if test="receiveName != null" >
        RECEIVE_NAME,
      </if>
      <if test="receivePhone != null" >
        RECEIVE_PHONE,
      </if>
      <if test="receiveEmail != null" >
        RECEIVE_EMAIL,
      </if>
      <if test="province != null" >
        PROVINCE,
      </if>
      <if test="city != null" >
        CITY,
      </if>
      <if test="town != null" >
        TOWN,
      </if>
      <if test="isTransfer != null" >
        IS_TRANSFER,
      </if>
      <if test="taxNo != null" >
        TAX_NO,
      </if>
      <if test="regPhone != null" >
        REG_PHONE,
      </if>
      <if test="bankName != null" >
        BANK_NAME,
      </if>
      <if test="bankNumber != null" >
        BANK_NUMBER,
      </if>
      <if test="confirmSource != null" >
        CONFIRM_SOURCE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="formId != null" >
        #{formId,jdbcType=BIGINT},
      </if>
      <if test="formTemplate != null" >
        #{formTemplate,jdbcType=VARCHAR},
      </if>
      <if test="testCycle != null" >
        #{testCycle,jdbcType=INTEGER},
      </if>
      <if test="companyNameCn != null" >
        #{companyNameCn,jdbcType=VARCHAR},
      </if>
      <if test="companyAddressCn != null" >
        #{companyAddressCn,jdbcType=VARCHAR},
      </if>
      <if test="companyNameEn != null" >
        #{companyNameEn,jdbcType=VARCHAR},
      </if>
      <if test="companyAddressEn != null" >
        #{companyAddressEn,jdbcType=VARCHAR},
      </if>
      <if test="linkPerson != null" >
        #{linkPerson,jdbcType=VARCHAR},
      </if>
      <if test="linkPhone != null" >
        #{linkPhone,jdbcType=VARCHAR},
      </if>
      <if test="linkEmail != null" >
        #{linkEmail,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        #{state,jdbcType=TINYINT},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        #{stateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="isRefundSample != null" >
        #{isRefundSample},
      </if>
      <if test="reportMethod != null" >
        #{reportMethod},
      </if>
      <if test="reportSendCc != null" >
        #{reportSendCc,jdbcType=VARCHAR},
      </if>
      <if test="testMemo != null" >
        #{testMemo,jdbcType=VARCHAR},
      </if>
      <if test="receiveName != null" >
        #{receiveName,jdbcType=VARCHAR},
      </if>
      <if test="receivePhone != null" >
        #{receivePhone,jdbcType=VARCHAR},
      </if>
      <if test="receiveEmail != null" >
        #{receiveEmail,jdbcType=VARCHAR},
      </if>
      <if test="province != null" >
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null" >
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="town != null" >
        #{town,jdbcType=VARCHAR},
      </if>
      <if test="isTransfer != null" >
        #{isTransfer},
      </if>
      <if test="taxNo != null" >
        #{taxNo,jdbcType=VARCHAR},
      </if>
      <if test="regPhone != null" >
        #{regPhone,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null" >
        #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="bankNumber != null" >
        #{bankNumber,jdbcType=VARCHAR},
      </if>
      <if test="confirmSource != null" >
        #{confirmSource,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateEntity" parameterType="com.sgs.ecom.member.entity.order.OrderApplicationForm" >
    update ORDER_APPLICATION_FORM
    <set >
      <if test="formTemplate != null" >
        FORM_TEMPLATE = #{formTemplate,jdbcType=VARCHAR},
      </if>
      <if test="testCycle != null" >
        TEST_CYCLE = #{testCycle,jdbcType=INTEGER},
      </if>
      <if test="companyNameCn != null" >
        COMPANY_NAME_CN = #{companyNameCn,jdbcType=VARCHAR},
      </if>
      <if test="companyAddressCn != null" >
        COMPANY_ADDRESS_CN = #{companyAddressCn,jdbcType=VARCHAR},
      </if>
      <if test="companyNameEn != null" >
        COMPANY_NAME_EN = #{companyNameEn,jdbcType=VARCHAR},
      </if>
      <if test="companyAddressEn != null" >
        COMPANY_ADDRESS_EN = #{companyAddressEn,jdbcType=VARCHAR},
      </if>
      <if test="linkPerson != null" >
        LINK_PERSON = #{linkPerson,jdbcType=VARCHAR},
      </if>
      <if test="linkPhone != null" >
        LINK_PHONE = #{linkPhone,jdbcType=VARCHAR},
      </if>
      <if test="linkEmail != null" >
        LINK_EMAIL = #{linkEmail,jdbcType=VARCHAR},
      </if>
      <if test="receiveName != null" >
        RECEIVE_NAME = #{receiveName,jdbcType=VARCHAR},
      </if>
      <if test="receivePhone != null" >
        RECEIVE_PHONE = #{receivePhone,jdbcType=VARCHAR},
      </if>
      <if test="receiveEmail != null" >
        RECEIVE_EMAIL = #{receiveEmail,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        STATE = #{state,jdbcType=TINYINT},
      </if>
      <if test="createDate != null" >
        CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        STATE_DATE = #{stateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="isRefundSample != null" >
        IS_REFUND_SAMPLE = #{isRefundSample,jdbcType=INTEGER},
      </if>
      <if test="reportMethod != null" >
        REPORT_METHOD = #{reportMethod},
      </if>
      <if test="reportSendCc != null" >
        REPORT_SEND_CC = #{reportSendCc,jdbcType=VARCHAR},
      </if>
      <if test="testMemo != null" >
        TEST_MEMO = #{testMemo,jdbcType=VARCHAR},
      </if>
      <if test="province != null" >
        PROVINCE = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null" >
        CITY = #{city,jdbcType=VARCHAR},
      </if>
      <if test="town != null" >
        TOWN = #{town,jdbcType=VARCHAR},
      </if>
      <if test="isTransfer != null" >
        IS_TRANSFER = #{isTransfer,jdbcType=INTEGER},
      </if>
      <if test="confirmSource != null" >
        CONFIRM_SOURCE = #{confirmSource},
      </if>
    </set>
    where FORM_ID = #{formId,jdbcType=BIGINT}
  </update>

  <insert id="copyByOrderNo" parameterType="com.sgs.ecom.member.dto.order.CopyByOrder">
    insert into ORDER_APPLICATION_FORM (
      ORDER_NO,FORM_TEMPLATE,TEST_CYCLE,company_NAME_CN,COMPANY_ADDRESS_CN,company_NAME_EN,COMPANY_ADDRESS_EN,
      LINK_PERSON,LINK_PHONE,LINK_EMAIL,STATE,CREATE_DATE,STATE_DATE,REPORT_SEND_CC,IS_REFUND_SAMPLE,REPORT_METHOD,
       TEST_MEMO,RECEIVE_NAME,RECEIVE_PHONE,RECEIVE_EMAIL,PROVINCE,CITY,TOWN,IS_TRANSFER,TAX_NO,REG_PHONE,
       BANK_NUMBER,BANK_NAME,CONFIRM_SOURCE
    )select
      #{toOrderNo} ,FORM_TEMPLATE,TEST_CYCLE,company_NAME_CN,COMPANY_ADDRESS_CN,company_NAME_EN,COMPANY_ADDRESS_EN,
      LINK_PERSON,LINK_PHONE,LINK_EMAIL,1,now(),now(),REPORT_SEND_CC,IS_REFUND_SAMPLE,REPORT_METHOD,
      TEST_MEMO,RECEIVE_NAME,RECEIVE_PHONE,RECEIVE_EMAIL,PROVINCE,CITY,TOWN,IS_TRANSFER,TAX_NO,REG_PHONE,
      BANK_NUMBER,BANK_NAME,CONFIRM_SOURCE from ORDER_APPLICATION_FORM where ORDER_NO= #{useOrderNo} and state=1
  </insert>
</mapper>