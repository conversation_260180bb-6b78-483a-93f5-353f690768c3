package com.sgs.ecom.member.dto.bbc;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;

import java.math.BigDecimal;

public class BbcOrderCouponDTO {
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String orderNo;

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String couponName;

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String store;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String storeId;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BigDecimal amount;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BigDecimal discountAmount=BigDecimal.ZERO;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int reportNums;
    private int urgentNums;
    public String getOrderNo() {
        return orderNo;
    }

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount;
    }

    public String getStore() {
        return store;
    }

    public void setStore(String store) {
        this.store = store;
    }

    public int getReportNums() {
        return reportNums;
    }

    public void setReportNums(int reportNums) {
        this.reportNums = reportNums;
    }

    public int getUrgentNums() {
        return urgentNums;
    }

    public void setUrgentNums(int urgentNums) {
        this.urgentNums = urgentNums;
    }

    public String getCouponName() {
        return couponName;
    }

    public void setCouponName(String couponName) {
        this.couponName = couponName;
    }
}
