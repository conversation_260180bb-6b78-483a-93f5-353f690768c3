package com.sgs.ecom.member.dto.user;
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import java.util.Date; 
import com.platform.util.json.DateFormatSerializer; 
import com.platform.annotation.ApiAnno; 
import com.platform.annotation.BeanAnno; 
import com.sgs.base.BaseQryFilter; 

public class UserToolkitDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select EXP_DATE,USE_NUMS,STATE_DATE,USER_ID,STATE,TOOL_ICON,TOOL_MEMO,TOOL_DESC,CREATE_DATE,TOOL_ID,EFF_DATE,TOOL_CODE,TOOL_NAME from USER_TOOLKIT"; 
 

 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="EXP_DATE", getName="getExpDate", setName="setExpDate")
 	private Date expDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="USE_NUMS", getName="getUseNums", setName="setUseNums")
 	private int useNums;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private Timestamp stateDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="USER_ID", getName="getUserId", setName="setUserId")
 	private long userId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="TOOL_ICON", getName="getToolIcon", setName="setToolIcon")
 	private String toolIcon;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="TOOL_MEMO", getName="getToolMemo", setName="setToolMemo")
 	private String toolMemo;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="TOOL_DESC", getName="getToolDesc", setName="setToolDesc")
 	private String toolDesc;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="TOOL_ID", getName="getToolId", setName="setToolId")
 	private long toolId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="EFF_DATE", getName="getEffDate", setName="setEffDate")
 	private Date effDate;
 	@ApiAnno(groups={Default.class,QuerySummary.class})
 	@BeanAnno(value="TOOL_CODE", getName="getToolCode", setName="setToolCode")
 	private String toolCode;
 	@ApiAnno(groups={Default.class,QuerySummary.class})
 	@BeanAnno(value="TOOL_NAME", getName="getToolName", setName="setToolName")
 	private String toolName;

 	public void setExpDate(Date expDate){
 		 this.expDate=expDate;
 	}
 	@JsonSerialize(using = DateFormatSerializer.class) 
 	public Date getExpDate(){
 		 return this.expDate;
 	}
 
 	 
 	public void setUseNums(int useNums){
 		 this.useNums=useNums;
 	}
 	public int getUseNums(){
 		 return this.useNums;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setUserId(long userId){
 		 this.userId=userId;
 	}
 	public long getUserId(){
 		 return this.userId;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setToolIcon(String toolIcon){
 		 this.toolIcon=toolIcon;
 	}
 	public String getToolIcon(){
 		 return this.toolIcon;
 	}
 
 	 
 	public void setToolMemo(String toolMemo){
 		 this.toolMemo=toolMemo;
 	}
 	public String getToolMemo(){
 		 return this.toolMemo;
 	}
 
 	 
 	public void setToolDesc(String toolDesc){
 		 this.toolDesc=toolDesc;
 	}
 	public String getToolDesc(){
 		 return this.toolDesc;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setToolId(long toolId){
 		 this.toolId=toolId;
 	}
 	public long getToolId(){
 		 return this.toolId;
 	}
 
 	 
 	public void setEffDate(Date effDate){
 		 this.effDate=effDate;
 	}
 	@JsonSerialize(using = DateFormatSerializer.class) 
 	public Date getEffDate(){
 		 return this.effDate;
 	}
 
 	 
 	public void setToolCode(String toolCode){
 		 this.toolCode=toolCode;
 	}
 	public String getToolCode(){
 		 return this.toolCode;
 	}
 
 	 
 	public void setToolName(String toolName){
 		 this.toolName=toolName;
 	}
 	public String getToolName(){
 		 return this.toolName;
 	}
 
 	 
}