<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.user.dao.UserNoticeLogMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.entity.user.UserNoticeLog" >
    <id column="LOG_ID" property="logId" jdbcType="BIGINT" />
    <result column="NOTICE_TYPE" property="noticeType" jdbcType="TINYINT" />
    <result column="BUSINESS_TYPE" property="businessType" jdbcType="VARCHAR" />
    <result column="BUSINESS_CODE" property="businessCode" jdbcType="VARCHAR" />
    <result column="BUSINESS_NO" property="businessNo" jdbcType="VARCHAR" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="BU" property="bu" jdbcType="VARCHAR" />
    <result column="NOTICE_METHOD" property="noticeMethod" jdbcType="TINYINT" />
    <result column="SEND_TEMPLATE" property="sendTemplate" jdbcType="VARCHAR" />
    <result column="NEXT_NOTICE_DATE" property="nextNoticeDate" jdbcType="TIMESTAMP" />
    <result column="STATE" property="state" jdbcType="TINYINT" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    LOG_ID, NOTICE_TYPE, BUSINESS_TYPE, BUSINESS_CODE, BUSINESS_NO, USER_ID, BU, NOTICE_METHOD, 
    SEND_TEMPLATE, NEXT_NOTICE_DATE, STATE, CREATE_DATE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from USER_NOTICE_LOG
    where LOG_ID = #{logId,jdbcType=BIGINT}
  </select>

  <insert id="insertSelective" parameterType="com.sgs.ecom.member.entity.user.UserNoticeLog" >
    insert into USER_NOTICE_LOG
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="logId != null" >
        LOG_ID,
      </if>
      <if test="noticeType != null" >
        NOTICE_TYPE,
      </if>
      <if test="businessType != null" >
        BUSINESS_TYPE,
      </if>
      <if test="businessCode != null" >
        BUSINESS_CODE,
      </if>
      <if test="businessNo != null" >
        BUSINESS_NO,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
      <if test="bu != null" >
        BU,
      </if>
      <if test="noticeMethod != null" >
        NOTICE_METHOD,
      </if>
      <if test="sendTemplate != null" >
        SEND_TEMPLATE,
      </if>
      <if test="nextNoticeDate != null" >
        NEXT_NOTICE_DATE,
      </if>
      <if test="state != null" >
        STATE,
      </if>
      <if test="createDate != null" >
        CREATE_DATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="logId != null" >
        #{logId,jdbcType=BIGINT},
      </if>
      <if test="noticeType != null" >
        #{noticeType,jdbcType=TINYINT},
      </if>
      <if test="businessType != null" >
        #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="businessCode != null" >
        #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="businessNo != null" >
        #{businessNo,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="bu != null" >
        #{bu,jdbcType=VARCHAR},
      </if>
      <if test="noticeMethod != null" >
        #{noticeMethod,jdbcType=TINYINT},
      </if>
      <if test="sendTemplate != null" >
        #{sendTemplate,jdbcType=VARCHAR},
      </if>
      <if test="nextNoticeDate != null" >
        #{nextNoticeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="state != null" >
        #{state,jdbcType=TINYINT},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.ecom.member.entity.user.UserNoticeLog" >
    update USER_NOTICE_LOG
    <set >
      <if test="noticeType != null" >
        NOTICE_TYPE = #{noticeType,jdbcType=TINYINT},
      </if>
      <if test="businessType != null" >
        BUSINESS_TYPE = #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="businessCode != null" >
        BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="businessNo != null" >
        BUSINESS_NO = #{businessNo,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        USER_ID = #{userId,jdbcType=BIGINT},
      </if>
      <if test="bu != null" >
        BU = #{bu,jdbcType=VARCHAR},
      </if>
      <if test="noticeMethod != null" >
        NOTICE_METHOD = #{noticeMethod,jdbcType=TINYINT},
      </if>
      <if test="sendTemplate != null" >
        SEND_TEMPLATE = #{sendTemplate,jdbcType=VARCHAR},
      </if>
      <if test="nextNoticeDate != null" >
        NEXT_NOTICE_DATE = #{nextNoticeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="state != null" >
        STATE = #{state,jdbcType=TINYINT},
      </if>
      <if test="createDate != null" >
        CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where LOG_ID = #{logId,jdbcType=BIGINT}
  </update>

</mapper>