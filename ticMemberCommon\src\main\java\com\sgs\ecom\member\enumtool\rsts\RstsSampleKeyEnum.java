package com.sgs.ecom.member.enumtool.rsts;

/**
 * @Description : rsts样品字段枚举
 * @<PERSON> <PERSON>
 * @Date  2023/10/13
 **/
public enum RstsSampleKeyEnum {

    sampleFeaturesStr("sampleFeaturesStr", 1, "EXTEND"),//样品特性
    sampleState("sampleState", 2, "EXTEND"),//样品状态
    sampleMemo("sampleMemo", 3, "EXTEND"),//样品备注
    sprcRevision("sprcRevision", 4, "SPEC-INFORMATION"),//X客户 特殊信息
    REPORTLUA("reportLua", 5, "BASIC");//报告语言    ;

    RstsSampleKeyEnum(String name, int index, String nameCh) {
        this.name = name;
        this.index = index;
        this.nameCh = nameCh;
    }

    public static Boolean getEnum(String name) {
        for (RstsSampleKeyEnum c : RstsSampleKeyEnum.values()) {
            if (name.equals(c.getName())) {
                return true;
            }
        }
        return false;
    }


    public static String addNameCh(String name) {
        for (RstsSampleKeyEnum c : RstsSampleKeyEnum.values()) {
            if (name.equals(c.getName())) {
                return c.getNameCh();
            }
        }
        return RstsSampleKeyEnum.REPORTLUA.getNameCh();
    }


    private String name;
    private int index;
    private String nameCh;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }
}
