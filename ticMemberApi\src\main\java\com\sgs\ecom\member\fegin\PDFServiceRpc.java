package com.sgs.ecom.member.fegin;

import com.sgs.ecom.member.request.rpc.BillRpcReq;
import com.sgs.ecom.member.service.util.interfaces.IPDFService;
import com.sgs.ecom.member.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/business/rpc.v2.member/pdf")
public class PDFServiceRpc {

	@Autowired
	private IPDFService ipdfService;
	/** 导出memo的pdf流
	*@Function: qryMemoPDF
	*@Description
	*@param: [orderNo, httpServletResponse]
	*@author: Xiwei_Qiu @date: 2021/12/31 @version:
	**/
	@RequestMapping(value = "getMemoPDF", method = { RequestMethod.POST })
	public void qryMemoPDF(
		@RequestParam(name = "orderNo", required = true) String orderNo,
		HttpServletResponse httpServletResponse) throws Exception{
		ipdfService.qryMemoPDF(orderNo,httpServletResponse);
	}

	/**
	*@Function: getBillPDF
	*@Description 导出bill账单的pdf流
	*@param: [billRpcReq, httpServletResponse]
	*@author: Xiwei_Qiu @date: 2021/12/31 @version:
	**/
	@RequestMapping(value = "getBillPDF", method = { RequestMethod.POST })
	public void getBillPDF(
		@RequestBody BillRpcReq billRpcReq,
		HttpServletResponse httpServletResponse) throws Exception{
		ipdfService.qryBillPDF(billRpcReq.getBillId(),billRpcReq.getFtlMap(),httpServletResponse);
	}
	/**
	*@Function: getBillPDFUrl
	*@Description 导出bill账单的map
	*@param: [billRpcReq]
	*@author: Xiwei_Qiu @date: 2021/12/31 @version:
	**/
	@RequestMapping(value = "getBillPDFUrl", method = { RequestMethod.POST })
	public ResultBody getBillPDFUrl(
		@RequestBody BillRpcReq billRpcReq) throws Exception{
		return ResultBody.newInstance(ipdfService.qryBillPDFUrl(billRpcReq.getBillId(),billRpcReq.getFtlMap()));
	}
}
