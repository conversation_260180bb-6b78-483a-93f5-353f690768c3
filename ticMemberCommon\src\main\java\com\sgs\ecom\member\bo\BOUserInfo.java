package com.sgs.ecom.member.bo; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import com.platform.annotation.CharacterVaild; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import com.platform.annotation.BeanAnno; 

public class BOUserInfo{
 
 	public static final String SEQUENCE = "USER_ID"; 
  
 	public static final String BO_SQL = "USER_INFO"; 
 
 	public static final String OWNER ="member";

 	public static final String USER_NICK="userNick";
 	public static final String LEVEL_NAME="levelName";
 	public static final String USER_EMAIL="userEmail";
 	public static final String STATE_DATE="stateDate";
 	public static final String USER_ID="userId";
 	public static final String STATE="state";
 	public static final String REG_IP="regIp";
 	public static final String USER_NAME="userName";
 	public static final String U_ID="uId";
 	public static final String LEVEL_ID="levelId";
 	public static final String USER_PHONE="userPhone";
 	public static final String CREATE_DATE="createDate";
 	public static final String BBC_PWD="bbcPwd";
 	public static final String USER_PWD="userPwd";

 	@BeanAnno("USER_NICK")
 	private String userNick;
 	@BeanAnno("LEVEL_NAME")
 	private String levelName;
 	@BeanAnno("USER_EMAIL")
 	private String userEmail;
 	@BeanAnno("STATE_DATE")
 	private Timestamp stateDate;
 	@BeanAnno("USER_ID")
 	private long userId;
 	@BeanAnno("STATE")
 	private long state;
 	@BeanAnno("REG_IP")
 	private String regIp;
 	@BeanAnno("USER_NAME")
 	private String userName;
 	@BeanAnno("U_ID")
 	private long uId;
 	@BeanAnno("LEVEL_ID")
 	private long levelId;
 	@BeanAnno("USER_PHONE")
 	private String userPhone;
 	@BeanAnno("CREATE_DATE")
 	private Timestamp createDate;
 	@BeanAnno("BBC_PWD")
 	private String bbcPwd;
 	@BeanAnno("USER_PWD")
 	private String userPwd;

 	@CharacterVaild(len = 30) 
 	public void setUserNick(String userNick){
 		 this.userNick=userNick;
 	}
 	public String getUserNick(){
 		 return this.userNick;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setLevelName(String levelName){
 		 this.levelName=levelName;
 	}
 	public String getLevelName(){
 		 return this.levelName;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setUserEmail(String userEmail){
 		 this.userEmail=userEmail;
 	}
 	public String getUserEmail(){
 		 return this.userEmail;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setUserId(long userId){
 		 this.userId=userId;
 	}
 	public long getUserId(){
 		 return this.userId;
 	}
 
 	 
 	public void setState(long state){
 		 this.state=state;
 	}
 	public long getState(){
 		 return this.state;
 	}
 
 	 
 	@CharacterVaild(len = 20) 
 	public void setRegIp(String regIp){
 		 this.regIp=regIp;
 	}
 	public String getRegIp(){
 		 return this.regIp;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setUserName(String userName){
 		 this.userName=userName;
 	}
 	public String getUserName(){
 		 return this.userName;
 	}
 
 	 
 	public void setUId(long uId){
 		 this.uId=uId;
 	}
 	public long getUId(){
 		 return this.uId;
 	}
 
 	 
 	public void setLevelId(long levelId){
 		 this.levelId=levelId;
 	}
 	public long getLevelId(){
 		 return this.levelId;
 	}
 
 	 
 	@CharacterVaild(len = 11) 
 	public void setUserPhone(String userPhone){
 		 this.userPhone=userPhone;
 	}
 	public String getUserPhone(){
 		 return this.userPhone;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setBbcPwd(String bbcPwd){
 		 this.bbcPwd=bbcPwd;
 	}
 	public String getBbcPwd(){
 		 return this.bbcPwd;
 	}
 
 	 
 	@CharacterVaild(len = 20) 
 	public void setUserPwd(String userPwd){
 		 this.userPwd=userPwd;
 	}
 	public String getUserPwd(){
 		 return this.userPwd;
 	}
 
 	 
}