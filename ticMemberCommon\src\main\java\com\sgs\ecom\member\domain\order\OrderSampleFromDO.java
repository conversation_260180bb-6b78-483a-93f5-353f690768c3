package com.sgs.ecom.member.domain.order;

import com.platform.util.ValidationUtil;
import com.sgs.base.BaseCopyObj;
import com.sgs.ecom.member.bo.OrderSampleFrom;
import com.sgs.ecom.member.dto.rpc.CenterSampleDTO;

import java.util.ArrayList;
import java.util.List;

public class OrderSampleFromDO extends OrderSampleFrom {

    public static final String SAMPLE_NAME="sampleName";
    public static final String SAMPLE_NAME_CN="sampleNameCn";
    public static final String SAMPLE_NAME_EN="sampleNameEn";


    public static final String OIQ_SAMPLE_NAME_CN="SAMPLE_NAME";
    public static final String OIQ_SAMPLE_NAME_EN="SAMPLE_NAME_EN";
    public static final String OIQ_USING_CHEMISTRY="USING_CHEMISTRY";
    public static final String OIQ_SAMPLE_COMPOSITION="SAMPLE_COMPOSITION";
    public static final String OIQ_SAMPLE_COLOR="SAMPLE_COLOR";
    public static final String OIQ_SAMPLE_FEATURES="SAMPLE_FEATURES";

    public static final String OIQ_SAMPLE_NUMBER="SAMPLE_NUMBER";
    public static final String OIQ_SAMPLE_NUMBER_EN="SAMPLE_NUMBER_EN";

    private BaseCopyObj baseCopy = new BaseCopyObj();


    public List<OrderSampleFrom> initOrderSampleFormByCreateOrder(List<CenterSampleDTO> centerSampleDTOList,
                                                                  String orderNo, String groupNo, String sampleNo, String dateStr) {
        List<OrderSampleFrom> list = new ArrayList<>();
        if(ValidationUtil.isEmpty(centerSampleDTOList))
            return list;

        for (CenterSampleDTO centerSampleDTO : centerSampleDTOList) {
            OrderSampleFrom orderSampleFrom = new OrderSampleFrom();
            baseCopy.copy(orderSampleFrom, centerSampleDTO);
            orderSampleFrom.setOrderNo(orderNo);
            orderSampleFrom.setGroupNo(groupNo);
            orderSampleFrom.setSampleNo(sampleNo);
            orderSampleFrom.setState(1);
            orderSampleFrom.setCreateDate(dateStr);
            orderSampleFrom.setStateDate(dateStr);
            orderSampleFrom.setSampleValue("");
            list.add(orderSampleFrom);
        }

        return list;
    }

    //获取化学的申请表
    public static List<String> getChemistryList(){
        List<String> list=new ArrayList<>();
        list.add(OIQ_USING_CHEMISTRY);
        list.add(OIQ_SAMPLE_COMPOSITION);
        list.add(OIQ_SAMPLE_COLOR);
        list.add(OIQ_SAMPLE_FEATURES);
        return list;
    }
}
