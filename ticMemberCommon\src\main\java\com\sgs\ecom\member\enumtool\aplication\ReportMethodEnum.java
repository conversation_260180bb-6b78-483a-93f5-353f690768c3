package com.sgs.ecom.member.enumtool.aplication;

public enum ReportMethodEnum {
    ONE("one", 1, "一个订单一份报告"),
    <PERSON>OR<PERSON>("more", 2, "一个样品一份报告"),
    <PERSON><PERSON><PERSON>("other", 3, "其他"),
    <PERSON><PERSON><PERSON>("fore", 4, "多份样品一份报告"),
    FIVE("five", 5, "一个样品按照项目出报告"),//ro 转



    ;

    ReportMethodEnum(String name, int index, String nameCh) {
        this.name = name;
        this.index = index;
        this.nameCh = nameCh;
    }

    public static String getName(int index) {
        for (ReportMethodEnum c : ReportMethodEnum.values()) {
            if (c.getIndex()==index) {
                return c.getName();
            }
        }
        return "";
    }

    public static String getNameCh(int index) {
        for (ReportMethodEnum c : ReportMethodEnum.values()) {
            if (c.getIndex()==index) {
                return c.getNameCh();
            }
        }
        return "";
    }


    private String name;
    private int index;
    private String nameCh;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }
}
