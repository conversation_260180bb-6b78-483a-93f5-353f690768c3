package com.sgs.ecom.member.dto.portal;

import com.sgs.ecom.member.dto.center.BusinessLineDTO;
import com.sgs.ecom.member.dto.center.LabDTO;
import com.sgs.ecom.member.dto.dml.DmlMainReqDTO;
import com.sgs.ecom.member.dto.user.UserDTO;
import com.sgs.ecom.member.entity.order.OrderBaseInfo;
import com.sgs.ecom.member.entity.order.OrderReport;

public class OrderPortalDTO {

    private String dateStr;
    private int type;

    private String inquiryOrderNo;
    private String orderNo;
    private Long inquiryOrderId;
    private Long orderId;
    private int oldState;
    private String groupNo;
    private Boolean isAdd;
    private UserDTO userDTO;
    private LabDTO labDTO;
    private BusinessLineDTO businessLineDTO;
    private OrderBaseInfo orderBaseInfo;
    private OrderReport orderReport;


    private DmlMainReqDTO dmlMainReqDTO=new DmlMainReqDTO();

    public OrderPortalDTO() {
    }

    public OrderPortalDTO(String inquiryOrderNo, String orderNo, String groupNo, Boolean isAdd,UserDTO userDTO,String dateStr) {
        this.inquiryOrderNo = inquiryOrderNo;
        this.orderNo = orderNo;
        this.groupNo = groupNo;
        this.isAdd = isAdd;
        this.userDTO=userDTO;
        this.setDateStr(dateStr);
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getGroupNo() {
        return groupNo;
    }

    public void setGroupNo(String groupNo) {
        this.groupNo = groupNo;
    }

    public Boolean getAdd() {
        return isAdd;
    }

    public void setAdd(Boolean add) {
        isAdd = add;
    }

    public String getInquiryOrderNo() {
        return inquiryOrderNo;
    }

    public void setInquiryOrderNo(String inquiryOrderNo) {
        this.inquiryOrderNo = inquiryOrderNo;
    }

    public Long getInquiryOrderId() {
        return inquiryOrderId;
    }

    public void setInquiryOrderId(Long inquiryOrderId) {
        this.inquiryOrderId = inquiryOrderId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public UserDTO getUserDTO() {
        return userDTO;
    }

    public void setUserDTO(UserDTO userDTO) {
        this.userDTO = userDTO;
    }

    public DmlMainReqDTO getDmlMainReqDTO() {
        return dmlMainReqDTO;
    }

    public void setDmlMainReqDTO(DmlMainReqDTO dmlMainReqDTO) {
        this.dmlMainReqDTO = dmlMainReqDTO;
    }

    public LabDTO getLabDTO() {
        return labDTO;
    }

    public void setLabDTO(LabDTO labDTO) {
        this.labDTO = labDTO;
    }

    public OrderBaseInfo getOrderBaseInfo() {
        return orderBaseInfo;
    }

    public void setOrderBaseInfo(OrderBaseInfo orderBaseInfo) {
        this.orderBaseInfo = orderBaseInfo;
    }

    public String getDateStr() {
        return dateStr;
    }

    public void setDateStr(String dateStr) {
        this.dateStr = dateStr;
    }

    public BusinessLineDTO getBusinessLineDTO() {
        return businessLineDTO;
    }

    public void setBusinessLineDTO(BusinessLineDTO businessLineDTO) {
        this.businessLineDTO = businessLineDTO;
    }

    public OrderReport getOrderReport() {
        return orderReport;
    }

    public void setOrderReport(OrderReport orderReport) {
        this.orderReport = orderReport;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getOldState() {
        return oldState;
    }

    public void setOldState(int oldState) {
        this.oldState = oldState;
    }
}
