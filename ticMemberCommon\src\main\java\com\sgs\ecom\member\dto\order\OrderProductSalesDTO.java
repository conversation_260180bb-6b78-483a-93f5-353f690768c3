package com.sgs.ecom.member.dto.order;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;

public class OrderProductSalesDTO extends BaseQryFilter {
 	@ApiAnno(groups={Default.class})
 	private int prodId;
	@ApiAnno(groups={Default.class})
	private int count;//商品下单数

	public int getCount() {
		return count;
	}

	public void setCount(int count) {
		this.count = count;
	}

	public int getProdId() {
		return prodId;
	}

	public void setProdId(int prodId) {
		this.prodId = prodId;
	}
}