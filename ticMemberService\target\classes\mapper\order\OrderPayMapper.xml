<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.order.dao.OrderPayMapper" >
  <resultMap id="EntityResultMap" type="com.sgs.ecom.member.entity.order.OrderPay" >
    <id column="PAYMENT_ID" property="paymentId" jdbcType="BIGINT" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="PAY_METHOD" property="payMethod" jdbcType="INTEGER" />
    <result column="PAY_PRICE" property="payPrice" jdbcType="INTEGER" />
    <result column="STATE" property="state" jdbcType="TINYINT" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="PAY_DATE" property="payDate" jdbcType="TIMESTAMP" />
    <result column="TRANS_NO" property="transNo" jdbcType="VARCHAR" />
    <result column="PAY_ACCOUNT" property="payAccount" jdbcType="VARCHAR" />
    <result column="MEMO" property="memo" jdbcType="VARCHAR" />
    <result column="DETAIL_CODE" property="detailCode" jdbcType="VARCHAR" />
    <result column="ACCOUNT_NAME" property="accountName" jdbcType="VARCHAR" />
    <result column="PAYMENT_NO" property="paymentNo" jdbcType="VARCHAR" />
    <result column="BOSS_NO" property="bossNo" jdbcType="VARCHAR" />
    <result column="IS_FULL" property="isFull" jdbcType="INTEGER" />
    <result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR" />
    <result column="F_CODE" property="fCode" jdbcType="VARCHAR" />
    <result column="REFUND_INFO" property="refundInfo" jdbcType="VARCHAR" />
    <result column="PAY_TYPE" property="payType" jdbcType="INTEGER" />
    <result column="RELATE_TRANS_NO" property="relateTransNo" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="BUYER_INFO" property="buyerInfo" jdbcType="VARCHAR" />
    <result column="BUYER_NAME" property="buyerName" jdbcType="VARCHAR" />
    <result column="INVOICE_NAME" property="invoiceName" jdbcType="VARCHAR" />
    <result column="ORIGINAL_ORDER_NO" property="originalOrderNo" jdbcType="VARCHAR" />
    <result column="IS_CS" property="isCs" jdbcType="INTEGER" />
  </resultMap>
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.vo.VOOrderPay" >
    <id column="PAYMENT_ID" property="paymentId" jdbcType="BIGINT" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="PAY_METHOD" property="payMethod" jdbcType="INTEGER" />
    <result column="PAY_PRICE" property="payPrice" jdbcType="INTEGER" />
    <result column="STATE" property="state" jdbcType="TINYINT" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="PAY_DATE" property="payDate" jdbcType="TIMESTAMP" />
    <result column="TRANS_NO" property="transNo" jdbcType="VARCHAR" />
    <result column="PAY_ACCOUNT" property="payAccount" jdbcType="VARCHAR" />
    <result column="MEMO" property="memo" jdbcType="VARCHAR" />
    <result column="DETAIL_CODE" property="detailCode" jdbcType="VARCHAR" />
    <result column="PAY_TYPE" property="payType" jdbcType="INTEGER" />
    <result column="REUNFD_INFO" property="refundInfo" jdbcType="VARCHAR" />
    <result column="IS_CS" property="isCs" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Entity_Column_List" >
    PAYMENT_ID, ORDER_NO, USER_ID, PAY_METHOD, PAY_PRICE, STATE, CREATE_DATE, PAY_DATE,
    TRANS_NO, PAY_ACCOUNT, MEMO, DETAIL_CODE, ACCOUNT_NAME, PAYMENT_NO, BOSS_NO, IS_FULL,
    COMPANY_NAME, F_CODE, REFUND_INFO,PAY_TYPE, RELATE_TRANS_NO, REMARK, BUYER_INFO, BUYER_NAME,
    INVOICE_NAME, ORIGINAL_ORDER_NO, IS_CS
  </sql>
  <sql id="Base_Column_List" >
    PAYMENT_ID, ORDER_NO, USER_ID, PAY_METHOD, PAY_PRICE, STATE, CREATE_DATE, PAY_DATE, 
    TRANS_NO, PAY_ACCOUNT, MEMO, DETAIL_CODE, REUNFD_INFO,PAY_TYPE,BUYER_INFO,IS_CS
  </sql>
  <select id="qryPaymentListByOrder" resultMap="EntityResultMap" parameterType="java.util.List">
    select
    <include refid="Entity_Column_List" />
    from ORDER_PAY
    where ORDER_NO in
    <foreach collection="orderNos" index="index" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    order by PAYMENT_ID
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from ORDER_PAY
    where PAYMENT_ID = #{paymentId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from ORDER_PAY
    where PAYMENT_ID = #{paymentId,jdbcType=BIGINT}
  </delete>

  <insert id="insertSelective" parameterType="com.sgs.ecom.member.vo.VOOrderPay" >
    insert into ORDER_PAY
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="paymentId != null" >
        PAYMENT_ID,
      </if>
      <if test="orderNo != null" >
        ORDER_NO,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
      <if test="payMethod != null" >
        PAY_METHOD,
      </if>
      <if test="payPrice != null" >
        PAY_PRICE,
      </if>
      <if test="state != null" >
        STATE,
      </if>
      <if test="createDate != null" >
        CREATE_DATE,
      </if>
      <if test="payDate != null" >
        PAY_DATE,
      </if>
      <if test="transNo != null" >
        TRANS_NO,
      </if>
      <if test="payAccount != null" >
        PAY_ACCOUNT,
      </if>
      <if test="memo != null" >
        MEMO,
      </if>
      <if test="detailCode != null" >
        DETAIL_CODE,
      </if>
      <if test="accountName != null" >
        ACCOUNT_NAME,
      </if>
      <if test="paymentNo != null" >
        PAYMENT_NO,
      </if>
      <if test="refundInfo != null" >
        REFUND_INFO,
      </if>
      <if test="payType != null" >
        PAY_TYPE,
      </if>
      <if test="buyerInfo != null" >
        BUYER_INFO,
      </if>
      <if test="invoiceName != null" >
        INVOICE_NAME,
      </if>
      <if test="originalOrderNo != null" >
        ORIGINAL_ORDER_NO,
      </if>
      <if test="buyerName != null" >
        BUYER_NAME,
      </if>
      <if test="isCs != null">
        IS_CS,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="paymentId != null" >
        #{paymentId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="payMethod != null" >
        #{payMethod,jdbcType=INTEGER},
      </if>
      <if test="payPrice != null" >
        #{payPrice,jdbcType=INTEGER},
      </if>
      <if test="state != null" >
        #{state,jdbcType=TINYINT},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="payDate != null" >
        #{payDate,jdbcType=TIMESTAMP},
      </if>
      <if test="transNo != null" >
        #{transNo,jdbcType=VARCHAR},
      </if>
      <if test="payAccount != null" >
        #{payAccount,jdbcType=VARCHAR},
      </if>
      <if test="memo != null" >
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="detailCode != null" >
        #{detailCode,jdbcType=VARCHAR},
      </if>
      <if test="accountName != null" >
        #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="paymentNo != null" >
        #{paymentNo,jdbcType=VARCHAR},
      </if>
      <if test="refundInfo != null" >
        #{refundInfo,jdbcType=VARCHAR},
      </if>
      <if test="payType != null" >
        #{payType,jdbcType=INTEGER},
      </if>
      <if test="buyerInfo != null" >
        #{buyerInfo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceName != null" >
        #{invoiceName,jdbcType=VARCHAR},
      </if>
      <if test="originalOrderNo != null" >
        #{originalOrderNo},
      </if>
      <if test="buyerName != null" >
        #{buyerName},
      </if>
      <if test="isCs != null">
       #{isCs,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.ecom.member.vo.VOOrderPay" >
    update ORDER_PAY
    <set >
      <if test="orderNo != null" >
        ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        USER_ID = #{userId,jdbcType=BIGINT},
      </if>
      <if test="payMethod != null" >
        PAY_METHOD = #{payMethod,jdbcType=INTEGER},
      </if>
      <if test="payPrice != null" >
        PAY_PRICE = #{payPrice,jdbcType=INTEGER},
      </if>
      <if test="state != null" >
        STATE = #{state,jdbcType=TINYINT},
      </if>
      <if test="createDate != null" >
        CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="payDate != null" >
        PAY_DATE = #{payDate,jdbcType=TIMESTAMP},
      </if>
      <if test="transNo != null" >
        TRANS_NO = #{transNo,jdbcType=VARCHAR},
      </if>
      <if test="payAccount != null" >
        PAY_ACCOUNT = #{payAccount,jdbcType=VARCHAR},
      </if>
      <if test="memo != null" >
        MEMO = #{memo,jdbcType=VARCHAR},
      </if>
      <if test="detailCode != null" >
        DETAIL_CODE = #{detailCode,jdbcType=VARCHAR},
      </if>
      <if test="paymentNo != null" >
        PAYMENT_NO = #{paymentNo,jdbcType=VARCHAR},
      </if>
      <if test="isCs != null">
        IS_CS = #{isCs,jdbcType=INTEGER},
      </if>
    </set>
    where PAYMENT_ID = #{paymentId,jdbcType=BIGINT}
  </update>


  <!--自定义 -->
  <resultMap id="resultDTO" type="com.sgs.ecom.member.dto.pay.OrderPayDTO" >
    <id column="PAYMENT_ID" property="paymentId" jdbcType="BIGINT" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="PAY_METHOD" property="payMethod" jdbcType="INTEGER" />
    <result column="PAY_PRICE" property="payPrice" jdbcType="INTEGER" />
    <result column="STATE" property="state" jdbcType="TINYINT" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="PAY_DATE" property="payDate" jdbcType="TIMESTAMP" />
    <result column="TRANS_NO" property="transNo" jdbcType="VARCHAR" />
    <result column="PAY_ACCOUNT" property="payAccount" jdbcType="VARCHAR" />
    <result column="MEMO" property="memo" jdbcType="VARCHAR" />
    <result column="DETAIL_CODE" property="detailCode" jdbcType="VARCHAR" />
    <result column="ACCOUNT_NAME" property="accountName" jdbcType="VARCHAR" />
    <result column="PAYMENT_NO" property="paymentNo" jdbcType="VARCHAR" />
    <result column="BOSS_NO" property="bossNo" jdbcType="VARCHAR" />
    <result column="IS_FULL" property="isFull" jdbcType="TINYINT" />
    <result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR" />
    <result column="PAY_TYPE" property="payType" jdbcType="INTEGER" />
    <result column="REFUND_INFO" property="refundInfo" jdbcType="VARCHAR" />
    <result column="BUYER_INFO" property="buyerInfo" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="IS_CS" property="isCs" jdbcType="INTEGER" />
  </resultMap>

  <sql id="sqlListDTO" >
    PAYMENT_ID, ORDER_NO, USER_ID, PAY_METHOD, PAY_PRICE, STATE, CREATE_DATE, PAY_DATE,
    TRANS_NO, PAY_ACCOUNT, MEMO, DETAIL_CODE,ACCOUNT_NAME,PAYMENT_NO,BOSS_NO,IS_FULL,COMPANY_NAME,PAY_TYPE,
    REFUND_INFO, BUYER_INFO, REMARK,IS_CS
  </sql>

  <select id="selectListByMap" resultMap="resultDTO" >
    select
    <include refid="sqlListDTO" />
    from ORDER_PAY
    <include refid="baseQueryWhere"/>
    <include refid="groupBy"/>
    order by PAYMENT_ID desc
  </select>
  
  <select id="selectLastOrderPayDTOByMap" resultMap="resultDTO" >
    select
    <include refid="sqlListDTO" />
    from ORDER_PAY
    <include refid="baseQueryWhere"/>
    order by PAYMENT_ID desc limit 1
  </select>
    <select id="selectRefundListByMap" resultMap="resultDTO">
      SELECT
        *
      FROM
        order_pay
      WHERE
            1=1
      <if test="orderNoList != null ">
      AND  ( ORDER_NO IN
          <foreach collection="orderNoList" index="index" item="item" open="(" separator="," close=")">
            #{item}
          </foreach>

         OR ORDER_NO IN ( SELECT ORDER_NO FROM order_base_info WHERE RELATE_ORDER_NO IN
        <foreach collection="orderNoList" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
          ) )
      </if>


      <if test="state != null">
        AND STATE=#{state}
      </if>
      <if test="payType != null">
        AND PAY_TYPE=#{payType}
      </if>

    </select>

    <sql id="baseQueryWhere">
    where 1=1
    <if test="orderNo != null">
      AND ORDER_NO=#{orderNo}
    </if>
    <if test="transNo != null">
      AND TRANS_NO=#{transNo}
    </if>
    <if test="userId != null">
      AND USER_ID=#{userId}
    </if>
    <if test="state != null">
      AND STATE=#{state}
    </if>
    <if test="payMethod != null">
      AND PAY_METHOD=#{payMethod}
    </if>
     <if test="payType != null">
      AND PAY_TYPE=#{payType}
    </if>
    <if test="refundInfo != null">
      AND REFUND_INFO=#{refundInfo}
    </if>
    <if test="isCs != null">
      AND IS_CS=#{isCs,jdbcType=INTEGER}
    </if>
    <if test="orderNoList != null ">
      AND ORDER_NO in
      <foreach collection="orderNoList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>

  </sql>
  <sql id="groupBy">
    <if test="groupBy != null">
      group by ORDER_NO
    </if>
  </sql>


  <resultMap id="resultPayDTO" type="com.sgs.ecom.member.dto.pay.OrderPayPriceDTO" >
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="payPrice" property="payPrice" jdbcType="INTEGER" />
    <result column="PAY_METHOD" property="payMethod" jdbcType="INTEGER" />
  </resultMap>

  <select id="selectPayAmountByOrder" resultMap="resultPayDTO" >
    select ORDER_NO,sum(if(PAY_TYPE=1,PAY_PRICE,-PAY_PRICE)) as payPrice,PAY_METHOD
    from ORDER_PAY
    where PAY_TYPE in (1,2) and STATE in (1,2) and ORDER_NO in
    <foreach collection="orderNoList" index="index" item="item" open="(" separator="," close=")">
     #{item}
    </foreach>
    group by ORDER_NO,PAY_METHOD
  </select>


</mapper>