package com.sgs.ecom.member.domain.order;


import com.platform.util.ValidationUtil;
import com.sgs.base.BaseCopyObj;
import com.sgs.ecom.member.bo.OrderSampleFrom;
import com.sgs.ecom.member.dto.oiq.OiqOrderReqDTO;
import com.sgs.ecom.member.dto.oiq.applicationInfo.OiqSampleDTO;
import com.sgs.ecom.member.dto.order.OrderSampleFromDTO;
import com.sgs.ecom.member.dto.order.OrderSampleMoreDTO;
import com.sgs.ecom.member.dto.rpc.CenterSampleDTO;
import com.sgs.ecom.member.dto.sample.SampleCategoryDTO;
import com.sgs.ecom.member.entity.order.OrderSample;
import com.sgs.ecom.member.request.oiq.OiqOrderSampleFromReq;
import com.sgs.ecom.member.request.oiq.OiqSampleReq;
import com.sgs.ecom.member.request.order.SampleCategoryReq;
import com.sgs.ecom.member.util.order.UseDateUtil;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class OrderSampleDO extends OrderSample {

    private BaseCopyObj baseCopy = new BaseCopyObj();



    public OrderSample orderSampleAddSampleNo(OiqSampleReq oiqSampleReq,OiqOrderReqDTO oiqOrderReqDTO, String sampleNo, Map<String, Object> collectMap){
        String dateStr=UseDateUtil.getDateString(new Date());
        OrderSample orderSample=new OrderSample();
        baseCopy.copyWithNull(orderSample,oiqSampleReq);
        initState(orderSample,dateStr);
        orderSample.setSampleNo(sampleNo);
        orderSample.setOrderNo(oiqOrderReqDTO.getOrderNo());
        orderSample.setGroupNo(oiqOrderReqDTO.getGroupNo());
        orderSample.setSampleNameCn(collectMap.getOrDefault(OrderSampleFromDO.OIQ_SAMPLE_NAME_CN,"").toString());
        orderSample.setSampleNameEn(collectMap.getOrDefault(OrderSampleFromDO.OIQ_SAMPLE_NAME_EN,"").toString());
        return orderSample;
    }






    public List<OrderSampleFrom> orderSampleFormAddSampleNo(OiqSampleReq oiqSampleReq,String orderNo,String groupNo, String sampleNo){
        List<OrderSampleFrom> list=new ArrayList<>();
        for(OiqOrderSampleFromReq orderSampleFromDTO:oiqSampleReq.getSampleFromDTOList()){
            OrderSampleFrom orderSampleFrom=new OrderSampleFrom();
            baseCopy.copyWithNull(orderSampleFrom,orderSampleFromDTO);
            orderSampleFrom.setSampleNo(sampleNo);
            orderSampleFrom.setOrderNo(orderNo);
            orderSampleFrom.setGroupNo(groupNo);
            orderSampleFrom.setState(1);
            list.add(orderSampleFrom);
        }
        return list;
    }

    public  OrderSampleMoreDTO sampleToMore(OrderSample orderSample,List<OrderSampleFrom> list){
        OrderSampleMoreDTO orderSampleMoreDTO=new OrderSampleMoreDTO();
        baseCopy.copyWithNull(orderSampleMoreDTO,orderSample);

        List<OrderSampleFromDTO> sampleFromDTOList=new ArrayList<>();
        for(OrderSampleFrom orderSampleFrom:list){
            OrderSampleFromDTO orderSampleFromDTO=new OrderSampleFromDTO();
            baseCopy.copyWithNull(orderSampleFromDTO,orderSampleFrom);
            sampleFromDTOList.add(orderSampleFromDTO);
        }
        orderSampleMoreDTO.setSampleFromDTOList(sampleFromDTOList);
        return orderSampleMoreDTO;
    }


    public  List<OrderSampleMoreDTO> centerSampleToSample(List<CenterSampleDTO> centerSampleDTOList,int size){
        List<OrderSampleMoreDTO> list=new ArrayList<>();
        OrderSampleMoreDTO orderSampleMoreDTO=new OrderSampleMoreDTO();
        List<OrderSampleFromDTO> sampleFromDTOList=new ArrayList<>();
        for(CenterSampleDTO centerSampleDTO:centerSampleDTOList){
            OrderSampleFromDTO orderSampleFromDTO=new OrderSampleFromDTO();
            baseCopy.copyWithNull(orderSampleFromDTO,centerSampleDTO);
            if(orderSampleFromDTO.getFillLen()==0){
                orderSampleFromDTO.setFillLen(size);
            }
           /* if(centerSampleDTO.getType()==){

            }*/

            sampleFromDTOList.add(orderSampleFromDTO);
        }
        orderSampleMoreDTO.setSampleFromDTOList(sampleFromDTOList);
        list.add(orderSampleMoreDTO);
        return list;
    }




    public List<OrderSampleMoreDTO>  sampleMoreAddCenterSample(List<OrderSampleMoreDTO> sampleDTOList,List<CenterSampleDTO> centerSampleDTOList,int size){
        if(ValidationUtil.isEmpty(centerSampleDTOList)){
            return sampleDTOList;
        }
        Map<String,CenterSampleDTO> centerSampleDTOMap=centerSampleDTOList.stream().collect(Collectors.toMap(CenterSampleDTO::getSampleKey, Function.identity(), (key1, key2) -> key1, LinkedHashMap::new));
        for(int n=0;n<sampleDTOList.size();n++){
            OrderSampleMoreDTO orderSampleMoreDTO=sampleDTOList.get(n);
            List<OrderSampleFromDTO> list=orderSampleMoreDTO.getSampleFromDTOList();
            if(!ValidationUtil.isEmpty(list)){
                for(int t=0;t<list.size();t++){
                    OrderSampleFromDTO orderSampleFromDTO=list.get(t);
                    CenterSampleDTO centerSampleDTO=centerSampleDTOMap.getOrDefault(orderSampleFromDTO.getSampleKey(),new CenterSampleDTO());
                    orderSampleFromDTO.setIsMust(centerSampleDTO.getIsMust());
                    orderSampleFromDTO.setFillLen(centerSampleDTO.getFillLen());
                    orderSampleFromDTO.setRowNum(centerSampleDTO.getRowNum());
                    orderSampleFromDTO.setRegexRule(centerSampleDTO.getRegexRule());
                    orderSampleFromDTO.setAttrRules(centerSampleDTO.getAttrRules());
                    orderSampleFromDTO.setShowAttr(centerSampleDTO.getShowAttr());
                    orderSampleFromDTO.setType(centerSampleDTO.getType());
                    orderSampleFromDTO.setFillNotice(centerSampleDTO.getFillNotice());
                    if(StringUtils.isBlank(orderSampleFromDTO.getLua())){
                        orderSampleFromDTO.setLua(centerSampleDTO.getLua());
                    }
                    if(StringUtils.isBlank(orderSampleFromDTO.getSampleGroup())){
                        orderSampleFromDTO.setSampleGroup(centerSampleDTO.getSampleGroup());
                    }
                    if(orderSampleFromDTO.getFillLen()==0){
                        orderSampleFromDTO.setFillLen(size);
                    }
                    orderSampleFromDTO.setShowAttr(centerSampleDTO.getShowAttr());
                    orderSampleFromDTO.setRegexRule(centerSampleDTO.getRegexRule());
                    if(orderSampleFromDTO.getRowNum()==0){
                        orderSampleFromDTO.setRowNum(2);
                    }

                }

            }
        }
        return sampleDTOList;
    }

    public void a(){

    }





    private void initState(OrderSample orderSample,String dateStr) {
        orderSample.setState(1);
        orderSample.setCreateDate(dateStr);
        orderSample.setStateDate(dateStr);
    }


    public List<OiqSampleReq>  addOther(List<OiqSampleReq> sampleList, SampleCategoryReq sampleCategory) {
        if(ValidationUtil.isEmpty(sampleCategory)){
            return sampleList;
        }
        if(StringUtils.isBlank(sampleCategory.getSampleCategoryCode()) && StringUtils.isBlank(sampleCategory.getSampleShapeCode())){
            return sampleList;
        }
        for(OiqSampleReq oiqSampleReq:sampleList){
            oiqSampleReq.setSampleCategoryCode(sampleCategory.getSampleCategoryCode());
            oiqSampleReq.setSampleShapeCode(sampleCategory.getSampleShapeCode());
            oiqSampleReq.setSampleName(sampleCategory.getSampleName());
        }
        return sampleList;

    }

    public static SampleCategoryDTO listToSampleCategory(List<OiqSampleDTO> sampleDTOList){
        SampleCategoryDTO sampleCategoryDTO=new SampleCategoryDTO();
        for(OiqSampleDTO oiqSampleDTO:sampleDTOList){
            sampleCategoryDTO.setSampleShapeCode(oiqSampleDTO.getSampleShapeCode());
            sampleCategoryDTO.setSampleCategoryCode(oiqSampleDTO.getSampleCategoryCode());
            sampleCategoryDTO.setSampleName(oiqSampleDTO.getSampleName());
        }
        return sampleCategoryDTO;
    }


    public OrderSample initOrderSampleByCreateOrder(String orderNo, String groupNo, String sampleNo, String dateStr) {
        OrderSample orderSample = new OrderSample();
        orderSample.setOrderNo(orderNo);
        orderSample.setCreateDate(dateStr);
        orderSample.setStateDate(dateStr);
        orderSample.setGroupNo(groupNo);
        orderSample.setSampleNo(sampleNo);
        orderSample.setState(1);
        return orderSample;
    }


}
