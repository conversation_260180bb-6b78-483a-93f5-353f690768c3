package com.sgs.ecom.member.enumtool.event;

public enum EventEnum {

	SAVE_INVOICE_BOSS("saveInvoiceBoss","保存boss的发票数据"),//

	SAVE_ORDER_TO_END("saveOrderToEnd","保存订单为已完成"),

	CREATE_ORDER_TO_LEADS("createOrderToLeads","创建询价单同步leads"),

	CREATE_ORDER_ADD_LABEL("CREATE_ORDER_ADD_LABEL","创建订单的时候生成订单标签"),

	MOD_USER_LABEL("modUserLabel","新增或取消订单修改订单的复购标记"),

	CLOSE_USER_LABEL("CLOSE_USER_LABEL","取消询价单删除标签"),

	//oiq使用线上支付成功 线下到账
	PAY_TO_LEADS("payToLeads","支付或到账后同步leads"),

	//ro 确认方案处理历史
	RO_CONFIRM_OLD("roConfirmOld","确认方案处理历史"),

	SEND_WECHAT_MSG("sendWechatMsg","用于小程序的推送数据"),

	SAVE_TO_SEND_DML_MAIL("SAVE_TO_SEND_DML_MAIL","用于发dml邮件"),

	PAY_TO_QUOTATION_CONFIRM("PAY_TO_QUOTATION_CONFIRM","用于支付通知确认报价逻辑")
	;
	private String name;
	private String nameCh;

	EventEnum(String name, String nameCh) {
		this.name = name;
		this.nameCh = nameCh;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}


	public String getNameCh() {
		return nameCh;
	}

	public void setNameCh(String nameCh) {
		this.nameCh = nameCh;
	}
}
