package com.sgs.ecom.member.dto.min;

import com.platform.annotation.ApiAnno;
import com.sgs.ecom.member.base.BaseOrderFilter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class RSTSMinDTO {

	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private String roId;
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private List<AppFormField> appFormFields=new ArrayList<>();
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private String appFormName;//特殊申请表 rosh4想
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private List<RSTSFileDTO> quotationFiles=new ArrayList<>();//备注附件
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private List<RSTSFileDTO> certificateFiles=new ArrayList<>(); //营业执照
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private String saler;//csCode
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private int testCycle;

	//申请方相关
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private String shipContactName;//linkPerson 申请方联系人
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private String shipContactMobile;//linkPhone 申请方联系人手机
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private String shipContactEmail;//linkEmail 申请方客户邮件
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private String shipCustomerName;//发票数据
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private String shipCustomerAddr;//companyAddressCn
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private String shipCustomerAreaCode;//企业信息中对应的销售的所在实验室的区域代码

	//付款方名字和地址
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private String billCustomerName;//reportCompanyNameCn
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private String billCustomerAddr;//reportAddressCn
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private String reportCustomerName;//reportCompanyNameCn 改成对象处理了
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private MinReportTitleDTO reportTitles=new MinReportTitleDTO();
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private int billTypeInd;//发票类型 0 普票，1专票
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private String csQuotationDate;//createDate
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private String confirmQuotationDate;//createDate
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private String quoteAmt;//realAmount
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private String originalPrice;//realAmount
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private int settleStyleInd;//0普通 1月结

	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private int needPaperReport;//0无需纸质报告 1需要纸质报告
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private int reportQtyCn;//其他时缺省0份
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private int reportQtyEn;
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private String reportRemark;
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private String remark;

	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private String billReceiveContact; //invoiceAddress
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private String billReceiveTel;//invoiceAddress
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private String taxNo;
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private String billReceiveAddr;//invoiceAddress
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private String billReceiveEmails;//发票收取邮箱
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private String reportReceiveEmails;//报告收取邮箱列表
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private int needReturnSample;//isRefundSample

	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private int reportAntiCounterfeiting;//isOpen
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private int uploadToDatabase;//isOpen





	//固定值
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private String currency;//人民币 CNY
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private String labAreaCode;//1
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private String costCentreInd;//4259
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private String billCustomerNo;//Ronew
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private String shipCustomerNo;//Ronew
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private int testTypeId;//2
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private int billReceiveTypeInd;//1
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	private int expediteInd;//0




	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	public List<MinItemDTO> testItems=new ArrayList<>();
	@ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
	public List<MinSampleDTO> samples=new ArrayList<>();

	public List<MinItemDTO> getTestItems() {
		return testItems;
	}

	public void setTestItems(List<MinItemDTO> testItems) {
		this.testItems = testItems;
	}

	public List<MinSampleDTO> getSamples() {
		return samples;
	}

	public void setSamples(List<MinSampleDTO> samples) {
		this.samples = samples;
	}

	public String getRoId() {
		return roId;
	}

	public void setRoId(String roId) {
		this.roId = roId;
	}

	public String getSaler() {
		return saler;
	}

	public void setSaler(String saler) {
		this.saler = saler;
	}

	public String getShipCustomerNo() {
		return shipCustomerNo;
	}

	public void setShipCustomerNo(String shipCustomerNo) {
		this.shipCustomerNo = shipCustomerNo;
	}

	public String getShipContactName() {
		return shipContactName;
	}

	public void setShipContactName(String shipContactName) {
		this.shipContactName = shipContactName;
	}

	public String getShipContactMobile() {
		return shipContactMobile;
	}

	public void setShipContactMobile(String shipContactMobile) {
		this.shipContactMobile = shipContactMobile;
	}

	public String getShipContactEmail() {
		return shipContactEmail;
	}

	public void setShipContactEmail(String shipContactEmail) {
		this.shipContactEmail = shipContactEmail;
	}

	public String getShipCustomerName() {
		return shipCustomerName;
	}

	public void setShipCustomerName(String shipCustomerName) {
		this.shipCustomerName = shipCustomerName;
	}

	public String getShipCustomerAddr() {
		return shipCustomerAddr;
	}

	public void setShipCustomerAddr(String shipCustomerAddr) {
		this.shipCustomerAddr = shipCustomerAddr;
	}

	public String getBillCustomerNo() {
		return billCustomerNo;
	}

	public void setBillCustomerNo(String billCustomerNo) {
		this.billCustomerNo = billCustomerNo;
	}

	public String getBillCustomerName() {
		return billCustomerName;
	}

	public void setBillCustomerName(String billCustomerName) {
		this.billCustomerName = billCustomerName;
	}

	public String getBillCustomerAddr() {
		return billCustomerAddr;
	}

	public void setBillCustomerAddr(String billCustomerAddr) {
		this.billCustomerAddr = billCustomerAddr;
	}

	public String getReportCustomerName() {
		return reportCustomerName;
	}

	public void setReportCustomerName(String reportCustomerName) {
		this.reportCustomerName = reportCustomerName;
	}

	public int getBillTypeInd() {
		return billTypeInd;
	}

	public void setBillTypeInd(int billTypeInd) {
		this.billTypeInd = billTypeInd;
	}

	public String getCsQuotationDate() {
		return csQuotationDate;
	}

	public void setCsQuotationDate(String csQuotationDate) {
		this.csQuotationDate = csQuotationDate;
	}

	public String getConfirmQuotationDate() {
		return confirmQuotationDate;
	}

	public void setConfirmQuotationDate(String confirmQuotationDate) {
		this.confirmQuotationDate = confirmQuotationDate;
	}

	public String getQuoteAmt() {
		return quoteAmt;
	}

	public void setQuoteAmt(String quoteAmt) {
		this.quoteAmt = quoteAmt;
	}

	public String getOriginalPrice() {
		return originalPrice;
	}

	public void setOriginalPrice(String originalPrice) {
		this.originalPrice = originalPrice;
	}

	public String getCostCentreInd() {
		return costCentreInd;
	}

	public void setCostCentreInd(String costCentreInd) {
		this.costCentreInd = costCentreInd;
	}

	public int getTestTypeId() {
		return testTypeId;
	}

	public void setTestTypeId(int testTypeId) {
		this.testTypeId = testTypeId;
	}

	public String getLabAreaCode() {
		return labAreaCode;
	}

	public void setLabAreaCode(String labAreaCode) {
		this.labAreaCode = labAreaCode;
	}

	public int getNeedPaperReport() {
		return needPaperReport;
	}

	public void setNeedPaperReport(int needPaperReport) {
		this.needPaperReport = needPaperReport;
	}

	public int getReportQtyCn() {
		return reportQtyCn;
	}

	public void setReportQtyCn(int reportQtyCn) {
		this.reportQtyCn = reportQtyCn;
	}

	public int getReportQtyEn() {
		return reportQtyEn;
	}

	public void setReportQtyEn(int reportQtyEn) {
		this.reportQtyEn = reportQtyEn;
	}

	public int getBillReceiveTypeInd() {
		return billReceiveTypeInd;
	}

	public void setBillReceiveTypeInd(int billReceiveTypeInd) {
		this.billReceiveTypeInd = billReceiveTypeInd;
	}

	public String getBillReceiveContact() {
		return billReceiveContact;
	}

	public void setBillReceiveContact(String billReceiveContact) {
		this.billReceiveContact = billReceiveContact;
	}

	public String getBillReceiveTel() {
		return billReceiveTel;
	}

	public void setBillReceiveTel(String billReceiveTel) {
		this.billReceiveTel = billReceiveTel;
	}

	public String getBillReceiveAddr() {
		return billReceiveAddr;
	}

	public void setBillReceiveAddr(String billReceiveAddr) {
		this.billReceiveAddr = billReceiveAddr;
	}

	public int getExpediteInd() {
		return expediteInd;
	}

	public void setExpediteInd(int expediteInd) {
		this.expediteInd = expediteInd;
	}

	public int getNeedReturnSample() {
		return needReturnSample;
	}

	public void setNeedReturnSample(int needReturnSample) {
		this.needReturnSample = needReturnSample;
	}

	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public List<AppFormField> getAppFormFields() {
		return appFormFields;
	}

	public void setAppFormFields(List<AppFormField> appFormFields) {
		this.appFormFields = appFormFields;
	}

	public String getReportRemark() {
		return reportRemark;
	}

	public void setReportRemark(String reportRemark) {
		this.reportRemark = reportRemark;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public List<RSTSFileDTO> getQuotationFiles() {
		return quotationFiles;
	}

	public void setQuotationFiles(List<RSTSFileDTO> quotationFiles) {
		this.quotationFiles = quotationFiles;
	}

	public List<RSTSFileDTO> getCertificateFiles() {
		return certificateFiles;
	}

	public void setCertificateFiles(List<RSTSFileDTO> certificateFiles) {
		this.certificateFiles = certificateFiles;
	}

	public int getReportAntiCounterfeiting() {
		return reportAntiCounterfeiting;
	}

	public void setReportAntiCounterfeiting(int reportAntiCounterfeiting) {
		this.reportAntiCounterfeiting = reportAntiCounterfeiting;
	}

	public int getUploadToDatabase() {
		return uploadToDatabase;
	}

	public void setUploadToDatabase(int uploadToDatabase) {
		this.uploadToDatabase = uploadToDatabase;
	}

	public String getAppFormName() {
		return appFormName;
	}

	public void setAppFormName(String appFormName) {
		this.appFormName = appFormName;
	}

	public String getShipCustomerAreaCode() {
		return shipCustomerAreaCode;
	}

	public void setShipCustomerAreaCode(String shipCustomerAreaCode) {
		this.shipCustomerAreaCode = shipCustomerAreaCode;
	}

	public int getSettleStyleInd() {
		return settleStyleInd;
	}

	public void setSettleStyleInd(int settleStyleInd) {
		this.settleStyleInd = settleStyleInd;
	}

	public String getTaxNo() {
		return taxNo;
	}

	public void setTaxNo(String taxNo) {
		this.taxNo = taxNo;
	}

	public int getTestCycle() {
		return testCycle;
	}

	public void setTestCycle(int testCycle) {
		this.testCycle = testCycle;
	}

	public String getBillReceiveEmails() {
		return billReceiveEmails;
	}

	public void setBillReceiveEmails(String billReceiveEmails) {
		this.billReceiveEmails = billReceiveEmails;
	}

	public String getReportReceiveEmails() {
		return reportReceiveEmails;
	}

	public void setReportReceiveEmails(String reportReceiveEmails) {
		this.reportReceiveEmails = reportReceiveEmails;
	}

	public MinReportTitleDTO getReportTitles() {
		return reportTitles;
	}

	public void setReportTitles(MinReportTitleDTO reportTitles) {
		this.reportTitles = reportTitles;
	}
}
