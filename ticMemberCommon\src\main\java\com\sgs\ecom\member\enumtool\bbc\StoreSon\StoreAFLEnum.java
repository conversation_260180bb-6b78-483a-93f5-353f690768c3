package com.sgs.ecom.member.enumtool.bbc.StoreSon;

public enum StoreAFLEnum {
	DEFAULT("AFL-DEFAULT", "AFL-DEFAULT"),
	;

	StoreAFLEnum(String name, String nameCh) {
		this.name = name;
		this.nameCh = nameCh;
	}

	public static String getNameCh(String name) {
		for (StoreAFLEnum c : StoreAFLEnum.values()) {
			if (c.getName().equals(name)) {
				return c.getNameCh();
			}
		}
		return null;
	}



	private String name;
	private String nameCh;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}


	public String getNameCh() {
		return nameCh;
	}

	public void setNameCh(String nameCh) {
		this.nameCh = nameCh;
	}


}
