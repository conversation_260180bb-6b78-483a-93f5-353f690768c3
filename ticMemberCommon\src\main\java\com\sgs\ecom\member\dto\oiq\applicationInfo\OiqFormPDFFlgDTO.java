package com.sgs.ecom.member.dto.oiq.applicationInfo;

import com.sgs.ecom.member.dto.OrderDetailDTO;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class OiqFormPDFFlgDTO {

    public static final String SAMPLE_FLG="sampleFlg";

    private int reportLuaFlg;
    private int reportFormFlg;
    private int reportMethodFlg;
    private int companyNameFlg;
    private int companyNameEnFlg;
    private int companyAddressCnFlg;
    private int companyAddressEnFlg;
    private int reportCompanyNameCnFlg;
    private int reportCompanyNameEnFlg;
    private int reportAddressCnFlg;
    private int reportAddressEnFlg;

    private int payerNameFlg;
    private int payerPhoneFlg;
    private int payerEmailFlg;
    private int invoiceTypeShowFlg;

    private int isUrgentShowFlg;
    private int testMemoFlg;
    private int isRefundSampleFlg;
    private int reportStrFlg;
    private int backStrFlg;
    private OiqInvoiceFlgDTO oiqInvoiceFlgDTO=new OiqInvoiceFlgDTO();
    private OiqDetailFlgDTO oiqDetailFlgDTO=new OiqDetailFlgDTO();
    private int invoiceEmailFlg;
    private int reportEmailFlg;


    public OiqFormPDFFlgDTO() {
    }

    public OiqFormPDFFlgDTO(ConcurrentHashMap<String,String> map, List<OrderDetailDTO> orderDetailDTOList) {
        System.out.println("pdf-map=="+map);
        if(map.containsKey("-report-reportLuaValue")){
            this.reportLuaFlg=1;
        }
        if(map.containsKey("-report-reportFormValue")){
            this.reportFormFlg=1;
        }
        if(map.containsKey("-report-reportMethodShow")){
            this.reportMethodFlg=1;
        }
        if(map.containsKey("-application-companyNameCn")){
            this.companyNameFlg=1;
        }
        if(map.containsKey("-application-companyNameEn")){
            this.companyNameEnFlg=1;
        }
        if(map.containsKey("-application-companyAddressCn")){
            this.companyAddressCnFlg=1;
        }
        if(map.containsKey("-application-companyAddressEn")){
            this.companyAddressEnFlg=1;
        }
        if(map.containsKey("-report-reportCompanyNameCn")){
            this.reportCompanyNameCnFlg=1;
        }
        if(map.containsKey("-report-reportCompanyNameEn")){
            this.reportCompanyNameEnFlg=1;
        }
        if(map.containsKey("-report-reportAddressCn")){
            this.reportAddressCnFlg=1;
        }
        if(map.containsKey("-report-reportAddressEn")){
            this.reportAddressEnFlg=1;
        }
        if(map.containsKey("-orderInvoice-payerName")){
            this.payerNameFlg=1;
        }
        if(map.containsKey("-orderInvoice-payerPhone")){
            this.payerPhoneFlg=1;
        }
        if(map.containsKey("-orderInvoice-payerEmail")){
            this.payerEmailFlg=1;
        }
        if(map.containsKey("-orderInvoice-invoiceTypeShow")){
            this.invoiceTypeShowFlg=1;
        }
        if(map.containsKey("-application-testMemo")){
            this.testMemoFlg=1;
        }
        if(map.containsKey("-orderBase-isUrgentShow")){
            this.isUrgentShowFlg=1;
        }
        if(map.containsKey("-orderInvoice-invoice-isForeign")){
            this.oiqInvoiceFlgDTO=new OiqInvoiceFlgDTO(1);
        }else{
            OiqInvoiceFlgDTO oiqInvoiceFlgDTO=new OiqInvoiceFlgDTO();
            if(map.containsKey("-orderInvoice-invoice-invoiceTitle")){
                oiqInvoiceFlgDTO.setInvoiceTitleFlg(1);
            }
            if(map.containsKey("-orderInvoice-invoice-bankName")){
                oiqInvoiceFlgDTO.setBankNameFlg(1);
            }
            if(map.containsKey("-orderInvoice-invoice-regAddress")){
                oiqInvoiceFlgDTO.setRegAddressFlg(1);
            }
            if(map.containsKey("-orderInvoice-invoice-regPhone")){
                oiqInvoiceFlgDTO.setRegPhoneFlg(1);
            }
            if(map.containsKey("-orderInvoice-invoice-taxNo")){
                oiqInvoiceFlgDTO.setTaxNoFlg(1);
            }
            if(map.containsKey("-orderInvoice-invoice-bankNumber")){
                oiqInvoiceFlgDTO.setBankNumberFlg(1);
            }
            if(map.containsKey("-orderInvoice-invoice-country")){
                oiqInvoiceFlgDTO.setCountryFlg(1);
            }
            if(map.containsKey("-orderInvoice-invoice-foreignCity")){
                oiqInvoiceFlgDTO.setForeignCityFlg(1);
            }
            if(map.containsKey("-orderInvoice-invoice-postCode")){
                oiqInvoiceFlgDTO.setPostCodeFlg(1);
            }
            if(map.containsKey("-orderInvoice-invoice-contact")){
                oiqInvoiceFlgDTO.setContactFlg(1);
            }
            this.oiqInvoiceFlgDTO=oiqInvoiceFlgDTO;
        }
        if(map.containsKey("-application-isRefundSample")){
            this.isRefundSampleFlg=1;
        }
        if(map.containsKey("backStrFlg")){
            this.backStrFlg=1;
        }
        if(map.containsKey("reportStrFlg")){
            this.reportStrFlg=1;
        }
        if(map.containsKey("invoiceEmailFlg")){
            this.invoiceEmailFlg=1;
        }
        if(map.containsKey("reportEmailFlg")){
            this.reportEmailFlg=1;
        }
        if(map.containsKey(OiqDetailFlgDTO.ITEM_FLG)){
            this.oiqDetailFlgDTO=new OiqDetailFlgDTO(1);
        }else{
            OiqDetailFlgDTO oiqDetailFlgDTO1=new OiqDetailFlgDTO();
            for(int n=0;n<orderDetailDTOList.size();n++){
                String key="-itemList-"+n+"-";
                if(map.containsKey(key+"itemName")){
                    oiqDetailFlgDTO1.setItemNameFlg(1);
                }
                if(map.containsKey(key+"standardCode")){
                    oiqDetailFlgDTO1.setStandardCodeFlg(1);
                }
                if(map.containsKey(key+"testMemo")){
                    oiqDetailFlgDTO1.setTestMemoFlg(1);
                }
                if(map.containsKey(key+"isDetermine")){
                    oiqDetailFlgDTO1.setIsDetermineFlg(1);
                }

            }
            this.oiqDetailFlgDTO=oiqDetailFlgDTO1;
        }


    }



    public int getReportLuaFlg() {
        return reportLuaFlg;
    }

    public void setReportLuaFlg(int reportLuaFlg) {
        this.reportLuaFlg = reportLuaFlg;
    }

    public int getReportFormFlg() {
        return reportFormFlg;
    }

    public void setReportFormFlg(int reportFormFlg) {
        this.reportFormFlg = reportFormFlg;
    }

    public int getReportMethodFlg() {
        return reportMethodFlg;
    }

    public void setReportMethodFlg(int reportMethodFlg) {
        this.reportMethodFlg = reportMethodFlg;
    }

    public int getCompanyNameFlg() {
        return companyNameFlg;
    }

    public void setCompanyNameFlg(int companyNameFlg) {
        this.companyNameFlg = companyNameFlg;
    }

    public int getCompanyNameEnFlg() {
        return companyNameEnFlg;
    }

    public void setCompanyNameEnFlg(int companyNameEnFlg) {
        this.companyNameEnFlg = companyNameEnFlg;
    }

    public int getCompanyAddressCnFlg() {
        return companyAddressCnFlg;
    }

    public void setCompanyAddressCnFlg(int companyAddressCnFlg) {
        this.companyAddressCnFlg = companyAddressCnFlg;
    }

    public int getCompanyAddressEnFlg() {
        return companyAddressEnFlg;
    }

    public void setCompanyAddressEnFlg(int companyAddressEnFlg) {
        this.companyAddressEnFlg = companyAddressEnFlg;
    }

    public int getReportCompanyNameCnFlg() {
        return reportCompanyNameCnFlg;
    }

    public void setReportCompanyNameCnFlg(int reportCompanyNameCnFlg) {
        this.reportCompanyNameCnFlg = reportCompanyNameCnFlg;
    }

    public int getReportCompanyNameEnFlg() {
        return reportCompanyNameEnFlg;
    }

    public void setReportCompanyNameEnFlg(int reportCompanyNameEnFlg) {
        this.reportCompanyNameEnFlg = reportCompanyNameEnFlg;
    }

    public int getReportAddressCnFlg() {
        return reportAddressCnFlg;
    }

    public void setReportAddressCnFlg(int reportAddressCnFlg) {
        this.reportAddressCnFlg = reportAddressCnFlg;
    }

    public int getReportAddressEnFlg() {
        return reportAddressEnFlg;
    }

    public void setReportAddressEnFlg(int reportAddressEnFlg) {
        this.reportAddressEnFlg = reportAddressEnFlg;
    }

    public int getPayerNameFlg() {
        return payerNameFlg;
    }

    public void setPayerNameFlg(int payerNameFlg) {
        this.payerNameFlg = payerNameFlg;
    }

    public int getPayerPhoneFlg() {
        return payerPhoneFlg;
    }

    public void setPayerPhoneFlg(int payerPhoneFlg) {
        this.payerPhoneFlg = payerPhoneFlg;
    }

    public int getPayerEmailFlg() {
        return payerEmailFlg;
    }

    public void setPayerEmailFlg(int payerEmailFlg) {
        this.payerEmailFlg = payerEmailFlg;
    }

    public int getInvoiceTypeShowFlg() {
        return invoiceTypeShowFlg;
    }

    public void setInvoiceTypeShowFlg(int invoiceTypeShowFlg) {
        this.invoiceTypeShowFlg = invoiceTypeShowFlg;
    }

    public int getIsUrgentShowFlg() {
        return isUrgentShowFlg;
    }

    public void setIsUrgentShowFlg(int isUrgentShowFlg) {
        this.isUrgentShowFlg = isUrgentShowFlg;
    }

    public int getTestMemoFlg() {
        return testMemoFlg;
    }

    public void setTestMemoFlg(int testMemoFlg) {
        this.testMemoFlg = testMemoFlg;
    }

    public OiqInvoiceFlgDTO getOiqInvoiceFlgDTO() {
        return oiqInvoiceFlgDTO;
    }

    public void setOiqInvoiceFlgDTO(OiqInvoiceFlgDTO oiqInvoiceFlgDTO) {
        this.oiqInvoiceFlgDTO = oiqInvoiceFlgDTO;
    }

    public int getIsRefundSampleFlg() {
        return isRefundSampleFlg;
    }

    public void setIsRefundSampleFlg(int isRefundSampleFlg) {
        this.isRefundSampleFlg = isRefundSampleFlg;
    }

    public int getReportStrFlg() {
        return reportStrFlg;
    }

    public void setReportStrFlg(int reportStrFlg) {
        this.reportStrFlg = reportStrFlg;
    }

    public int getBackStrFlg() {
        return backStrFlg;
    }

    public void setBackStrFlg(int backStrFlg) {
        this.backStrFlg = backStrFlg;
    }

    public int getInvoiceEmailFlg() {
        return invoiceEmailFlg;
    }

    public void setInvoiceEmailFlg(int invoiceEmailFlg) {
        this.invoiceEmailFlg = invoiceEmailFlg;
    }

    public int getReportEmailFlg() {
        return reportEmailFlg;
    }

    public void setReportEmailFlg(int reportEmailFlg) {
        this.reportEmailFlg = reportEmailFlg;
    }

    public OiqDetailFlgDTO getOiqDetailFlgDTO() {
        return oiqDetailFlgDTO;
    }

    public void setOiqDetailFlgDTO(OiqDetailFlgDTO oiqDetailFlgDTO) {
        this.oiqDetailFlgDTO = oiqDetailFlgDTO;
    }
}