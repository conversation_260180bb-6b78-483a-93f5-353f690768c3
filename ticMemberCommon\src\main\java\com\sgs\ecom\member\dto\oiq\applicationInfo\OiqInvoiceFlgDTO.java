package com.sgs.ecom.member.dto.oiq.applicationInfo;

public class OiqInvoiceFlgDTO {
    
    private int invoiceTitleFlg;
    private int bankNameFlg;
    private int regAddressFlg;
    private int regPhoneFlg;
    private int taxNoFlg;
    private int bankNumberFlg;
    private int countryFlg;
    private int foreignCityFlg;
    private int postCodeFlg;
    private int contactFlg;

    public OiqInvoiceFlgDTO() {
    }

    public OiqInvoiceFlgDTO(Integer n) {
        this.invoiceTitleFlg = n;
        this.bankNameFlg = n;
        this.regAddressFlg = n;
        this.regPhoneFlg = n;
        this.taxNoFlg = n;
        this.bankNumberFlg = n;
        this.countryFlg = n;
        this.foreignCityFlg = n;
        this.postCodeFlg = n;
        this.contactFlg = n;
    }

    public int getInvoiceTitleFlg() {
        return invoiceTitleFlg;
    }

    public void setInvoiceTitleFlg(int invoiceTitleFlg) {
        this.invoiceTitleFlg = invoiceTitleFlg;
    }

    public int getBankNameFlg() {
        return bankNameFlg;
    }

    public void setBankNameFlg(int bankNameFlg) {
        this.bankNameFlg = bankNameFlg;
    }

    public int getRegAddressFlg() {
        return regAddressFlg;
    }

    public void setRegAddressFlg(int regAddressFlg) {
        this.regAddressFlg = regAddressFlg;
    }

    public int getRegPhoneFlg() {
        return regPhoneFlg;
    }

    public void setRegPhoneFlg(int regPhoneFlg) {
        this.regPhoneFlg = regPhoneFlg;
    }

    public int getTaxNoFlg() {
        return taxNoFlg;
    }

    public void setTaxNoFlg(int taxNoFlg) {
        this.taxNoFlg = taxNoFlg;
    }

    public int getBankNumberFlg() {
        return bankNumberFlg;
    }

    public void setBankNumberFlg(int bankNumberFlg) {
        this.bankNumberFlg = bankNumberFlg;
    }

    public int getCountryFlg() {
        return countryFlg;
    }

    public void setCountryFlg(int countryFlg) {
        this.countryFlg = countryFlg;
    }

    public int getForeignCityFlg() {
        return foreignCityFlg;
    }

    public void setForeignCityFlg(int foreignCityFlg) {
        this.foreignCityFlg = foreignCityFlg;
    }

    public int getPostCodeFlg() {
        return postCodeFlg;
    }

    public void setPostCodeFlg(int postCodeFlg) {
        this.postCodeFlg = postCodeFlg;
    }

    public int getContactFlg() {
        return contactFlg;
    }

    public void setContactFlg(int contactFlg) {
        this.contactFlg = contactFlg;
    }
}
