package com.sgs.ecom.member.dto.rpc;

public class CenterSampleDTO {
	private String sampleKey;
	private String sampleKeyName;
	private int sortShow;
	private int isMerge;
	private int isMust;//是否必输
	private int type;//字段定义页面类型 0-输入框 1-前缀方形勾选框 2-圆形前缀勾选' AFTER `SORT_SHOW`

	private String lua;
	private String sampleGroup;

	private String enumConfig;

	private int rowNum;
	private String dataType;
	private String fillNotice;
	private String fillNoticeEn;
	private int fillLen;
	private int isTitle;
	private String showLua;
	private int isHide;
	private String areaCode;
	private String regexRule;
	private String attrRules;
	private String tipsCn;
	private String tipsEn;
	private String showAttr;
	private String enums;
	private int state;
	private String remarkEnumConfig;


	public int getRowNum() {
		return rowNum;
	}

	public void setRowNum(int rowNum) {
		this.rowNum = rowNum;
	}

	public String getDataType() {
		return dataType;
	}

	public void setDataType(String dataType) {
		this.dataType = dataType;
	}

	public String getFillNotice() {
		return fillNotice;
	}

	public void setFillNotice(String fillNotice) {
		this.fillNotice = fillNotice;
	}

	public String getFillNoticeEn() {
		return fillNoticeEn;
	}

	public void setFillNoticeEn(String fillNoticeEn) {
		this.fillNoticeEn = fillNoticeEn;
	}

	public int getFillLen() {
		return fillLen;
	}

	public void setFillLen(int fillLen) {
		this.fillLen = fillLen;
	}

	public int getIsTitle() {
		return isTitle;
	}

	public void setIsTitle(int isTitle) {
		this.isTitle = isTitle;
	}

	public String getShowLua() {
		return showLua;
	}

	public void setShowLua(String showLua) {
		this.showLua = showLua;
	}

	public int getIsHide() {
		return isHide;
	}

	public void setIsHide(int isHide) {
		this.isHide = isHide;
	}

	public String getAreaCode() {
		return areaCode;
	}

	public void setAreaCode(String areaCode) {
		this.areaCode = areaCode;
	}

	public String getRegexRule() {
		return regexRule;
	}

	public void setRegexRule(String regexRule) {
		this.regexRule = regexRule;
	}

	public String getAttrRules() {
		return attrRules;
	}

	public void setAttrRules(String attrRules) {
		this.attrRules = attrRules;
	}

	public String getTipsCn() {
		return tipsCn;
	}

	public void setTipsCn(String tipsCn) {
		this.tipsCn = tipsCn;
	}

	public String getTipsEn() {
		return tipsEn;
	}

	public void setTipsEn(String tipsEn) {
		this.tipsEn = tipsEn;
	}

	public String getShowAttr() {
		return showAttr;
	}

	public void setShowAttr(String showAttr) {
		this.showAttr = showAttr;
	}

	public String getEnums() {
		return enums;
	}

	public void setEnums(String enums) {
		this.enums = enums;
	}

	public CenterSampleDTO() {


	}

	public String getLua() {
		return lua;
	}

	public void setLua(String lua) {
		this.lua = lua;
	}

	public int getIsMust() {
		return isMust;
	}

	public void setIsMust(int isMust) {
		this.isMust = isMust;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	public String getSampleKey() {
		return sampleKey;
	}

	public void setSampleKey(String sampleKey) {
		this.sampleKey = sampleKey;
	}

	public String getSampleKeyName() {
		return sampleKeyName;
	}

	public void setSampleKeyName(String sampleKeyName) {
		this.sampleKeyName = sampleKeyName;
	}

	public int getSortShow() {
		return sortShow;
	}

	public void setSortShow(int sortShow) {
		this.sortShow = sortShow;
	}

	public int getIsMerge() {
		return isMerge;
	}

	public void setIsMerge(int isMerge) {
		this.isMerge = isMerge;
	}

	public String getEnumConfig() {
		return enumConfig;
	}

	public void setEnumConfig(String enumConfig) {
		this.enumConfig = enumConfig;
	}

	public String getSampleGroup() {
		return sampleGroup;
	}

	public void setSampleGroup(String sampleGroup) {
		this.sampleGroup = sampleGroup;
	}

	public int getState() {
		return state;
	}

	public void setState(int state) {
		this.state = state;
	}

	public String getRemarkEnumConfig() {
		return remarkEnumConfig;
	}

	public void setRemarkEnumConfig(String remarkEnumConfig) {
		this.remarkEnumConfig = remarkEnumConfig;
	}
}
