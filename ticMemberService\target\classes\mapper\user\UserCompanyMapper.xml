<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.user.dao.UserCompanyMapper" >


  <insert id="insertSelective" parameterType="com.sgs.ecom.member.vo.VOUserCompany" >
    insert into USER_COMPANY
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="companyId != null" >
        COMPANY_ID,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
      <if test="companyName != null" >
        COMPANY_NAME,
      </if>
      <if test="companyNameEn != null" >
        COMPANY_NAME_EN,
      </if>
      <if test="country != null" >
        COUNTRY,
      </if>
      <if test="provice != null" >
        PROVICE,
      </if>
      <if test="city != null" >
        CITY,
      </if>
      <if test="town != null" >
        TOWN,
      </if>
      <if test="address != null" >
        ADDRESS,
      </if>
      <if test="state != null" >
        STATE,
      </if>
      <if test="createDate != null" >
        CREATE_DATE,
      </if>
      <if test="stateDate != null" >
        STATE_DATE,
      </if>
      <if test="companyAddrEn != null" >
        COMPANY_ADDR_EN,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="companyId != null" >
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="companyName != null" >
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="companyNameEn != null" >
        #{companyNameEn,jdbcType=VARCHAR},
      </if>
      <if test="country != null" >
        #{country,jdbcType=VARCHAR},
      </if>
      <if test="provice != null" >
        #{provice,jdbcType=VARCHAR},
      </if>
      <if test="city != null" >
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="town != null" >
        #{town,jdbcType=VARCHAR},
      </if>
      <if test="address != null" >
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        #{state,jdbcType=TINYINT},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        #{stateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="companyAddrEn != null" >
        #{companyAddrEn,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.ecom.member.vo.VOUserCompany" >
    update USER_COMPANY
    <set >
      <if test="userId != null" >
        USER_ID = #{userId,jdbcType=BIGINT},
      </if>
      <if test="companyName != null" >
        COMPANY_NAME = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="companyNameEn != null" >
        COMPANY_NAME_EN = #{companyNameEn,jdbcType=VARCHAR},
      </if>
      <if test="country != null" >
        COUNTRY = #{country,jdbcType=VARCHAR},
      </if>
      <if test="provice != null" >
        PROVICE = #{provice,jdbcType=VARCHAR},
      </if>
      <if test="city != null" >
        CITY = #{city,jdbcType=VARCHAR},
      </if>
      <if test="town != null" >
        TOWN = #{town,jdbcType=VARCHAR},
      </if>
      <if test="address != null" >
        ADDRESS = #{address,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        STATE = #{state,jdbcType=TINYINT},
      </if>
      <if test="createDate != null" >
        CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        STATE_DATE = #{stateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="companyAddrEn != null" >
        COMPANY_ADDR_EN = #{companyAddrEn,jdbcType=VARCHAR},
      </if>
    </set>
    where COMPANY_ID = #{companyId,jdbcType=BIGINT}
  </update>


  <!--自定义 -->
  <sql id="sqlListDTO" >
     COMPANY_ID, USER_ID, COMPANY_NAME, COMPANY_NAME_EN, COUNTRY, PROVICE, CITY, TOWN,
    ADDRESS, STATE, CREATE_DATE, STATE_DATE, COMPANY_ADDR_EN
  </sql>

  <resultMap id="resultDTO" type="com.sgs.ecom.member.dto.user.UserCompanyDTO" >
    <id column="COMPANY_ID" property="companyId" jdbcType="BIGINT" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR" />
    <result column="COMPANY_NAME_EN" property="companyNameEn" jdbcType="VARCHAR" />
    <result column="COUNTRY" property="country" jdbcType="VARCHAR" />
    <result column="PROVICE" property="provice" jdbcType="VARCHAR" />
    <result column="CITY" property="city" jdbcType="VARCHAR" />
    <result column="TOWN" property="town" jdbcType="VARCHAR" />
    <result column="ADDRESS" property="address" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="TINYINT" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
    <result column="COMPANY_ADDR_EN" property="companyAddrEn" jdbcType="VARCHAR" />
  </resultMap>

  <select id="selectListByMap" resultMap="resultDTO" >
    select
    <include refid="sqlListDTO" />
    from USER_COMPANY
    <include refid="baseQueryWhere"/>
    order by COMPANY_ID desc
  </select>

  <select id="selectDTOByUserId" resultMap="resultDTO" >
    select
    <include refid="sqlListDTO" />
    from USER_COMPANY
    where USER_ID=#{userId} and STATE=1
    order by COMPANY_ID desc
    limit 1
  </select>

  <sql id="baseQueryWhere">
    where 1=1
    <if test="userId != null">
      AND USER_ID=#{userId}
    </if>
    <if test="state != null">
      AND STATE=#{state}
    </if>

  </sql>


</mapper>