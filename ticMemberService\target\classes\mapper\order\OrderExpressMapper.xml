<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.order.dao.OrderExpressMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.vo.VOOrderExpress" >
    <id column="EXPRESS_ID" property="expressId" jdbcType="BIGINT" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="DELIVER_TYPE" property="deliverType" jdbcType="INTEGER" />
    <result column="EXPRESS_TYPE" property="expressType" jdbcType="VARCHAR" />
    <result column="EXPRESS_NO" property="expressNo" jdbcType="VARCHAR" />
    <result column="SEND_PERSON" property="sendPerson" jdbcType="VARCHAR" />
    <result column="SEND_PHONE" property="sendPhone" jdbcType="VARCHAR" />
    <result column="SEND_PROVICE" property="sendProvice" jdbcType="VARCHAR" />
    <result column="SEND_CITY" property="sendCity" jdbcType="VARCHAR" />
    <result column="SEND_TOWN" property="sendTown" jdbcType="VARCHAR" />
    <result column="SEND_ADDR" property="sendAddr" jdbcType="VARCHAR" />
    <result column="RECEIPT_PERSON" property="receiptPerson" jdbcType="VARCHAR" />
    <result column="RECEIPT_PHONE" property="receiptPhone" jdbcType="VARCHAR" />
    <result column="RECEIPT_PROVICE" property="receiptProvice" jdbcType="VARCHAR" />
    <result column="RECEIPT_CITY" property="receiptCity" jdbcType="VARCHAR" />
    <result column="RECEIPT_TOWN" property="receiptTown" jdbcType="VARCHAR" />
    <result column="RECEIPT_ADDR" property="receiptAddr" jdbcType="VARCHAR" />
    <result column="RECEIPT_COMPANY" property="receiptCompany" jdbcType="VARCHAR" />
    <result column="LAB_ID" property="labId" jdbcType="BIGINT" />
    <result column="LAB_NAME" property="labName" jdbcType="VARCHAR" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
    <result column="EXPRESS_CODE" property="expressCode" jdbcType="VARCHAR" />
    <result column="PRE_EXPRESS_DATE" property="preExpressDate" jdbcType="TIMESTAMP" />
    <result column="GOODS_NAME" property="goodsName" jdbcType="VARCHAR" />
    <result column="GOODS_PRICE" property="goodsPrice" jdbcType="DECIMAL" />
    <result column="GOODS_TYPE" property="goodsType" jdbcType="INTEGER" />
    <result column="PACKAGE_NUMS" property="packageNums" jdbcType="INTEGER" />
    <result column="ADDRESS_ID" property="addressId" jdbcType="BIGINT" />
    <result column="IS_CS" property="isCs" jdbcType="INTEGER" />
    <result column="MEMO" property="memo" jdbcType="VARCHAR"/>
  </resultMap>
  <resultMap id="BaseResult" type="com.sgs.ecom.member.entity.order.OrderExpress" >
    <id column="EXPRESS_ID" property="expressId" jdbcType="BIGINT" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="DELIVER_TYPE" property="deliverType" jdbcType="INTEGER" />
    <result column="EXPRESS_TYPE" property="expressType" jdbcType="VARCHAR" />
    <result column="EXPRESS_NO" property="expressNo" jdbcType="VARCHAR" />
    <result column="SEND_PERSON" property="sendPerson" jdbcType="VARCHAR" />
    <result column="SEND_PHONE" property="sendPhone" jdbcType="VARCHAR" />
    <result column="SEND_PROVICE" property="sendProvice" jdbcType="VARCHAR" />
    <result column="SEND_CITY" property="sendCity" jdbcType="VARCHAR" />
    <result column="SEND_TOWN" property="sendTown" jdbcType="VARCHAR" />
    <result column="SEND_ADDR" property="sendAddr" jdbcType="VARCHAR" />
    <result column="RECEIPT_PERSON" property="receiptPerson" jdbcType="VARCHAR" />
    <result column="RECEIPT_PHONE" property="receiptPhone" jdbcType="VARCHAR" />
    <result column="RECEIPT_PROVICE" property="receiptProvice" jdbcType="VARCHAR" />
    <result column="RECEIPT_CITY" property="receiptCity" jdbcType="VARCHAR" />
    <result column="RECEIPT_TOWN" property="receiptTown" jdbcType="VARCHAR" />
    <result column="RECEIPT_ADDR" property="receiptAddr" jdbcType="VARCHAR" />
    <result column="RECEIPT_COMPANY" property="receiptCompany" jdbcType="VARCHAR" />
    <result column="LAB_ID" property="labId" jdbcType="BIGINT" />
    <result column="LAB_NAME" property="labName" jdbcType="VARCHAR" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
    <result column="EXPRESS_CODE" property="expressCode" jdbcType="VARCHAR" />
    <result column="PRE_EXPRESS_DATE" property="preExpressDate" jdbcType="TIMESTAMP" />
    <result column="GOODS_NAME" property="goodsName" jdbcType="VARCHAR" />
    <result column="GOODS_PRICE" property="goodsPrice" jdbcType="DECIMAL" />
    <result column="GOODS_TYPE" property="goodsType" jdbcType="INTEGER" />
    <result column="PACKAGE_NUMS" property="packageNums" jdbcType="INTEGER" />
    <result column="ADDRESS_ID" property="addressId" jdbcType="BIGINT" />
    <result column="IS_CS" property="isCs" jdbcType="INTEGER" />
    <result column="MEMO" property="memo" jdbcType="VARCHAR"/>
  </resultMap>
  <sql id="Base_Column_List" >
    EXPRESS_ID, ORDER_NO, DELIVER_TYPE, EXPRESS_TYPE, EXPRESS_NO, SEND_PERSON, SEND_PHONE,
    SEND_PROVICE, SEND_CITY, SEND_TOWN, SEND_ADDR, RECEIPT_PERSON, RECEIPT_PHONE, RECEIPT_PROVICE, 
    RECEIPT_CITY, RECEIPT_TOWN, RECEIPT_ADDR, RECEIPT_COMPANY, LAB_ID, LAB_NAME, CREATE_DATE, STATE_DATE,
    EXPRESS_CODE, PRE_EXPRESS_DATE, GOODS_NAME, GOODS_PRICE, PACKAGE_NUMS,ADDRESS_ID,GOODS_TYPE,IS_CS,MEMO
  </sql>



  <insert id="insertVOSelective" parameterType="com.sgs.ecom.member.vo.VOOrderExpress" >
    <selectKey resultType="java.lang.Long" keyProperty="expressId" order="AFTER">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ORDER_EXPRESS
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="expressId != null" >
        EXPRESS_ID,
      </if>
      <if test="orderNo != null" >
        ORDER_NO,
      </if>
      <if test="deliverType != null" >
        DELIVER_TYPE,
      </if>
      <if test="expressType != null" >
        EXPRESS_TYPE,
      </if>
      <if test="expressNo != null" >
        EXPRESS_NO,
      </if>
      <if test="sendPerson != null" >
        SEND_PERSON,
      </if>
      <if test="sendPhone != null" >
        SEND_PHONE,
      </if>
      <if test="sendProvice != null" >
        SEND_PROVICE,
      </if>
      <if test="sendCity != null" >
        SEND_CITY,
      </if>
      <if test="sendTown != null" >
        SEND_TOWN,
      </if>
      <if test="sendAddr != null" >
        SEND_ADDR,
      </if>
      <if test="receiptPerson != null" >
        RECEIPT_PERSON,
      </if>
      <if test="receiptCompany != null" >
        RECEIPT_COMPANY,
      </if>
      <if test="receiptPhone != null" >
        RECEIPT_PHONE,
      </if>
      <if test="receiptProvice != null" >
        RECEIPT_PROVICE,
      </if>
      <if test="receiptCity != null" >
        RECEIPT_CITY,
      </if>
      <if test="receiptTown != null" >
        RECEIPT_TOWN,
      </if>
      <if test="receiptAddr != null" >
        RECEIPT_ADDR,
      </if>
      <if test="labId != null" >
        LAB_ID,
      </if>
      <if test="labName != null" >
        LAB_NAME,
      </if>
      <if test="createDate != null" >
        CREATE_DATE,
      </if>
      <if test="stateDate != null" >
        STATE_DATE,
      </if>
      <if test="expressCode != null" >
        EXPRESS_CODE,
      </if>
      <if test="preExpressDate != null" >
        PRE_EXPRESS_DATE,
      </if>
      <if test="goodsName != null" >
        GOODS_NAME,
      </if>
      <if test="goodsPrice != null" >
        GOODS_PRICE,
      </if>
      <if test="goodsType != null" >
        GOODS_TYPE,
      </if>
      <if test="packageNums != null" >
        PACKAGE_NUMS,
      </if>
      <if test="state != null" >
        state,
      </if>
      <if test="addressId != null" >
        ADDRESS_ID,
      </if>
      <if test="payMethond != null" >
        PAY_METHOND,
      </if>
      <if test="monthlyCard != null" >
        MONTHLY_CARD,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
      <if test="sendCompany != null" >
        SEND_COMPANY,
      </if>
      <if test="isCs != null" >
        IS_CS,
      </if>
      <if test="memo != null">
        MEMO,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="expressId != null" >
        #{expressId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="deliverType != null" >
        #{deliverType,jdbcType=INTEGER},
      </if>
      <if test="expressType != null" >
        #{expressType,jdbcType=VARCHAR},
      </if>
      <if test="expressNo != null" >
        #{expressNo,jdbcType=VARCHAR},
      </if>
      <if test="sendPerson != null" >
        #{sendPerson,jdbcType=VARCHAR},
      </if>
      <if test="sendPhone != null" >
        #{sendPhone,jdbcType=VARCHAR},
      </if>
      <if test="sendProvice != null" >
        #{sendProvice,jdbcType=VARCHAR},
      </if>
      <if test="sendCity != null" >
        #{sendCity,jdbcType=VARCHAR},
      </if>
      <if test="sendTown != null" >
        #{sendTown,jdbcType=VARCHAR},
      </if>
      <if test="sendAddr != null" >
        #{sendAddr,jdbcType=VARCHAR},
      </if>
      <if test="receiptPerson != null" >
        #{receiptPerson,jdbcType=VARCHAR},
      </if>
      <if test="receiptCompany != null" >
        #{receiptCompany,jdbcType=VARCHAR},
      </if>
      <if test="receiptPhone != null" >
        #{receiptPhone,jdbcType=VARCHAR},
      </if>
      <if test="receiptProvice != null" >
        #{receiptProvice,jdbcType=VARCHAR},
      </if>
      <if test="receiptCity != null" >
        #{receiptCity,jdbcType=VARCHAR},
      </if>
      <if test="receiptTown != null" >
        #{receiptTown,jdbcType=VARCHAR},
      </if>
      <if test="receiptAddr != null" >
        #{receiptAddr,jdbcType=VARCHAR},
      </if>
      <if test="labId != null" >
        #{labId,jdbcType=BIGINT},
      </if>
      <if test="labName != null" >
        #{labName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        #{stateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="expressCode != null" >
        #{expressCode,jdbcType=VARCHAR},
      </if>
      <if test="preExpressDate != null" >
        #{preExpressDate,jdbcType=TIMESTAMP},
      </if>
      <if test="goodsName != null" >
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="goodsPrice != null" >
        #{goodsPrice,jdbcType=DECIMAL},
      </if>
      <if test="goodsType != null" >
        #{goodsType,jdbcType=INTEGER},
      </if>
      <if test="packageNums != null" >
        #{packageNums,jdbcType=INTEGER},
      </if>
      <if test="state != null" >
        #{state,jdbcType=INTEGER},
      </if>
      <if test="addressId != null" >
        #{addressId},
      </if>
      <if test="payMethond != null" >
        #{payMethond},
      </if>
      <if test="monthlyCard != null" >
        #{monthlyCard},
      </if>
      <if test="userId != null" >
        #{userId},
      </if>
      <if test="sendCompany != null" >
        #{sendCompany},
      </if>
      <if test="isCs != null" >
        #{isCs},
      </if>
      <if test="memo != null">
        #{MEMO},
      </if>
    </trim>
  </insert>
  <insert id="insertBatch">
    insert into ORDER_EXPRESS(EXPRESS_ID, ORDER_NO, DELIVER_TYPE, EXPRESS_TYPE, EXPRESS_NO, SEND_PERSON, SEND_PHONE,
    SEND_PROVICE, SEND_CITY, SEND_TOWN, SEND_ADDR, RECEIPT_PERSON, RECEIPT_PHONE,
     RECEIPT_PROVICE,RECEIPT_CITY, RECEIPT_TOWN, RECEIPT_ADDR, LAB_ID, LAB_NAME,
     CREATE_DATE, STATE_DATE,EXPRESS_CODE, PRE_EXPRESS_DATE, GOODS_NAME, GOODS_PRICE,
     PACKAGE_NUMS,ADDRESS_ID,GOODS_TYPE,STATE,PAY_METHOND,MONTHLY_CARD,
     USER_ID,RECEIPT_COMPANY,SEND_COMPANY,IS_CS ,MEMO) values

    <foreach collection="list" item="item" index="index" separator=",">
      ( #{item.expressId},#{item.orderNo}, #{item.deliverType}, #{item.expressType}, #{item.expressNo},#{item.sendPerson}, #{item.sendPhone},
      #{item.sendProvice}, #{item.sendCity}, #{item.sendTown}, #{item.sendAddr},#{item.receiptPerson}, #{item.receiptPhone},
      #{item.receiptProvice}, #{item.receiptCity}, #{item.receiptTown}, #{item.receiptAddr},#{item.labId}, #{item.labName},
      #{item.createDate}, #{item.stateDate}, #{item.expressCode}, #{item.preExpressDate},#{item.goodsName}, #{item.goodsPrice},
      #{item.packageNums}, #{item.addressId}, #{item.goodsType}, #{item.state}, #{item.payMethond},#{item.monthlyCard}
      , #{item.userId}, #{item.receiptCompany}, #{item.sendCompany}, #{item.isCs}, #{item.memo})
    </foreach>
  </insert>
  <update id="updateVOByPrimaryKeySelective" parameterType="com.sgs.ecom.member.vo.VOOrderExpress" >
    update ORDER_EXPRESS
    <set >
      <if test="orderNo != null" >
        ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="deliverType != null" >
        DELIVER_TYPE = #{deliverType,jdbcType=INTEGER},
      </if>
      <if test="expressType != null" >
        EXPRESS_TYPE = #{expressType,jdbcType=VARCHAR},
      </if>
      <if test="expressNo != null" >
        EXPRESS_NO = #{expressNo,jdbcType=VARCHAR},
      </if>
      <if test="sendPerson != null" >
        SEND_PERSON = #{sendPerson,jdbcType=VARCHAR},
      </if>
      <if test="sendPhone != null" >
        SEND_PHONE = #{sendPhone,jdbcType=VARCHAR},
      </if>
      <if test="sendProvice != null" >
        SEND_PROVICE = #{sendProvice,jdbcType=VARCHAR},
      </if>
      <if test="sendCity != null" >
        SEND_CITY = #{sendCity,jdbcType=VARCHAR},
      </if>
      <if test="sendTown != null" >
        SEND_TOWN = #{sendTown,jdbcType=VARCHAR},
      </if>
      <if test="sendAddr != null" >
        SEND_ADDR = #{sendAddr,jdbcType=VARCHAR},
      </if>
      <if test="receiptPerson != null" >
        RECEIPT_PERSON = #{receiptPerson,jdbcType=VARCHAR},
      </if>
      <if test="receiptCompany != null" >
        RECEIPT_COMPANY = #{receiptCompany},
      </if>
      <if test="receiptPhone != null" >
        RECEIPT_PHONE = #{receiptPhone,jdbcType=VARCHAR},
      </if>
      <if test="receiptProvice != null" >
        RECEIPT_PROVICE = #{receiptProvice,jdbcType=VARCHAR},
      </if>
      <if test="receiptCity != null" >
        RECEIPT_CITY = #{receiptCity,jdbcType=VARCHAR},
      </if>
      <if test="receiptTown != null" >
        RECEIPT_TOWN = #{receiptTown,jdbcType=VARCHAR},
      </if>
      <if test="receiptAddr != null" >
        RECEIPT_ADDR = #{receiptAddr,jdbcType=VARCHAR},
      </if>
      <if test="labId != null" >
        LAB_ID = #{labId,jdbcType=BIGINT},
      </if>
      <if test="labName != null" >
        LAB_NAME = #{labName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        STATE_DATE = #{stateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="expressCode != null" >
        EXPRESS_CODE = #{expressCode,jdbcType=VARCHAR},
      </if>
      <if test="preExpressDate != null" >
        PRE_EXPRESS_DATE = #{preExpressDate,jdbcType=TIMESTAMP},
      </if>
      <if test="goodsName != null" >
        GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="goodsPrice != null" >
        GOODS_PRICE = #{goodsPrice,jdbcType=DECIMAL},
      </if>
      <if test="goodsType != null" >
        GOODS_TYPE = #{goodsType,jdbcType=INTEGER},
      </if>
      <if test="packageNums != null" >
        PACKAGE_NUMS = #{packageNums,jdbcType=INTEGER},
      </if>
      <if test="state != null" >
        STATE = #{state,jdbcType=INTEGER},
      </if>
      <if test="addressId != null" >
        ADDRESS_ID = #{addressId},
      </if>
      <if test="payMethond != null" >
        PAY_METHOND = #{payMethond},
      </if>
      <if test="monthlyCard != null" >
        MONTHLY_CARD = #{monthlyCard},
      </if>
      <if test="sendCompany != null" >
        SEND_COMPANY=#{sendCompany},
      </if>
      <if test="isCs != null" >
        IS_CS=#{isCs},
      </if>
      <if test="memo != null">
        MEMO = #{memo},
      </if>
    </set>
    where EXPRESS_ID = #{expressId,jdbcType=BIGINT}
  </update>
  <update id="updateOrderExpress">
    update ORDER_EXPRESS SET STATE  = 0 WHERE ORDER_NO = #{orderNo} AND DELIVER_TYPE IN (10,11) AND IS_CS=#{isCs};
  </update>


  <!--自定义 -->
  <resultMap id="resultDTO" type="com.sgs.ecom.member.dto.detail.OrderExpressDTO" >
    <id column="EXPRESS_ID" property="expressId" jdbcType="BIGINT" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="DELIVER_TYPE" property="deliverType" jdbcType="INTEGER" />
    <result column="EXPRESS_TYPE" property="expressType" jdbcType="VARCHAR" />
    <result column="EXPRESS_NO" property="expressNo" jdbcType="VARCHAR" />
    <result column="SEND_PERSON" property="sendPerson" jdbcType="VARCHAR" />
    <result column="SEND_PHONE" property="sendPhone" jdbcType="VARCHAR" />
    <result column="SEND_PROVICE" property="sendProvice" jdbcType="VARCHAR" />
    <result column="SEND_CITY" property="sendCity" jdbcType="VARCHAR" />
    <result column="SEND_TOWN" property="sendTown" jdbcType="VARCHAR" />
    <result column="SEND_ADDR" property="sendAddr" jdbcType="VARCHAR" />
    <result column="RECEIPT_PERSON" property="receiptPerson" jdbcType="VARCHAR" />
    <result column="RECEIPT_PHONE" property="receiptPhone" jdbcType="VARCHAR" />
    <result column="RECEIPT_PROVICE" property="receiptProvice" jdbcType="VARCHAR" />
    <result column="RECEIPT_CITY" property="receiptCity" jdbcType="VARCHAR" />
    <result column="RECEIPT_TOWN" property="receiptTown" jdbcType="VARCHAR" />
    <result column="RECEIPT_ADDR" property="receiptAddr" jdbcType="VARCHAR" />
    <result column="LAB_ID" property="labId" jdbcType="BIGINT" />
    <result column="LAB_NAME" property="labName" jdbcType="VARCHAR" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
    <result column="EXPRESS_CODE" property="expressCode" jdbcType="VARCHAR" />
    <result column="PRE_EXPRESS_DATE" property="preExpressDate" jdbcType="TIMESTAMP" />
    <result column="GOODS_NAME" property="goodsName" jdbcType="VARCHAR" />
    <result column="GOODS_PRICE" property="goodsPrice" jdbcType="DECIMAL" />
    <result column="GOODS_TYPE" property="goodsType" jdbcType="INTEGER" />
    <result column="PACKAGE_NUMS" property="packageNums" jdbcType="INTEGER" />
    <result column="STATE" property="state" jdbcType="INTEGER" />
    <result column="ADDRESS_ID" property="addressId" jdbcType="BIGINT" />
    <result column="PAY_METHOND" property="payMethond" jdbcType="INTEGER" />
    <result column="MONTHLY_CARD" property="monthlyCard" jdbcType="VARCHAR" />
    <result column="RECEIPT_COMPANY" property="receiptCompany" jdbcType="VARCHAR" />
    <result column="SEND_COMPANY" property="sendCompany" jdbcType="VARCHAR" />
    <result column="IS_CS" property="isCs" jdbcType="INTEGER" />
    <result column="MEMO" property="memo" jdbcType="VARCHAR"/>
    <result column="RECEIPT_EMAIL" property="receiptEmail" jdbcType="VARCHAR"/>
  </resultMap>

  <sql id="sqlListDTO" >
    EXPRESS_ID, ORDER_NO, DELIVER_TYPE, EXPRESS_TYPE, EXPRESS_NO, SEND_PERSON, SEND_PHONE,
    SEND_PROVICE, SEND_CITY, SEND_TOWN, SEND_ADDR, SEND_COMPANY, RECEIPT_PERSON, RECEIPT_PHONE, RECEIPT_PROVICE,RECEIPT_EMAIL,
    RECEIPT_CITY, RECEIPT_TOWN, RECEIPT_ADDR, LAB_ID, LAB_NAME, CREATE_DATE, STATE_DATE,
    EXPRESS_CODE, PRE_EXPRESS_DATE, GOODS_NAME, GOODS_PRICE, PACKAGE_NUMS,STATE,ADDRESS_ID,PAY_METHOND,MONTHLY_CARD,RECEIPT_COMPANY,GOODS_TYPE,IS_CS,MEMO
  </sql>

  <select id="selectListByMap" resultMap="resultDTO" >
    select
    <include refid="sqlListDTO" />
    from ORDER_EXPRESS
    <include refid="baseQueryWhere"/>
  </select>
  <select id="selectDTOByMap" resultMap="resultDTO" >
    select
    <include refid="sqlListDTO" />
    from ORDER_EXPRESS
    <include refid="baseQueryWhere"/>
    order by EXPRESS_ID desc limit 1
  </select>


  <sql id="baseQueryWhere">
    where 1=1
    <if test="orderNo != null">
      AND ORDER_NO=#{orderNo}
    </if>
    <if test="userId != null">
      AND USER_ID=#{userId}
    </if>
    <if test="deliverType != null">
      AND DELIVER_TYPE=#{deliverType}
    </if>
    <if test="state != null">
      AND STATE=#{state}
    </if>
    <if test="isCs != null">
      AND IS_CS=#{isCs}
    </if>
    <if test="deliverTypeList != null ">
      AND DELIVER_TYPE in
      <foreach collection="deliverTypeList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </sql>


  <resultMap id="resultOldExpress" type="com.sgs.ecom.member.dto.detail.OrderExpessOldDTO" >
    <result column="ADDRESS_ID" property="addressId" jdbcType="BIGINT" />
    <result column="DELIVER_TYPE" property="deliverType" jdbcType="INTEGER" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
  </resultMap>

  <select id="selectOldExpress" resultMap="resultOldExpress" >
    select B.EXPRESS_ID,B.ADDRESS_ID,B.DELIVER_TYPE,B.CREATE_DATE from(
    select EXPRESS_ID,ADDRESS_ID,DELIVER_TYPE,CREATE_DATE from ORDER_EXPRESS
    <include refid="baseQueryWhere"/>
    order by CREATE_DATE desc
    ) B group by B.DELIVER_TYPE
  </select>


  <select id="selectOldExpressByMap" resultMap="resultOldExpress" >
    select B.EXPRESS_ID,B.ADDRESS_ID,B.DELIVER_TYPE,B.CREATE_DATE from ORDER_EXPRESS B
    where (DELIVER_TYPE,EXPRESS_ID) in
    (
    select oe.DELIVER_TYPE,max(oe.EXPRESS_ID) EXPRESS_ID
    from ORDER_EXPRESS oe,ORDER_BASE_INFO obi
    where oe.ORDER_NO=obi.ORDER_NO
    <if test="userId != null">
      AND obi.USER_ID=#{userId}
    </if>
    <if test="deliverType != null">
      AND oe.DELIVER_TYPE=#{deliverType}
    </if>
    <if test="orderType != null">
      AND  obi.ORDER_TYPE=#{orderType}
    </if>
    group by oe.DELIVER_TYPE
    )
  </select>
  <select id="selectCountByMap" resultType="java.lang.Long">
    select
    count(*)
    from ORDER_EXPRESS
    <include refid="baseQueryWhere"/>

  </select>
    <select id="qryOrderExpress" resultMap="BaseResult">
      select
      <include refid="sqlListDTO" />
      from ORDER_EXPRESS
      <include refid="baseQueryWhere"/>
    </select>


    <delete id="delExpressByMap">
    delete from ORDER_EXPRESS where ORDER_NO=#{orderNo} and DELIVER_TYPE  in (30,31)
  </delete>
  <delete id="delListByMap">
    DELETE FROM ORDER_EXPRESS
    <include refid="baseQueryWhere"/>
  </delete>


  <insert id="copyByOrderNo" parameterType="com.sgs.ecom.member.dto.order.CopyByOrder">
    insert into ORDER_EXPRESS (
      ORDER_NO,DELIVER_TYPE,EXPRESS_TYPE,EXPRESS_NO,SEND_PERSON,SEND_PHONE,SEND_PROVICE,SEND_CITY,SEND_TOWN,
      SEND_ADDR,RECEIPT_PERSON,RECEIPT_PHONE,RECEIPT_PROVICE,RECEIPT_CITY,RECEIPT_TOWN,RECEIPT_ADDR,LAB_ID,LAB_NAME,
      CREATE_DATE,STATE_DATE,EXPRESS_CODE,PRE_EXPRESS_DATE,GOODS_TYPE,GOODS_NAME,GOODS_PRICE,PACKAGE_NUMS,ADDRESS_ID,
      PAY_METHOND,MONTHLY_CARD,USER_ID,STATE,RECEIPT_COMPANY,SEND_COMPANY,IS_CS,MEMO,RECEIPT_EMAIL
    )select
      #{toOrderNo},DELIVER_TYPE,EXPRESS_TYPE,EXPRESS_NO,SEND_PERSON,SEND_PHONE,SEND_PROVICE,SEND_CITY,SEND_TOWN,
      SEND_ADDR,RECEIPT_PERSON,RECEIPT_PHONE,RECEIPT_PROVICE,RECEIPT_CITY,RECEIPT_TOWN,RECEIPT_ADDR,LAB_ID,LAB_NAME,
      now(),now(),EXPRESS_CODE,PRE_EXPRESS_DATE,GOODS_TYPE,GOODS_NAME,GOODS_PRICE,PACKAGE_NUMS,ADDRESS_ID,
      PAY_METHOND,MONTHLY_CARD,USER_ID,1,RECEIPT_COMPANY,SEND_COMPANY,IS_CS,MEMO,RECEIPT_EMAIL
    from ORDER_EXPRESS where ORDER_NO= #{useOrderNo} and state=1 and DELIVER_TYPE  in (30,31)
  </insert>

  <update id="deleteByDeliverType" parameterType="com.sgs.ecom.member.vo.base.VOBase">
    update ORDER_EXPRESS set state=0 where ORDER_NO=#{orderNo} and DELIVER_TYPE=#{deliverType}
  </update>
</mapper>