package com.sgs.ecom.member.dto.rsts;

import com.platform.annotation.ApiAnno;
import com.platform.util.ValidationUtil;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.enums.rsts.RstsCenterOrderStateEnum;

/**
 * <AUTHOR>
 * @Description :
 * @date 2023/11/22
 */
public class RstsPersonCenterDTO extends BaseOrderFilter {

    @ApiAnno(groups={OrderStateNumList.class})
    private int state;
    @ApiAnno(groups={OrderStateNumList.class})
    private int stateCount;

    @ApiAnno(groups={OrderStateNumList.class})
    private String stateName;


    private String orderNoStr;

    private int sortShow;

    public String getOrderNoStr() {
        return orderNoStr;
    }

    public void setOrderNoStr(String orderNoStr) {
        this.orderNoStr = orderNoStr;
    }

    public int getSortShow() {
        return sortShow;
    }

    public void setSortShow(int sortShow) {
        this.sortShow = sortShow;
    }

    public String getStateName() {
        if(ValidationUtil.isEmpty(state))
            return "";

        return RstsCenterOrderStateEnum.getName(state);
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public int getStateCount() {
        return stateCount;
    }

    public void setStateCount(int stateCount) {
        this.stateCount = stateCount;
    }
}
