package com.sgs.ecom.member.entity.user;


public class UserSampleFrom {

    public static  final String SAMPLE_NAME="SAMPLE_NAME";
    public static  final String PRODUCT_INFO="PRODUCT_INFO";
    public static  final String PRODUCT_BATCH="PRODUCT_BATCH";
    public static  final String MATERIAL_GRADE="MATERIAL_GRADE";
    public static  final String REMARK="REMARK";


    private Long formId;

    private Long userId;

    private String sampleNo;

    private String sampleKeyName;

    private String sampleKey;

    private String sampleValue;

    private int state;

    private String createDate;

    private String stateDate;

    private int sortShow;

    private String areaCode;
    private String sampleValueRemark;
    private String enumConfig;

    private String sampleExplain;
    private String sampleExplainEn;
    private String remark;

    private int type;

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getSampleExplain() {
        return sampleExplain;
    }

    public void setSampleExplain(String sampleExplain) {
        this.sampleExplain = sampleExplain;
    }

    public String getSampleExplainEn() {
        return sampleExplainEn;
    }

    public void setSampleExplainEn(String sampleExplainEn) {
        this.sampleExplainEn = sampleExplainEn;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getEnumConfig() {
        return enumConfig;
    }

    public void setEnumConfig(String enumConfig) {
        this.enumConfig = enumConfig;
    }

    public String getSampleValueRemark() {
        return sampleValueRemark;
    }

    public void setSampleValueRemark(String sampleValueRemark) {
        this.sampleValueRemark = sampleValueRemark;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public Long getFormId() {
        return formId;
    }

    public void setFormId(Long formId) {
        this.formId = formId;
    }


    public String getSampleNo() {
        return sampleNo;
    }

    public void setSampleNo(String sampleNo) {
        this.sampleNo = sampleNo == null ? null : sampleNo.trim();
    }


    public String getSampleKeyName() {
        return sampleKeyName;
    }

    public void setSampleKeyName(String sampleKeyName) {
        this.sampleKeyName = sampleKeyName == null ? null : sampleKeyName.trim();
    }

    public String getSampleKey() {
        return sampleKey;
    }

    public void setSampleKey(String sampleKey) {
        this.sampleKey = sampleKey == null ? null : sampleKey.trim();
    }

    public String getSampleValue() {
        return sampleValue;
    }

    public void setSampleValue(String sampleValue) {
        this.sampleValue = sampleValue == null ? null : sampleValue.trim();
    }

    public void setState(int state) {
        this.state = state;
    }


    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getStateDate() {
        return stateDate;
    }

    public void setStateDate(String stateDate) {
        this.stateDate = stateDate;
    }

    public int getState() {
        return state;
    }

    public int getSortShow() {
        return sortShow;
    }

    public void setSortShow(int sortShow) {
        this.sortShow = sortShow;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
}