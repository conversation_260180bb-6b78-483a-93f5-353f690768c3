package com.sgs.ecom.member.dto.order;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.request.base.BaseFileReq;

import java.util.ArrayList;
import java.util.List;

public class OrderSampleFromDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select CREATE_DATE,GROUP_NO,STATE_DATE,SAMPLE_NO,STATE,ORDER_NO,SAMPLE_KEY_NAME,FORM_ID,SAMPLE_VALUE,SAMPLE_KEY from ORDER_SAMPLE_FROM";

 

 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="GROUP_NO", getName="getGroupNo", setName="setGroupNo")
 	private String groupNo;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="SAMPLE_NO", getName="getSampleNo", setName="setSampleNo")
 	private String sampleNo;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="ORDER_NO", getName="getOrderNo", setName="setOrderNo")
 	private String orderNo;
 	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class,BaseOrderFilter.OrderToOther.class})
 	@BeanAnno(value="SAMPLE_KEY_NAME", getName="getSampleKeyName", setName="setSampleKeyName")
 	private String sampleKeyName;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="FORM_ID", getName="getFormId", setName="setFormId")
 	private long formId;
 	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class,BaseOrderFilter.OrderToOther.class})
 	@BeanAnno(value="SAMPLE_VALUE", getName="getSampleValue", setName="setSampleValue")
 	private String sampleValue;
 	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class,BaseOrderFilter.OrderToOther.class})
 	@BeanAnno(value="SAMPLE_KEY", getName="getSampleKey", setName="setSampleKey")
 	private String sampleKey;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class,BaseOrderFilter.OrderToOther.class})
	private int sortShow;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private int isMust;//是否必输

	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private int type;//字段定义页面类型

	@ApiAnno(groups={Default.class})
	private Integer isReportShow;
	@ApiAnno(groups={Default.class})
	private String  sampleExplain;

	@ApiAnno(groups={Default.class})
	private String  areaCode;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class,BaseOrderFilter.OrderToOther.class})
	private String  sampleValueRemark;

	//附件出来
	@ApiAnno(groups={Default.class})
	private List<BaseFileReq> fileReqList=new ArrayList<>();

	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private String  remark;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private String enumConfig;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private String remarkEnumConfig;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private String sampleGroup;

	@ApiAnno(groups={Default.class})
	private String sampleExplainEn;

	@ApiAnno(groups={Default.class})
	private String areaName;

	@ApiAnno(groups={Default.class})
	private String areaNameEn;

	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private String sampleKeyNameEn;
	@ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
	private int isHide;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class,BaseOrderFilter.OrderToOther.class})
	private String lua;

	private int tableFlg;

	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private int rowNum;
	@ApiAnno(groups={Default.class})
	private String dataType;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private String fillNotice;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private String fillNoticeEn;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private int fillLen;
	@ApiAnno(groups={Default.class})
	private int isTitle;
	@ApiAnno(groups={Default.class})
	private String showLua;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private String regexRule;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	@JsonProperty(access = JsonProperty.Access.READ_ONLY)
	private String attrRules;
	@ApiAnno(groups={Default.class})
	private String tipsCn;
	@ApiAnno(groups={Default.class})
	private String tipsEn;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	@JsonProperty(access = JsonProperty.Access.READ_ONLY)
	private String showAttr;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	@JsonProperty(access = JsonProperty.Access.READ_ONLY)
	private String enums;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	@JsonProperty(access = JsonProperty.Access.READ_ONLY)
	private String remarkEnums;


	public String getEnums() {
		return enums;
	}

	public void setEnums(String enums) {
		this.enums = enums;
	}

	public int getRowNum() {
		return rowNum;
	}

	public void setRowNum(int rowNum) {
		this.rowNum = rowNum;
	}

	public String getDataType() {
		return dataType;
	}

	public void setDataType(String dataType) {
		this.dataType = dataType;
	}

	public String getFillNotice() {
		return fillNotice;
	}

	public void setFillNotice(String fillNotice) {
		this.fillNotice = fillNotice;
	}

	public String getFillNoticeEn() {
		return fillNoticeEn;
	}

	public void setFillNoticeEn(String fillNoticeEn) {
		this.fillNoticeEn = fillNoticeEn;
	}

	public int getFillLen() {
		return fillLen;
	}

	public void setFillLen(int fillLen) {
		this.fillLen = fillLen;
	}

	public int getIsTitle() {
		return isTitle;
	}

	public void setIsTitle(int isTitle) {
		this.isTitle = isTitle;
	}

	public String getShowLua() {
		return showLua;
	}

	public void setShowLua(String showLua) {
		this.showLua = showLua;
	}

	public String getRegexRule() {
		return regexRule;
	}

	public void setRegexRule(String regexRule) {
		this.regexRule = regexRule;
	}

	public String getAttrRules() {
		return attrRules;
	}

	public void setAttrRules(String attrRules) {
		this.attrRules = attrRules;
	}

	public String getTipsCn() {
		return tipsCn;
	}

	public void setTipsCn(String tipsCn) {
		this.tipsCn = tipsCn;
	}

	public String getTipsEn() {
		return tipsEn;
	}

	public void setTipsEn(String tipsEn) {
		this.tipsEn = tipsEn;
	}

	public String getShowAttr() {
		return showAttr;
	}

	public void setShowAttr(String showAttr) {
		this.showAttr = showAttr;
	}



	public String getLua() {
		if(lua==null){
			return "";
		}
		return lua;
	}

	public void setLua(String lua) {
		this.lua = lua;
	}

	public int getIsHide() {
		return isHide;
	}

	public void setIsHide(int isHide) {
		this.isHide = isHide;
	}

	public String getAreaName() {
		return areaName;
	}

	public void setAreaName(String areaName) {
		this.areaName = areaName;
	}

	public String getAreaNameEn() {
		return areaNameEn;
	}

	public void setAreaNameEn(String areaNameEn) {
		this.areaNameEn = areaNameEn;
	}

	public String getSampleKeyNameEn() {
		return sampleKeyNameEn;
	}

	public void setSampleKeyNameEn(String sampleKeyNameEn) {
		this.sampleKeyNameEn = sampleKeyNameEn;
	}

	public String getSampleExplainEn() {
		return sampleExplainEn;
	}

	public void setSampleExplainEn(String sampleExplainEn) {
		this.sampleExplainEn = sampleExplainEn;
	}

	public String getEnumConfig() {
		return enumConfig;
	}

	public void setEnumConfig(String enumConfig) {
		this.enumConfig = enumConfig;
	}


	public String getSampleValueRemark() {
		return sampleValueRemark;
	}

	public void setSampleValueRemark(String sampleValueRemark) {
		this.sampleValueRemark = sampleValueRemark;
	}

	public String getAreaCode() {
		return areaCode;
	}

	public void setAreaCode(String areaCode) {
		this.areaCode = areaCode;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	private String code;//用于做搜索条件使用的

	public Integer getIsReportShow() {
		return isReportShow;
	}

	public void setIsReportShow(Integer isReportShow) {
		this.isReportShow = isReportShow;
	}

	public OrderSampleFromDTO() {
	}

	public OrderSampleFromDTO(String sampleKeyName, String sampleValue, int sortShow) {
		this.sampleKeyName = sampleKeyName;
		this.sampleValue = sampleValue;
		this.sortShow = sortShow;
	}

	public int getIsMust() {
		return isMust;
	}

	public void setIsMust(int isMust) {
		this.isMust = isMust;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	public void setGroupNo(String groupNo){
 		 this.groupNo=groupNo;
 	}
 	public String getGroupNo(){
 		 return this.groupNo;
 	}

 
 	 
 	public void setSampleNo(String sampleNo){
 		 this.sampleNo=sampleNo;
 	}
 	public String getSampleNo(){
 		 return this.sampleNo;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setOrderNo(String orderNo){
 		 this.orderNo=orderNo;
 	}
 	public String getOrderNo(){
 		 return this.orderNo;
 	}
 
 	 
 	public void setSampleKeyName(String sampleKeyName){
 		 this.sampleKeyName=sampleKeyName;
 	}
 	public String getSampleKeyName(){
 		 return this.sampleKeyName;
 	}
 
 	 
 	public void setFormId(long formId){
 		 this.formId=formId;
 	}
 	public long getFormId(){
 		 return this.formId;
 	}
 
 	 
 	public void setSampleValue(String sampleValue){
 		 this.sampleValue=sampleValue;
 	}
 	public String getSampleValue(){
		if(sampleValue==null){
			return "";
		}
		return this.sampleValue;
 	}
 
 	 
 	public void setSampleKey(String sampleKey){
 		 this.sampleKey=sampleKey;
 	}
 	public String getSampleKey(){
 		 return this.sampleKey;
 	}

	public int getSortShow() {
		return sortShow;
	}

	public void setSortShow(int sortShow) {
		this.sortShow = sortShow;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getSampleExplain() {
		return sampleExplain;
	}

	public void setSampleExplain(String sampleExplain) {
		this.sampleExplain = sampleExplain;
	}

	public List<BaseFileReq> getFileReqList() {
		return fileReqList;
	}

	public void setFileReqList(List<BaseFileReq> fileReqList) {
		this.fileReqList = fileReqList;
	}

	public String getSampleGroup() {
		return sampleGroup;
	}

	public void setSampleGroup(String sampleGroup) {
		this.sampleGroup = sampleGroup;
	}

	public int getTableFlg() {
		return tableFlg;
	}

	public void setTableFlg(int tableFlg) {
		this.tableFlg = tableFlg;
	}

	public String getRemarkEnumConfig() {
		return remarkEnumConfig;
	}

	public void setRemarkEnumConfig(String remarkEnumConfig) {
		this.remarkEnumConfig = remarkEnumConfig;
	}

	public String getRemarkEnums() {
		return remarkEnums;
	}

	public void setRemarkEnums(String remarkEnums) {
		this.remarkEnums = remarkEnums;
	}


}