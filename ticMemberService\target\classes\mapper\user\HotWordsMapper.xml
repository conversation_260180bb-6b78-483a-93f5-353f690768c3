<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.user.dao.HotWordsMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.entity.HotWords" >
    <id column="ID" property="id" jdbcType="BIGINT" />
    <result column="WORDS_TYPE" property="wordsType" jdbcType="VARCHAR" />
    <result column="SEARCH_WORDS" property="searchWords" jdbcType="VARCHAR" />
    <result column="SEARCH_NUMS" property="searchNums" jdbcType="INTEGER" />
    <result column="IS_HOT" property="isHot" jdbcType="INTEGER" />
    <result column="SHOW_SORT" property="showSort" jdbcType="INTEGER" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, WORDS_TYPE, SEARCH_WORDS, SEARCH_NUMS, IS_HOT, SHOW_SORT, CREATE_DATE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from HOT_WORDS
    where ID = #{id,jdbcType=BIGINT}
  </select>
  <select id="qryByWords" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from HOT_WORDS
    where SEARCH_WORDS = #{searchWords,jdbcType=VARCHAR}
    limit 1
  </select>
  <insert id="insertSelective" parameterType="com.sgs.ecom.member.entity.HotWords" >
    insert into HOT_WORDS
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="wordsType != null" >
        WORDS_TYPE,
      </if>
      <if test="searchWords != null" >
        SEARCH_WORDS,
      </if>
      <if test="searchNums != null" >
        SEARCH_NUMS,
      </if>
      <if test="isHot != null" >
        IS_HOT,
      </if>
      <if test="showSort != null" >
        SHOW_SORT,
      </if>
      <if test="createDate != null" >
        CREATE_DATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="wordsType != null" >
        #{wordsType,jdbcType=VARCHAR},
      </if>
      <if test="searchWords != null" >
        #{searchWords,jdbcType=VARCHAR},
      </if>
      <if test="searchNums != null" >
        #{searchNums,jdbcType=INTEGER},
      </if>
      <if test="isHot != null" >
        #{isHot,jdbcType=INTEGER},
      </if>
      <if test="showSort != null" >
        #{showSort,jdbcType=INTEGER},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="addClickNums" parameterType="com.sgs.ecom.member.entity.HotWords" >
    update HOT_WORDS
    set SEARCH_NUMS = SEARCH_NUMS + 1
    where ID = #{id,jdbcType=BIGINT}
  </update>
</mapper>