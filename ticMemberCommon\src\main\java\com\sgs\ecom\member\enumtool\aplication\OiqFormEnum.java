package com.sgs.ecom.member.enumtool.aplication;

import java.util.ArrayList;
import java.util.List;

public enum OiqFormEnum {

	IS_DISPUTE("isDispute", "是否法律纠纷","baseForm"),
	DEFAULT_EMAIL_FLG("defaultEmailFlg", "是否取默认客服邮箱","baseForm"),
	STANDARD_MODEL("standardModel", "项目里面的测试标准","baseForm"),
	SALES_EMAIL_FLG("salesEmailFlg", "销售顾问的是否默认邮箱","baseForm"),
	REPORT_TITLE_TYPE("reportTitleType", "报告抬头类型","baseForm"),
	IS_DISPUTE_MEMO("isDisputeMemo", "是否法律纠纷备注","baseForm"),
	TEST_MEMO_IMG("testMemoImg", "测试备注图片","baseForm"),
	LAB_CODE("labCode", "实验室编码","baseForm"),
	SAMPLE_BUSINESS_CODE("sampleBusinessCode", "样品业务线code","baseForm"),
	QUALIFICATIONS("qualifications", "是否加盖资质章","baseForm"),
	DESTINATION_COUNTRY("destinationCountry", "目的国","baseForm"),
	INVOICE_FLG("invoiceFlg", "发票暂不清楚","baseForm"),
	;

	OiqFormEnum(String name, String nameCh, String formCode) {
		this.name = name;
		this.nameCh = nameCh;
		this.formCode=formCode;
	}

	public static String getNameCh(String name) {
		for (OiqFormEnum c : OiqFormEnum.values()) {
			if (c.getName().equals(name)) {
				return c.getNameCh();
			}
		}
		return "";
	}

	public static String getFormCode(String name) {
		for (OiqFormEnum c : OiqFormEnum.values()) {
			if (c.getName().equals(name)) {
				return c.getFormCode();
			}
		}
		return null;
	}

	private String name;
	private String nameCh;
	private String formCode;



	public static List<String> getIndStringList(){
		List<String> list=new ArrayList<>();
		list.add(IS_DISPUTE.getName());
		list.add(DEFAULT_EMAIL_FLG.getName());
		list.add(STANDARD_MODEL.getName());
		list.add(SALES_EMAIL_FLG.getName());
		list.add(REPORT_TITLE_TYPE.getName());
		list.add(IS_DISPUTE_MEMO.getName());
		list.add(TEST_MEMO_IMG.getName());
		list.add(LAB_CODE.getName());
		list.add(SAMPLE_BUSINESS_CODE.getName());
		list.add(QUALIFICATIONS.getName());
		list.add(INVOICE_FLG.getName());
		return list;
	}


	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}


	public String getNameCh() {
		return nameCh;
	}

	public void setNameCh(String nameCh) {
		this.nameCh = nameCh;
	}

	public String getFormCode() {
		return formCode;
	}

	public void setFormCode(String formCode) {
		this.formCode = formCode;
	}
}
