package com.sgs.ecom.member.controller.order;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.ecom.member.request.OrderNoReq;
import com.sgs.ecom.member.service.util.interfaces.IOrderApplicationService;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.order/express")
public class OrderExpressController extends ControllerUtil {

    @Autowired
    private IOrderApplicationService orderApplicationService;

    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "list", method = { RequestMethod.POST })
    public ResultBody list(
            @RequestHeader(value="accessToken") String accessToken,
            @RequestBody OrderNoReq orderNoReq) throws Exception {
        return ResultBody.newInstance(orderApplicationService.selectExpressList(orderNoReq.getOrderNo(),getUserDTO(accessToken)));
    }
}
