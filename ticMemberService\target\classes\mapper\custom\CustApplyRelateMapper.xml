<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.mapper.custom.CustApplyRelateMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.bo.CustApplyRelate" >
    <id column="RELATE_ID" property="relateId" jdbcType="BIGINT" />
    <result column="CUST_ID" property="custId" jdbcType="BIGINT" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="BU" property="bu" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="VARCHAR" />
  </resultMap>
  <resultMap id="QryResultMap" type="com.sgs.ecom.member.dto.CustInfoDTO" >
    <result column="CUST_ID" property="custId" jdbcType="BIGINT" />
    <result column="CUST_CODE" property="custCode" jdbcType="VARCHAR" />
    <result column="CURRENCY" property="currency" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    RELATE_ID, CUST_ID, USER_ID, CREATE_DATE, BU
  </sql>
  <select id="qryUserCustByBu" resultMap="BaseResultMap" >
    select ci.CUST_ID,ci.CUST_CODE,car.BU,ci.CURRENCY,car.STATE,car.RELATE_ID
    from TB_CUST_INFO ci,TB_CUST_APPLY_RELATE car 
    where ci.CUST_ID=car.CUST_ID 
	and car.USER_ID = #{userId,jdbcType=BIGINT}
    and BU = #{bu,jdbcType=VARCHAR}
	and ci.BUSI_CODE='MALL'
  </select>
  <select id="qryUserCust" resultMap="QryResultMap" >
    select ci.CUST_ID,ci.CUST_CODE,car.BU,ci.CURRENCY  
    from TB_CUST_INFO ci,TB_CUST_APPLY_RELATE car 
    where ci.CUST_ID=car.CUST_ID 
	and car.USER_ID = #{userId,jdbcType=BIGINT}
    and BU = #{bu,jdbcType=VARCHAR}
	and ci.BUSI_CODE='MALL'
  </select>
  <insert id="insertSelective" parameterType="com.sgs.ecom.member.bo.CustApplyRelate">
    insert into TB_CUST_APPLY_RELATE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="relateId != null">
        RELATE_ID,
      </if>
      <if test="custId != null">
        CUST_ID,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="createDate != null">
        CREATE_DATE,
      </if>
      <if test="bu != null">
        BU,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="relateId != null">
        #{relateId,jdbcType=BIGINT},
      </if>
      <if test="custId != null">
        #{custId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="bu != null">
        #{bu,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="batchUpdateCustApplyRelate">
    update TB_CUST_APPLY_RELATE set STATE  = 1 WHERE state=0 and RELATE_ID IN
    <foreach collection="relateIdList" index="index" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>

  </update>


  <resultMap id="qryResultMap" type="com.sgs.ecom.member.dto.cust.CustInfoRelateDTO" >
    <id column="num" property="num" jdbcType="INTEGER" />
    <id column="RELATE_ID" property="relateId" jdbcType="BIGINT" />
    <result column="CUST_ID" property="custId" jdbcType="BIGINT" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="CUST_CODE" property="custCode" jdbcType="BIGINT" />
    <result column="COMPANY_NAME" property="companyName" jdbcType="BIGINT" />
    <result column="STATE" property="state" jdbcType="INTEGER" />
  </resultMap>

  <select id="qryUserCustInfoByMap" resultMap="qryResultMap" >

    select
      @i:= @i+ 1 as num ,car.RELATE_ID,car.STATE,ci.CUST_ID,car.USER_ID,ci.CUST_CODE,ci.COMPANY_NAME
    from TB_CUST_INFO ci,TB_CUST_APPLY_RELATE car,(SELECT @i:=0) as ii
    where ci.CUST_ID=car.CUST_ID
      and car.USER_ID = #{userId,jdbcType=BIGINT}
      and BU = #{bu,jdbcType=VARCHAR}
      and ci.BUSI_CODE='MALL'
      and ci.IS_DELETE = 0
    order by car.CREATE_DATE desc
  </select>


  <resultMap id="qryCustUser" type="com.sgs.ecom.member.dto.cust.CustUserDTO" >
    <result column="CUST_ID" property="custId" jdbcType="BIGINT" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
  </resultMap>

  <select id="qryMarkUserByCust" resultMap="qryCustUser">
    select tcar.cust_id,tcar.user_id from tb_cust_info tci ,tb_cust_apply_relate tcar,tb_cust_label tcl
    where tci.cust_id=tcar.CUST_ID  and tcar.state=1 and tcar.bu="901"
      and tci.cust_id=tcl.cust_id and tcl.state=1 and LABEL_CODE="Buyer" and LABEL_VALUE ="1"
      and tci.busi_code="CUSTOMER"
      and tci.cust_id IN
    <foreach collection="custIdList" index="index" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

</mapper>