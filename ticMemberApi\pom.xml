<?xml version="1.0"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.sgs.ecom</groupId>
    <artifactId>ticMember</artifactId>
    <version>1.1</version>
  </parent>
  <artifactId>ticMemberApi</artifactId>
  <version>${ticMemberApi.version}</version>
  <packaging>jar</packaging>
  <name>ticMemberApi</name>
  <url>http://maven.apache.org</url>
  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
    
    <dependency>
	    <groupId>com.sgs.ecom</groupId>
	    <artifactId>ticMemberCommon</artifactId>
	    <version>${ticMemberCommon.version}</version>
	</dependency>
    <dependency>
	    <groupId>com.sgs.ecom</groupId>
	    <artifactId>ticMemberService</artifactId>
	    <version>${ticMemberService.version}</version>
	</dependency>

  </dependencies>
	
  <build>
     <!-- <finalName>ticMember</finalName>-->
        <resources>
			<resource>
				<directory>src/main/resources</directory>
				<includes>
                    <include>**/*</include>
				</includes>
				<filtering>false</filtering>
			</resource>
            <resource>
                <directory>src/main/webapp</directory>
                <includes>
                    <include>**/*</include>
                </includes>
            </resource>
		</resources>
		<plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
	        <!-- mybatis generator 自动生成代码插件 -->
            <plugin>
               <groupId>org.mybatis.generator</groupId>
               <artifactId>mybatis-generator-maven-plugin</artifactId>
               <version>1.3.2</version>
               <configuration>
                   <configurationFile>${basedir}/src/main/resources/generatorConfig.xml</configurationFile>
                   <overwrite>true</overwrite>
                   <verbose>true</verbose>
               </configuration>
           </plugin>
        </plugins>
  </build>
</project>
