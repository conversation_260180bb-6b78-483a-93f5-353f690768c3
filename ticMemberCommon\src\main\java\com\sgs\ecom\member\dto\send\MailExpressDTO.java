package com.sgs.ecom.member.dto.send;

import com.sgs.ecom.member.enumtool.aplication.DeliverTypeEnum;
import com.sgs.ecom.member.enumtool.aplication.ExpressCodeEnum;
import com.sgs.ecom.member.util.collection.StrUtil;
import com.sgs.ecom.member.vo.VOOrderExpress;

/**
 * <AUTHOR>
 */
public class MailExpressDTO {
    private String expressNo;
    private String expressCodeShow;
    private String deliverTypeShow;

    public MailExpressDTO() {
    }



    public MailExpressDTO(VOOrderExpress voOrderExpress) {
        this.expressNo = StrUtil.toStr(voOrderExpress.getExpressNo());
        this.expressCodeShow = ExpressCodeEnum.getNameCh(StrUtil.toStr(voOrderExpress.getExpressCode()));
        this.deliverTypeShow= DeliverTypeEnum.getNameCh(String.valueOf(voOrderExpress.getDeliverType()));

    }
    public String getExpressNo() {
        return expressNo;
    }

    public void setExpressNo(String expressNo) {
        this.expressNo = expressNo;
    }

    public String getExpressCodeShow() {
        return expressCodeShow;
    }

    public void setExpressCodeShow(String expressCodeShow) {
        this.expressCodeShow = expressCodeShow;
    }

    public String getDeliverTypeShow() {
        return deliverTypeShow;
    }

    public void setDeliverTypeShow(String deliverTypeShow) {
        this.deliverTypeShow = deliverTypeShow;
    }
}
