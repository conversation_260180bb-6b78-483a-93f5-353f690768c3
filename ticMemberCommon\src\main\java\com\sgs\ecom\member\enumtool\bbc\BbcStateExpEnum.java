package com.sgs.ecom.member.enumtool.bbc;

public enum BbcStateExpEnum {


	TO_PAY("orderM", 10, "待付款","M,G"),//G表示已上传支付凭证
	WAIT_APPLY("orderA", 11, "待提交申请表","A"),//相当于已支付
	WAIT_SEND("waitSend", 12, "待寄送样品",""),
	WAIT_EXAMINE("orderQ", 13, "待实验室审核","Q"),
	END("orderF", 80, "报告/证书下载","F"),
	SERVICE("orderI", 14, "服务中","I"),
	CLOSE("orderI", 91, "已取消","X"),
	;

	BbcStateExpEnum(String name, int index, String nameCh, String min) {
		this.name = name;
		this.index = index;
		this.nameCh = nameCh;
		this.min = min;
	}

	public static String getName(int index) {
		for (BbcStateExpEnum c : BbcStateExpEnum.values()) {
			if (c.getIndex()==index) {
				return c.getName();
			}
		}
		return null;
	}
	public static String getNameCh(int index) {
		for (BbcStateExpEnum c : BbcStateExpEnum.values()) {
			if (c.getIndex()==index) {
				return c.getNameCh();
			}
		}
		return "";
	}

	public static BbcStateExpEnum getEnum(String name) {
		for (BbcStateExpEnum c : BbcStateExpEnum.values()) {
			if (c.getName().equals(name)) {
				return c;
			}
		}
		return null;
	}

	public static String getMinByIndex(int index) {
		for (BbcStateExpEnum c : BbcStateExpEnum.values()) {
			if (c.getIndex()==index) {
				if(index==10){
					return "M";
				}
				return c.getMin();
			}
		}
		return "";
	}

	public  static int getEnumStateByMin(String min){
		for (BbcStateExpEnum c : BbcStateExpEnum.values()) {
			if (c.getMin().contains(min)) {
				return c.getIndex();
			}
		}
		return -2;
	}

	private String name;
	private int index;
	private String nameCh;
	private String min;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public int getIndex() {
		return index;
	}

	public void setIndex(int index) {
		this.index = index;
	}

	public String getNameCh() {
		return nameCh;
	}

	public void setNameCh(String nameCh) {
		this.nameCh = nameCh;
	}

	public String getMin() {
		return min;
	}

	public void setMin(String min) {
		this.min = min;
	}
}
