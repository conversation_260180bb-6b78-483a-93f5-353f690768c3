package com.sgs.ecom.member.enumtool.aplication;

public enum ProgramUrlEnum {

    IND("1001", "/pages/application/input/input"),
    ALL("all", "/pages/application/basic/basic"),
    OIQ("oiq","/pages/application/input/input"),
    ;

    ProgramUrlEnum(String key, String page) {
        this.key = key;
        this.page = page;
    }


    public static String getProgramUrl(String key) {
        for (ProgramUrlEnum c : ProgramUrlEnum.values()) {
            if (c.getKey().equals(key)) {
                return c.getPage();
            }
        }
        return ProgramUrlEnum.ALL.getPage();
    }

    private String key;
    private String page;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getPage() {
        return page;
    }

    public void setPage(String page) {
        this.page = page;
    }
}
