package com.sgs.ecom.member.enumtool.rsts;

public enum SampleFeaturesEnum {
	ONE("one", 1, "无危险"),
	TWO("two", 2, "毒性"),
	THREE("three", 3, "致癌"),
	FOUR("four", 4, "易燃易爆"),
	FIVE("five", 5, "挥发性"),
	SIX("six", 6, "磁性"),
	SEVEN("seven", 7, "腐蚀性"),
	EIGHT("eight", 8, "其他特性"),
	;

	SampleFeaturesEnum(String name, int index, String nameCh) {
		this.name = name;
		this.index = index;
		this.nameCh = nameCh;
	}

	public static SampleFeaturesEnum getEnum(String index) {
		for (SampleFeaturesEnum c : SampleFeaturesEnum.values()) {
			if (index.equals(String.valueOf(c.getIndex()))) {
				return c;
			}
		}
		return null;
	}

	private String name;
	private int index;
	private String nameCh;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public int getIndex() {
		return index;
	}

	public void setIndex(int index) {
		this.index = index;
	}

	public String getNameCh() {
		return nameCh;
	}

	public void setNameCh(String nameCh) {
		this.nameCh = nameCh;
	}
}
