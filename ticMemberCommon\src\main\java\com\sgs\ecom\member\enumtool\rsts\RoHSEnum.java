package com.sgs.ecom.member.enumtool.rsts;

import org.apache.commons.lang.StringUtils;

public enum RoHSEnum {

//    RoHS("roHS", 1, "rohs 4项镀层"),
//    SPEC("spec", 2, "X客户专用"),
//    EN71("EN71", 3, "EN71-3"),
//    PAHS("PAHS", 4, "多环芳烃(PAHs)"),


    RoHS("roHS", 1, "rohs 4项镀层"),
    SPEC("spec", 2, "SPEC-INFORMATION"),
    EN71("EN71", 3, "EN71-3"),
    PAHS("PAHS", 4, "多环芳烃(PAHs)"),
    ;

    RoHSEnum(String name, int index, String nameCh) {
        this.name = name;
        this.index = index;
        this.nameCh = nameCh;
    }

    public static Boolean getRoHsFlg(String nameCh){
        if(StringUtils.isBlank(nameCh)){
            return false;
        }
        if(nameCh.contains(RoHSEnum.RoHS.getNameCh())){
            return true;
        }
        return false;
    }

    public static Boolean getSpecFlg(String nameCh){
        if(StringUtils.isBlank(nameCh)){
            return false;
        }

        if(nameCh.contains(RoHSEnum.SPEC.getNameCh())){
            return true;
        }
        return false;
    }

    public static Boolean getEN71Flg(String nameCh){
        if(StringUtils.isBlank(nameCh)){
            return false;
        }

        if(nameCh.contains(RoHSEnum.EN71.getNameCh())){
            return true;
        }
        return false;
    }

    public static Boolean getPAHSFlg(String nameCh){
        if(StringUtils.isBlank(nameCh)){
            return false;
        }

        if(nameCh.contains(RoHSEnum.PAHS.getNameCh())){
            return true;
        }
        return false;
    }


    private String name;
    private int index;
    private String nameCh;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }
}
