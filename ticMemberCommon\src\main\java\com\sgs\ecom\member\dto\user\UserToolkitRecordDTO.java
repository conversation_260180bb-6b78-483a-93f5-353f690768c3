package com.sgs.ecom.member.dto.user;
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.json.TimeFormatSerializer;
import com.sgs.base.BaseQryFilter;

import java.sql.Timestamp;
import java.util.List;

public class UserToolkitRecordDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select CREATE_DATE,STATE_DATE,RECORD_ID,USER_ID,STATE,TOOL_CODE,MAKE_TITLE,MAKE_NO,MAKE_TYPE from USER_TOOLKIT_RECORD"; 
 
 
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private Timestamp stateDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="RECORD_ID", getName="getRecordId", setName="setRecordId")
 	private long recordId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="USER_ID", getName="getUserId", setName="setUserId")
 	private long userId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="TOOL_CODE", getName="getToolCode", setName="setToolCode")
 	private String toolCode;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="MAKE_TITLE", getName="getMakeTitle", setName="setMakeTitle")
 	private String makeTitle;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="MAKE_NO", getName="getMakeNo", setName="setMakeNo")
 	private String makeNo;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="MAKE_TYPE", getName="getMakeType", setName="setMakeType")
 	private String makeType;
	@ApiAnno(groups={Default.class})
	@BeanAnno(value="SHOW_MODE", getName="getShowMode", setName="setShowMode")
	private String showMode;

	public String getShowMode() {
		return showMode;
	}

	public void setShowMode(String showMode) {
		this.showMode = showMode;
	}

	//正常字段属性列表
	@ApiAnno(groups={Default.class})
	List<UserToolkitRecordDtlDTO> normalList;

	public List<UserToolkitRecordDtlDTO> getNormalList() {
		return normalList;
	}

	public void setNormalList(List<UserToolkitRecordDtlDTO> normalList) {
		this.normalList = normalList;
	}

	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setRecordId(long recordId){
 		 this.recordId=recordId;
 	}
 	public long getRecordId(){
 		 return this.recordId;
 	}
 
 	 
 	public void setUserId(long userId){
 		 this.userId=userId;
 	}
 	public long getUserId(){
 		 return this.userId;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setToolCode(String toolCode){
 		 this.toolCode=toolCode;
 	}
 	public String getToolCode(){
 		 return this.toolCode;
 	}
 
 	 
 	public void setMakeTitle(String makeTitle){
 		 this.makeTitle=makeTitle;
 	}
 	public String getMakeTitle(){
 		 return this.makeTitle;
 	}
 
 	 
 	public void setMakeNo(String makeNo){
 		 this.makeNo=makeNo;
 	}
 	public String getMakeNo(){
 		 return this.makeNo;
 	}
 
 	 
 	public void setMakeType(String makeType){
 		 this.makeType=makeType;
 	}
 	public String getMakeType(){
 		 return this.makeType;
 	}
 
 	 
}