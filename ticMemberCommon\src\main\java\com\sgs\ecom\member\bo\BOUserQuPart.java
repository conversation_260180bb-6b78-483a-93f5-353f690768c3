package com.sgs.ecom.member.bo; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import com.platform.annotation.CharacterVaild; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import com.platform.annotation.BeanAnno; 

public class BOUserQuPart{
 
 	public static final String SEQUENCE = "PART_ID"; 
  
 	public static final String BO_SQL = "TB_USER_QU_PART"; 
 
 	public static final String OWNER ="member";

 	public static final String PART_NO="partNo";
 	public static final String STATE_DATE="stateDate";
 	public static final String STATE="state";
 	public static final String USER_ID="userId";
 	public static final String ORDER_NO="orderNo";
 	public static final String PART_SCORE="partScore";
 	public static final String FINAL_SCORE="finalScore";
 	public static final String PART_ID="partId";
 	public static final String CREATE_DATE="createDate";
 	public static final String PART_NAME="partName";
 	public static final String QUESTION_ID="questionId";
 	public static final String REPLY_ID="replyId";
 	public static final String SORT_SHOW="sortShow";
 	public static final String REAL_SCORE="realScore";
 	public static final String PART_NAME_EN="partNameEn";

 	@BeanAnno("PART_NO")
 	private String partNo;
 	@BeanAnno("STATE_DATE")
 	private Timestamp stateDate;
 	@BeanAnno("STATE")
 	private int state;
 	@BeanAnno("USER_ID")
 	private long userId;
 	@BeanAnno("ORDER_NO")
 	private String orderNo;
 	@BeanAnno("PART_SCORE")
 	private long partScore;
 	@BeanAnno("FINAL_SCORE")
 	private long finalScore;
 	@BeanAnno("PART_ID")
 	private long partId;
 	@BeanAnno("CREATE_DATE")
 	private Timestamp createDate;
 	@BeanAnno("PART_NAME")
 	private String partName;
 	@BeanAnno("QUESTION_ID")
 	private long questionId;
 	@BeanAnno("REPLY_ID")
 	private long replyId;
 	@BeanAnno("SORT_SHOW")
 	private long sortShow;
 	@BeanAnno("REAL_SCORE")
 	private long realScore;
 	@BeanAnno("PART_NAME_EN")
 	private String partNameEn;

 	@CharacterVaild(len = 10) 
 	public void setPartNo(String partNo){
 		 this.partNo=partNo;
 	}
 	public String getPartNo(){
 		 return this.partNo;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setUserId(long userId){
 		 this.userId=userId;
 	}
 	public long getUserId(){
 		 return this.userId;
 	}
 
 	 
 	@CharacterVaild(len = 30) 
 	public void setOrderNo(String orderNo){
 		 this.orderNo=orderNo;
 	}
 	public String getOrderNo(){
 		 return this.orderNo;
 	}
 
 	 
 	public void setPartScore(long partScore){
 		 this.partScore=partScore;
 	}
 	public long getPartScore(){
 		 return this.partScore;
 	}
 
 	 
 	public void setFinalScore(long finalScore){
 		 this.finalScore=finalScore;
 	}
 	public long getFinalScore(){
 		 return this.finalScore;
 	}
 
 	 
 	public void setPartId(long partId){
 		 this.partId=partId;
 	}
 	public long getPartId(){
 		 return this.partId;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setPartName(String partName){
 		 this.partName=partName;
 	}
 	public String getPartName(){
 		 return this.partName;
 	}
 
 	 
 	public void setQuestionId(long questionId){
 		 this.questionId=questionId;
 	}
 	public long getQuestionId(){
 		 return this.questionId;
 	}
 
 	 
 	public void setReplyId(long replyId){
 		 this.replyId=replyId;
 	}
 	public long getReplyId(){
 		 return this.replyId;
 	}
 
 	 
 	public void setSortShow(long sortShow){
 		 this.sortShow=sortShow;
 	}
 	public long getSortShow(){
 		 return this.sortShow;
 	}
 
 	 
 	public void setRealScore(long realScore){
 		 this.realScore=realScore;
 	}
 	public long getRealScore(){
 		 return this.realScore;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setPartNameEn(String partNameEn){
 		 this.partNameEn=partNameEn;
 	}
 	public String getPartNameEn(){
 		 return this.partNameEn;
 	}
 
 	 
}