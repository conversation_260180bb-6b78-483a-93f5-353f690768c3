package com.sgs.ecom.member.enums;

public enum OrderPrintUserEnum {

	USER("客户端", "user"),
    CS("客服端", "cs");

    // 成员变量
    private String name;
    private String index;
    // 构造方法
    private OrderPrintUserEnum(String name, String index) {
        this.name = name;  
        this.index = index;  
    }  

    // 普通方法  
    public static String getName(String index) {  
        for (OrderPrintUserEnum c : OrderPrintUserEnum.values()) {
            if (c.getIndex().equals(index)) {  
                return c.name;  
            }  
        }  
        return null;  
    }  
    // get set 方法  
    public String getName() {  
        return name;  
    }  

    public String getIndex() {  
        return index;  
    }  
}
