import java.io.BufferedOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URLDecoder;
import java.security.SecureRandom;
import java.util.HashSet;
import java.util.Set;

import com.itextpdf.text.BaseColor;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Element;
import com.itextpdf.text.Image;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfContentByte;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import com.platform.busi.dao.interfaces.ISysDataConfigDAO;
import com.platform.cache.util.GetCacheManage;
import com.platform.util.CollectionService;
import com.platform.util.SystemConfig;
import com.platform.util.json.JSONUtil;
import com.sgs.ecom.member.acct.service.interfaces.IAcctBalanceSV;
import com.sgs.ecom.member.question.service.interfaces.IUserQuPartSV;
import com.sgs.ecom.member.question.service.interfaces.IUserQuestionSV;
import com.sgs.ecom.member.user.service.interfaces.IUserInfoSV;
import com.sgs.ecom.member.user.service.interfaces.IUserPackagesSV;
import com.sgs.ecom.member.util.UtilTools;

import net.sf.json.JSONObject;

public class Test {
	
	private static IUserPackagesSV userPackagesSV = CollectionService.getService(IUserPackagesSV.class);
	private static ISysDataConfigDAO sysDataConfigDAO = CollectionService.getService(ISysDataConfigDAO.class);
	private static IUserQuPartSV userQuPartSV = CollectionService.getService(IUserQuPartSV.class);
	private static IUserQuestionSV userQuestionSV = CollectionService.getService(IUserQuestionSV.class);
	private static IAcctBalanceSV acctBalanceSV = CollectionService.getService(IAcctBalanceSV.class);
	private static IUserInfoSV userInfoSV = CollectionService.getService(IUserInfoSV.class);

	public static void main(String[] args) {

		boolean flag = false;

		System.out.println("asdasd"+flag);
	}
	
	
	/**
     * 
     * @param bos输出文件的位置
     * @param input
     *            原PDF位置
     * @param waterMarkName
     *            页脚添加水印
     * @param permission
     *            权限码
     * @throws DocumentException
     * @throws IOException
     */
    public static void setWatermark(BufferedOutputStream bos, String input, String path)
            throws DocumentException, IOException {
        PdfReader reader = new PdfReader(input);
        PdfStamper stamper = new PdfStamper(reader, bos);
        int total = reader.getNumberOfPages() + 1;
        PdfContentByte content;
        BaseFont base = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.EMBEDDED);
//        PdfGState gs = new PdfGState();
        for (int i = 1; i < total; i++) {
            content = stamper.getOverContent(i);// 在内容上方加水印
            // content = stamper.getUnderContent(i);//在内容下方加水印
//            gs.setFillOpacity(0.2f);
            // content.setGState(gs);
//            content.beginText();
//            content.setColorFill(BaseColor.LIGHT_GRAY);
//            content.setFontAndSize(base, 50);
//            content.setTextMatrix(70, 200);
//            content.showTextAligned(Element.ALIGN_CENTER, "公司内部文件，请注意保密！", 300, 350, 55);
            Image image = Image.getInstance("/home/<USER>/temp/printer.png");
            image.setAbsolutePosition(0, 0); 
//            image.scaleToFit(200, 200);
            
            Image logoImage = Image.getInstance(path + SystemConfig.getParaValue("logo_img"));
            logoImage.setAbsolutePosition(50, 750); 
            logoImage.scaleToFit(57, 40);
            
            Image lineImage = Image.getInstance(path + SystemConfig.getParaValue("line_img"));
            lineImage.setAbsolutePosition(50, 25); 
            
            content.beginText();
            content.addImage(image);
            content.addImage(logoImage);
            content.addImage(lineImage);
            content.setColorFill(BaseColor.BLACK);
            content.setFontAndSize(base, 6);
            content.showTextAligned(Element.ALIGN_LEFT, "全球社会组织最佳实践对标在线自测报告", 50, 15, 0);
            content.showTextAligned(Element.ALIGN_LEFT, "服务邮箱: <EMAIL>", 50, 5, 0);

            content.setFontAndSize(base, 8);
            content.showTextAligned(Element.ALIGN_RIGHT, "第 " + i + " 页", 550, 15, 0);
            content.endText();

        }
        stamper.close();
    }
	
	/**

	 * 在pdf文件中添加水印

	 *

	 * @param inputFile

	 *            原始文件

	 * @param outputFile

	 *            水印输出文件

	 * @param waterMarkName

	 *            水印名字

	 */

	private static void waterMark(String inputFile, String outputFile,
	        String waterMarkName) {
		String imageFilePath = "/home/<USER>/temp/pointer.png";

	    try {

	        PdfReader reader = new PdfReader(inputFile);
	        PdfStamper stamper = new PdfStamper(reader, new FileOutputStream(outputFile));

	        BaseFont base = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

	        int total = reader.getNumberOfPages() + 1;

	        Image image = Image.getInstance(imageFilePath);

	        image.setAbsolutePosition(200, 400);

	        PdfContentByte under;

	        int j = waterMarkName.length();

	        char c = 0;

	        int rise = 0;

	        for (int i = 1; i < total; i++) {

	            rise = 500;

	            under = stamper.getUnderContent(i);

	            // 添加图片

	            // under.addImage(image);

	            under.beginText();

	            under.setColorFill(BaseColor.CYAN);

	            under.setFontAndSize(base, 30);

	            // 设置水印文字字体倾斜 开始

	            if (j >= 15) {

	                under.setTextMatrix(200, 0);

	                for (int k = 0; k < j; k++) {

	                    under.setTextRise(rise);

	                    c = waterMarkName.charAt(k);

	                    under.showText(c + "");

	                    rise -= 20;

	                }

	            } else {

	                under.setTextMatrix(180, 0);

	                for (int k = 0; k < j; k++) {

	                    under.setTextRise(rise);

	                    c = waterMarkName.charAt(k);

	                    under.showText(c + "");

	                    rise -= 18;

	                }

	            }

	            // 字体设置结束

	            under.endText();

	            // 画一个圆

	            // under.ellipse(250, 450, 350, 550);

	            // under.setLineWidth(1f);

	            // under.stroke();

	        }

	        stamper.close();

	    } catch (Exception e) {

	        

	    }

	}

	
	
//	public static void pdfTest(String fileName) { 
//	    Document doc=new Document(); 
//	      
//	    try { 
//	      PdfWriter.getInstance(doc, new FileOutputStream(fileName)); 
//	      BaseFont fontChinese=null; 
//	      try { 
//	        fontChinese = BaseFont.createFont("STSong-Light","UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);//设置中文字体 
//	      } catch (IOException e) { 
//	        e.printStackTrace(); 
//	      } 
//	      
//	      Font chinese = new Font(fontChinese, 10, Font.NORMAL);  
//	        
//	      /** 
//	       * HeaderFooter的第2个参数为非false时代表打印页码 
//	       * 页眉页脚中也可以加入图片，并非只能是文字 
//	       */
//	      HeaderFooter header=new HeaderFooter(new Phrase("这仅仅是个页眉，页码在页脚处",chinese),false); 
//	        
//	      //设置是否有边框等 
////	     header.setBorder(Rectangle.NO_BORDER); 
//	      header.setBorder(Rectangle.BOTTOM); 
//	      header.setAlignment(1); 
//	      header.setBorderColor(Color.red); 
//	      doc.setHeader(header); 
//	        
//	      HeaderFooter footer=new HeaderFooter(new Phrase("-",chinese),new Phrase("-",chinese)); 
//	      /** 
//	       * 0是靠左 
//	       * 1是居中 
//	       * 2是居右 
//	       */
//	      footer.setAlignment(1); 
//	      footer.setBorderColor(Color.red); 
//	      footer.setBorder(Rectangle.BOX); 
//	      doc.setFooter(footer); 
//	      
//	      /** 
//	       * 页眉页脚的设置一定要在open前设置好 
//	       */
//	      doc.open(); 
//	      /** 
//	       * true:代表要排序,10代表序号与文字之间的间距 
//	       * false:代表不排序，则文字前的符号为"-" 
//	       */
//	      List itextList=new List(true,10); 
//	        
//	      /** 
//	       * 也可以改变列表的符号[可选] 
//	       * 
//	$$$ 
//	       * 要改变列表符号时，上面的List构造方法第一参数值必须为false 
//	       * 
//	$$$ 
//	       * 可以使用字符串，Chunk,Image等作列表符号,如下 
//	       */
//	      //itextList.setListSymbol("*"); 
//	        
//	      ListItem firstItem=new ListItem("first paragraph"); 
//	      ListItem secondItem=new ListItem("second paragraph"); 
//	      ListItem thirdItem=new ListItem("third paragraph"); 
//	      itextList.add(firstItem); 
//	      itextList.add(secondItem); 
//	      itextList.add(thirdItem); 
//	        
//	      doc.add(itextList); 
//	        
//	      //添加注释,注释有标题和内容,注释可以是文本，内部链接，外部链接，图片等 
//	      Annotation annotation=new Annotation("what's this?","it's a tree and it is not a big"); 
//	        
//	      doc.add(annotation); 
//	        
//	      doc.close(); 
//	    } catch (FileNotFoundException e) { 
//	      e.printStackTrace(); 
//	    } catch (DocumentException e) { 
//	      e.printStackTrace(); 
//	    } 
//	  } 
	
}