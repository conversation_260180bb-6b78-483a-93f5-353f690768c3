package com.sgs.ecom.member.dto.rpc;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * AiPrompt
 *
 * @version: 1.0
 * @author: huangting.lu
 * @throws Exception
 * @date: 2025/5/16
 * <p>
 * Modification History: Date         Author          Version            Description
 * ---------------------------------------------------------* 修改时间     修改人           版本
 * 修改原因 2025/5/16      huangting.lu         v1.0               新增
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AiPrompt {

    /**
     * 系统提示词
     */
    private String sysContext;

    /**
     * 信息
     */
    private List<AiPromptDetail> messages;

    /**
     * 最大token
     */
    private Long max_tokens = 2000L;

}
