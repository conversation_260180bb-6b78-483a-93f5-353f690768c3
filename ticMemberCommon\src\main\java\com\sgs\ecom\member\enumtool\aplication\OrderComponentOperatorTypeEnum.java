package com.sgs.ecom.member.enumtool.aplication;

import com.platform.util.ValidationUtil;
import org.apache.commons.lang.StringUtils;

import java.util.List;

/**
 * RSTS-MSDS/SDS
 * sds标准对应的动态字段的枚举值
 */
public enum OrderComponentOperatorTypeEnum {
    EQUALS("", "10", "等于"),
    GREATER_AND_EQUAL("≥", "11", "大于等于"),
    LESS_AND_EQUAL("≤", "12", "小于等于"),
    INTERVAL("", "13", "区间"),


    ;

    OrderComponentOperatorTypeEnum(String name, String index, String nameCh) {
        this.name = name;
        this.index = index;
        this.nameCh = nameCh;
    }

    public static String getName(String index) {
        for (OrderComponentOperatorTypeEnum c : OrderComponentOperatorTypeEnum.values()) {
            if (c.getIndex().equals(index)) {
                return c.getName();
            }
        }
        return "3";
    }

    public static String getNameCh(String index) {
        for (OrderComponentOperatorTypeEnum c : OrderComponentOperatorTypeEnum.values()) {
            if (c.getIndex().equals(index)) {
                return c.getNameCh();
            }
        }
        return "3";
    }


    public static String getIndex(String name) {
        for (OrderComponentOperatorTypeEnum c : OrderComponentOperatorTypeEnum.values()) {
            if (c.name().equals(name)) {
                return c.getIndex();
            }
        }
        return "";
    }

    public static String getNameChByName(String name) {
        if(StringUtils.isBlank(name)){
            return "";
        }

        for (OrderComponentOperatorTypeEnum c : OrderComponentOperatorTypeEnum.values()) {
            if (c.getName().equals(name.toLowerCase())) {
                return c.getNameCh();
            }
        }
        return "";
    }


    public static String getNameByNameCh(String nameCh) {
        for (OrderComponentOperatorTypeEnum c : OrderComponentOperatorTypeEnum.values()) {
            if (c.getNameCh().equals(nameCh)) {
                return c.getName().toUpperCase();
            }
        }
        return "";
    }


    public static String getIndexByName(List<String> list) {
        if(ValidationUtil.isEmpty(list))
            return "";

        StringBuilder stringBuilder = new StringBuilder();
        list.stream().forEach(extendName ->{
            String index = OrderComponentOperatorTypeEnum.getIndex(extendName);
            if(!ValidationUtil.isEmpty(index)){
                stringBuilder.append(index+",");
            }
        });
        String toString = stringBuilder.toString();
        return  ValidationUtil.isEmpty(toString)?"":toString.substring(0,toString.length());
    }


    private String name;
    private String index;
    private String nameCh;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }
}
