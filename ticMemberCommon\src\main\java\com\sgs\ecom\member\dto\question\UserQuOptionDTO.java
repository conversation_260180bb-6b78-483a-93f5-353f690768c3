package com.sgs.ecom.member.dto.question;
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import com.platform.annotation.ApiAnno; 
import com.platform.annotation.BeanAnno; 

public class UserQuOptionDTO{
 
 	public static final String CREATE_SQL = "select PART_NO,OPTION_INFO_EN,STATE_DATE,STATE,OPTION_VAL,OPTION_INFO,SUBJECT_NO,FINAL_SCORE,OPTION_TEXT,CREATE_DATE,ANSWER_ID,QUESTION_ID,REPLY_ID,SORT_SHOW,OPTION_NO,STANDARD_CODE,OPTION_SCORE from TB_USER_QU_OPTION"; 
 
 
 	@ApiAnno(serviceName={"qryAnswerByPart","submitAnswer"})
 	@BeanAnno(value="PART_NO", getName="getPartNo", setName="setPartNo")
 	private String partNo;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="OPTION_INFO_EN", getName="getOptionInfoEn", setName="setOptionInfoEn")
 	private String optionInfoEn;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private Timestamp stateDate;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(serviceName={"qryAnswerByPart"})
 	@BeanAnno(value="OPTION_VAL", getName="getOptionVal", setName="setOptionVal")
 	private String optionVal;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="OPTION_INFO", getName="getOptionInfo", setName="setOptionInfo")
 	private String optionInfo;
 	@ApiAnno(serviceName={"qryAnswerByPart"})
 	@BeanAnno(value="SUBJECT_NO", getName="getSubjectNo", setName="setSubjectNo")
 	private String subjectNo;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="FINAL_SCORE", getName="getFinalScore", setName="setFinalScore")
 	private long finalScore;
 	@ApiAnno(serviceName={"qryAnswerByPart"})
 	@BeanAnno(value="OPTION_TEXT", getName="getOptionText", setName="setOptionText")
 	private String optionText;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="ANSWER_ID", getName="getAnswerId", setName="setAnswerId")
 	private long answerId;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="QUESTION_ID", getName="getQuestionId", setName="setQuestionId")
 	private long questionId;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="REPLY_ID", getName="getReplyId", setName="setReplyId")
 	private long replyId;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="SORT_SHOW", getName="getSortShow", setName="setSortShow")
 	private long sortShow;
 	@ApiAnno(serviceName={"qryAnswerByPart"})
 	@BeanAnno(value="OPTION_NO", getName="getOptionNo", setName="setOptionNo")
 	private String optionNo;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STANDARD_CODE", getName="getStandardCode", setName="setStandardCode")
 	private String standardCode;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="OPTION_SCORE", getName="getOptionScore", setName="setOptionScore")
 	private long optionScore;

 	public void setPartNo(String partNo){
 		 this.partNo=partNo;
 	}
 	public String getPartNo(){
 		 return this.partNo;
 	}
 
 	 
 	public void setOptionInfoEn(String optionInfoEn){
 		 this.optionInfoEn=optionInfoEn;
 	}
 	public String getOptionInfoEn(){
 		 return this.optionInfoEn;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setOptionVal(String optionVal){
 		 this.optionVal=optionVal;
 	}
 	public String getOptionVal(){
 		 return this.optionVal;
 	}
 
 	 
 	public void setOptionInfo(String optionInfo){
 		 this.optionInfo=optionInfo;
 	}
 	public String getOptionInfo(){
 		 return this.optionInfo;
 	}
 
 	 
 	public void setSubjectNo(String subjectNo){
 		 this.subjectNo=subjectNo;
 	}
 	public String getSubjectNo(){
 		 return this.subjectNo;
 	}
 
 	 
 	public void setFinalScore(long finalScore){
 		 this.finalScore=finalScore;
 	}
 	public long getFinalScore(){
 		 return this.finalScore;
 	}
 
 	 
 	public void setOptionText(String optionText){
 		 this.optionText=optionText;
 	}
 	public String getOptionText(){
 		 return this.optionText;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setAnswerId(long answerId){
 		 this.answerId=answerId;
 	}
 	public long getAnswerId(){
 		 return this.answerId;
 	}
 
 	 
 	public void setQuestionId(long questionId){
 		 this.questionId=questionId;
 	}
 	public long getQuestionId(){
 		 return this.questionId;
 	}
 
 	 
 	public void setReplyId(long replyId){
 		 this.replyId=replyId;
 	}
 	public long getReplyId(){
 		 return this.replyId;
 	}
 
 	 
 	public void setSortShow(long sortShow){
 		 this.sortShow=sortShow;
 	}
 	public long getSortShow(){
 		 return this.sortShow;
 	}
 
 	 
 	public void setOptionNo(String optionNo){
 		 this.optionNo=optionNo;
 	}
 	public String getOptionNo(){
 		 return this.optionNo;
 	}
 
 	 
 	public void setStandardCode(String standardCode){
 		 this.standardCode=standardCode;
 	}
 	public String getStandardCode(){
 		 return this.standardCode;
 	}
 
 	 
 	public void setOptionScore(long optionScore){
 		 this.optionScore=optionScore;
 	}
 	public long getOptionScore(){
 		 return this.optionScore;
 	}
 
 	 
}