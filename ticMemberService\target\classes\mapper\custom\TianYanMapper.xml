<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.mapper.custom.TianYanMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.bo.TianYan" >
    <id column="ID" property="id" jdbcType="BIGINT" />
    <result column="CREDIT_CODE" property="creditCode" jdbcType="VARCHAR" />
    <result column="NAME" property="name" jdbcType="VARCHAR" />
    <result column="REG_LOCATION" property="regLocation" jdbcType="VARCHAR" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
  </resultMap>

  <insert id="insertForeach"  >
    insert into TIAN_YAN
    ( CREDIT_CODE, NAME, REG_LOCATION)
    values
    <foreach collection ="list" item="item" index= "index" separator =",">
      (
      #{item.creditCode},#{item.name}, #{item.regLocation}
      )
    </foreach>
  </insert>

  <sql id="Base_Column_List">
    ID, CREDIT_CODE, NAME, REG_LOCATION, CREATE_DATE
  </sql>

  <select id="selectListByPara" resultMap="BaseResultMap" parameterType="com.sgs.ecom.member.dto.rpc.TianYanDTO">
    select
    <include refid="Base_Column_List"/>
    from TIAN_YAN
    <where>
      <include refid="Base_Qry_List"/>
    </where>
  </select>


  <sql id="Base_Qry_List">
    <if test="id != null">
      AND ID = #{id, jdbcType=BIGINT}
    </if>
    <if test="creditCode != null">
      AND CREDIT_CODE = #{creditCode}
    </if>
  </sql>

  <update id="updateByPara" parameterType="com.sgs.ecom.member.dto.rpc.TianYanDTO">
    update TIAN_YAN
    <set>
      <if test="regLocation != null">
        REG_LOCATION = #{regLocation,jdbcType=VARCHAR},
      </if>
    </set>
    <where>
      <include refid="Base_Qry_List"/>
    </where>
  </update>
  
</mapper>