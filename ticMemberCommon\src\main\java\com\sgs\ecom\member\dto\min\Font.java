package com.sgs.ecom.member.dto.min;

import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.sgs.ecom.member.base.BaseOrderFilter;

public class Font {
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private Boolean bold;
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private String highlightColorHex;
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private Boolean underLine;

    public Font(String highlightColorHex) {
        this.bold=true;
        this.highlightColorHex=highlightColorHex;
        this.underLine=false;
    }


    public String getHighlightColorHex() {
        return highlightColorHex;
    }

    public void setHighlightColorHex(String highlightColorHex) {
        this.highlightColorHex = highlightColorHex;
    }

    public Boolean getBold() {
        return bold;
    }

    public void setBold(Boolean bold) {
        this.bold = bold;
    }

    public Boolean getUnderLine() {
        return underLine;
    }

    public void setUnderLine(Boolean underLine) {
        this.underLine = underLine;
    }
}
