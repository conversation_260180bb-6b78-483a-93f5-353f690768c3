package com.sgs.ecom.member.dto.rpc;

public class CenterBossDTO {
	private String customerNameCn;
	private String taxRegistrationNumber;
	private String addressCn;
	private String bankName;//bankName+(bankBranchName)
	private String bankBranchName;
	private String bankAccountNum;
	private String telephone;

	public String getCustomerNameCn() {
		return customerNameCn;
	}

	public void setCustomerNameCn(String customerNameCn) {
		this.customerNameCn = customerNameCn;
	}

	public String getTaxRegistrationNumber() {
		return taxRegistrationNumber;
	}

	public void setTaxRegistrationNumber(String taxRegistrationNumber) {
		this.taxRegistrationNumber = taxRegistrationNumber;
	}

	public String getAddressCn() {
		return addressCn;
	}

	public void setAddressCn(String addressCn) {
		this.addressCn = addressCn;
	}

	public String getBankName() {
		return bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}

	public String getBankBranchName() {
		return bankBranchName;
	}

	public void setBankBranchName(String bankBranchName) {
		this.bankBranchName = bankBranchName;
	}

	public String getBankAccountNum() {
		return bankAccountNum;
	}

	public void setBankAccountNum(String bankAccountNum) {
		this.bankAccountNum = bankAccountNum;
	}

	public String getTelephone() {
		return telephone;
	}

	public void setTelephone(String telephone) {
		this.telephone = telephone;
	}
}
