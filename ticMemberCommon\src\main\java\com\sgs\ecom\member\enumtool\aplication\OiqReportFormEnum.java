package com.sgs.ecom.member.enumtool.aplication;

public enum OiqReportFormEnum {

    PAPER("paper", "1", "纸质报告","纸质报告(加收纸质报告费)"),
    ELECTRON("electron", "2", "电子报告","电子报告"),
    ELECTRON_PAPER("electronPaper", "3", "电子+纸质报告","电子+纸质报告");




    OiqReportFormEnum(String name, String index, String nameCh,String minNameCh) {
        this.name = name;
        this.index = index;
        this.nameCh = nameCh;
        this.minNameCh=minNameCh;
    }

    public static String getNameCh(String index) {
        for (OiqReportFormEnum c : OiqReportFormEnum.values()) {
            if (c.getIndex().equals(index)) {
                return c.getNameCh();
            }
        }
        return "";
    }

    public static String getMinNameCh(String index) {
        for (OiqReportFormEnum c : OiqReportFormEnum.values()) {
            if (c.getIndex().equals(index)) {
                return c.getMinNameCh();
            }
        }
        return "";
    }



    private String name;
    private String index;
    private String nameCh;
    private String minNameCh;



    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }

    public String getMinNameCh() {
        return minNameCh;
    }

    public void setMinNameCh(String minNameCh) {
        this.minNameCh = minNameCh;
    }
}
