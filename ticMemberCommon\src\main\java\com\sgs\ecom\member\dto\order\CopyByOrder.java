package com.sgs.ecom.member.dto.order;

import com.sgs.ecom.member.dto.base.BaseOrderDTO;

public class CopyByOrder {

    private String useOrderNo;
    private String toOrderNo;
    private BaseOrderDTO inquiry;

    public CopyByOrder(String useOrderNo, String toOrderNo) {
        this.useOrderNo = useOrderNo;
        this.toOrderNo = toOrderNo;
    }

    public CopyByOrder(String useOrderNo, String toOrderNo,BaseOrderDTO inquiry) {
        this.useOrderNo = useOrderNo;
        this.toOrderNo = toOrderNo;
        this.inquiry=inquiry;
    }

    public String getUseOrderNo() {
        return useOrderNo;
    }

    public void setUseOrderNo(String useOrderNo) {
        this.useOrderNo = useOrderNo;
    }

    public String getToOrderNo() {
        return toOrderNo;
    }

    public void setToOrderNo(String toOrderNo) {
        this.toOrderNo = toOrderNo;
    }

    public BaseOrderDTO getInquiry() {
        return inquiry;
    }

    public void setInquiry(BaseOrderDTO inquiry) {
        this.inquiry = inquiry;
    }
}
