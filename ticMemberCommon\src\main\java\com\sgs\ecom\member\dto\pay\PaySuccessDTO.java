package com.sgs.ecom.member.dto.pay;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.dto.OrderDetailDTO;
import com.sgs.ecom.member.util.serializer.PriceNumFormatSerializer;

import java.math.BigDecimal;
import java.util.List;

public class PaySuccessDTO {

    @ApiAnno(groups={BaseOrderFilter.RstsDetail.class})
    private  Long orderId;
    @ApiAnno(groups={BaseOrderFilter.RstsDetail.class})
    private String orderNo;
    @ApiAnno(groups={BaseOrderFilter.RstsDetail.class})
    private String orderType;
    @JsonSerialize(using = PriceNumFormatSerializer.class)
    @ApiAnno(groups={BaseOrderFilter.RstsDetail.class})
    private BigDecimal realAmount;
    @ApiAnno(groups={BaseOrderFilter.RstsDetail.class})
    private int payState;
    @ApiAnno(groups={BaseOrderFilter.RstsDetail.class})
    private String payMethod;
    @ApiAnno(groups={BaseOrderFilter.RstsDetail.class})
    private String toOrderNo;
    @ApiAnno(groups={BaseOrderFilter.RstsDetail.class})
    private List<OrderDetailDTO> orderDetailDTOList;
    @ApiAnno(groups={BaseOrderFilter.RstsDetail.class})
    private String recommendReason;


    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public BigDecimal getRealAmount() {
        return realAmount;
    }

    public void setRealAmount(BigDecimal realAmount) {
        this.realAmount = realAmount;
    }

    public int getPayState() {
        return payState;
    }

    public void setPayState(int payState) {
        this.payState = payState;
    }

    public String getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(String payMethod) {
        this.payMethod = payMethod;
    }

    public String getToOrderNo() {
        return toOrderNo;
    }

    public void setToOrderNo(String toOrderNo) {
        this.toOrderNo = toOrderNo;
    }

    public List<OrderDetailDTO> getOrderDetailDTOList() {
        return orderDetailDTOList;
    }

    public void setOrderDetailDTOList(List<OrderDetailDTO> orderDetailDTOList) {
        this.orderDetailDTOList = orderDetailDTOList;
    }

    public String getRecommendReason() {
        return recommendReason;
    }

    public void setRecommendReason(String recommendReason) {
        this.recommendReason = recommendReason;
    }
}
