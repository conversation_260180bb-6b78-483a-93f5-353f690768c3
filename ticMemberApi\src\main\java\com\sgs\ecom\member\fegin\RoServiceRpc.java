package com.sgs.ecom.member.fegin;

import com.sgs.base.BaseBean;
import com.sgs.ecom.member.dto.user.UserDTO;
import com.sgs.ecom.member.service.rsts.interfaces.IRSTSApplicationService;
import com.sgs.ecom.member.service.util.rpc.interfaces.IRoRpcService;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import com.sgs.ecom.member.vo.rsts.VORstsQryOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/business/rpc.v2.member/ro")
public class RoServiceRpc  extends ControllerUtil {

	@Autowired
	private IRoRpcService iRoRpcService;
	@Resource
	private IRSTSApplicationService irstsApplicationService;

	@RequestMapping(value = "roOrderToInfo", method = { RequestMethod.POST })
	public ResultBody orderToInfo(
		@RequestParam(name = "orderNo", required = true) String orderNo) throws Exception{
		return ResultBody.newInstance(iRoRpcService.roOrderToInfo(orderNo));
	}

	@RequestMapping(value = "roForm", method = { RequestMethod.POST })
	public ResultBody roForm(
		@RequestParam(name = "orderNo", required = true) String orderNo) throws Exception{
		return ResultBody.newInstance(iRoRpcService.roForm(orderNo));
	}

	/**
	* @params [orderNo]
	* @return com.sgs.ecom.member.util.ResultBody
	* @description 用于获取打印报价单的数据
	* <AUTHOR> || created at 2023/12/26 15:15
	*/
	@RequestMapping(value = "roMinDTO", method = { RequestMethod.POST })
	public ResultBody roMinDTO(
			@RequestParam(name = "orderNo", required = true) String orderNo) throws Exception{
		return ResultBody.newInstance(iRoRpcService.roMinDTO(orderNo));
	}

	@RequestMapping(value = "roMinDTO1", method = { RequestMethod.POST })
	public ResultBody roMinDTO() throws Exception{
		return ResultBody.newInstance(iRoRpcService.roMinDTO("TIC1020240117161531DKSZ"));
	}


	@RequestMapping(value = "qryRSTSFormData", method = { RequestMethod.POST })
	public ResultBody qryRSTSFormData(
			@Validated(BaseBean.Query.class)
			@RequestBody VORstsQryOrder voRstsQryOrder ) throws Exception{
		UserDTO userDTO=new UserDTO(voRstsQryOrder.getUserId());
		userDTO.setUncheck(1);
		return ResultBody.newInstance(irstsApplicationService.qryFormData(userDTO,voRstsQryOrder,false));
	}


}
