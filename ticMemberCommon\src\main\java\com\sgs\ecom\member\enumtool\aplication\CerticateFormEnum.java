package com.sgs.ecom.member.enumtool.aplication;

public enum CerticateFormEnum {
    PAPER("paper", "1", "纸质证书"),
    ELECTRON("electron", "2", "电子证书"),
    ELECTRON_PAPER("electronPaper", "3", "电子证书+纸质证书"),
    ELECTRON_AND_PAPER("electronPaper", "3", "电子+纸质证书"),
    ;




    CerticateFormEnum(String name, String index, String nameCh) {
        this.name = name;
        this.index = index;
        this.nameCh = nameCh;
    }


    private String name;
    private String index;
    private String nameCh;

    public static String getIndexByNameCh(String nameCh) {
        for (CerticateFormEnum c : CerticateFormEnum.values()) {
            if (c.getNameCh().equals(nameCh)) {
                return c.getIndex();
            }
        }
        return "";
    }





    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }
}
