package com.sgs.ecom.member.question.controller;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.platform.annotation.AuthRequired;
import com.platform.util.CollectionService;
import com.platform.web.BaseAction;
import com.sgs.ecom.member.question.service.interfaces.IUserQuestionSV;
import com.sgs.ecom.member.util.ResultBody;

@RestController
@RequestMapping("/business/api.v2.question/question")
public class UserQuestionController extends BaseAction {

	private static IUserQuestionSV userQuestionSV = CollectionService.getService(IUserQuestionSV.class);
	
    /**   
	* @Function: createQuestion
	* @Description: 创建问卷
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2019-12-12
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2019-12-12  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "NULL", sign = false)
    @RequestMapping(value = "create", method = { RequestMethod.POST })
    public ResultBody createQuestion(@RequestBody String data) throws Exception {
		return ResultBody.newInstance(userQuestionSV.createQuestion(data));
	}
    
    /**   
	* @Function: qryQuestion
	* @Description: 查询问卷信息
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2019-12-12
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2019-12-12  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "NULL", sign = false)
    @RequestMapping(value = "qryQuestion", method = { RequestMethod.POST })
    public ResultBody qryQuestion(@RequestBody String data) throws Exception {
		return ResultBody.newInstance(userQuestionSV.qryQuestion(data));
	}
    
    /**   
	* @Function: backQuestuon
	* @Description: 返回上一章节
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2019-12-12
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2019-12-12  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "NULL", sign = false)
    @RequestMapping(value = "backQuestuon", method = { RequestMethod.POST })
    public ResultBody backQuestuon(@RequestBody String data) throws Exception {
    	userQuestionSV.backQuestuon(data);
		return ResultBody.success();
	}

    
    /**   
	* @Function: getDownUrl
	* @Description: 获取下载地址
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2018-09-23
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2018-09-23  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "NULL", sign = true)
    @RequestMapping(value = "getDownUrl", method = { RequestMethod.POST })
    public ResultBody getDownUrl(@RequestBody String data) throws Exception {
		return ResultBody.success(userQuestionSV.getDownUrl(data));
	}
}
