<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.user.dao.UserAccountManagerMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.entity.UserAccountManager" >
    <id column="MANAGER_ID" property="managerId" jdbcType="BIGINT" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="BU" property="bu" jdbcType="VARCHAR" />
    <result column="PERSON_CODE" property="personCode" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="INTEGER" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    MANAGER_ID, USER_ID, BU, PERSON_CODE, STATE, CREATE_DATE, STATE_DATE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from USER_ACCOUNT_MANAGER
    where MANAGER_ID = #{managerId,jdbcType=BIGINT}
  </select>
  <select id="qryUserManager" resultMap="BaseResultMap" >
    select 
    <include refid="Base_Column_List" />
    from USER_ACCOUNT_MANAGER
    where USER_ID = #{userId,jdbcType=BIGINT}
    and STATE = 1
    <if test="bu != null and bu != ''">
    	and BU = #{bu,jdbcType=VARCHAR}
    </if>
    limit 1
  </select>
</mapper>