package com.sgs.ecom.member.enumtool.bbc;

public enum AppFormCodeEnum {
	LEVEL_SERVICE("levelService", "检验服务"),
	LEVEL_TEST("levelTest", "检验水平"),
	INSPECTION_INFORMATION("inspectionInformation", "验货信息"),
	INSPECTION_DATA("inspectionData", "验货资料"),//图片
	INSPECTION_ADDR("inspectionAddr", "验货地址"),
	BUY_ADDRESS("buyAddress", "买家"),
	SUPPLIER("supplierAddress", "供应商"),
	FACTORY_ADDRESS("factoryAddress", "工厂"),
	FORM_MEMO("formMemo", "申请表备注"),
	LAB_INFO("labInfo", "实验室信息"),
	SAMPLE_NAME_FORM("sampleNameForm", "样品信息"),
	METHODS_USED("methodsUsed", "采用标准/方法"),
	RSTS_CERTIFICATE("identificationInfo", "认证信息"),
	IS_SHANGHAI_UPLOAD_PROJECT("isShanghaiUploadProject", "是否属于上海市环境监测社会化服务监管系统上传项目"),//
	RSTS_SDS_("isShanghaiUploadProject", "是否属于上海市环境监测社会化服务监管系统上传项目"),//


	;

	AppFormCodeEnum(String name,  String nameCh) {
		this.name = name;
		this.nameCh = nameCh;
	}

	public static Boolean getAddressFlg(String code) {
		if(BUY_ADDRESS.getName().equals(code) || SUPPLIER.getName().equals(code)||FACTORY_ADDRESS.getName().equals(code)){
			return true;
		}
		return false;
	}

	private String name;
	private String nameCh;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}


	public String getNameCh() {
		return nameCh;
	}

	public void setNameCh(String nameCh) {
		this.nameCh = nameCh;
	}
}
