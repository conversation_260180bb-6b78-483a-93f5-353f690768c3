package com.sgs.ecom.member.dto.oiq.applicationInfo;

import com.platform.annotation.ApiAnno;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.dto.application.OrderApplicationAttrDTO;
import com.sgs.ecom.member.enumtool.aplication.OiqFormEnum;

import java.util.Map;
import lombok.Data;

@Data
public class OiqApplicationAttrDTO {
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private Integer isDispute=0;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String isDisputeMemo="";
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private Integer defaultEmailFlg=0;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String qualifications="";
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private Integer salesEmailFlg=0;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String qualificationsShow="";
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private Integer invoiceFlg=0;

    public OiqApplicationAttrDTO() {
    }

    public OiqApplicationAttrDTO(Map<String, OrderApplicationAttrDTO> attrDTOMap) {
        OrderApplicationAttrDTO attrDTOInt= new OrderApplicationAttrDTO("0");
        OrderApplicationAttrDTO attrDTOStr= new OrderApplicationAttrDTO("");
        this.isDispute =Integer.parseInt(attrDTOMap.getOrDefault(OiqFormEnum.IS_DISPUTE.getName(),attrDTOInt).getAttrValue());
        this.isDisputeMemo = attrDTOMap.getOrDefault(OiqFormEnum.IS_DISPUTE_MEMO.getName(),attrDTOStr).getAttrValue();
        this.defaultEmailFlg = Integer.parseInt(attrDTOMap.getOrDefault(OiqFormEnum.DEFAULT_EMAIL_FLG.getName(),attrDTOInt).getAttrValue());
        this.salesEmailFlg=Integer.parseInt(attrDTOMap.getOrDefault(OiqFormEnum.SALES_EMAIL_FLG.getName(),attrDTOInt).getAttrValue());
        this.qualifications= attrDTOMap.getOrDefault(OiqFormEnum.QUALIFICATIONS.getName(),attrDTOStr).getAttrValue();
        this.invoiceFlg=Integer.parseInt(attrDTOMap.getOrDefault(OiqFormEnum.INVOICE_FLG.getName(),attrDTOInt).getAttrValue());
    }
}
