package com.sgs.ecom.member.enumtool.order;

import org.apache.commons.lang.StringUtils;

public enum FromSourceEnum {
    ORDER_CREATE("orderCreate", "客服创建"),
    MEMBER_CREATE_("memberCreate", "客户创建"),//历史客户创建 割接的
    ORDER_LIST("orderlist", "询价单列表"),
    NAVIGATION("navigation", "全局导航"),
    MAIL("mail", "客户邮件"),
    HOME_BANNER_SIDE("homeBannerSide", "首页右侧"),
    HOME_FLOOR_PLAN("homeFloorPlan", "首页楼层"),

    //ro
    SELF_CREATE("selfCreate", "客户自主下单"),
    AGAIN_CREATE("againCreate", "客户再来一单"),
    CRM_SELF_CREATE("crmSelfCreate", "客服自主下单"),
    CRM_AGAIN_CREATE("crmAgainCreate", "客服再来一单"),
    MINI_PROGRAM_PERSON_CENTER("miniProgramPersonCenter", "小程序个人中心"),

    ;

    private String fromSource;
    private String fromSourceShow;

    FromSourceEnum(String fromSource, String fromSourceShow) {
        this.fromSource = fromSource;
        this.fromSourceShow = fromSourceShow;
    }

    public static String getNameCh(String fromSource) {
        if(StringUtils.isBlank(fromSource)){
            return "客户创建";
        }


        for (FromSourceEnum c :FromSourceEnum.values()) {
            if (c.getFromSource().equals(fromSource)) {
                return c.getFromSourceShow();
            }
        }
        return "";
    }

    public static String getCrmShow(String fromSource){
        if("selfCreate".equals(fromSource)){
            return "crmSelfCreate";
        }
        if("againCreate".equals(fromSource)){
            return "crmAgainCreate";
        }
        return "orderCreate";
    }

    public String getFromSource() {
        return fromSource;
    }

    public void setFromSource(String fromSource) {
        this.fromSource = fromSource;
    }

    public String getFromSourceShow() {
        return fromSourceShow;
    }

    public void setFromSourceShow(String fromSourceShow) {
        this.fromSourceShow = fromSourceShow;
    }
}
