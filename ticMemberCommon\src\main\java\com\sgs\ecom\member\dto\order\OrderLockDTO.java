package com.sgs.ecom.member.dto.order;

import com.sgs.ecom.member.dto.OrderBaseInfoCheckDTO;
import com.sgs.ecom.member.util.order.UseDateUtil;

import java.util.Date;

public class OrderLockDTO {
    public static final String DELETE_FLG = "deleteFlg";
    public static final String PAY_FLG = "payFlg";
    public static final String MONTH_PAY_FLG = "monthPayFlg";
    public static final String RECEIVED_FLG = "receivedFlg";


    private Long orderId;
    private int oldState;
    private int newState;
    private String closeCode;
    private String closeReason;
    private String stateDate;
    private int hisState;
    private  Integer isRead;

    private String deleteFlg;
    private int oldDelete;
    private int newDelete;

    private String payFlg;
    private int oldPayState;
    private int newPayState;

    private String monthPayFlg;
    private int oldMonthPay;
    private int newMonthPay;

    private String isPayReceivedFlg;
    private int oldReceived;
    private int newReceived;

    public OrderLockDTO() {
    }

    /**
    *@Function: OrderLockDTO
    *@Description 基础锁的信息
    *@param: [orderBaseInfoCheckDTO]
    *@author: Xiwei_Qiu @date: 2022/6/13 @version:
    **/
    public OrderLockDTO(OrderBaseInfoCheckDTO orderBaseInfoCheckDTO) {
        this.orderId = orderBaseInfoCheckDTO.getOrderId();
        this.oldState = orderBaseInfoCheckDTO.getState();
        this.newState = orderBaseInfoCheckDTO.getState();
        this.stateDate= UseDateUtil.getDateString(new Date());
    }







    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public int getOldState() {
        return oldState;
    }

    public void setOldState(int oldState) {
        this.oldState = oldState;
    }

    public int getNewState() {
        return newState;
    }

    public void setNewState(int newState) {
        this.newState = newState;
    }

    public String getCloseCode() {
        return closeCode;
    }

    public void setCloseCode(String closeCode) {
        this.closeCode = closeCode;
    }

    public String getCloseReason() {
        return closeReason;
    }

    public void setCloseReason(String closeReason) {
        this.closeReason = closeReason;
    }

    public int getOldDelete() {
        return oldDelete;
    }

    public void setOldDelete(int oldDelete) {
        this.oldDelete = oldDelete;
    }

    public int getNewDelete() {
        return newDelete;
    }

    public void setNewDelete(int newDelete) {
        this.newDelete = newDelete;
    }

    public String getDeleteFlg() {
        return deleteFlg;
    }

    public void setDeleteFlg(String deleteFlg) {
        this.deleteFlg = deleteFlg;
    }

    public String getPayFlg() {
        return payFlg;
    }

    public void setPayFlg(String payFlg) {
        this.payFlg = payFlg;
    }

    public int getOldPayState() {
        return oldPayState;
    }

    public void setOldPayState(int oldPayState) {
        this.oldPayState = oldPayState;
    }

    public int getNewPayState() {
        return newPayState;
    }

    public void setNewPayState(int newPayState) {
        this.newPayState = newPayState;
    }

    public String getMonthPayFlg() {
        return monthPayFlg;
    }

    public void setMonthPayFlg(String monthPayFlg) {
        this.monthPayFlg = monthPayFlg;
    }

    public int getOldMonthPay() {
        return oldMonthPay;
    }

    public void setOldMonthPay(int oldMonthPay) {
        this.oldMonthPay = oldMonthPay;
    }

    public int getNewMonthPay() {
        return newMonthPay;
    }

    public void setNewMonthPay(int newMonthPay) {
        this.newMonthPay = newMonthPay;
    }

    public String getIsPayReceivedFlg() {
        return isPayReceivedFlg;
    }

    public void setIsPayReceivedFlg(String isPayReceivedFlg) {
        this.isPayReceivedFlg = isPayReceivedFlg;
    }

    public int getOldReceived() {
        return oldReceived;
    }

    public void setOldReceived(int oldReceived) {
        this.oldReceived = oldReceived;
    }

    public int getNewReceived() {
        return newReceived;
    }

    public void setNewReceived(int newReceived) {
        this.newReceived = newReceived;
    }

    public int getHisState() {
        return hisState;
    }

    public void setHisState(int hisState) {
        this.hisState = hisState;
    }

    public String getStateDate() {
        return stateDate;
    }

    public void setStateDate(String stateDate) {
        this.stateDate = stateDate;
    }

    public Integer getIsRead() {
        return isRead;
    }

    public void setIsRead(Integer isRead) {
        this.isRead = isRead;
    }
}
