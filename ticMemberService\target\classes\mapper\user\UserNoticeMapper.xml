<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.user.dao.UserNoticeMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.entity.user.UserNotice" >
    <id column="NOTICE_ID" property="noticeId" jdbcType="BIGINT" />
    <result column="NOTICE_TYPE" property="noticeType" jdbcType="TINYINT" />
    <result column="BUSINESS_TYPE" property="businessType" jdbcType="VARCHAR" />
    <result column="BUSINESS_CODE" property="businessCode" jdbcType="VARCHAR" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="BU" property="bu" jdbcType="VARCHAR" />
    <result column="DATE_TYPE" property="dateType" jdbcType="INTEGER" />
    <result column="NOTICE_INTERVAL" property="noticeInterval" jdbcType="INTEGER" />
    <result column="STATE" property="state" jdbcType="TINYINT" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    NOTICE_ID, NOTICE_TYPE, BUSINESS_TYPE, BUSINESS_CODE, USER_ID, BU, DATE_TYPE, NOTICE_INTERVAL, 
    STATE, CREATE_DATE, STATE_DATE
  </sql>

  <select id="qryUserNotice" resultMap="BaseResultMap" parameterType="com.sgs.ecom.member.vo.VOUserNotice" >
    select 
    <include refid="Base_Column_List" />
    from USER_NOTICE
    where USER_ID = #{userId,jdbcType=BIGINT}
    AND BU = #{bu,jdbcType=VARCHAR}
    AND BUSINESS_TYPE = #{businessType,jdbcType=VARCHAR}
    AND BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR}
  </select>

</mapper>