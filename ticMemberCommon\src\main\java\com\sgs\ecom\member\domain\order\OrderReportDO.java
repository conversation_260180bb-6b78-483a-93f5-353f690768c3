package com.sgs.ecom.member.domain.order;

import com.alibaba.fastjson.JSON;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseCopyObj;
import com.sgs.ecom.member.dto.detail.OrderReportDTO;
import com.sgs.ecom.member.dto.dml.DmlOrderInvoiceDTO;
import com.sgs.ecom.member.dto.oiq.OiqOrderReqDTO;
import com.sgs.ecom.member.entity.order.OrderReport;
import com.sgs.ecom.member.enumtool.aplication.OiqReportFormEnum;
import com.sgs.ecom.member.enumtool.aplication.ReportLuaEnum;
import com.sgs.ecom.member.enumtool.aplication.ReportMethodEnum;
import com.sgs.ecom.member.enumtool.dml.BusinessCodeEnum;
import com.sgs.ecom.member.request.oiq.OiqApplicationReq;
import com.sgs.ecom.member.request.oiq.OiqReportReq;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;

public class OrderReportDO extends OrderReport {

    private OrderReport orderReport = new OrderReport();
    private BaseCopyObj baseCopy = new BaseCopyObj();


    public OrderReport getReportByReq(OiqReportReq oiqReportReq, String orderNo, OiqOrderReqDTO oiqOrderReqDTO){
        if(ValidationUtil.isEmpty(oiqReportReq)){
            return null;
        }

        //同申请方 付款方
        if(oiqReportReq.getReportTitleType()!=null){
            if(oiqReportReq.getReportTitleType()==0){
                OiqApplicationReq applicationReq=oiqOrderReqDTO.getOiqOrderReq().getApplication();
                oiqReportReq.setReportCompanyNameCn(applicationReq.getCompanyNameCn());
                oiqReportReq.setReportCompanyNameEn(applicationReq.getCompanyNameEn());
                oiqReportReq.setReportAddressCn(applicationReq.getCompanyAddressCn());
                oiqReportReq.setReportAddressEn(applicationReq.getCompanyAddressEn());
            }
            if(oiqReportReq.getReportTitleType()==1) {
                DmlOrderInvoiceDTO dmlOrderInvoiceDTO=oiqOrderReqDTO.getDmlMainReqDTO().getOrderInvoiceDTO();
                if(!ValidationUtil.isEmpty(dmlOrderInvoiceDTO) && dmlOrderInvoiceDTO.getInvoiceType()!=2){
                    oiqReportReq.setReportCompanyNameCn(dmlOrderInvoiceDTO.getInvoiceTitle());
                    oiqReportReq.setReportAddressCn(dmlOrderInvoiceDTO.getRegAddress());
                }
            }
        }

        baseCopy.copyWithNull(orderReport,oiqReportReq);
        orderReport.setReportLua(ReportLuaEnum.getNameChByName(oiqReportReq.getReportLuaCode()));
        orderReport.setReportForm(OiqReportFormEnum.getNameCh(oiqReportReq.getReportFormCode()));
        if(oiqOrderReqDTO.getPortal() && BusinessCodeEnum.toMin(oiqOrderReqDTO.getBusinessLineDTO().getPlatformCode())){
            orderReport.setReportLua(ReportLuaEnum.getMinNameChByName(oiqReportReq.getReportLuaCode()));
            orderReport.setReportForm(OiqReportFormEnum.getMinNameCh(oiqReportReq.getReportFormCode()));
        }



        if(StringUtils.isNotBlank(oiqReportReq.getReportFormCode()) && oiqReportReq.getReportFormCode().toLowerCase().equals("other")){
            orderReport.setReportForm(oiqReportReq.getReportFormValue());
        }


        orderReport.setReportPerson(JSON.toJSONString(new ArrayList<>()));
        orderReport.setOrderNo(orderNo);
        orderReport.setState(1);
        if(oiqReportReq.getReportMethod()!=null && ReportMethodEnum.OTHER.getIndex()!=oiqReportReq.getReportMethod()){
            orderReport.setReportMethodMemo("");
        }
        return this.orderReport;
    }




    public void entityToDTO(OrderReportDTO orderReportDTO, OrderReport orderReport){
        baseCopy.copyWithNull(orderReportDTO,orderReport);

    }
}
