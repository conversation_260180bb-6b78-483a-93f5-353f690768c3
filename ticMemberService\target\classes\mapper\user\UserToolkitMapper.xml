<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.user.dao.UserToolkitMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.bo.UserToolkit" >
    <id column="TOOL_ID" property="toolId" jdbcType="BIGINT" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="TOOL_NAME" property="toolName" jdbcType="VARCHAR" />
    <result column="TOOL_CODE" property="toolCode" jdbcType="VARCHAR" />
    <result column="TOOL_ICON" property="toolIcon" jdbcType="VARCHAR" />
    <result column="TOOL_DESC" property="toolDesc" jdbcType="VARCHAR" />
    <result column="TOOL_MEMO" property="toolMemo" jdbcType="VARCHAR" />
    <result column="EFF_DATE" property="effDate" jdbcType="DATE" />
    <result column="EXP_DATE" property="expDate" jdbcType="DATE" />
    <result column="USE_NUMS" property="useNums" jdbcType="INTEGER" />
    <result column="STATE" property="state" jdbcType="INTEGER" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    TOOL_ID, USER_ID, TOOL_NAME, TOOL_CODE, TOOL_ICON, TOOL_DESC, TOOL_MEMO, EFF_DATE, 
    EXP_DATE, USE_NUMS, STATE, CREATE_DATE, STATE_DATE
  </sql>
  <sql id="baseQueryWhere">
    <if test="userId != null">
      and  USER_ID = #{userId}
    </if>
    <if test="toolCode != null">
      and  TOOL_CODE = #{toolCode}
    </if>
    <if test="state != null">
      and   STATE = #{state}
    </if>

  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from USER_TOOLKIT
    where TOOL_ID = #{toolId,jdbcType=BIGINT}
  </select>


    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from USER_TOOLKIT
    where TOOL_ID = #{toolId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.sgs.ecom.member.bo.UserToolkit" >
    insert into USER_TOOLKIT (TOOL_ID, USER_ID, TOOL_NAME,
      TOOL_CODE, TOOL_ICON, TOOL_DESC, 
      TOOL_MEMO, EFF_DATE, EXP_DATE, 
      USE_NUMS, STATE, CREATE_DATE, 
      STATE_DATE)
    values (#{toolId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{toolName,jdbcType=VARCHAR}, 
      #{toolCode,jdbcType=VARCHAR}, #{toolIcon,jdbcType=VARCHAR}, #{toolDesc,jdbcType=VARCHAR}, 
      #{toolMemo,jdbcType=VARCHAR}, #{effDate,jdbcType=DATE}, #{expDate,jdbcType=DATE}, 
      #{useNums,jdbcType=INTEGER}, #{state,jdbcType=INTEGER}, #{createDate,jdbcType=TIMESTAMP}, 
      #{stateDate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.ecom.member.bo.UserToolkit" >
    insert into USER_TOOLKIT
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="toolId != null" >
        TOOL_ID,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
      <if test="toolName != null" >
        TOOL_NAME,
      </if>
      <if test="toolCode != null" >
        TOOL_CODE,
      </if>
      <if test="toolIcon != null" >
        TOOL_ICON,
      </if>
      <if test="toolDesc != null" >
        TOOL_DESC,
      </if>
      <if test="toolMemo != null" >
        TOOL_MEMO,
      </if>
      <if test="effDate != null" >
        EFF_DATE,
      </if>
      <if test="expDate != null" >
        EXP_DATE,
      </if>
      <if test="useNums != null" >
        USE_NUMS,
      </if>
      <if test="state != null" >
        STATE,
      </if>
      <if test="createDate != null" >
        CREATE_DATE,
      </if>
      <if test="stateDate != null" >
        STATE_DATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="toolId != null" >
        #{toolId,jdbcType=BIGINT},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="toolName != null" >
        #{toolName,jdbcType=VARCHAR},
      </if>
      <if test="toolCode != null" >
        #{toolCode,jdbcType=VARCHAR},
      </if>
      <if test="toolIcon != null" >
        #{toolIcon,jdbcType=VARCHAR},
      </if>
      <if test="toolDesc != null" >
        #{toolDesc,jdbcType=VARCHAR},
      </if>
      <if test="toolMemo != null" >
        #{toolMemo,jdbcType=VARCHAR},
      </if>
      <if test="effDate != null" >
        #{effDate,jdbcType=DATE},
      </if>
      <if test="expDate != null" >
        #{expDate,jdbcType=DATE},
      </if>
      <if test="useNums != null" >
        #{useNums,jdbcType=INTEGER},
      </if>
      <if test="state != null" >
        #{state,jdbcType=INTEGER},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        #{stateDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.ecom.member.bo.UserToolkit" >
    update USER_TOOLKIT
    <set >
      <if test="userId != null" >
        USER_ID = #{userId,jdbcType=BIGINT},
      </if>
      <if test="toolName != null" >
        TOOL_NAME = #{toolName,jdbcType=VARCHAR},
      </if>
      <if test="toolCode != null" >
        TOOL_CODE = #{toolCode,jdbcType=VARCHAR},
      </if>
      <if test="toolIcon != null" >
        TOOL_ICON = #{toolIcon,jdbcType=VARCHAR},
      </if>
      <if test="toolDesc != null" >
        TOOL_DESC = #{toolDesc,jdbcType=VARCHAR},
      </if>
      <if test="toolMemo != null" >
        TOOL_MEMO = #{toolMemo,jdbcType=VARCHAR},
      </if>
      <if test="effDate != null" >
        EFF_DATE = #{effDate,jdbcType=DATE},
      </if>
      <if test="expDate != null" >
        EXP_DATE = #{expDate,jdbcType=DATE},
      </if>
      <if test="useNums != null" >
        USE_NUMS = #{useNums,jdbcType=INTEGER},
      </if>
      <if test="state != null" >
        STATE = #{state,jdbcType=INTEGER},
      </if>
      <if test="createDate != null" >
        CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        STATE_DATE = #{stateDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where TOOL_ID = #{toolId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.ecom.member.bo.UserToolkit" >
    update USER_TOOLKIT
    set USER_ID = #{userId,jdbcType=BIGINT},
      TOOL_NAME = #{toolName,jdbcType=VARCHAR},
      TOOL_CODE = #{toolCode,jdbcType=VARCHAR},
      TOOL_ICON = #{toolIcon,jdbcType=VARCHAR},
      TOOL_DESC = #{toolDesc,jdbcType=VARCHAR},
      TOOL_MEMO = #{toolMemo,jdbcType=VARCHAR},
      EFF_DATE = #{effDate,jdbcType=DATE},
      EXP_DATE = #{expDate,jdbcType=DATE},
      USE_NUMS = #{useNums,jdbcType=INTEGER},
      STATE = #{state,jdbcType=INTEGER},
      CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      STATE_DATE = #{stateDate,jdbcType=TIMESTAMP}
    where TOOL_ID = #{toolId,jdbcType=BIGINT}
  </update>
  <select id="selectListByMap" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from USER_TOOLKIT
    <include refid="baseQueryWhere"> </include>
  </select>
</mapper>