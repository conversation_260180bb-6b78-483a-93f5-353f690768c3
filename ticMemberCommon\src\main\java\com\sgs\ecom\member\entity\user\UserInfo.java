package com.sgs.ecom.member.entity.user;

import com.platform.annotation.BeanAnno;
import com.platform.annotation.ExplainAnno;
import com.sgs.base.BaseBean;

import javax.persistence.Id;
import javax.validation.constraints.Size;


public class UserInfo {



 	private String userNick;

 	private String levelName;

 	private Integer userSex;

 	private String userEmail;

 	private String stateDate;
	@Id
 	private Long userId;

 	private Integer state;

 	private String regIp;

 	private String userName;

 	private Long uId;

 	private Integer levelId;

 	private String userPhone;

 	private String createDate;

 	private String bbcPwd;

 	private Integer isBlack;

 	private String userPwd;

	private Integer isTest=0;


 	public void setUserNick(String userNick){
 		 this.userNick=userNick;
 	}
 	public String getUserNick(){
 		 return this.userNick;
 	}
 
 	 
 	public void setLevelName(String levelName){
 		 this.levelName=levelName;
 	}
 	public String getLevelName(){
 		 return this.levelName;
 	}
 
 	 
 	public void setUserSex(Integer userSex){
 		 this.userSex=userSex;
 	}
 	public Integer getUserSex(){
 		 return this.userSex;
 	}
 
 	 
 	public void setUserEmail(String userEmail){
 		 this.userEmail=userEmail;
 	}
 	public String getUserEmail(){
 		 return this.userEmail;
 	}
 
 	 
 	public void setStateDate(String stateDate){
 		 this.stateDate=stateDate;
 	}
 	public String getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setUserId(Long userId){
 		 this.userId=userId;
 	}
 	public Long getUserId(){
 		 return this.userId;
 	}
 
 	 
 	public void setState(Integer state){
 		 this.state=state;
 	}
 	public Integer getState(){
 		 return this.state;
 	}
 
 	 
 	public void setRegIp(String regIp){
 		 this.regIp=regIp;
 	}
 	public String getRegIp(){
 		 return this.regIp;
 	}
 
 	 
 	public void setUserName(String userName){
 		 this.userName=userName;
 	}
 	public String getUserName(){
 		 return this.userName;
 	}
 
 	 
 	public void setUId(Long uId){
 		 this.uId=uId;
 	}
 	public Long getUId(){
 		 return this.uId;
 	}
 
 	 
 	public void setLevelId(Integer levelId){
 		 this.levelId=levelId;
 	}
 	public Integer getLevelId(){
 		 return this.levelId;
 	}
 
 	 
 	public void setUserPhone(String userPhone){
 		 this.userPhone=userPhone;
 	}
 	public String getUserPhone(){
 		 return this.userPhone;
 	}
 
 	 
 	public void setCreateDate(String createDate){
 		 this.createDate=createDate;
 	}
 	public String getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setBbcPwd(String bbcPwd){
 		 this.bbcPwd=bbcPwd;
 	}
 	public String getBbcPwd(){
 		 return this.bbcPwd;
 	}
 
 	 
 	public void setIsBlack(Integer isBlack){
 		 this.isBlack=isBlack;
 	}
 	public Integer getIsBlack(){
 		 return this.isBlack;
 	}
 
 	 
 	public void setUserPwd(String userPwd){
 		 this.userPwd=userPwd;
 	}
 	public String getUserPwd(){
 		 return this.userPwd;
 	}

	public Integer getIsTest() {
		return isTest;
	}

	public void setIsTest(Integer isTest) {
		this.isTest = isTest;
	}
}