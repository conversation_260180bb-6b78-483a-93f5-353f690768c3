package com.sgs.ecom.member.enumtool.order;

public enum OrderDeleteEnum {
    NO("no", 0, "正常"),
    DELETE("delete", 1, "删除的数据"),
    NOSEE("noSee", 2, "回收站"),
            ;

    OrderDeleteEnum(String name, int index, String nameCh) {
        this.name = name;
        this.index = index;
        this.nameCh = nameCh;
    }



    private String name;
    private int index;
    private String nameCh;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }
}
