package com.sgs.ecom.member.bo; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import com.platform.annotation.CharacterVaild; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import com.platform.util.json.PriceFormatSerializer; 
import com.platform.annotation.BeanAnno; 

public class BOAcctBalanceLog{
 
 	public static final String SEQUENCE = "LOG_ID"; 
  
 	public static final String BO_SQL = "{ACCT_BALANCE_LOG}"; 
 
 	public static final String OWNER ="member";

 	public static final String INOUT_TYPE="inoutType";
 	public static final String STATE_DATE="stateDate";
 	public static final String USER_ID="userId";
 	public static final String STATE="state";
 	public static final String TRANS_TYPE="transType";
 	public static final String OUT_AMOUNT="outAmount";
 	public static final String IN_AMOUNT="inAmount";
 	public static final String BALANCE="balance";
 	public static final String CREATE_DATE="createDate";
 	public static final String BALANCE_TYPE="balanceType";
 	public static final String AMOUNT="amount";
 	public static final String BALANCE_ID="balanceId";
 	public static final String OPP_FLOW_NUM="oppFlowNum";
 	public static final String USER_TYPE="userType";
 	public static final String LOG_ID="logId";
 	public static final String MEMO="memo";

 	@BeanAnno("INOUT_TYPE")
 	private long inoutType;
 	@BeanAnno("STATE_DATE")
 	private Timestamp stateDate;
 	@BeanAnno("USER_ID")
 	private long userId;
 	@BeanAnno("STATE")
 	private long state;
 	@BeanAnno("TRANS_TYPE")
 	private long transType;
 	@BeanAnno("OUT_AMOUNT")
 	private long outAmount;
 	@BeanAnno("IN_AMOUNT")
 	private long inAmount;
 	@BeanAnno("BALANCE")
 	private long balance;
 	@BeanAnno("CREATE_DATE")
 	private Timestamp createDate;
 	@BeanAnno("BALANCE_TYPE")
 	private long balanceType;
 	@BeanAnno("AMOUNT")
 	private long amount;
 	@BeanAnno("BALANCE_ID")
 	private long balanceId;
 	@BeanAnno("OPP_FLOW_NUM")
 	private String oppFlowNum;
 	@BeanAnno("USER_TYPE")
 	private int userType;
 	@BeanAnno("LOG_ID")
 	private long logId;
 	@BeanAnno("MEMO")
 	private String memo;

 	public void setInoutType(long inoutType){
 		 this.inoutType=inoutType;
 	}
 	public long getInoutType(){
 		 return this.inoutType;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setUserId(long userId){
 		 this.userId=userId;
 	}
 	public long getUserId(){
 		 return this.userId;
 	}
 
 	 
 	public void setState(long state){
 		 this.state=state;
 	}
 	public long getState(){
 		 return this.state;
 	}
 
 	 
 	public void setTransType(long transType){
 		 this.transType=transType;
 	}
 	public long getTransType(){
 		 return this.transType;
 	}
 
 	 
 	public void setOutAmount(long outAmount){
 		 this.outAmount=outAmount;
 	}
 	@JsonSerialize(using = PriceFormatSerializer.class) 
 	public long getOutAmount(){
 		 return this.outAmount;
 	}
 
 	 
 	public void setInAmount(long inAmount){
 		 this.inAmount=inAmount;
 	}
 	@JsonSerialize(using = PriceFormatSerializer.class) 
 	public long getInAmount(){
 		 return this.inAmount;
 	}
 
 	 
 	public void setBalance(long balance){
 		 this.balance=balance;
 	}
 	public long getBalance(){
 		 return this.balance;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setBalanceType(long balanceType){
 		 this.balanceType=balanceType;
 	}
 	public long getBalanceType(){
 		 return this.balanceType;
 	}
 
 	 
 	public void setAmount(long amount){
 		 this.amount=amount;
 	}
 	@JsonSerialize(using = PriceFormatSerializer.class) 
 	public long getAmount(){
 		 return this.amount;
 	}
 
 	 
 	public void setBalanceId(long balanceId){
 		 this.balanceId=balanceId;
 	}
 	public long getBalanceId(){
 		 return this.balanceId;
 	}
 
 	 
 	@CharacterVaild(len = 30) 
 	public void setOppFlowNum(String oppFlowNum){
 		 this.oppFlowNum=oppFlowNum;
 	}
 	public String getOppFlowNum(){
 		 return this.oppFlowNum;
 	}
 
 	 
 	public void setUserType(int userType){
 		 this.userType=userType;
 	}
 	public int getUserType(){
 		 return this.userType;
 	}
 
 	 
 	public void setLogId(long logId){
 		 this.logId=logId;
 	}
 	public long getLogId(){
 		 return this.logId;
 	}
 
 	 
 	@CharacterVaild(len = 1000) 
 	public void setMemo(String memo){
 		 this.memo=memo;
 	}
 	public String getMemo(){
 		 return this.memo;
 	}
 
 	 
}