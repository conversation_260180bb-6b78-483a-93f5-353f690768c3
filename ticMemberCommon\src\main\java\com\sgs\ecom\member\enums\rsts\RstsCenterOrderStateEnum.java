package com.sgs.ecom.member.enums.rsts;

import com.sgs.ecom.member.dto.OrderBaseInfoDTO;
import com.sgs.ecom.member.dto.rsts.RstsPersonCenterDTO;
import com.sgs.ecom.member.enums.PreOrderStatus;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collector;
import java.util.stream.Collectors;

public enum RstsCenterOrderStateEnum {


    WAITAPPLY("待提交申请表", 11,0),
    NO_PAY("待支付", 60,1),
    WAITSEND("待寄送样品", 12,2),
    WAITEXAMINE("待实验室审核", 13,3),
    SERVICE("检测中", 14,4),
    END("已完成", 80,5),;

    // 成员变量
    private String name;
    private int index;
    private int sortShow;
    // 构造方法
    private RstsCenterOrderStateEnum(String name, int index,int sortShow) {
        this.name = name;
        this.index = index;
        this.sortShow = sortShow;
    }

    // 普通方法
    public static String getName(int index) {
        for (RstsCenterOrderStateEnum c : RstsCenterOrderStateEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }
    public static List<RstsPersonCenterDTO>  sortShowList() {
        List<RstsPersonCenterDTO> list = new ArrayList<>();
        for (RstsCenterOrderStateEnum c : RstsCenterOrderStateEnum.values()) {
            RstsPersonCenterDTO rstsPersonCenterDTO = new RstsPersonCenterDTO();
            rstsPersonCenterDTO.setState(c.getIndex());
            rstsPersonCenterDTO.setSortShow(c.getSortShow());
            list.add(rstsPersonCenterDTO);
        }
        return list.stream().sorted(Comparator.comparing(RstsPersonCenterDTO::getSortShow)).collect(Collectors.toList());
    }


    // get set 方法
    public String getName() {
        return name;
    }

    public int getIndex() {
        return index;
    }

    public int getSortShow() {
        return sortShow;
    }
}
