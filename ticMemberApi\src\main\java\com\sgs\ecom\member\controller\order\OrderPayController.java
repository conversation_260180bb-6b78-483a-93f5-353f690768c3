package com.sgs.ecom.member.controller.order;

import com.alibaba.fastjson.JSONObject;
import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.base.BaseBean;
import com.sgs.ecom.member.request.*;
import com.sgs.ecom.member.request.rpc.PayReq;
import com.sgs.ecom.member.service.order.interfaces.IOrderPayService;
import com.sgs.ecom.member.service.pay.interfaces.IPayService;
import com.sgs.ecom.member.service.util.interfaces.IPayUtilService;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.order/pay")
public class OrderPayController extends ControllerUtil {

    @Autowired
    private IOrderPayService orderPayService;
    @Autowired
    private IPayUtilService payUtilService;
    @Autowired
    private IPayService iPayService;
    /**
    *@Function: payment
    *@Description 
    *@param: [orderNoReq, token]
    *@author: Xiwei_Qiu @date: 2022/1/24 @version: 
    **/
//    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "payment", method = { RequestMethod.POST })
    public ResultBody payment(
            @RequestHeader(value = "lang",required = false) String lua,
            @RequestHeader(value="accessToken") String token,
            @RequestBody PayReq payReq) throws Exception {
        lua= StringUtils.isBlank(lua)?"zh_CN":lua;
        return ResultBody.newInstance(payUtilService.selectPayment(payReq,getUserDTO(token),token,lua));
    }

    /**
     *@Function: bank
     *@Description
     *@param: [orderNoReq, token]
     *@author: Xiwei_Qiu @date: 2022/1/24 @version:
     **/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "bank", method = { RequestMethod.POST })
    public ResultBody bank(
        @RequestBody OrderPayReq orderPayReq,
        @RequestHeader(value="accessToken") String token) throws Exception {
        return ResultBody.newInstance(payUtilService.selectBank(orderPayReq,getUserDTO(token),token));
    }




    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "toPay", method = { RequestMethod.POST })
    public ResultBody toPay(
            @RequestHeader(value="accessToken") String token,
            @Validated(BaseBean.Query.class)
            @RequestBody OrderPayTransReq orderPayTransReq) throws Exception {
        return ResultBody.newInstance(JSONObject.parseObject(orderPayService.toPay(orderPayTransReq,getUserDTO(token),token)));
    }

    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "refund", method = { RequestMethod.POST })
    public ResultBody refund(
            @RequestBody OrderNoReq orderNoReq,
            @RequestHeader(value="accessToken") String token) throws Exception {
        orderPayService.updateRefund(orderNoReq, getUserDTO(token));
        return ResultBody.success();
    }

    /**
    *@Function: orderList
    *@Description
    *@param: [orderNoReq, token]
    *@author: Xiwei_Qiu @date: 2022/5/28 @version:
    **/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "orderList", method = { RequestMethod.POST })
    public ResultBody orderList(@Validated(BaseBean.Query.class)
        @RequestBody PayReq payReq,
        @RequestHeader(value="accessToken") String token) throws Exception {
        return ResultBody.newInstance(orderPayService.orderList(payReq,getUserDTO(token)));
    }
    
    /**
     *@Function: customerRefund
     *@Description 客户发起退款申请
     *@param: [orderRefundReq, token]
     *@author: sundeqing @date: 2022/5/10 @version: 
     **/
     @HystrixCommand
     @AuthRequired(login = "SSO", sign = true)
     @RequestMapping(value = "customerRefund", method = { RequestMethod.POST })
     public ResultBody customerRefund(
             @RequestBody OrderRefundReq orderRefundReq,
             @RequestHeader(value="accessToken") String token) throws Exception {
    	 orderPayService.customerRefund(orderRefundReq, getUserDTO(token));
         return ResultBody.success();
     }


    /**
     *@Function: info
     *@Description 支付和补差价支付及补差价数据
     *@param: [token, orderIdReq]
     *@author: Xiwei_Qiu @date: 2022/5/26 @version:
     **/
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "payInfo", method = { RequestMethod.POST })
    public ResultBody info (
        @RequestBody OrderNoReq orderNoReq,
        @RequestHeader(value="accessToken") String token)throws Exception{
        return ResultBody.newInstance(payUtilService.payInfo(orderNoReq,getUserDTO(token)));
    }

    /**
    *@Function: qryWx
    *@Description 60秒循环查询
    *@param: [token, orderNoReq]
    *@author: Xiwei_Qiu @date: 2022/7/20 @version:
    **/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "qryWx", method = { RequestMethod.POST })
    public ResultBody qryWx (
        @RequestHeader(value="accessToken") String token,
        @RequestBody OrderPayReq orderPayReq)throws Exception{
        return ResultBody.newInstance(payUtilService.qryWx(orderPayReq,getUserDTO(token)));
    }

    /**
     *@Function: checkPay
     *@Description
     *@param: [token, orderNoReq]
     *@author: Xiwei_Qiu @date: 2022/7/20 @version:
     **/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "checkPay", method = { RequestMethod.POST })
    public ResultBody checkPay (
        @RequestHeader(value="accessToken") String token,
        @RequestBody OrderPayTransReq orderPayTransReq)throws Exception{
        payUtilService.checkPay(orderPayTransReq,getUserDTO(token));
        return ResultBody.success();
    }

    /**
     * @Description: 传联合支付号
     * @Author: bowen zhang
     * @Date: 2022/8/12
     * @param token:
     * @param payReq:
     * @return: com.sgs.ecom.member.util.ResultBody
     **/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "qryOrderNoListByTransNo", method = { RequestMethod.POST })
    public ResultBody qryOrderNoListByTransNo (
            @RequestHeader(value="accessToken") String token,
            @RequestBody PayReq payReq)throws Exception{
        return ResultBody.success(iPayService.qryOrderNoListByTransNo(payReq));
    }

    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "createTransNo", method = { RequestMethod.POST })
    public ResultBody createTransNo (
            @RequestHeader(value="accessToken") String token,
            @RequestBody PayReq payReq)throws Exception{
        return ResultBody.success(payUtilService.createTransNo(payReq.getOrderNoList(),getUserDTO(token)));
    }

//    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "modPayMethod", method = { RequestMethod.POST })
    public ResultBody modPayMethod (
            @Validated(BaseBean.Update.class)
            @RequestBody PayMethodReq payMethodReq)throws Exception{
        payUtilService.modPayMethod(payMethodReq);
        return ResultBody.success();
    }

    /**
     * @Function: getOrderPayInfo
     * @Description: 查询订单支付信息
     *
     * @param: orderNo
     * @return: ResultBody
     *
     * @version: 1.0
     * @author: shenyi
     * @date: 2024-07-05
     *
     * Modification History:
     * Date         Author          Version            Description
     *---------------------------------------------------------*
     * 修改时间      修改人            版本                 修改原因
     * 2024-07-05  shenyi           v1.0                 新增
     */
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "getOrderPay/{orderNo}", method = { RequestMethod.POST })
    public ResultBody getOrderPayInfo(@PathVariable String orderNo,
                                      @RequestHeader(value="accessToken") String token) throws Exception {
        return ResultBody.newInstance(orderPayService.getOrderPayInfo(orderNo, getUserDTO(token)));
    }
}
