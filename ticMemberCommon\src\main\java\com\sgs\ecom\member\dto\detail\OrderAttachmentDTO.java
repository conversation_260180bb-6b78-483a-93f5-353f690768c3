package com.sgs.ecom.member.dto.detail;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sgs.ecom.member.util.serializer.TimeStringFormatSerializer;

public class OrderAttachmentDTO {

    private String fileId;
    private String cloudId;
    private String fileName;
    private String attType;
    private Long attachmentId;
    private String fileUrl;
    private String createDate;
    private String orderNo;
    private String downLoad;
    private String groupNo;
    private String reportNo;
    private int  state;

    private int isCs;
    private Integer uploadType;

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public int getIsCs() {
        return isCs;
    }

    public void setIsCs(int isCs) {
        this.isCs = isCs;
    }

    public String getGroupNo() {
        return groupNo;
    }

    public void setGroupNo(String groupNo) {
        this.groupNo = groupNo;
    }

    public String getDownLoad() {
		return downLoad;
	}

	public void setDownLoad(String downLoad) {
		this.downLoad = downLoad;
	}

	public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getCloudId() {
        return cloudId;
    }

    public void setCloudId(String cloudId) {
        this.cloudId = cloudId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Long getAttachmentId() {
        return attachmentId;
    }

    public void setAttachmentId(Long attachmentId) {
        this.attachmentId = attachmentId;
    }

    public String getAttType() {
        return attType;
    }

    public void setAttType(String attType) {
        this.attType = attType;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }
    @JsonSerialize(using = TimeStringFormatSerializer.class)
    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getUploadType() {
        return uploadType;
    }

    public void setUploadType(Integer uploadType) {
        this.uploadType = uploadType;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }
}
