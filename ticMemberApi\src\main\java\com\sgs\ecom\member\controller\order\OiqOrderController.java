package com.sgs.ecom.member.controller.order;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.base.BaseBean;
import com.sgs.ecom.member.request.OrderNoReq;
import com.sgs.ecom.member.request.oiq.OiqCancelReq;
import com.sgs.ecom.member.request.oiq.OiqListReq;
import com.sgs.ecom.member.request.pdf.OrderWordPrintReq;
import com.sgs.ecom.member.res.PDFGeneraterRes;
import com.sgs.ecom.member.service.oiq.interfaces.IOiqOrderService;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.oiq/order")
public class OiqOrderController extends ControllerUtil {

    @Resource
    private IOiqOrderService oiqOrderService;

    /**
     * @Function: updateDetail
     * @Description 查询详情
     * @param: [token, orderNoReq]
     * @author: Xiwei_Qiu @date: 2021/11/15 @version:
     **/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "qryDetail", method = {RequestMethod.POST})
    public ResultBody updateDetail(
            @RequestHeader(value = "accessToken") String token,
            @RequestHeader(value = "frontUrl",required = false) String frontUrl,
            @Validated(BaseBean.Query.class)
            @RequestBody OrderNoReq orderNoReq) throws Exception {
        return ResultBody.success(oiqOrderService.qryDetail(orderNoReq.getOrderNo(),getUserDTO(token),frontUrl));
    }

    /**
     * @Function: qryList
     * @Description 查询订单列表
     * @param: [orderBaseInfoReq, token]
     * @author: Xiwei_Qiu @date: 2021/11/15 @version:
     **/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "qryList", method = {RequestMethod.POST})
    public ResultBody qryList(
            @RequestHeader(value = "accessToken") String token,
            @RequestBody OiqListReq oiqListReq,
            @RequestHeader(value = "bu",required = false) String bu) throws Exception {
        if(StringUtils.isNotBlank(bu)){
            oiqListReq.setBu(bu);
        }
        return ResultBody.newInstance(oiqOrderService.getPageList(oiqListReq, getUserDTO(token)));
    }

    /**
     * @Function: getStateNum
     * @Description 获取订单各状态的数据
     * @author: Xiwei_Qiu
     * @date: 2020/12/3
     **/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "getStateNum", method = {RequestMethod.POST})
    public ResultBody getStateNum(
            @RequestHeader(value = "accessToken") String token,
            @RequestBody OiqListReq oiqListReq,
            @RequestHeader(value = "bu",required = false) String bu) throws Exception {
        if(StringUtils.isNotBlank(bu)){
            oiqListReq.setBu(bu);
        }
        return ResultBody.newInstance(oiqOrderService.getStateNum(oiqListReq, getUserDTO(token)));
    }


    /**
    * @params [token, orderOperationReq]
    * @return com.sgs.ecom.member.util.ResultBody
    * @description 取消订单
    * <AUTHOR> || created at 2023/11/22 10:44
    */
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "cancelOrder", method = {RequestMethod.POST})
    public ResultBody cancelOrder(
            @RequestHeader(value = "accessToken") String token,
            @RequestBody OiqCancelReq oiqCancelReq) throws Exception {
        oiqOrderService.cancelOrder(oiqCancelReq, getUserDTO(token));
        return ResultBody.success();
    }


    /**
    *打印报价单 orderWordPrintReq
    * @param
    * @return com.sgs.ecom.member.util.ResultBody
    * <AUTHOR> || created at 2024/9/11 16:06
    * @throws Exception 抛出错误
    */
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "getWordPdf", method = { RequestMethod.POST })
    public ResultBody getWordPdf (
            @Validated(value = BaseBean.Query.class)
            @RequestBody OrderWordPrintReq orderWordPrintReq) throws Exception {
        PDFGeneraterRes pdfGeneraterRes=oiqOrderService.getWordPdfUrl(orderWordPrintReq,new ConcurrentHashMap<>());
        JSONObject jsonObject=new JSONObject();
        jsonObject.put("res",pdfGeneraterRes.getUrl());
        return ResultBody.newInstance(jsonObject);
    }


    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "getWordStream", method = { RequestMethod.POST })
    public void getWordStream (
            @RequestHeader(value="accessToken") String token,
            @RequestBody OrderWordPrintReq orderWordPrintReq,
            HttpServletResponse httpServletResponse)throws Exception{
        oiqOrderService.getWordStream(orderWordPrintReq,getUserDTO(token),httpServletResponse);
    }

}
