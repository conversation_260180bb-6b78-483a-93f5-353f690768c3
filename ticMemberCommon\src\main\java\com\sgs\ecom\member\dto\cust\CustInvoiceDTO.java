package com.sgs.ecom.member.dto.cust;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.dto.rsts.CustBossDTO;
import com.sgs.ecom.wechat.util.ValidationUtil;

public class CustInvoiceDTO {

    @ApiAnno(groups={BaseQryFilter.QueryList.class})
    private Long custId;
    @ApiAnno(groups={BaseQryFilter.QueryList.class})
    private Long invoiceId;
    @ApiAnno(groups={BaseQryFilter.QueryList.class})
    private String taxNo;
    @ApiAnno(groups={BaseQryFilter.QueryList.class})
    private String regPhone;
    @ApiAnno(groups={BaseQryFilter.QueryList.class})
    private String bankName;
    @ApiAnno(groups={BaseQryFilter.QueryList.class})
    private String bankNumber;
    @ApiAnno(groups={BaseQryFilter.QueryList.class})
    private String invoiceTitle;
    @ApiAnno(groups={BaseQryFilter.QueryList.class})
    private String regAddress;
    @ApiAnno(groups={BaseQryFilter.QueryList.class})
    private String customerNumber;


    @ApiAnno(groups={BaseQryFilter.QueryList.class})
    private Boolean delFlg;

    public static CustBossDTO  invoiceToBoss(CustBossDTO custBossDTO,CustInvoiceDTO custInvoiceDTO) {
      if(ValidationUtil.isEmpty(custInvoiceDTO)){
          return custBossDTO;
      }
      custBossDTO.setTaxNo(custInvoiceDTO.getTaxNo());
      custBossDTO.setInvoiceBossNo(custInvoiceDTO.getCustomerNumber());
      return  custBossDTO;
    }

    public Long getCustId() {
        return custId;
    }

    public void setCustId(Long custId) {
        this.custId = custId;
    }

    public String getTaxNo() {
        return taxNo;
    }

    public void setTaxNo(String taxNo) {
        this.taxNo = taxNo;
    }

    public String getRegPhone() {
        return regPhone;
    }

    public void setRegPhone(String regPhone) {
        this.regPhone = regPhone;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankNumber() {
        return bankNumber;
    }

    public void setBankNumber(String bankNumber) {
        this.bankNumber = bankNumber;
    }

    public String getInvoiceTitle() {
        return invoiceTitle;
    }

    public void setInvoiceTitle(String invoiceTitle) {
        this.invoiceTitle = invoiceTitle;
    }

    public String getRegAddress() {
        return regAddress;
    }

    public void setRegAddress(String regAddress) {
        this.regAddress = regAddress;
    }

    public Long getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(Long invoiceId) {
        this.invoiceId = invoiceId;
    }

    public Boolean getDelFlg() {
        return delFlg;
    }

    public void setDelFlg(Boolean delFlg) {
        this.delFlg = delFlg;
    }

    public String getCustomerNumber() {
        return customerNumber;
    }

    public void setCustomerNumber(String customerNumber) {
        this.customerNumber = customerNumber;
    }
}
