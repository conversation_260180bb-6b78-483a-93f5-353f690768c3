<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.order.dao.OrderShippingContactMapper" >

    <update id="delContactByOrderNo"  >
        update ORDER_SHIPPING_CONTACT set STATE=0 where STATE=1 and ORDER_NO=#{orderNo}
    </update>

    <resultMap id="resultDTO" type="com.sgs.ecom.member.dto.oiq.applicationInfo.OrderShippingContactDTO">
        <result column="CONTACT_TYPE" jdbcType="VARCHAR" property="contactType"/>
        <result column="CONTACT_COUNTRY" jdbcType="VARCHAR" property="contactCountry"/>
        <result column="CONTACT_CITY" jdbcType="VARCHAR" property="contactCity"/>
        <result column="CONTACT_ADDRESS" jdbcType="VARCHAR" property="contactAddress"/>
        <result column="CONTACT_ZIP_CODE" jdbcType="VARCHAR" property="contactZipCode"/>
        <result column="CONTACT_NAME" jdbcType="VARCHAR" property="contactName"/>
        <result column="CONTACT_PHONE" jdbcType="VARCHAR" property="contactPhone"/>
        <result column="CONTACT_EMAIL" jdbcType="VARCHAR" property="contactEmail"/>
        <result column="CONTACT_FXI_NO" jdbcType="VARCHAR" property="contactFxiNo"/>
        <result column="CONTACT_TAX_NO" jdbcType="VARCHAR" property="contactTaxNo"/>
        <result column="CONTACT_TRANS_NO" jdbcType="VARCHAR" property="contactTransNo"/>
        <result column="ACCOUNT_NUMBER" jdbcType="VARCHAR" property="accountNumber"/>
    </resultMap>
    <sql id="sqlListDTO">
        CONTACT_TYPE, CONTACT_COUNTRY, CONTACT_CITY, CONTACT_ADDRESS, CONTACT_ZIP_CODE, CONTACT_NAME,
        CONTACT_PHONE,CONTACT_EMAIL,CONTACT_FXI_NO,CONTACT_TAX_NO,CONTACT_TRANS_NO,ACCOUNT_NUMBER
    </sql>


    <select id="qryOrderShippingContactListByOrderNo" resultMap="resultDTO">
        select
        <include refid="sqlListDTO"/>
        from ORDER_SHIPPING_CONTACT
        where ORDER_NO=#{orderNo} and state=1
    </select>


</mapper>