package com.sgs.ecom.member.dto.bbc;

public class TicAppFormDTO {


	private String criticalDefect;//致命缺陷
	private String seriousDefect;//重缺陷
	private String mildDefect; //轻缺陷;

	private String productName;//产品名称
	private String creditNo;//信用证号


	private String orderNo;//订单号
	private String number;//款号
	private String maker;//目的地
	private String sailSchedule;//船期
	private String factoryName;//工厂名称
	private String contactPerson;//联系人
	private String phone;//电话
	private String province;//省
	private String city;//市
	private String address;//详细地址
	private String sampleNums;

	public String getCriticalDefect() {
		return criticalDefect;
	}

	public void setCriticalDefect(String criticalDefect) {
		this.criticalDefect = criticalDefect;
	}

	public String getSeriousDefect() {
		return seriousDefect;
	}

	public void setSeriousDefect(String seriousDefect) {
		this.seriousDefect = seriousDefect;
	}

	public String getMildDefect() {
		return mildDefect;
	}

	public void setMildDefect(String mildDefect) {
		this.mildDefect = mildDefect;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public String getCreditNo() {
		return creditNo;
	}

	public void setCreditNo(String creditNo) {
		this.creditNo = creditNo;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getNumber() {
		return number;
	}

	public void setNumber(String number) {
		this.number = number;
	}

	public String getMaker() {
		return maker;
	}

	public void setMaker(String maker) {
		this.maker = maker;
	}

	public String getSailSchedule() {
		return sailSchedule;
	}

	public void setSailSchedule(String sailSchedule) {
		this.sailSchedule = sailSchedule;
	}

	public String getFactoryName() {
		return factoryName;
	}

	public void setFactoryName(String factoryName) {
		this.factoryName = factoryName;
	}

	public String getContactPerson() {
		return contactPerson;
	}

	public void setContactPerson(String contactPerson) {
		this.contactPerson = contactPerson;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public TicAppFormDTO() {
	}

	public TicAppFormDTO(String all) {
		this.criticalDefect = all;
		this.seriousDefect = all;
		this.mildDefect = all;
		this.productName = all;
		this.creditNo = all;
		this.orderNo = all;
		this.number = all;
		this.maker = all;
		this.sailSchedule = all;
		this.factoryName = all;
		this.contactPerson = all;
		this.phone = all;
		this.province = all;
		this.city = all;
		this.address = all;
	}

	public String getSampleNums() {
		return sampleNums;
	}

	public void setSampleNums(String sampleNums) {
		this.sampleNums = sampleNums;
	}
}
