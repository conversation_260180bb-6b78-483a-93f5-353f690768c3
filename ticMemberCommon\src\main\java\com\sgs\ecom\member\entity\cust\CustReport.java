package com.sgs.ecom.member.entity.cust;

import java.math.BigDecimal;
import java.util.Date;

public class CustReport {
    private Long reportId;

    private Long custId;

    private String currency;

    private String bu;

    private BigDecimal reportPrice;

    private BigDecimal taxRate;

    private Integer state;

    private Date createDate;

    private Date stateDate;

    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    public Long getCustId() {
        return custId;
    }

    public void setCustId(Long custId) {
        this.custId = custId;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    public String getBu() {
        return bu;
    }

    public void setBu(String bu) {
        this.bu = bu == null ? null : bu.trim();
    }

    public BigDecimal getReportPrice() {
        return reportPrice;
    }

    public void setReportPrice(BigDecimal reportPrice) {
        this.reportPrice = reportPrice;
    }

    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getStateDate() {
        return stateDate;
    }

    public void setStateDate(Date stateDate) {
        this.stateDate = stateDate;
    }
}