<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd padding: 10px 0 0 0; margin: 0;">
<html xmlns="http://www.w3.org/1999/xhtml padding: 10px 0 0 0; margin: 0;">
<head>
  <meta charset="UTF-8"></meta>
  <meta name="viewport"
        content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"></meta>
  <meta http-equiv="X-UA-Compatible" content="ie=edge"></meta>
  <title>Document</title>
  <style>
    * {
      padding: 0;
      margin: 0;
    }
    td {
      padding: 2px 5px;
    }
    .header img {
      height: 55px;
    }
    .header-right {
      font-size: 9px;
      line-height: 18px;
      color: #000000;
      float: right;
    }
    .title {
      font-size: 22px;
      line-height: 18px;
      color: #000000;
      text-align: center;
      margin-bottom: 10px;
    }
    .title-en {
      font-size: 16px;
      line-height: 18px;
      color: #000000;
      font-weight: bold;
      text-align: center;
      margin-bottom: 10px;
    }
    .basic-table .header-row {
      background-color: #ff6600;
    }
    .basic-table .row {
      display: flex;
      min-height: 30px;
      box-sizing: border-box;
      border: 2px solid #000;
    }
    .basic-table .row > div {
      padding: 4px 2px;
      font-size: 14px;
      line-height: 18px;
      color: #000000;
      box-sizing: border-box;
    }
    .basic-table .row .no-td {
      width: 50%;
      border-right: 2px solid #000;
    }
    .basic-table .row .label-td {
      width: 9%;
      border-right: 2px solid #000;
    }
    .basic-table .row .value-td {
      width: 41%;
      border-right: 2px solid #000;
    }
    .basic-table .row .email-td {
      font-size: 14px;
      line-height: 18px;
      color: #0000ff;
      text-decoration: underline;
    }
    .basic-table .row:not(:first-child) {
      border-top: none;
    }
    .basic-table .row > div:last-child {
      border: none;
    }
    .basic-table .header-row > div {
      font-size: 17px;
      line-height: 18px;
      color: #ffffff;
      font-weight: bold;
    }
    .msg {
      font-size: 12px;
      line-height: 18px;
      color: #000000;
      margin: 10px 0;
    }
    .detail-table .row {
      min-height: 32px;
      display: flex;
      /*align-items: center;*/
      border: 2px solid #000;
    }
    .detail-table .row:not(:first-child) {
      border-top: 0;
    }
    .detail-table .row > div {
      border-right: 2px solid #000;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      padding: 5px;
      box-sizing: border-box;
    }
    .detail-table .row > div:last-child {
      border: none;
    }
    .detail-table .row .td1 {
      /*flex-basis: 95px;
      flex-shrink: 0;*/
      text-align: center;
      width: 10%;
    }
    .detail-table .row:not(:first-child) .td1 {
      text-align: left;
      padding-left: 9px;
    }
    .detail-table .row .td2 {
      /*flex: 191px;*/
      width: 20%;
    }
    .detail-table .row .td3 {
      /*flex: 162px;*/
      width: 18%;
    }
    .detail-table .row .td4 {
      /*flex: 92px;*/
      width: 10%;
    }
    .detail-table .row .td5 {
      /*flex: 112px;*/
      width: 12%;
    }
    .detail-table .row .td6 {
      /*flex: 123px;*/
      width: 13%;
    }
    .detail-table .row .td7 {
      /*flex-basis: 164px;*/
      /*flex-shrink: 0;*/
      width: 17%;
    }
    .detail-table .row .td7.total {
      color: #ff0000;
    }
    .detail-table .row .total-td {
      /*flex: 1;*/
      width: 83%;
      justify-content: flex-start;
    }
    .detail-table .header-row {
      font-size: 14px;
      line-height: 18px;
      color: #000000;
      font-weight: bold;
      font-family: "Microsoft Ya Hei";
      background-color: #d9d9d9;
    }
    .detail-table .row {
      font-size: 14px;
      line-height: 18px;
      color: #000000;
      font-family: "Microsoft Ya Hei";
    }
    .detail-table .row.total {
      color: #ff0000;
      font-weight: bold;
    }
    .detail-table .row.tips {
      font-size: 14px;
      line-height: 17px;
      color: #000000;
      padding: 10px 5px;
      flex-wrap: wrap;
    }
    .total-tips {
      background-color: #fcd5b5;
      border: 0.7699999809265137px solid #e46c0a;
      margin-top: 20px;
      padding: 5px;
      font-size: 11px;
      line-height: 11px;
      color: #000000;
    }
    .seal-content {
      /*display: flex;*/
      /*align-items: center;*/
      margin-top: 20px;
      position: relative;
    }
    .seal-content img {
      margin-right: 20px;
      width: 120px;
      position: absolute;
    }
    .seal-content .content {
      margin-left: 181px;
      padding-top: 30px;
    }
    .seal-content .top {
      font-size: 12px;
      line-height: 20px;
      color: #000000;
      margin-bottom: 20px;
    }
    .seal-content .detail {
      font-size: 8px;
      line-height: 11px;
      color: #000000;
    }
    .bottom {
      font-size: 12px;
      line-height: 23px;
      color: #000000;
    }
    .bottom > div > div {
      /*display: flex;*/
      display: inline-block;
    }
    .bottom .div1 {
      width: 30%;
      margin-right: 20px;
    }
    .bottom .div2 {
      width: 30%;
      margin-right: 20px;
    }
    .Supplement_offer table{
      border:1px solid red;
    }
    table {
      border-top: 0.5px solid #000;
      border-right: 0.5px solid #000;
    }
    td {
      border-bottom: 0.5px solid #000;
      border-left: 0.5px solid #000;
    }
  </style>
</head>
<body style="font-family: SimSun; color: black; margin: 0;font-size:9">
<div class="header">
  <img src="${imglogo}" alt="" ></img>
  <div class="header-right">
    <div>No.: IND-QP-4.4-01-03</div>
    <div>Version:  1.1</div>
    <div>Issue date:  Jan.1st,2017</div>
  </div>
</div>
<div class="title">报  价  单</div>
<div class="title-en">QUOTATION</div>

<table style="width: 100%; border-color: #000; font-size: 14px; line-height: 18px; color: #000000; word-break: break-all;"  width="100%" cellspacing="0">
  <col width="15%" />
  <col width="35%" />
  <col width="10%" />
  <col width="40%" />
  <tbody style="font-size: 12px;">


  <tr style="background-color: #d9d9d9;">
    <td style="text-align:center;font-weight: bold;">SGS报价单号</td>
    <td>
      ${(orderNo)!''}
    </td>
    <td  style="text-align:center;font-weight: bold;">日期</td>
    <td>${(logTime)!''} </td>
  </tr>

  <tr>
    <td  style="text-align:center;font-weight: bold;">申请方</td>
    <td>
      ${(orderApplicationFormDTO.companyNameCn)!''}
    </td>
    <td  style="text-align:center;font-weight: bold;">SGS公司</td>
    <td>${(bankDTO.accountName)!''} </td>
  </tr>
  <tr>
    <td  style="text-align:center;font-weight: bold;" >付款方/发票抬头</td>
    <td > ${(orderInvoiceDTO.invoiceTitle)!''}</td>
    <td style="text-align:center;font-weight: bold;" >SGS实验室</td>
    <td>${(orderBaseInfoMoreDTO.labNameValue)!''}</td>
  </tr>
  <tr>
    <td  style="text-align:center;font-weight: bold;">发票类型</td>
    <td >${(orderInvoiceDTO.invoiceTypeShow)!''}</td>

    <td  style="text-align:center;font-weight: bold;">地址</td>
    <td><#if labFlg==1> ${(labDTO.labAddressShow)!''}</#if></td>
  </tr>
  <tr>
    <td  style="text-align:center;font-weight: bold;">联系人</td>
    <td> ${(orderBaseInfoMoreDTO.userName)!''} </td>
    <td  style="text-align:center;font-weight: bold;">SGS联系人</td>
    <td>${(orderBaseInfoMoreDTO.csName)!''}</td>
  </tr>

  <tr>
    <td  style="text-align:center;font-weight: bold;">邮箱</td>
    <td><a href="mailto:<EMAIL>">${(orderBaseInfoMoreDTO.userEmail)!''} </a> </td>
    <td   style="text-align:center;font-weight: bold;">邮箱</td>
    <td><a href="mailto:<EMAIL>">${(orderBaseInfoMoreDTO.csEmail)!''}</a></td>
  </tr>

  <tr>
    <td   style="text-align:center;font-weight: bold;">电话</td>
    <td >${(orderBaseInfoMoreDTO.userPhone)!''}  </td>
    <td  style="text-align:center;font-weight: bold;">电话</td>
    <td >${(orderBaseInfoMoreDTO.csPhone)!''}</td>
  </tr>


  </tbody>
</table>
<div class="msg">
  尊敬的客户：感谢您对本公司的信任和支持! 依据您的测试需求，我们做如下报价
</div>
<table style="border-color: #000; width: 100%; word-break: break-all;"  width="100%" cellspacing="0">
  <tbody style="font-size: 9px;">
  <tr style="background-color: #d9d9d9;font-size: 12px; ">
    <td style="width: 5%; text-align: center;" colspan="2" width="82">序号</td>
    <td style="text-align: center;" width="125">测试项目</td>
    <td style="text-align: center;" width="121">测试方法</td>
    <td style="text-align: center;" width="64">单价<br />(RMB)</td>
    <td style="text-align: center;" width="81">周期要求</td>
    <td style="text-align: center;" width="30">数量</td>
    <td style="text-align: center;" width="105">测试备注</td>
    <td style="text-align: center;" width="117"><span style="color: #ff0000;">费用合计</span><br /><span style="color: #ff0000;">(RMB)</span></td>
  </tr>


  <#if orderDetailDTOList?has_content>
    <#list orderDetailDTOList as orderDetail>
      <#if orderDetail.isDefault==1>
        <tr  style="font-size: 12px;">
          <td style="text-align: center;" colspan="2">${orderDetail_index+1}</td>
          <td style="text-align: center;word-wrap : break-word ;word-break:break-all;" width="125">
            ${(orderDetail.itemName)!''}
            <br></br>
            <#if orderDetail.orderSampleDTOList?? && (orderDetail.orderSampleDTOList?size > 0)  >
              <#list orderDetail.orderSampleDTOList as orderSample>
                <span style="font-size:8px;color:#878787;">
              ${(orderSample.row)!''}#${(orderSample.sampleNameShow)!''}
              </span>
              </#list>
            </#if>

            <#if orderDetail.isGive==1>
              <span style="color: #ff0000;">【赠送】</span>
            </#if>
          </td>
          <td style="text-align: center; word-wrap : break-word ;word-break:break-all;" width="121">${(orderDetail.standardCode)!''}</td>
          <td style="text-align: center;word-wrap : break-word ;word-break:break-all;" width="64">
            <#if orderDetail.price=="">待定</#if>
            <#if orderDetail.price!="">${(orderDetail.originalPrice)!''}</#if>
          </td>
          <td style="text-align: center;word-wrap : break-word ;word-break:break-all;" width="81">${(orderDetail.urgentTypeShow)!''}</td>
          <td style="text-align: center;word-wrap : break-word ;word-break:break-all;" width="30">${(orderDetail.buyNums)!''}</td>
          <td style="text-align: center;word-wrap : break-word ;word-break:break-all;" width="105">${(orderDetail.testMemo)!''}</td>
          <td style="text-align: center;word-wrap : break-word ;word-break:break-all;" width="117">
            <#if orderDetail.totalPrice=="">待定</#if>
            <#if orderDetail.totalPrice!="">${(orderDetail.totalPrice)!''}</#if>
          </td>
        </tr>
      </#if>
    </#list>
  </#if>

  <#if orderBaseInfoMoreDTO.orderType="200001">
    <tr style="font-size: 12px;">
      <td style="padding-left: 20px;" colspan="9" >测试备注: ${(orderBaseInfoMoreDTO.recommendReason)!''}</td>
    </tr>
  </#if>

  <#if orderBaseInfoMoreDTO.priceType=1 >
    <tr style="font-size: 12px;">
      <td style="padding-left: 20px;text-align: right;border-bottom: none;" colspan="8">费用合计： </td>
      <td style="text-align: center;border-left:none;border-bottom: none;" width="132"><span style="color: #ff0000;">${(orderBaseInfoMoreDTO.orderAmount)!''}</span></td>
    </tr>
    <!--
    <tr style="font-size: 12px;">
      <td style="padding-left: 20px;" colspan="8" >报告语言（${(orderBaseInfoMoreDTO.reportLuaValue)!''}）</td>
      <td style="text-align: center;" width="132"><span style="color: #ff0000;">${(orderBaseOtherDTO.reportLua)!''}</span></td>
    </tr>
    <tr style="font-size: 12px;">
      <td style="padding-left: 20px;" colspan="8">
        报告形式（${(orderBaseInfoMoreDTO.reportFormValue)!''}）</td>
      <td style="text-align: center;" width="132"><span style="color: #ff0000;">${(orderBaseOtherDTO.reportForm)!''}</span></td>
    </tr>
    -->

    <tr style="font-size: 12px;">
      <td style="padding-left: 20px;text-align: right;border-bottom: none;" colspan="8">增值税（6%）：</td>
      <td style="text-align: center;border-left:none;border-bottom: none;" width="132"><span style="color: #ff0000;">${(pdfAmountDTO.taxAmount)!''}</span></td>
    </tr>

    <tr style="font-size: 12px;">
      <td style="padding-left: 20px;text-align: right;<#if discountFlg==1>border-bottom: none;</#if>" colspan="8">税后总价：</td>
      <td style="text-align: center;border-left:none;<#if discountFlg==1>border-bottom: none;</#if>" width="132">
        <div style="color: #ff0000;">${(pdfAmountDTO.total)!''}</div>
        <#if isShow==1>
          <div>（不含价格待定项目）</div>
        </#if>
      </td>
    </tr>

    <#if discountFlg==1>
      <tr style="font-size: 12px;">
        <td style="padding-left: 20px;text-align: right;border-bottom: none;" colspan="8"><span style="color: #ff0000;">含税一口价：</span></td>
        <td style="text-align: center;border-left:none;border-bottom: none;" width="132">
          <div style="color: #ff0000">  ${(orderBaseInfoMoreDTO.realAmount)!''}</div>
        </td>
      </tr>


      <tr style="font-size: 12px;">
        <td style="padding-left: 20px;text-align: right;" colspan="8">优惠信息（-${(discountNum)!''}%）：</td>
        <td style="text-align: center;border-left:none;" width="132"><span style="color: #ff0000;">-${(orderBaseInfoMoreDTO.discountAmount)!''}</span></td>
      </tr>

    </#if>

  </#if>




  <#if logFlg==1>
    <tr style="font-size: 12px;">
      <td style="padding-left: 20px;" colspan="9">修改记录</td>
    </tr>
    <#list orderOperatorLogDTOList as orderOperatorLogDTO >

      <tr style="font-size: 12px;">
        <td style="padding-left: 20px;" colspan="3" >${(orderOperatorLogDTO.operatorDate)!''}</td>
        <td style="text-align: center;" colspan="5">
          <div style="word-wrap : break-word ;word-break:break-all;" >  ${(orderOperatorLogDTO.memo)!''}</div>
        </td>
        <td style="text-align: center;word-wrap : break-word ;word-break:break-all;"
            width="132">
          <div style="color: #ff0000">  ${(orderOperatorLogDTO.differPrice)!''}</div>
        </td>
      </tr>
    </#list>
  </#if>
  <#if subOrderFlg==1>
    <tr style="font-size: 12px;">
      <td style="padding-left: 20px;" colspan="9">补充报价</td>
    </tr>
    <#list subOrderDTOList as subOrder >

      <tr style="font-size: 12px;">
        <td style="padding-left: 20px;" colspan="3" >${(subOrder.createDate)!''}</td>
        <td style="text-align: center;" colspan="5">
          <div style="word-wrap : break-word ;word-break:break-all;" > 订单编号：${(subOrder.orderNo)!''} ,${(subOrder.recommendReason)!''} </div>
        </td>
        <td style="text-align: center;word-wrap : break-word ;word-break:break-all;"
            width="132">
          <div style="color: #ff0000">  ${(subOrder.realAmountStr)!''}</div>
        </td>
      </tr>
    </#list>
  </#if>

  <#if newSubOrderFlg==1>
    <#list newSubOrderDTOList as subOrder >
      <tr style="font-size: 12px;">
        <td style="padding-left: 20px;" colspan="9">补充报价</td>
      </tr>
      <#list subOrder.orderDetailDTOList as orderDetail>
        <tr  style="font-size: 12px;">
          <td style="text-align: center;" colspan="2">${orderDetail_index+1}</td>
          <td style="text-align: center;word-wrap : break-word ;word-break:break-all;" width="125">
            ${(orderDetail.itemName)!''}
            <br></br>
            <span style="font-size:8px;color:#878787;">
                ${(orderDetail.sampleName)!''}
                </span>

          </td>
          <td style="text-align: center; word-wrap : break-word ;word-break:break-all;" width="121">${(orderDetail.standardCode)!''}</td>
          <td style="text-align: center;word-wrap : break-word ;word-break:break-all;" width="64">
            <#if orderDetail.price=="">待定</#if>
            <#if orderDetail.price!="">${(orderDetail.originalPrice)!''}</#if>
          </td>
          <td style="text-align: center;word-wrap : break-word ;word-break:break-all;" width="81">${(orderDetail.urgentTypeShow)!''}</td>
          <td style="text-align: center;word-wrap : break-word ;word-break:break-all;" width="30">${(orderDetail.buyNums)!''}</td>
          <td style="text-align: center;word-wrap : break-word ;word-break:break-all;" width="105">${(orderDetail.testMemo)!''}</td>
          <td style="text-align: center;word-wrap : break-word ;word-break:break-all;" width="117">
            <#if orderDetail.totalPrice=="">待定</#if>
            <#if orderDetail.totalPrice!="">${(orderDetail.originalTotalPrice)!''}</#if>
          </td>
        </tr>
      </#list>
      <tr style="font-size: 12px;">
        <td style="padding-left: 20px;" colspan="8">优惠信息（-${(subOrder.discountNum)!''}%）</td>
        <td style="text-align: center;" width="132"><span style="color: #ff0000;">-${(subOrder.disAmount)!''}</span></td>
      </tr>
      <tr style="font-size: 12px;">
        <td style="padding-left: 20px;" colspan="8">增值税（${(subOrder.taxRates)!''}%）</td>
        <td style="text-align: center;" width="132"><span style="color: #ff0000;">${(subOrder.taxAmount)!''}</span></td>
      </tr>
    </#list>
  </#if>





  <#if subOrderFlg==1 || logFlg==1 || newSubOrderFlg==1>
    <tr style="font-size: 12px;">
      <td style="padding-left: 20px;" colspan="8">
        <span style="color: #ff0000;">实付金额</span></td>
      <td style="text-align: center;" width="132" >
        <div style="color: #ff0000">  ${(realAmount)!''}</div>
      </td>
    </tr>
  </#if>
  </tbody>


</table>

<div style="margin: 20px 0;font-size: 12px;">
  <p><span style="margin-right: 10px;font-weight: bold;">测试周期:</span>收到样品和测试费用后，<span style="color: #ff0000;">${(orderBaseInfoMoreDTO.testCycle)!''}</span>个工作日完成。${(testCycleMemo)!''}</p>
  <p style="margin-top: 5px;"><span style="margin-right: 10px;font-weight: bold;">样品要求:</span> <span style="">${(orderBaseInfoMoreDTO.sampleRequirements)!''}</span></p>
</div>

<div style="margin: 20px 0;font-size: 12px;">
  <div style="font-weight: bold;">银行账户（CNY）</div>
  <p style="margin-top: 5px;"><span style="margin-right: 10px;font-weight: bold;">公司名称:</span> <#if bankFlg==1>${(bankDTO.accountName)!''}  </#if></p>
  <p style="margin-top: 5px;"><span style="margin-right: 10px;font-weight: bold;">银行帐号:</span> <#if bankFlg==1><span style="color: #ff0000;">${(bankDTO.bankNo)!''}</span>  </#if></p>
  <p style="margin-top: 5px;"><span style="margin-right: 10px;font-weight: bold;">开户银行:</span> <#if bankFlg==1>${(bankDTO.bankName)!''}  </#if></p>
  <p style="margin-top: 5px;"><span style="margin-right: 10px;font-weight: bold;">银行地址:</span> <#if bankFlg==1>${(bankDTO.bankAddress)!''}  </#if></p>
  <p style="margin-top: 5px;"><span style="margin-right: 10px;font-weight: bold;">银行热线电话:</span> <#if bankFlg==1>${(bankDTO.bankTel)!''}  </#if></p>
</div>

<div style="font-weight: bold;font-size: 12px;">
  重要提示：付款方必须与发票抬头一致！
</div>

<div class="total-tips">
  <p style="font-weight: bold;margin-bottom: 10px;font-size: 12px;">客户须知：</p>
  <p style="word-wrap : break-word ;word-break:break-all;">1. 本报价单自签发日期起有效期为30天；若超过30天未收到您的回复,样品将不予保留。</p>
  <p style="word-wrap : break-word ;word-break:break-all;">2. 我们在收到样品和付款凭证（或通知）后安排检测，发票将在检测完成后按您的要求快递或邮件给贵司。</p>
  <p style="word-wrap : break-word ;word-break:break-all;">3. 本公司对来样进行检测出具报告。测试完成后样品保留30天，超期后本公司有权自行处理来样。如对测试结果有疑问，请在30天内提出，超期或样品已经退还，本公司只对测试报告本身负责。</p>
  <p style="word-wrap : break-word ;word-break:break-all;">4. 本公司测试报告依据SGS集团“检验和测试服务通用条款”https://www.sgs.com/en/terms-and-conditions签发，我们对测试报告只承担被证实的过失责任，并且赔偿不超过测试费用的十倍。</p>
  <p style="word-wrap : break-word ;word-break:break-all;">5.实验室获得认可的资质及能力范围查询地址： <br></br>
    CMA:http://shzj.scjgj.sh.gov.cn/col/col32201/index.html  <br></br>
    CNAS:http://www.cnas.org.cn</p>
  <p style="word-wrap : break-word ;word-break:break-all;">6.作为 CMA/CNAS/HKAS等认可的机构，在认可过程中，可能需要向认可机构(CMA/CNAS/HKAS等)披露测试报告等资料，认可机构负责对收所集资料保密。</p>
</div>
<div class="seal-content">
  <div> <img src="${imgseal}" alt="" ></img></div>
  <div class="content">
    <p class="top">
      <span style="margin-right: 40px;">
         <#if bankFlg==1>${(bankDTO.accountName)!''}  </#if>
      </span>
      <span>日期： ${(logTime1)!""}</span>
    </p>
    <p class="detail">
      The order is accepted and the report is issued by the Company subject to its General Conditions of Service.
      本公司测试申请和测试报告签发均依据本公司的”检验和测试服务通用条款”。详细内容见：
      https://www.sgsgroup.com.cn/zh-cn/terms-and-conditions
    </p>
  </div>
</div>
<img src="${imghx}" style="margin: 20px 0; width: 100%;" alt=""></img>
</body>
</html>
