<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.user.dao.UserInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.vo.VOUserInfo" >
    <id column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="USER_NICK" property="userNick" jdbcType="VARCHAR" />
    <result column="USER_PHONE" property="userPhone" jdbcType="VARCHAR" />
    <result column="USER_PWD" property="userPwd" jdbcType="VARCHAR" />
    <result column="USER_NAME" property="userName" jdbcType="VARCHAR" />
    <result column="USER_EMAIL" property="userEmail" jdbcType="VARCHAR" />
    <result column="LEVEL_ID" property="levelId" jdbcType="INTEGER" />
    <result column="LEVEL_NAME" property="levelName" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="INTEGER" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
    <result column="BBC_PWD" property="bbcPwd" jdbcType="VARCHAR" />
    <result column="REG_IP" property="regIp" jdbcType="VARCHAR" />
    <result column="U_ID" property="uId" jdbcType="BIGINT" />
    <result column="USER_SEX" property="userSex" jdbcType="TINYINT" />
    <result column="IS_BLACK" property="isBlack" jdbcType="INTEGER" />
    <result column="IS_TEST" property="isTest" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    USER_ID, USER_NICK, USER_PHONE, USER_PWD, USER_NAME, USER_EMAIL, LEVEL_ID, LEVEL_NAME, 
    STATE, CREATE_DATE, STATE_DATE, BBC_PWD, REG_IP, U_ID, USER_SEX, IS_BLACK,IS_TEST
  </sql>
  <select id="selectVOByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from USER_INFO
    where USER_ID = #{userId,jdbcType=BIGINT}
  </select>


  <!--自定义 -->
  <sql id="sqlListDTO" >
    USER_ID, USER_NICK, USER_PHONE, USER_PWD, USER_NAME, USER_EMAIL, LEVEL_ID, LEVEL_NAME,
    STATE, CREATE_DATE, STATE_DATE, BBC_PWD, REG_IP, U_ID, USER_SEX, IS_BLACK
  </sql>
  <resultMap id="resultDTO" type="com.sgs.ecom.member.dto.user.UserInfoDTO" >
    <id column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="USER_NICK" property="userNick" jdbcType="VARCHAR" />
    <result column="USER_PHONE" property="userPhone" jdbcType="VARCHAR" />
    <result column="USER_PWD" property="userPwd" jdbcType="VARCHAR" />
    <result column="USER_NAME" property="userName" jdbcType="VARCHAR" />
    <result column="USER_EMAIL" property="userEmail" jdbcType="VARCHAR" />
    <result column="LEVEL_ID" property="levelId" jdbcType="INTEGER" />
    <result column="LEVEL_NAME" property="levelName" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="INTEGER" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
    <result column="BBC_PWD" property="bbcPwd" jdbcType="VARCHAR" />
    <result column="REG_IP" property="regIp" jdbcType="VARCHAR" />
    <result column="U_ID" property="uId" jdbcType="BIGINT" />
    <result column="USER_SEX" property="userSex" jdbcType="TINYINT" />
    <result column="IS_BLACK" property="isBlack" jdbcType="INTEGER" />
  </resultMap>

  <select id="selectListByMap" resultMap="resultDTO" >
    select
    <include refid="sqlListDTO" />
    from USER_INFO
    <include refid="baseQueryWhere"/>
    order by USER_ID desc
  </select>

  <resultMap id="resultUserDTO" type="com.sgs.ecom.member.dto.order.OrderUserInfoDTO" >
    <id column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="USER_NICK" property="userName" jdbcType="VARCHAR" />
    <result column="USER_PHONE" property="userPhone" jdbcType="VARCHAR" />
    <result column="USER_EMAIL" property="userEmail" jdbcType="VARCHAR" />
  </resultMap>


  <select id="selectByUserId" resultMap="resultUserDTO" >
    select
    USER_NICK,USER_PHONE,USER_EMAIL,USER_ID
    from USER_INFO
    where USER_ID=#{userId}
    order by USER_ID desc
    limit 1
  </select>









  <sql id="baseQueryWhere">
    where 1=1
    <if test="userId != null">
      AND USER_ID=#{userId}
    </if>
    <if test="userPhone != null">
      AND USER_PHONE=#{userPhone}
    </if>
    <if test="userIdList != null ">
      AND USER_ID in
      <foreach collection="userIdList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>

  </sql>

</mapper>