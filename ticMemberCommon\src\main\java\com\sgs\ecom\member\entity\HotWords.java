package com.sgs.ecom.member.entity;

import java.util.Date;

public class HotWords {
    private Long id;

    private String wordsType;

    private String searchWords;

    private Integer searchNums;

    private Integer isHot;

    private Integer showSort;

    private Date createDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getWordsType() {
        return wordsType;
    }

    public void setWordsType(String wordsType) {
        this.wordsType = wordsType == null ? null : wordsType.trim();
    }

    public String getSearchWords() {
        return searchWords;
    }

    public void setSearchWords(String searchWords) {
        this.searchWords = searchWords == null ? null : searchWords.trim();
    }

    public Integer getSearchNums() {
        return searchNums;
    }

    public void setSearchNums(Integer searchNums) {
        this.searchNums = searchNums;
    }

    public Integer getIsHot() {
        return isHot;
    }

    public void setIsHot(Integer isHot) {
        this.isHot = isHot;
    }

    public Integer getShowSort() {
        return showSort;
    }

    public void setShowSort(Integer showSort) {
        this.showSort = showSort;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }
}