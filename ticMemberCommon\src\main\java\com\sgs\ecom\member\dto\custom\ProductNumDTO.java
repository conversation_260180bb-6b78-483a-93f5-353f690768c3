package com.sgs.ecom.member.dto.custom;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.util.serializer.TimeStringFormatSerializer;

public class ProductNumDTO {
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int productId;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int num;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    @JsonSerialize(using = TimeStringFormatSerializer.class)
    private String payDate;

    public int getProductId() {
        return productId;
    }

    public void setProductId(int productId) {
        this.productId = productId;
    }

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

    public String getPayDate() {
        return payDate;
    }

    public void setPayDate(String payDate) {
        this.payDate = payDate;
    }
}
