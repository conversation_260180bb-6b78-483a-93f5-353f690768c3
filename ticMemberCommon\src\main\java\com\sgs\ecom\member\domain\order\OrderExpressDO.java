package com.sgs.ecom.member.domain.order;

import com.platform.util.ValidationUtil;
import com.sgs.ecom.member.dto.detail.OrderExpressDTO;
import com.sgs.ecom.member.dto.oiq.applicationInfo.OiqAddressDTO;
import com.sgs.ecom.member.dto.oiq.applicationInfo.OiqUserAddressDTO;

import java.util.ArrayList;
import java.util.List;

public class OrderExpressDO {

    public static List<OiqAddressDTO> expressListToAddressList(List<OrderExpressDTO> orderExpressDTOList) {
        if(ValidationUtil.isEmpty(orderExpressDTOList)){
            return new ArrayList<>();
        }
        List<OiqAddressDTO> list=new ArrayList<>();
        for(int n=0;n<orderExpressDTOList.size();n++){
            OrderExpressDTO orderExpressDTO=orderExpressDTOList.get(n);
            OiqUserAddressDTO userAddressDTO=new OiqUserAddressDTO();
            userAddressDTO.setAddressId(orderExpressDTO.getAddressId());
            userAddressDTO.setCity(orderExpressDTO.getReceiptCity());
            userAddressDTO.setProvince(orderExpressDTO.getReceiptProvice());
            userAddressDTO.setTown(orderExpressDTO.getReceiptTown());
            userAddressDTO.setUserName(orderExpressDTO.getReceiptPerson());
            userAddressDTO.setUserPhone(orderExpressDTO.getReceiptPhone());
            userAddressDTO.setCompanyAddress(orderExpressDTO.getReceiptAddr());
            userAddressDTO.setCompanyName(orderExpressDTO.getReceiptCompany());
            userAddressDTO.setUserMail(orderExpressDTO.getReceiptEmail());
            list.add(new OiqAddressDTO(orderExpressDTO.getAddressId(),userAddressDTO));
        }
        return list;
    }


}
