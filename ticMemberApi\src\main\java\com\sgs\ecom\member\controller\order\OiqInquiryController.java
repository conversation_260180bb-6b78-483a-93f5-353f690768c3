package com.sgs.ecom.member.controller.order;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.base.BaseBean;
import com.sgs.ecom.member.event.EventApiUtil;
import com.sgs.ecom.member.request.OrderNoReq;
import com.sgs.ecom.member.request.oiq.OiqCloseReq;
import com.sgs.ecom.member.request.oiq.OiqListReq;
import com.sgs.ecom.member.request.oiq.OiqWechatReq;
import com.sgs.ecom.member.service.oiq.interfaces.IOiqInquiryService;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import org.apache.commons.lang.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.oiq/inquiry")
public class OiqInquiryController extends ControllerUtil {

    @Resource
    private IOiqInquiryService oiqInquiryService;



    /**
    * @params [token, orderNoReq]
    * @return com.sgs.ecom.member.util.ResultBody
    * @description 查询询价单详情
    * <AUTHOR> created at 2023/8/28 21:26
    */
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "qryDetail", method = {RequestMethod.POST})
    public ResultBody qryDetail(
            @RequestHeader(value = "accessToken") String token,
            @RequestHeader(value = "frontUrl",required = false) String frontUrl,
            @Validated(BaseBean.Query.class)
            @RequestBody OrderNoReq orderNoReq) throws Exception {
        return ResultBody.success(oiqInquiryService.qryDetail(orderNoReq.getOrderNo(),getUserDTO(token),frontUrl));
    }

    /**
     * @Function: qryList
     * @Description 查询订单列表
     * @param: [orderBaseInfoReq, token]
     * @author: Xiwei_Qiu @date: 2021/11/15 @version:
     **/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "qryList", method = {RequestMethod.POST})
    public ResultBody qryList(
            @RequestHeader(value = "accessToken") String token,
            @RequestBody OiqListReq oiqListReq,
            @RequestHeader(value = "bu",required = false) String bu) throws Exception {
        if(StringUtils.isNotBlank(bu)){
            oiqListReq.setBu(bu);
        }
        return ResultBody.newInstance(oiqInquiryService.getPageList(oiqListReq, getUserDTO(token)));
    }


    /**
     * @Function: getStateNum
     * @Description 获取订单各状态的数据
     * @author: Xiwei_Qiu
     * @date: 2020/12/3
     **/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "getStateNum", method = {RequestMethod.POST})
    public ResultBody getStateNum(
            @RequestHeader(value = "accessToken") String token ,
            @RequestHeader(value = "bu",required = false) String bu) throws Exception {
        return ResultBody.newInstance(oiqInquiryService.getStateNum(bu,getUserDTO(token)));
    }


    /**
    * @params [token, orderOperationReq]
    * @return com.sgs.ecom.member.util.ResultBody
    * @description 取消询价单
    * <AUTHOR> || created at 2023/10/18 9:55
    */
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "close", method = {RequestMethod.POST})
    public ResultBody close(
            @RequestHeader(value = "accessToken") String token,
            @Validated(BaseBean.Insert.class)
            @RequestBody OiqCloseReq oiqCloseReq) throws Exception {
        oiqInquiryService.close(oiqCloseReq, getUserDTO(token));
        return ResultBody.success();
    }


    @AuthRequired(login = "NULL", sign = true)
    @RequestMapping(value = "getWechatUrl", method = {RequestMethod.POST})
    public ResultBody getWechatUrl(
            @RequestBody OiqWechatReq oiqWechatReq) throws Exception {
        return ResultBody.newInstance(oiqInquiryService.getWechatUrl(oiqWechatReq));
    }



}
