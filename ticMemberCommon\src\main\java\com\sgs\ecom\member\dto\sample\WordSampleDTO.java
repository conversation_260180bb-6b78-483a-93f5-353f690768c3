package com.sgs.ecom.member.dto.sample;

import com.platform.annotation.ApiAnno;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.dto.min.StylesDTO;

import java.util.List;

public class WordSampleDTO {

    public static  final String SAMPLE_NUMBER="SAMPLE_NUMBER";
    public static  final String SAMPLE_NUMBER_EN="SAMPLE_NUMBER_EN";
    public static  final String SAMPLE_WEIGHT="SAMPLE_WEIGHT";
    public static  final String SAMPLE_WEIGHT_EN="SAMPLE_WEIGHT_EN";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private String sampleNumber;
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private String sampleNumberName;
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private String sampleWeight;
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private String sampleWeightName;
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private List<StylesDTO> styles;

    public String getSampleNumber() {
        return sampleNumber;
    }

    public void setSampleNumber(String sampleNumber) {
        this.sampleNumber = sampleNumber;
    }

    public String getSampleNumberName() {
        return sampleNumberName;
    }

    public void setSampleNumberName(String sampleNumberName) {
        this.sampleNumberName = sampleNumberName;
    }

    public String getSampleWeight() {
        return sampleWeight;
    }

    public void setSampleWeight(String sampleWeight) {
        this.sampleWeight = sampleWeight;
    }

    public String getSampleWeightName() {
        return sampleWeightName;
    }

    public void setSampleWeightName(String sampleWeightName) {
        this.sampleWeightName = sampleWeightName;
    }

    public List<StylesDTO> getStyles() {
        return styles;
    }

    public void setStyles(List<StylesDTO> styles) {
        this.styles = styles;
    }
}
