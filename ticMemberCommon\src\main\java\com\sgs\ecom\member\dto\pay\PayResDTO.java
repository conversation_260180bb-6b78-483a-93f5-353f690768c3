package com.sgs.ecom.member.dto.pay;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;

import java.util.List;

/**
 * <AUTHOR> zhang
 * @date 2022年08月15日 13:22
 */
public class PayResDTO {
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String transNo;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String orderType;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private List<String> orderNoList;

    public List<String> getOrderNoList() {
        return orderNoList;
    }

    public void setOrderNoList(List<String> orderNoList) {
        this.orderNoList = orderNoList;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getTransNo() {
        return transNo;
    }

    public void setTransNo(String transNo) {
        this.transNo = transNo;
    }
}
