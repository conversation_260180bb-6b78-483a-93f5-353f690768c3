package com.sgs.ecom.member.bo; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import com.platform.annotation.CharacterVaild; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import com.platform.annotation.BeanAnno; 

public class BOUserAddress{
 
 	public static final String SEQUENCE = "ADDRESS_ID"; 
  
 	public static final String BO_SQL = "USER_ADDRESS"; 
 
 	public static final String OWNER ="member";

 	public static final String STATE_DATE="stateDate";
 	public static final String USER_ID="userId";
 	public static final String STATE="state";
 	public static final String USER_NAME="userName";
 	public static final String COMPANY_ADDRESS="companyAddress";
 	public static final String COMPANY_NAME="companyName";
 	public static final String USER_PHONE="userPhone";
 	public static final String CREATE_DATE="createDate";
 	public static final String CITY="city";
 	public static final String TOWN="town";
 	public static final String PROVINCE="province";
 	public static final String IS_DEFAULT="isDefault";
 	public static final String ADDRESS_ID="addressId";
 	public static final String USER_MAIL="userMail";

 	@BeanAnno("STATE_DATE")
 	private Timestamp stateDate;
 	@BeanAnno("USER_ID")
 	private long userId;
 	@BeanAnno("STATE")
 	private int state;
 	@BeanAnno("USER_NAME")
 	private String userName;
 	@BeanAnno("COMPANY_ADDRESS")
 	private String companyAddress;
 	@BeanAnno("COMPANY_NAME")
 	private String companyName;
 	@BeanAnno("USER_PHONE")
 	private String userPhone;
 	@BeanAnno("CREATE_DATE")
 	private Timestamp createDate;
 	@BeanAnno("CITY")
 	private String city;
 	@BeanAnno("TOWN")
 	private String town;
 	@BeanAnno("PROVINCE")
 	private String province;
 	@BeanAnno("IS_DEFAULT")
 	private int isDefault;
 	@BeanAnno("ADDRESS_ID")
 	private long addressId;
 	@BeanAnno("USER_MAIL")
 	private String userMail;

 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setUserId(long userId){
 		 this.userId=userId;
 	}
 	public long getUserId(){
 		 return this.userId;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setUserName(String userName){
 		 this.userName=userName;
 	}
 	public String getUserName(){
 		 return this.userName;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setCompanyAddress(String companyAddress){
 		 this.companyAddress=companyAddress;
 	}
 	public String getCompanyAddress(){
 		 return this.companyAddress;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setCompanyName(String companyName){
 		 this.companyName=companyName;
 	}
 	public String getCompanyName(){
 		 return this.companyName;
 	}
 
 	 
 	@CharacterVaild(len = 20) 
 	public void setUserPhone(String userPhone){
 		 this.userPhone=userPhone;
 	}
 	public String getUserPhone(){
 		 return this.userPhone;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setCity(String city){
 		 this.city=city;
 	}
 	public String getCity(){
 		 return this.city;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setTown(String town){
 		 this.town=town;
 	}
 	public String getTown(){
 		 return this.town;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setProvince(String province){
 		 this.province=province;
 	}
 	public String getProvince(){
 		 return this.province;
 	}
 
 	 
 	public void setIsDefault(int isDefault){
 		 this.isDefault=isDefault;
 	}
 	public int getIsDefault(){
 		 return this.isDefault;
 	}
 
 	 
 	public void setAddressId(long addressId){
 		 this.addressId=addressId;
 	}
 	public long getAddressId(){
 		 return this.addressId;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setUserMail(String userMail){
 		 this.userMail=userMail;
 	}
 	public String getUserMail(){
 		 return this.userMail;
 	}
 
 	 
}