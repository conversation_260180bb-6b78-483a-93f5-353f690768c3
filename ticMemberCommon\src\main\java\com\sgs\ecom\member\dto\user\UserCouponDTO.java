package com.sgs.ecom.member.dto.user;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.json.DateFormatSerializer;
import com.platform.util.json.TimeFormatSerializer;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.util.time.TimeCalendarUtil;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

public class UserCouponDTO extends BaseQryFilter {

	public static final String CREATE_SQL = "select AREA_ID,EXP_DATE,COUPON_NAME,STATE_DATE,USER_ID,STATE,DISCOUNT_RATE,ORDER_NO,IS_COMPOSIT,DISCOUNT_LIMIT,COUPON_ID,BUY_PRICE,DISCOUNT_RULE,USE_AMOUNT,CREATE_DATE,USE_LIMIT,COUPON_DESC,SKU_ID,COUPON_TYPE,STORE_ID,COUPON_CODE,EFF_DATE,COUPON_SCOPE from USER_COUPON";

	private long userId;
	private long areaId;
	private long skuId;
	private long storeId;
	private BigDecimal discountRate;
	private int discountNums;
	private int discountRule;

	@ApiAnno(groups={Default.class})
	@BeanAnno(value="COUPON_NAME", getName="getCouponName", setName="setCouponName")
	private String couponName;
	@ApiAnno(groups={Default.class})
	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
	private Timestamp stateDate;

	@ApiAnno(groups={Default.class})
	@BeanAnno(value="STATE", getName="getState", setName="setState")
	private int state;

	@ApiAnno(groups={Default.class})
	@BeanAnno(value="ORDER_NO", getName="getOrderNo", setName="setOrderNo")
	private String orderNo;
	@ApiAnno(groups={Default.class})
	@BeanAnno(value="IS_COMPOSIT", getName="getIsComposit", setName="setIsComposit")
	private int isComposit;

	@ApiAnno(groups={Default.class})
	@BeanAnno(value="COUPON_ID", getName="getCouponId", setName="setCouponId")
	private long couponId;
	@ApiAnno(groups={Default.class})
	@BeanAnno(value="BUY_PRICE", getName="getBuyPrice", setName="setBuyPrice")
	private int buyPrice;

	@ApiAnno(groups={Default.class})
	@BeanAnno(value="USE_AMOUNT", getName="getUseAmount", setName="setUseAmount")
	private BigDecimal useAmount;
	@ApiAnno(groups={Default.class})
	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
	private Timestamp createDate;
	@ApiAnno(groups={Default.class})
	@BeanAnno(value="USE_LIMIT", getName="getUseLimit", setName="setUseLimit")
	private int useLimit;
	@ApiAnno(groups={Default.class})
	@BeanAnno(value="COUPON_DESC", getName="getCouponDesc", setName="setCouponDesc")
	private String couponDesc;


	@ApiAnno(groups={Default.class})
	@BeanAnno(value="COUPON_TYPE", getName="getCouponType", setName="setCouponType")
	private int couponType;
	@ApiAnno(groups={Default.class})
	@BeanAnno(value="COUPON_CODE", getName="getCouponCode", setName="setCouponCode")
	private String couponCode;

	@ApiAnno(groups={Default.class})
	@BeanAnno(value="COUPON_SCOPE", getName="getCouponScope", setName="setCouponScope")
	private String couponScope;
	@ApiAnno(groups={Default.class})
	private String couponNo;
	@ApiAnno(groups={Default.class})
	private BigDecimal discountAmount;
	@ApiAnno(groups={Default.class})
	private int couponFlg=1;



	@ApiAnno(groups={Default.class})
	private String effDate;

	@ApiAnno(groups={Default.class})
	private String expDate;
	@ApiAnno(groups={Default.class})
	private int timeFlg;//0是能使用 1是未生效
	@ApiAnno(groups={Default.class})
	private String showName;
	@ApiAnno(groups={Default.class})
	private String couponExplain;
	@ApiAnno(groups={Default.class})
	private String couponRange;
	@ApiAnno(groups={Default.class})
	private String CouponUrl;


	public void setAreaId(long areaId){
		this.areaId=areaId;
	}
	public long getAreaId(){
		return this.areaId;
	}



	public void setCouponName(String couponName){
		this.couponName=couponName;
	}
	public String getCouponName(){
		return this.couponName;
	}


	public void setStateDate(Timestamp stateDate){
		this.stateDate=stateDate;
	}
	@JsonSerialize(using = TimeFormatSerializer.class)
	public Timestamp getStateDate(){
		return this.stateDate;
	}


	public void setUserId(long userId){
		this.userId=userId;
	}
	public long getUserId(){
		return this.userId;
	}


	public void setState(int state){
		this.state=state;
	}
	public int getState(){
		return this.state;
	}


	public BigDecimal getDiscountRate() {
		return discountRate;
	}

	public void setDiscountRate(BigDecimal discountRate) {
		this.discountRate = discountRate;
	}

	public void setOrderNo(String orderNo){
		this.orderNo=orderNo;
	}
	public String getOrderNo(){
		return this.orderNo;
	}


	public void setIsComposit(int isComposit){
		this.isComposit=isComposit;
	}
	public int getIsComposit(){
		return this.isComposit;
	}


	public int getDiscountNums() {
		return discountNums;
	}

	public void setDiscountNums(int discountNums) {
		this.discountNums = discountNums;
	}

	public void setCouponId(long couponId){
		this.couponId=couponId;
	}
	public long getCouponId(){
		return this.couponId;
	}


	public void setBuyPrice(int buyPrice){
		this.buyPrice=buyPrice;
	}
	public int getBuyPrice(){
		return this.buyPrice;
	}


	public void setDiscountRule(int discountRule){
		this.discountRule=discountRule;
	}
	public int getDiscountRule(){
		return this.discountRule;
	}


	public BigDecimal getUseAmount() {
		return useAmount;
	}

	public void setUseAmount(BigDecimal useAmount) {
		this.useAmount = useAmount;
	}

	public int getCouponFlg() {
		return couponFlg;
	}

	public void setCouponFlg(int couponFlg) {
		this.couponFlg = couponFlg;
	}

	public void setCreateDate(Timestamp createDate){
		this.createDate=createDate;
	}
	@JsonSerialize(using = TimeFormatSerializer.class)
	public Timestamp getCreateDate(){
		return this.createDate;
	}


	public void setUseLimit(int useLimit){
		this.useLimit=useLimit;
	}
	public int getUseLimit(){
		return this.useLimit;
	}


	public void setCouponDesc(String couponDesc){
		this.couponDesc=couponDesc;
	}
	public String getCouponDesc(){
		return this.couponDesc;
	}


	public void setSkuId(long skuId){
		this.skuId=skuId;
	}
	public long getSkuId(){
		return this.skuId;
	}


	public void setCouponType(int couponType){
		this.couponType=couponType;
	}
	public int getCouponType(){
		return this.couponType;
	}


	public void setStoreId(long storeId){
		this.storeId=storeId;
	}
	public long getStoreId(){
		return this.storeId;
	}


	public void setCouponCode(String couponCode){
		this.couponCode=couponCode;
	}
	public String getCouponCode(){
		return this.couponCode;
	}




	public void setCouponScope(String couponScope){
		this.couponScope=couponScope;
	}
	public String getCouponScope(){
		return this.couponScope;
	}

	public String getCouponNo() {
		return couponNo;
	}

	public void setCouponNo(String couponNo) {
		this.couponNo = couponNo;
	}

	public BigDecimal getDiscountAmount() {
		return discountAmount;
	}

	public void setDiscountAmount(BigDecimal discountAmount) {
		this.discountAmount = discountAmount;
	}

	public String getEffDate() {
		if(StringUtils.isNotBlank(effDate)){
			String str=effDate.split("\\.")[0];
			return str.replaceAll("-",".");
		}

		return effDate;
	}

	public void setEffDate(String effDate) {
		this.effDate = effDate;
	}

	public String getExpDate() {
		if(StringUtils.isNotBlank(expDate)){
			String str=expDate.split("\\.")[0];
			return str.replaceAll("-",".");
		}
		return expDate;
	}

	public void setExpDate(String expDate) {
		this.expDate = expDate;
	}

	public int getTimeFlg() {
		return timeFlg;
	}

	public void setTimeFlg(int timeFlg) {
		this.timeFlg = timeFlg;
	}

	public String getShowName() {
		return showName;
	}

	public void setShowName(String showName) {
		this.showName = showName;
	}

	public String getCouponExplain() {
		return couponExplain;
	}

	public void setCouponExplain(String couponExplain) {
		this.couponExplain = couponExplain;
	}

	public String getCouponRange() {
		return couponRange;
	}

	public void setCouponRange(String couponRange) {
		this.couponRange = couponRange;
	}

	public String getCouponUrl() {
		return CouponUrl;
	}

	public void setCouponUrl(String couponUrl) {
		CouponUrl = couponUrl;
	}
}
