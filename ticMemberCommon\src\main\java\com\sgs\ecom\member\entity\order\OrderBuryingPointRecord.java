package com.sgs.ecom.member.entity.order;

public class OrderBuryingPointRecord {

    private Long recordId;
    private String orderNo;
    private String buryingType;
    private String buryingAction;
    private String buryingUrl;
    private Long accountType;
    private Long accountId;
    private String createDate;

    public Long getRecordId() {
        return recordId;
    }

    public void setRecordId(Long recordId) {
        this.recordId = recordId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getBuryingType() {
        return buryingType;
    }

    public void setBuryingType(String buryingType) {
        this.buryingType = buryingType;
    }

    public String getBuryingAction() {
        return buryingAction;
    }

    public void setBuryingAction(String buryingAction) {
        this.buryingAction = buryingAction;
    }

    public String getBuryingUrl() {
        return buryingUrl;
    }

    public void setBuryingUrl(String buryingUrl) {
        this.buryingUrl = buryingUrl;
    }

    public Long getAccountType() {
        return accountType;
    }

    public void setAccountType(Long accountType) {
        this.accountType = accountType;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }
}
