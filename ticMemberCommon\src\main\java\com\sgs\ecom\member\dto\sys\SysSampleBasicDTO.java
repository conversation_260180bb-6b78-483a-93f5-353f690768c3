package com.sgs.ecom.member.dto.sys;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.json.TimeFormatSerializer;
import com.sgs.base.BaseQryFilter;

import java.sql.Timestamp;

public class SysSampleBasicDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select BUSINESS_ID,CREATE_DATE,BASIC_ID,BUSINESS_TYPE,IS_MUST,IS_MERGE,STATE_DATE,STATE,SAMPLE_KEY_NAME,SAMPLE_KEY from SYS_SAMPLE_BASIC"; 
 
 
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="BUSINESS_ID", getName="getBusinessId", setName="setBusinessId")
 	private long businessId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="BASIC_ID", getName="getBasicId", setName="setBasicId")
 	private long basicId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="BUSINESS_TYPE", getName="getBusinessType", setName="setBusinessType")
 	private String businessType;
 	@ApiAnno(groups={Default.class,QuerySummary.class,QueryList.class})
 	@BeanAnno(value="IS_MUST", getName="getIsMust", setName="setIsMust")
 	private int isMust;
 	@ApiAnno(groups={Default.class,QuerySummary.class,QueryList.class})
 	@BeanAnno(value="IS_MERGE", getName="getIsMerge", setName="setIsMerge")
 	private int isMerge;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private Timestamp stateDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
	@ApiAnno(groups={Default.class,QuerySummary.class,QueryList.class})
	@BeanAnno(value="SAMPLE_KEY_NAME", getName="getSampleKeyName", setName="setSampleKeyName")
	private String sampleKeyName;
	@ApiAnno(groups={Default.class,QuerySummary.class,QueryList.class})
	@BeanAnno(value="SAMPLE_KEY_NAME_EN", getName="getSampleKeyNameEn", setName="setSampleKeyNameEn")
	private String sampleKeyNameEn;
 	@ApiAnno(groups={Default.class,QuerySummary.class,QueryList.class})
 	@BeanAnno(value="SAMPLE_KEY", getName="getSampleKey", setName="setSampleKey")
 	private String sampleKey;
 	@ApiAnno(groups={Default.class,QuerySummary.class,QueryList.class})
 	@BeanAnno(value="SORT_SHOW", getName="getSortShow", setName="setSortShow")
 	private int sortShow;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="BUSINESS_CODE", getName="businessCode", setName="businessCode")
 	private String businessCode;
 	@ApiAnno(groups={Default.class,QuerySummary.class,QueryList.class})
 	@BeanAnno(value="TYPE", getName="getType", setName="setType")
 	private int type;
 	@ApiAnno(groups={Default.class,QuerySummary.class,QueryList.class})
 	@BeanAnno(value="DATA_TYPE", getName="getDataType", setName="setDataType")
 	private String dataType;
 	@ApiAnno(groups={Default.class,QuerySummary.class,QueryList.class})
 	@BeanAnno(value="FILL_NOTICE", getName="getFillNotice", setName="setFillNotice")
 	private String fillNotice;
 	@ApiAnno(groups={Default.class,QuerySummary.class,QueryList.class})
 	@BeanAnno(value="FILL_NOTICE_EN", getName="getFillNoticeEn", setName="setFillNoticeEn")
 	private String fillNoticeEn;
 	@ApiAnno(groups={Default.class,QuerySummary.class,QueryList.class})
 	@BeanAnno(value="ROW_NUM", getName="getRowNum", setName="setRowNum")
 	private int rowNum;
 	@ApiAnno(groups={Default.class,QuerySummary.class,QueryList.class})
 	@BeanAnno(value="FILL_LEN", getName="getFillLen", setName="setFillLen")
 	private int fillLen;
 	@ApiAnno(groups={Default.class,QuerySummary.class,QueryList.class})
 	@BeanAnno(value="IS_TITLE", getName="getIsTitle", setName="setIsTitle")
 	private int isTitle;
 	@ApiAnno(groups={Default.class,QuerySummary.class,QueryList.class})
 	@BeanAnno(value="LUA", getName="getLua", setName="setLua")
 	private String lua;
 	@ApiAnno(groups={Default.class,QuerySummary.class,QueryList.class})
 	@BeanAnno(value="ENUM_CONFIG", getName="getEnumConfig", setName="setEnumConfig")
 	private String enumConfig;
 	@ApiAnno(groups={Default.class,QuerySummary.class,QueryList.class})
 	@BeanAnno(value="IS_HIDE", getName="getIsHide", setName="setIsHide")
 	private int isHide;
	@ApiAnno(groups={Default.class,QuerySummary.class,QueryList.class})
	@BeanAnno(value="AREA_CODE", getName="getAreaCode", setName="setAreaCode")
	private String areaCode;
	@ApiAnno(groups={Default.class,QuerySummary.class,QueryList.class})
	@BeanAnno(value="REGEX_RULE", getName="getRegexRule", setName="setRegexRule")
	private String regexRule;
	@ApiAnno(groups={Default.class,QuerySummary.class,QueryList.class})
	@BeanAnno(value="ATTR_RULES", getName="getAttrRules", setName="setAttrRules")
	private String attrRules;

	@ApiAnno(groups={Default.class,QuerySummary.class,QueryList.class})
	private String enums;

	@ApiAnno(groups={Default.class,QuerySummary.class,QueryList.class})
	private String showLua;

	@ApiAnno(groups={Default.class,QuerySummary.class,QueryList.class})
	private String showAttr;

	public String getShowAttr() {
		return showAttr;
	}

	public void setShowAttr(String showAttr) {
		this.showAttr = showAttr;
	}

	public String getShowLua() {
		return showLua;
	}

	public void setShowLua(String showLua) {
		this.showLua = showLua;
	}

 	public void setBusinessId(long businessId){
 		 this.businessId=businessId;
 	}
 	public long getBusinessId(){
 		 return this.businessId;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setBasicId(long basicId){
 		 this.basicId=basicId;
 	}
 	public long getBasicId(){
 		 return this.basicId;
 	}
 
 	 
 	public void setBusinessType(String businessType){
 		 this.businessType=businessType;
 	}
 	public String getBusinessType(){
 		 return this.businessType;
 	}
 
 	 
 	public void setIsMust(int isMust){
 		 this.isMust=isMust;
 	}
 	public int getIsMust(){
 		 return this.isMust;
 	}
 
 	 
 	public void setIsMerge(int isMerge){
 		 this.isMerge=isMerge;
 	}
 	public int getIsMerge(){
 		 return this.isMerge;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setSampleKeyName(String sampleKeyName){
 		 this.sampleKeyName=sampleKeyName;
 	}
 	public String getSampleKeyName(){
 		 return this.sampleKeyName;
 	}
 
 	 
 	public void setSampleKey(String sampleKey){
 		 this.sampleKey=sampleKey;
 	}
 	public String getSampleKey(){
 		 return this.sampleKey;
 	}
	public int getSortShow() {
		return sortShow;
	}
	public void setSortShow(int sortShow) {
		this.sortShow = sortShow;
	}
	public String getBusinessCode() {
		return businessCode;
	}
	public void setBusinessCode(String businessCode) {
		this.businessCode = businessCode;
	}
	public int getType() {
		return type;
	}
	public void setType(int type) {
		this.type = type;
	}
	public String getDataType() {
		return dataType;
	}
	public void setDataType(String dataType) {
		this.dataType = dataType;
	}
	public String getFillNotice() {
		return fillNotice;
	}
	public void setFillNotice(String fillNotice) {
		this.fillNotice = fillNotice;
	}
	public int getRowNum() {
		return rowNum;
	}
	public void setRowNum(int rowNum) {
		this.rowNum = rowNum;
	}
	public int getFillLen() {
		return fillLen;
	}
	public void setFillLen(int fillLen) {
		this.fillLen = fillLen;
	}
	public int getIsTitle() {
		return isTitle;
	}
	public void setIsTitle(int isTitle) {
		this.isTitle = isTitle;
	}
	public String getLua() {
		return lua;
	}
	public void setLua(String lua) {
		this.lua = lua;
	}
	public String getEnumConfig() {
		return enumConfig;
	}
	public void setEnumConfig(String enumConfig) {
		this.enumConfig = enumConfig;
	}
	public String getFillNoticeEn() {
		return fillNoticeEn;
	}
	public void setFillNoticeEn(String fillNoticeEn) {
		this.fillNoticeEn = fillNoticeEn;
	}
	public int getIsHide() {
		return isHide;
	}
	public void setIsHide(int isHide) {
		this.isHide = isHide;
	}

	public String getAreaCode() {
		return areaCode;
	}

	public void setAreaCode(String areaCode) {
		this.areaCode = areaCode;
	}

	public String getRegexRule() {
		return regexRule;
	}

	public void setRegexRule(String regexRule) {
		this.regexRule = regexRule;
	}

	public String getEnums() {
		return enums;
	}

	public void setEnums(String enums) {
		this.enums = enums;
	}

	public String getAttrRules() {
		return attrRules;
	}

	public void setAttrRules(String attrRules) {
		this.attrRules = attrRules;
	}

	public String getSampleKeyNameEn() {
		return sampleKeyNameEn;
	}

	public void setSampleKeyNameEn(String sampleKeyNameEn) {
		this.sampleKeyNameEn = sampleKeyNameEn;
	}
}