package com.sgs.ecom.member.base;

import com.sgs.ecom.member.dto.send.OiqOtherMailDTO;
import com.sgs.ecom.member.dto.send.TicOtherMailDTO;
import com.sgs.ecom.member.enumtool.OiqMailEnum;

import java.util.HashMap;
import java.util.Map;

public class MailBaseSend {
	// mailType 1客户 2客服
	//String orderNo, String mail, String sendCC, Map map, OiqMailEnum oiqMailEnum, String buType, Long mailType
	private String orderNo;
	private String sendMail;
	private String sendFrom;
	private String sendCC;
	private Map<String,Object> mapData=new HashMap<>();
	private String mailEnum;
	private String buType;
	private Long mailType;//1用户 2客服
	private Boolean useOrderId;
	private Long orderId;
	private TicOtherMailDTO ticOtherMailDTO;
	private Long prodId;
	private int orderType;
	private Boolean isSub;//是否是子单
	private OiqOtherMailDTO oiqOtherMailDTO;
	private String sendType;
	private Long userId;
	public String getSendFrom() {
		return sendFrom;
	}

	public void setSendFrom(String sendFrom) {
		this.sendFrom = sendFrom;
	}

	public MailBaseSend() {
	}

	//不使用service数据
	public MailBaseSend(String orderNo, String sendMail, String sendCC, Map<String, Object> mapData, String mailEnum, String buType, Long mailType, Long prodId) {
		this.orderNo = orderNo;
		this.sendMail = sendMail;
		this.sendCC = sendCC;
		this.mapData = mapData;
		this.mailEnum = mailEnum;
		this.buType = buType;
		this.mailType = mailType;
		this.prodId=prodId;
		this.useOrderId=false;
	}

	public MailBaseSend(String orderNo, OiqMailEnum oiqMailEnum, Long mailType, OiqOtherMailDTO oiqOtherMailDTO){
		this.orderNo = orderNo;
		this.mailEnum = oiqMailEnum.getIndex();
		this.mailType=mailType;
		this.useOrderId=true;
		this.oiqOtherMailDTO=oiqOtherMailDTO;
	}


	public MailBaseSend(String orderNo, OiqMailEnum oiqMailEnum, Long mailType){
		this.orderNo = orderNo;
		this.mailEnum = oiqMailEnum.getIndex();
		this.mailType=mailType;
		this.useOrderId=true;
	}

	public MailBaseSend(Long orderId, OiqMailEnum oiqMailEnum, Long mailType, TicOtherMailDTO ticOtherMailDTO){
		this.orderId = orderId;
		this.mailEnum = oiqMailEnum.getIndex();
		this.mailType=mailType;
		this.useOrderId=true;
		this.ticOtherMailDTO=ticOtherMailDTO;
	}

	public MailBaseSend(Long orderId, OiqMailEnum oiqMailEnum, Long mailType, TicOtherMailDTO ticOtherMailDTO, Boolean isSub){
		this.orderId = orderId;
		this.mailEnum = oiqMailEnum.getIndex();
		this.mailType=mailType;
		this.useOrderId=true;
		this.ticOtherMailDTO=ticOtherMailDTO;
		this.isSub=isSub;
	}



	public MailBaseSend(OiqMailEnum oiqMailEnum, long mailType, TicOtherMailDTO ticOtherMailDTO) {
		this.mailEnum = oiqMailEnum.getIndex();
		this.mailType=mailType;
		this.ticOtherMailDTO=ticOtherMailDTO;
		this.sendType = "reportAuthenticity";
	}

	public String getSendType() {
		return sendType;
	}

	public void setSendType(String sendType) {
		this.sendType = sendType;
	}


	public OiqOtherMailDTO getOiqOtherMailDTO() {
		return oiqOtherMailDTO;
	}

	public void setOiqOtherMailDTO(OiqOtherMailDTO oiqOtherMailDTO) {
		this.oiqOtherMailDTO = oiqOtherMailDTO;
	}

	public Boolean getSub() {
		return isSub;
	}

	public void setSub(Boolean sub) {
		isSub = sub;
	}

	public int getOrderType() {
		return orderType;
	}

	public void setOrderType(int orderType) {
		this.orderType = orderType;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getSendMail() {
		return sendMail;
	}

	public void setSendMail(String sendMail) {
		this.sendMail = sendMail;
	}

	public String getSendCC() {
		return sendCC;
	}

	public void setSendCC(String sendCC) {
		this.sendCC = sendCC;
	}

	public Map<String, Object> getMapData() {
		return mapData;
	}

	public void setMapData(Map<String, Object> mapData) {
		this.mapData = mapData;
	}

	public String getMailEnum() {
		return mailEnum;
	}

	public void setMailEnum(String mailEnum) {
		this.mailEnum = mailEnum;
	}

	public String getBuType() {
		return buType;
	}

	public void setBuType(String buType) {
		this.buType = buType;
	}

	public Long getMailType() {
		return mailType;
	}

	public void setMailType(Long mailType) {
		this.mailType = mailType;
	}


	public Boolean getUseOrderId() {
		return useOrderId;
	}

	public void setUseOrderId(Boolean useOrderId) {
		this.useOrderId = useOrderId;
	}

	public Long getOrderId() {
		return orderId;
	}

	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}

	public Long getProdId() {
		return prodId;
	}

	public void setProdId(Long prodId) {
		this.prodId = prodId;
	}

	public TicOtherMailDTO getTicOtherMailDTO() {
		return ticOtherMailDTO;
	}

	public void setTicOtherMailDTO(TicOtherMailDTO ticOtherMailDTO) {
		this.ticOtherMailDTO = ticOtherMailDTO;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}
}
