package com.sgs.ecom.member.enumtool.aplication;

public enum ReportLuaFormEnum {

    CHI("CHI", "CHI", "中文"),
    EN("EN", "EN", "英文"),
    CHI_EN("CHI-EN", "CHI-EN", "中英文");




    ReportLuaFormEnum(String name, String index, String nameCh) {
        this.name = name;
        this.index = index;
        this.nameCh = nameCh;
    }


    private String name;
    private String index;
    private String nameCh;

    public static String getIndexByNameCh(String nameCh) {
        for (ReportLuaFormEnum c : ReportLuaFormEnum.values()) {
            if (c.getNameCh().equals(nameCh)) {
                return c.getIndex();
            }
        }
        return "";
    }

    public static boolean checkIsReportLua(String reportLua) {
        for (ReportLuaFormEnum c : ReportLuaFormEnum.values()) {
            if (c.getNameCh().equals(reportLua)) {
                return true;
            }
        }

        return false;
    }


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }
}
