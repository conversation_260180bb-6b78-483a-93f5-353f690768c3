package com.sgs.ecom.member.enumtool.send;

public enum WechatEnum {


    USER_SAVE_FORM("710015","客户提交申请表 发给客户","您的申请表提交成功，请寄送样品和申请表"),

    EDIT_REPORT("710010","发送报告文件","您的报告已上传/已更新，可下载"),//A



/*    CLOSE("3","方案即将关闭"),
    FORM_CONFIRM("4","申请表确认"),
    END("5","订单已完成"),*/




    ;
    private String index;
    private String nameCh;
    private String memo;

    WechatEnum( String index, String nameCh,String memo) {

        this.index = index;
        this.nameCh = nameCh;
        this.memo=memo;
    }




    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }
}
