<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.user.dao.UserLabelMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.entity.UserLabel" >
    <id column="LABEL_ID" property="labelId" jdbcType="BIGINT" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="LABEL_GROUP" property="labelGroup" jdbcType="VARCHAR" />
    <result column="LABEL_CODE" property="labelCode" jdbcType="VARCHAR" />
    <result column="LABEL_VALUE" property="labelValue" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="INTEGER" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    LABEL_ID, USER_ID, LABEL_GROUP, LABEL_CODE, LABEL_VALUE, STATE, CREATE_DATE, STATE_DATE
  </sql>
  <select id="qryUserLabel" resultMap="BaseResultMap"  >
    select 
    <include refid="Base_Column_List" />
    from USER_LABEL
    where USER_ID = #{userId,jdbcType=BIGINT}
    and LABEL_CODE = #{labelCode,jdbcType=VARCHAR}
    and LABEL_VALUE = #{labelValue,jdbcType=VARCHAR}
    and STATE = #{state}
    limit 1
  </select>
  <select id="qryUserLabelByCode" resultMap="BaseResultMap"  >
    select 
    <include refid="Base_Column_List" />
    from USER_LABEL
    where USER_ID = #{userId,jdbcType=BIGINT}
    and LABEL_CODE = #{labelCode,jdbcType=VARCHAR}
    and LABEL_GROUP = #{labelGroup,jdbcType=VARCHAR}
    and STATE = #{state}
    limit 1
  </select>
  <insert id="insert" parameterType="com.sgs.ecom.member.entity.UserLabel">
    insert into USER_LABEL
    ( USER_ID, LABEL_GROUP, LABEL_CODE,
    LABEL_VALUE, STATE)
    values
    ( #{userId,jdbcType=BIGINT}, #{labelGroup,jdbcType=VARCHAR}, #{labelCode,jdbcType=VARCHAR},
      #{labelValue,jdbcType=VARCHAR}, #{state,jdbcType=INTEGER} )
  </insert>
  <insert id="insertBatch">
    insert into USER_LABEL
    ( USER_ID,
    LABEL_GROUP,
    LABEL_CODE,
    LABEL_VALUE,
    STATE,
    CREATE_DATE,
    STATE_DATE)
    values
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.userId},
      #{item.labelGroup},
      #{item.labelCode},
      #{item.labelValue},
      #{item.state},
      #{item.createDate},
      #{item.stateDate})
    </foreach>

  </insert>


  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.ecom.member.entity.UserLabel" >
    update USER_LABEL
    <set >
      <if test="userId != null" >
        USER_ID = #{userId,jdbcType=BIGINT},
      </if>
      <if test="labelGroup != null" >
        LABEL_GROUP = #{labelGroup,jdbcType=VARCHAR},
      </if>
      <if test="labelCode != null" >
        LABEL_CODE = #{labelCode,jdbcType=VARCHAR},
      </if>
      <if test="labelValue != null" >
        LABEL_VALUE = #{labelValue,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        STATE = #{state,jdbcType=INTEGER},
      </if>
      <if test="stateDate != null" >
        STATE_DATE = #{stateDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where LABEL_ID = #{labelId,jdbcType=BIGINT}
  </update>


  <resultMap id="resultMap" type="com.sgs.ecom.member.dto.user.UserLabelDTO" >
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="LABEL_VALUE" property="labelValue" jdbcType="VARCHAR" />
  </resultMap>

  <select id="selectListByVOUserLabel" resultMap="resultMap"  >
    select USER_ID,LABEL_VALUE
    from USER_LABEL
    where STATE =1
    and LABEL_CODE = #{labelCode,jdbcType=VARCHAR}
    and LABEL_GROUP = #{labelGroup,jdbcType=VARCHAR}
    and LABEL_VALUE = #{labelValue,jdbcType=VARCHAR}
    <if test="userIdList != null ">
      AND USER_ID in
      <foreach collection="userIdList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </select>

</mapper>