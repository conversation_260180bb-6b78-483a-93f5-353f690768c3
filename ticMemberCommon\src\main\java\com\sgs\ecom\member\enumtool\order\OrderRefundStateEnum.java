package com.sgs.ecom.member.enumtool.order;


public enum OrderRefundStateEnum {
    NOOPERATOR("noOperator", 0, "未操作", "未操作", "未操作"),
    REFUND("refund", 1, "申请退款", "待客服确认更新", "待客服确认更新"),
    SUCCESS("success", 2, "退款成功", "客服已确认退款申请，请耐心等待返还","客服已确认"),
    FAIL("fail", 3, "退款拒绝", "客服已取消退款申请", "客服已取消"),
    REFUND_CONFIRMATION("refundConfirmation", 4, "退款中", "退款中", "退款中"),
    PART_REFUND_CONFIRMATION("partRefundConfirmation", 5, "有部分退款", "有部分退款", "有部分退款"),
    ;
    OrderRefundStateEnum(String name, int index, String nameCh, String nameChCus,String nameChServ) {
        this.name = name;
        this.index = index;
        this.nameCh = nameCh;
        this.nameChCus = nameChCus;
        this.nameChServ = nameChServ;
    }

    public static String getName(int index) {
        for (OrderRefundStateEnum c : OrderRefundStateEnum.values()) {
            if (c.getIndex()==index) {
                return c.getName();
            }
        }
        return null;
    }

    public static OrderRefundStateEnum getEnum(int index) {
        for (OrderRefundStateEnum c : OrderRefundStateEnum.values()) {
            if (c.getIndex()==index) {
                return c;
            }
        }
        return null;
    }

    public static String getNameCh(int index) {
        for (OrderRefundStateEnum c : OrderRefundStateEnum.values()) {
            if (c.getIndex()==index) {
                return c.getNameCh();
            }
        }
        return null;
    }





    private String name;
    private int index;
    private String nameCh;
    private String nameChCus;
    private String nameChServ;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }

	public String getNameChCus() {
		return nameChCus;
	}

	public void setNameChCus(String nameChCus) {
		this.nameChCus = nameChCus;
	}

	public String getNameChServ() {
		return nameChServ;
	}

	public void setNameChServ(String nameChServ) {
		this.nameChServ = nameChServ;
	}
    
    
}
