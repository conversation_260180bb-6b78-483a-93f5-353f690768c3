package com.sgs.ecom.member.controller.order;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.ecom.member.request.OrderReportReq;
import com.sgs.ecom.member.service.order.interfaces.IOrderAttachmentService;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.order/attchment")
public class OrderAttchmentController extends ControllerUtil {

    @Autowired
    private IOrderAttachmentService orderAttachmentService;
    
    /**
     *@Function: reportDownloadOne
     *@Description 客户下载单个报告
     *@param: [OrderReportReq, token]
     *@author: sundeqing @date: 2022/5/18 @version: 
     **/
     @HystrixCommand
     @AuthRequired(login = "SSO", sign = true)
     @RequestMapping(value = "reportDownloadOne", method = { RequestMethod.POST })
     public ResultBody reportDownloadOne(
             @RequestBody OrderReportReq orderReportReq,
             @RequestHeader(value="accessToken") String token) throws Exception {
    	 return ResultBody.newInstance(orderAttachmentService.reportDownloadOne(orderReportReq, getUserDTO(token)));
     }
     
     /**
      *@Function: reportDownloadOne
      *@Description 客户下载所有报告
      *@param: [OrderReportReq, token]
      *@author: sundeqing @date: 2022/5/18 @version: 
      **/
     @HystrixCommand
     @AuthRequired(login = "SSO", sign = true)
     @RequestMapping(value = "reportDownloadALL", method = { RequestMethod.POST })
     public ResultBody reportDownloadALL(
    		 @RequestBody OrderReportReq orderReportReq,
    		 @RequestHeader(value="accessToken") String token) throws Exception {
    	 orderAttachmentService.reportDownloadALL(orderReportReq, getUserDTO(token));
    	 return ResultBody.success();
     }
}
