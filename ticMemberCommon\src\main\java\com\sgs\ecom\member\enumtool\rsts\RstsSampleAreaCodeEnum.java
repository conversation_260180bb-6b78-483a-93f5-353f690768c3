package com.sgs.ecom.member.enumtool.rsts;

import org.apache.commons.lang.StringUtils;

/**
 * @Description : rsts样品
 * <AUTHOR>
 * @Date  2023/10/13
 **/
public enum RstsSampleAreaCodeEnum {

    BASIC("BASIC", 1, "BASIC"),
    OTHER_ROHS("OTHER-ROHS", 2, "OTHER-ROHS"),
    OTHER_EN71("OTHER-EN71", 3, "OTHER-EN71"),
    OTHER_PAHS("OTHER-PAHS", 4, "OTHER-PAHS"),
    ;

    RstsSampleAreaCodeEnum(String name, int index, String nameCh) {
        this.name = name;
        this.index = index;
        this.nameCh = nameCh;
    }




    private String name;
    private int index;
    private String nameCh;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }
}
