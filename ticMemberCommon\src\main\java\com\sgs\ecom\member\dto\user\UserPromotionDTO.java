package com.sgs.ecom.member.dto.user;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.sql.Timestamp;
import java.util.List;

import com.platform.util.json.TimeFormatSerializer;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.sgs.base.BaseQryFilter;

public class UserPromotionDTO extends BaseQryFilter {

	public static final String CREATE_SQL = "select FIRST_UPGRADE_DATE,USER_PHONE,CREATE_DATE,ACTIVITY_SOURCE,PERSON_CODE,STATE_DATE,USER_ID,STATE,ACTIVITY_CODE,ACTIVITY_ID,PROMOTION_CODE from USER_PROMOTION";


	@ApiAnno(groups={Default.class})
	@BeanAnno(value="FIRST_UPGRADE_DATE", getName="getFirstUpgradeDate", setName="setFirstUpgradeDate")
	private Timestamp firstUpgradeDate;
	@ApiAnno(groups={Default.class})
	@BeanAnno(value="USER_PHONE", getName="getUserPhone", setName="setUserPhone")
	private String userPhone;
	@ApiAnno(groups={Default.class})
	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
	private Timestamp createDate;
	@ApiAnno(groups={Default.class})
	@BeanAnno(value="ACTIVITY_SOURCE", getName="getActivitySource", setName="setActivitySource")
	private String activitySource;
	@ApiAnno(groups={Default.class})
	@BeanAnno(value="PERSON_CODE", getName="getPersonCode", setName="setPersonCode")
	private String personCode;
	@ApiAnno(groups={Default.class})
	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
	private Timestamp stateDate;
	@ApiAnno(groups={Default.class})
	@BeanAnno(value="USER_ID", getName="getUserId", setName="setUserId")
	private long userId;
	@ApiAnno(groups={Default.class})
	@BeanAnno(value="STATE", getName="getState", setName="setState")
	private int state;
	@ApiAnno(groups={Default.class,QuerySummary.class})
	@BeanAnno(value="ACTIVITY_CODE", getName="getActivityCode", setName="setActivityCode")
	private String activityCode;
	@ApiAnno(groups={Default.class})
	@BeanAnno(value="ACTIVITY_ID", getName="getActivityId", setName="setActivityId")
	private long activityId;
	@ApiAnno(groups={Default.class})
	@BeanAnno(value="PROMOTION_CODE", getName="getPromotionCode", setName="setPromotionCode")
	private String promotionCode;
	@ApiAnno(groups={Default.class})
	private List<UserPromotionAttrDTO> attrDTOList;

	public void setFirstUpgradeDate(Timestamp firstUpgradeDate){
		this.firstUpgradeDate=firstUpgradeDate;
	}
	@JsonSerialize(using = TimeFormatSerializer.class)
	public Timestamp getFirstUpgradeDate(){
		return this.firstUpgradeDate;
	}


	public void setUserPhone(String userPhone){
		this.userPhone=userPhone;
	}
	public String getUserPhone(){
		return this.userPhone;
	}


	public void setCreateDate(Timestamp createDate){
		this.createDate=createDate;
	}
	@JsonSerialize(using = TimeFormatSerializer.class)
	public Timestamp getCreateDate(){
		return this.createDate;
	}


	public void setActivitySource(String activitySource){
		this.activitySource=activitySource;
	}
	public String getActivitySource(){
		return this.activitySource;
	}


	public void setPersonCode(String personCode){
		this.personCode=personCode;
	}
	public String getPersonCode(){
		return this.personCode;
	}


	public void setStateDate(Timestamp stateDate){
		this.stateDate=stateDate;
	}
	@JsonSerialize(using = TimeFormatSerializer.class)
	public Timestamp getStateDate(){
		return this.stateDate;
	}


	public void setUserId(long userId){
		this.userId=userId;
	}
	public long getUserId(){
		return this.userId;
	}


	public void setState(int state){
		this.state=state;
	}
	public int getState(){
		return this.state;
	}


	public void setActivityCode(String activityCode){
		this.activityCode=activityCode;
	}
	public String getActivityCode(){
		return this.activityCode;
	}


	public void setActivityId(long activityId){
		this.activityId=activityId;
	}
	public long getActivityId(){
		return this.activityId;
	}


	public void setPromotionCode(String promotionCode){
		this.promotionCode=promotionCode;
	}
	public String getPromotionCode(){
		return this.promotionCode;
	}

	public List<UserPromotionAttrDTO> getAttrDTOList() {
		return attrDTOList;
	}

	public void setAttrDTOList(List<UserPromotionAttrDTO> attrDTOList) {
		this.attrDTOList = attrDTOList;
	}
}