package com.sgs.ecom.member.dto.user;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.base.BaseOrderFilter;
import lombok.Data;

@Data
public class UserAddressDTO{
 
 	public static final String CREATE_SQL = "select STATE_DATE,USER_ID,STATE,USER_NAME,COMPANY_ADDRESS,COMPANY_NAME,USER_PHONE,CREATE_DATE,CITY,TOWN,PROVINCE,IS_DEFAULT,ADDRESS_ID,USER_MAIL from USER_ADDRESS"; 
 

 	private long userId;
 	private int state;
	@ApiAnno(groups={BaseQryFilter.Default.class, BaseOrderFilter.OiqFormInfo.class})
 	private String userName;
	@ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqFormInfo.class})
 	private String companyAddress;
	@ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqFormInfo.class})
 	private String companyName;
	@ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqFormInfo.class})
 	private String userPhone;
	@ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqFormInfo.class})
 	private String city;
	@ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqFormInfo.class})
 	private String town;
	@ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqFormInfo.class})
 	private String province;
	@ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqFormInfo.class})
 	private int isDefault;
	@ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqFormInfo.class})
 	private Long addressId;
	@ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqFormInfo.class})
 	private String userMail;
	@ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqFormInfo.class})
	private String addressUuid;

}