package com.sgs.ecom.member.aop;

import com.platform.util.SysException;
import com.platform.util.ValidationUtil;
import com.sgs.ecom.member.domain.cust.service.interfaces.ICustApplyRelateDomainService;
import com.sgs.ecom.member.dto.CustInfoDTO;
import com.sgs.ecom.member.dto.cust.CustInfoRelateDTO;
import com.sgs.ecom.member.dto.user.UserDTO;
import com.sgs.ecom.member.enumtool.ResultEnumCode;
import com.sgs.ecom.member.service.custom.interfaces.ICustInfoSV;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.collection.MapUtil;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Aspect
@Component
public class VerifyCustPermission extends ControllerUtil{
    @Resource
    private ICustInfoSV custInfoSV;
    @Resource
    private ICustApplyRelateDomainService custApplyRelateDomainService;

    @Pointcut("@annotation(com.sgs.ecom.member.annotation.VerifyCust)")
    @Order(10)
    public void VerifyCust() {

    }



    @Before("VerifyCust()")
    public void CheckUserToolKitPermission(JoinPoint joinPoint) throws Exception {

        Object[] args = joinPoint.getArgs();
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes())
                .getRequest();
        String accessToken = request.getHeader("accessToken");

        UserDTO userDTO=getUserDTO(accessToken);
        if(ValidationUtil.isEmpty(userDTO))
            throw new SysException("9978");

        if(args.length <=1  )
            throw new BusinessException(ResultEnumCode.EDIT_UNABLE);

        Map<String, Object> map = MapUtil.objectToMap(args[1]);
        Long custId =  (Long)map.get("custId");
        if(0 == custId)
            throw new BusinessException(ResultEnumCode.EDIT_UNABLE);

        //校验企业信息
        CustInfoDTO custInfoDTO = custInfoSV.qryCustInfo(custId);
        if(ValidationUtil.isEmpty(custInfoDTO))
            throw new BusinessException(ResultEnumCode.EDIT_UNABLE);

        List<CustInfoRelateDTO> custInfoRelateDTOS = custApplyRelateDomainService.qryCustByUser(userDTO.getUserId(), "1302");
        if(ValidationUtil.isEmpty(custInfoRelateDTOS))
            throw new BusinessException(ResultEnumCode.EDIT_UNABLE);

        Map<Long, List<CustInfoRelateDTO>> custMap = custInfoRelateDTOS.stream()
                .collect(Collectors.groupingBy(E -> E.getCustId()));
        if(!custMap.containsKey(custId)){
            throw new BusinessException(ResultEnumCode.EDIT_UNABLE);
        }
    }
}
