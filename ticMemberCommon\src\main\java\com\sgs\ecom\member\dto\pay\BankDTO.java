package com.sgs.ecom.member.dto.pay;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.base.BaseOrderFilter;

public class BankDTO {
    @ApiAnno(groups={BaseQryFilter.Default.class, BaseQryFilter.QueryList.class, BaseOrderFilter.WordQuotation.class,BaseOrderFilter.WordForm.class})
    private String accountNo;
    @ApiAnno(groups={BaseQryFilter.Default.class, BaseQryFilter.QueryList.class, BaseOrderFilter.WordQuotation.class,BaseOrderFilter.WordForm.class})
    private String accountName;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String invoiceName;
    @ApiAnno(groups={BaseQryFilter.Default.class, BaseQryFilter.QueryList.class, BaseOrderFilter.WordQuotation.class,BaseOrderFilter.WordForm.class})
    private String bankNo;
    @ApiAnno(groups={BaseQryFilter.Default.class, BaseQryFilter.QueryList.class, BaseOrderFilter.WordQuotation.class,BaseOrderFilter.WordForm.class})
    private String bankName;
    @ApiAnno(groups={BaseQryFilter.Default.class, BaseQryFilter.QueryList.class, BaseOrderFilter.WordQuotation.class,BaseOrderFilter.WordForm.class})
    private String bankAddress;
    @ApiAnno(groups={BaseQryFilter.Default.class, BaseQryFilter.QueryList.class, BaseOrderFilter.WordQuotation.class,BaseOrderFilter.WordForm.class})
    private String bankTel;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String buCode;
    @ApiAnno(groups={BaseQryFilter.Default.class, BaseQryFilter.QueryList.class, BaseOrderFilter.WordQuotation.class,BaseOrderFilter.WordForm.class})
    private String bankBranchNo;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String sealImg;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String sealImgUrl;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String base64Url;
    @ApiAnno(groups={BaseOrderFilter.WordQuotation.class})
    private String swiftCode;

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getBankNo() {
        return bankNo;
    }

    public void setBankNo(String bankNo) {
        this.bankNo = bankNo;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankAddress() {
        return bankAddress;
    }

    public void setBankAddress(String bankAddress) {
        this.bankAddress = bankAddress;
    }

    public String getBankTel() {
        return bankTel;
    }

    public void setBankTel(String bankTel) {
        this.bankTel = bankTel;
    }

    public String getBuCode() {
        return buCode;
    }

    public void setBuCode(String buCode) {
        this.buCode = buCode;
    }

    public String getBankBranchNo() {
        return bankBranchNo;
    }

    public void setBankBranchNo(String bankBranchNo) {
        this.bankBranchNo = bankBranchNo;
    }

    public String getSealImg() {
        return sealImg;
    }

    public void setSealImg(String sealImg) {
        this.sealImg = sealImg;
    }

    public String getInvoiceName() {
        this.invoiceName=accountName;
        return invoiceName;
    }

    public void setInvoiceName(String invoiceName) {
        this.invoiceName = invoiceName;
    }

    public String getSealImgUrl() {
        return sealImgUrl;
    }

    public void setSealImgUrl(String sealImgUrl) {
        this.sealImgUrl = sealImgUrl;
    }

    public String getBase64Url() {
        return base64Url;
    }

    public void setBase64Url(String base64Url) {
        this.base64Url = base64Url;
    }

    public String getSwiftCode() {
        return swiftCode;
    }

    public void setSwiftCode(String swiftCode) {
        this.swiftCode = swiftCode;
    }
}
