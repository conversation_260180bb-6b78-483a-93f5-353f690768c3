package com.sgs.ecom.member.dto.oiq.applicationInfo;

import com.platform.annotation.ApiAnno;
import com.sgs.ecom.member.base.BaseOrderFilter;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class OrderShippingContactDTO {
    /**类型*/
    private String contactType;

    /**城市*/
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String contactCountry;

    /**城市*/
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String contactCity;

    /**地址*/
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String contactAddress;

    /**邮编*/
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String contactZipCode;

    /**联系人*/
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String contactName;

    /**联系电话*/
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String contactPhone;

    /**联系邮箱*/
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String contactEmail;

    /**联系人FXI号码*/
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String contactFxiNo;

    /**联系人纳税号*/
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String contactTaxNo;

    /**联系人交易号*/
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String contactTransNo;

    /**账号*/
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String accountNumber;
}
