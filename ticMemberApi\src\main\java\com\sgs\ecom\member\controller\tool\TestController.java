package com.sgs.ecom.member.controller.tool;

import com.alibaba.fastjson.JSON;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.sgs.ecom.member.base.BaseApi;
import com.sgs.ecom.member.enumtool.event.EventEnum;
import com.sgs.ecom.member.request.OrderReportReq;
import com.sgs.ecom.member.service.event.ApiEventService;
import com.sgs.ecom.member.util.ResultBody;
import com.sgs.ecom.member.util.send.ApiOtherDTO;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/business/api.v2.order/test")
public class TestController {

    @Autowired
    private ApiEventService api;

    @RequestMapping(value = "test", method = { RequestMethod.POST })
    public ResultBody fileList(
            @RequestBody JSONObject jsonObject) throws Exception {
        BaseApi baseApi=new BaseApi();
        baseApi.getApiOtherDTO().setDmlFormStr(JSON.toJSONString(jsonObject));
        baseApi.setOrderNo("TIC190020241025155956D5IM");
        baseApi.setEventEnum(EventEnum.SAVE_TO_SEND_DML_MAIL);
        api.saveToSendMail(baseApi);
        return null;
    }







}
