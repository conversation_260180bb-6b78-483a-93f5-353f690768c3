package com.sgs.ecom.member.controller.tic;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.base.BaseBean;
import com.sgs.ecom.member.base.BaseValidatedBean;
import com.sgs.ecom.member.request.tic.TicOrderLinkReq;
import com.sgs.ecom.member.service.order.interfaces.IOrderLinkSV;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.tic/orderLink")
public class TicOrderLinkController extends ControllerUtil {

	@Autowired
	private IOrderLinkSV iOrderLinkSV;

	/**
	 * @Description: SL-PX/CBE学员信息保存
	 * @Author: bowen zhang
	 * @Date: 2022/8/30
	 * @param token:
	 * @param ticOrderLinkReq:
	 * @return: com.sgs.ecom.member.util.ResultBody
	 **/
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "save", method = {RequestMethod.POST})
	public ResultBody save(
		@RequestHeader(value = "accessToken") String token,
		@Validated(BaseBean.Query.class)
		@RequestBody TicOrderLinkReq ticOrderLinkReq)throws Exception {
		iOrderLinkSV.save(ticOrderLinkReq, getUserDTO(token));
		return ResultBody.success();
	}

	/**
	 * @Description: 学员信息查询
	 * @Author: bowen zhang
	 * @Date: 2022/8/30
	 * @param token:
	 * @param ticOrderLinkReq:
	 * @return: com.sgs.ecom.member.util.ResultBody
	 **/
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "qry", method = {RequestMethod.POST})
	public ResultBody qry(
			@RequestHeader(value = "accessToken") String token,
			@Validated(BaseBean.Query.class)
			@RequestBody TicOrderLinkReq ticOrderLinkReq)throws Exception {
		return ResultBody.success(iOrderLinkSV.qry(ticOrderLinkReq, getUserDTO(token)));
	}

	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "del", method = {RequestMethod.POST})
	public ResultBody del(
			@RequestHeader(value = "accessToken") String token,
			@RequestBody TicOrderLinkReq ticOrderLinkReq)throws Exception {
			iOrderLinkSV.del(ticOrderLinkReq, getUserDTO(token));
		return ResultBody.success();
	}

	/**
	 * @Description :导入学员信息
	 * <AUTHOR> Zhang
	 * @Date  2024/9/2
	 * @param token:
	 * @param ticOrderLinkReq:
	 * @return: com.sgs.ecom.member.util.ResultBody
	 **/
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "impOrderLink", method = {RequestMethod.POST})
	public ResultBody impOrderLink(
			@RequestHeader(value = "accessToken") String token,
			@Validated(BaseValidatedBean.BatchImp.class)
			@RequestBody TicOrderLinkReq ticOrderLinkReq)throws Exception {
		return ResultBody.success(iOrderLinkSV.impOrderLink(ticOrderLinkReq, getUserDTO(token)));
	}


}
