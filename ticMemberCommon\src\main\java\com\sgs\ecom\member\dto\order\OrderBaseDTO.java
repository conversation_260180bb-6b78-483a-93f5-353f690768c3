package com.sgs.ecom.member.dto.order;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.dto.oiq.TfsOrderDTO;
import com.sgs.ecom.member.enumtool.BaseOrderStateEnum;
import com.sgs.ecom.member.util.serializer.TimeStringFormatSerializer;

public class OrderBaseDTO {
    @ApiAnno(groups={BaseOrderFilter.OiqOrderDetail.class})
    private String orderNo;
    @JsonSerialize(using = TimeStringFormatSerializer.class)
    @ApiAnno(groups={BaseOrderFilter.OiqOrderDetail.class})
    private String createDate;
    @ApiAnno(groups={BaseOrderFilter.OiqOrderDetail.class})
    private Integer state;
    @ApiAnno(groups={BaseOrderFilter.OiqOrderDetail.class})
    private String relateOrderNo;
    @ApiAnno(groups={BaseOrderFilter.OiqOrderDetail.class})
    private String stateShow;
    @ApiAnno(groups={BaseOrderFilter.OiqOrderDetail.class})
    private String orderType;
    @JsonSerialize(using = TimeStringFormatSerializer.class)
    @ApiAnno(groups={BaseOrderFilter.OiqOrderDetail.class})
    private String deadlineTime;
    @JsonSerialize(using = TimeStringFormatSerializer.class)
    @ApiAnno(groups={BaseOrderFilter.OiqOrderDetail.class})
    private String applySubmitDate;
    @ApiAnno(groups={BaseOrderFilter.OiqOrderDetail.class})
    private Integer payState;
    @ApiAnno(groups={BaseOrderFilter.OiqOrderDetail.class})
    private Integer isPayReceived;
    @ApiAnno(groups={BaseOrderFilter.OiqOrderDetail.class})
    private Integer refundState;

    @ApiAnno(groups={ BaseOrderFilter.OiqOrderDetail.class})
    private String itemNameStr;
    @ApiAnno(groups={ BaseOrderFilter.OiqOrderDetail.class})
    private String sampleNameStr;
    @ApiAnno(groups={ BaseOrderFilter.OiqOrderDetail.class})
    private TfsOrderDTO tfsOrder;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getRelateOrderNo() {
        return relateOrderNo;
    }

    public void setRelateOrderNo(String relateOrderNo) {
        this.relateOrderNo = relateOrderNo;
    }

    public String getStateShow() {
        String show=BaseOrderStateEnum.getNameCh(state);
        return show==null?"":show;
    }

    public void setStateShow(String stateShow) {
        this.stateShow = stateShow;
    }



    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getDeadlineTime() {
        return deadlineTime;
    }

    public void setDeadlineTime(String deadlineTime) {
        this.deadlineTime = deadlineTime;
    }

    public Integer getPayState() {
        return payState;
    }

    public void setPayState(Integer payState) {
        this.payState = payState;
    }

    public Integer getRefundState() {
        return refundState;
    }

    public void setRefundState(Integer refundState) {
        this.refundState = refundState;
    }

    public String getApplySubmitDate() {
        return applySubmitDate;
    }

    public void setApplySubmitDate(String applySubmitDate) {
        this.applySubmitDate = applySubmitDate;
    }

    public Integer getIsPayReceived() {
        return isPayReceived;
    }

    public void setIsPayReceived(Integer isPayReceived) {
        this.isPayReceived = isPayReceived;
    }

    public String getItemNameStr() {
        return itemNameStr;
    }

    public void setItemNameStr(String itemNameStr) {
        this.itemNameStr = itemNameStr;
    }

    public String getSampleNameStr() {
        return sampleNameStr;
    }

    public void setSampleNameStr(String sampleNameStr) {
        this.sampleNameStr = sampleNameStr;
    }

    public TfsOrderDTO getTfsOrder() {
        return tfsOrder;
    }

    public void setTfsOrder(TfsOrderDTO tfsOrder) {
        this.tfsOrder = tfsOrder;
    }
}
