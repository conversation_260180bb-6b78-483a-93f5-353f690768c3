package com.sgs.ecom.member.controller.log;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.base.BaseBean;
import com.sgs.ecom.member.enumtool.event.EventEnum;
import com.sgs.ecom.member.event.EventApiUtil;
import com.sgs.ecom.member.request.LogAddReq;
import com.sgs.ecom.member.request.LogReq;
import com.sgs.ecom.member.service.order.interfaces.IOrderLogService;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})

@RestController
@RequestMapping("/business/api.v2.order/log")
public class UserOiqLogController extends ControllerUtil {

	@Autowired
	private IOrderLogService orderLogService;
	@Resource
	private EventApiUtil eventApiUtil;


	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "addMsg", method = {RequestMethod.POST})
	public ResultBody addMsg(
		@RequestHeader(value = "accessToken") String token,
		@Validated(BaseBean.Insert.class)
		@RequestBody LogAddReq logAddReq) throws Exception {
		orderLogService.addMsg(logAddReq, getUserDTO(token));
		return ResultBody.success();
	}

	/**
	* @params [token, logReq]
	* @return com.sgs.ecom.member.util.ResultBody
	* @description 确认报价邮件 测试
	* <AUTHOR> || created at 2023/9/7 15:41
	*/
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "confirmMsg", method = {RequestMethod.POST})
	public ResultBody confirmMsg(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody LogReq logReq) throws Exception {
		JSONObject jsonObject=orderLogService.confirmMsg(logReq, getUserDTO(token));
		//把历史需要变更状态的数据变更下
		if(logReq.getEventFlg()==1){
			eventApiUtil.saveEvent(logReq.getEventOrderNo(), EventEnum.RO_CONFIRM_OLD,logReq.getApiOtherDTO());
		}
		return ResultBody.newInstance(jsonObject);
	}

	/**
	* 通用确认日志
	* @param token
	* @param logReq
	* @return com.sgs.ecom.member.util.ResultBody
	* <AUTHOR> || created at 2024/12/16 17:51
	* @throws Exception 抛出错误
	*/
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "confirmLog", method = {RequestMethod.POST})
	public ResultBody confirmLog(
			@RequestHeader(value = "accessToken") String token,
			@RequestBody LogReq logReq) throws Exception {
		orderLogService.confirmLog(logReq, getUserDTO(token));
		return ResultBody.success();
	}


	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "urge", method = {RequestMethod.POST})
	public ResultBody urge(
		@RequestHeader(value = "accessToken") String token,
		@Validated(BaseBean.Insert.class)
		@RequestBody LogAddReq logAddReq) throws Exception {
		Boolean flg = orderLogService.saveUrge(logAddReq, getUserDTO(token));
		if (flg) {
			return ResultBody.success("我们已经收到您的催一催，将尽快为您处理");
		} else {
			return ResultBody.error("false", "我们正在为您处理中，请耐心等待");
		}

	}


	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "list", method = {RequestMethod.POST})
	public ResultBody list(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody LogReq logReq
	) throws Exception {
		return ResultBody.newInstance(orderLogService.qryLogList(logReq, getUserDTO(token)));
	}


}
