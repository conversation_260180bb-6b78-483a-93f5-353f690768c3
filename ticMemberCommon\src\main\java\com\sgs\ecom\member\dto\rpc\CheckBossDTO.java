package com.sgs.ecom.member.dto.rpc;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.dto.detail.OrderInvoiceDTO;
import com.sgs.ecom.member.dto.user.UserInvoiceDTO;
import com.sgs.ecom.member.vo.VOOrderInvoice;
import com.sgs.ecom.member.vo.VOUserInfo;

/**
*@Function: TIC-10805
*@Description New和Update便于开票同事识别
*@param: 用于pdf判断
*@author: Xiwei_Qiu @date: 2022/6/30 @version:
**/
public class CheckBossDTO {

	private String invoiceTitle;
	private String taxNo;
	private String regAddress;
	private String bankName;
	private String bankNumber;
	private String regPhone;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private int tagFlg;//0没更新 1new 2update
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String tag;

	public CheckBossDTO() {
	}

	public CheckBossDTO(OrderInvoiceDTO orderInvoiceDTO) {
		this.invoiceTitle = orderInvoiceDTO.getInvoiceTitle();
		this.taxNo = orderInvoiceDTO.getTaxNo();
		this.regAddress = orderInvoiceDTO.getRegAddress();
		this.bankName = orderInvoiceDTO.getBankName();
		this.bankNumber = orderInvoiceDTO.getBankNumber();
		this.regPhone = orderInvoiceDTO.getRegPhone();
	}
	public CheckBossDTO(UserInvoiceDTO userInvoiceDTO) {
		this.invoiceTitle = userInvoiceDTO.getInvoiceTitle();
		this.taxNo = userInvoiceDTO.getTaxNo();
		this.regAddress = userInvoiceDTO.getRegAddress();
		this.bankName = userInvoiceDTO.getBankName();
		this.bankNumber = userInvoiceDTO.getBankNumber();
		this.regPhone = userInvoiceDTO.getRegPhone();
	}

	public CheckBossDTO(VOOrderInvoice voOrderInvoice) {
		this.invoiceTitle = voOrderInvoice.getInvoiceTitle();
		this.taxNo = voOrderInvoice.getTaxNo();
		this.regAddress = voOrderInvoice.getRegisterAddr();
		this.bankName = voOrderInvoice.getBankAddr();
		this.bankNumber = voOrderInvoice.getBankNumber();
		this.regPhone = voOrderInvoice.getRegisterPhone();
	}

	public String getInvoiceTitle() {
		return invoiceTitle;
	}

	public void setInvoiceTitle(String invoiceTitle) {
		this.invoiceTitle = invoiceTitle;
	}

	public String getTaxNo() {
		return taxNo;
	}

	public void setTaxNo(String taxNo) {
		this.taxNo = taxNo;
	}

	public String getRegAddress() {
		return regAddress;
	}

	public void setRegAddress(String regAddress) {
		this.regAddress = regAddress;
	}

	public String getBankName() {
		return bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}

	public String getBankNumber() {
		return bankNumber;
	}

	public void setBankNumber(String bankNumber) {
		this.bankNumber = bankNumber;
	}

	public String getRegPhone() {
		return regPhone;
	}

	public void setRegPhone(String regPhone) {
		this.regPhone = regPhone;
	}

	public String getTag() {
		return tag;
	}

	public void setTag(String tag) {
		this.tag = tag;
	}

	public int getTagFlg() {
		return tagFlg;
	}

	public void setTagFlg(int tagFlg) {
		this.tagFlg = tagFlg;
	}
}
