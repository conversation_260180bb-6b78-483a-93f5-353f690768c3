<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.user.dao.UserLikeMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.entity.user.UserLike" >
    <id column="LIKE_ID" property="likeId" jdbcType="BIGINT" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="LIKE_TYPE" property="likeType" jdbcType="VARCHAR" />
    <result column="BUSINESS_CODE" property="businessCode" jdbcType="VARCHAR" />
    <result column="PAGE_URL" property="pageUrl" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="TINYINT" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    LIKE_ID, USER_ID, LIKE_TYPE, BUSINESS_CODE, PAGE_URL, STATE, CREATE_DATE, STATE_DATE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from USER_LIKE
    where LIKE_ID = #{likeId,jdbcType=BIGINT}
  </select>
  <select id="qryLikeByBusi" resultMap="BaseResultMap" parameterType="com.sgs.ecom.member.vo.VOUserLike" >
    select 
    <include refid="Base_Column_List" />
    from USER_LIKE
    where USER_ID = #{userId,jdbcType=BIGINT}
    and LIKE_TYPE = #{likeType,jdbcType=VARCHAR}
    and BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR}
    and STATE = 1
  </select>
  <insert id="insertSelective" parameterType="com.sgs.ecom.member.entity.user.UserLike" >
    insert into USER_LIKE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="likeId != null" >
        LIKE_ID,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
      <if test="likeType != null" >
        LIKE_TYPE,
      </if>
      <if test="businessCode != null" >
        BUSINESS_CODE,
      </if>
      <if test="pageUrl != null" >
        PAGE_URL,
      </if>
      <if test="state != null" >
        STATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="likeId != null" >
        #{likeId,jdbcType=BIGINT},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="likeType != null" >
        #{likeType,jdbcType=VARCHAR},
      </if>
      <if test="businessCode != null" >
        #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="pageUrl != null" >
        #{pageUrl,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        #{state,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="unLike" parameterType="com.sgs.ecom.member.entity.user.UserLike" >
    update USER_LIKE set STATE = 0
    where LIKE_ID = #{likeId,jdbcType=BIGINT}
  </update>
</mapper>