package com.sgs.ecom.member.enumtool.pay;

public enum PayTypeEnum {
	PAY("pay", 1, "支付"),
	BACK("back", 2, "返销"),
	REFUND("refund", 3, "退款"),

	;
	PayTypeEnum(String name, int index, String nameCh) {
		this.name = name;
		this.index = index;
		this.nameCh = nameCh;
	}

	public static String getName(int index) {
		for (OrderPayRefundEnum c : OrderPayRefundEnum.values()) {
			if (c.getIndex()==index) {
				return c.getName();
			}
		}
		return null;
	}

	public static OrderPayRefundEnum getEnum(int index) {
		for (OrderPayRefundEnum c : OrderPayRefundEnum.values()) {
			if (c.getIndex()==index) {
				return c;
			}
		}
		return null;
	}

	public static String getNameCh(int index) {
		for (OrderPayRefundEnum c : OrderPayRefundEnum.values()) {
			if (c.getIndex()==index) {
				return c.getNameCh();
			}
		}
		return null;
	}





	private String name;
	private int index;
	private String nameCh;


	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public int getIndex() {
		return index;
	}

	public void setIndex(int index) {
		this.index = index;
	}

	public String getNameCh() {
		return nameCh;
	}

	public void setNameCh(String nameCh) {
		this.nameCh = nameCh;
	}
}
