package com.sgs.ecom.member.entity.sobot;

import com.platform.annotation.BeanAnno;
import com.sgs.ecom.member.dto.user.UserDTO;
import com.sgs.ecom.member.request.report.ReportAuthReq;

/**
 * <AUTHOR>
 * @Description :
 * @date 2023/6/24
 */
public class Ticket {
    private String user_nick;
    private String user_tels;
    private String user_emails;

    private String nick;
    private String email;
    private String tel;
    private Integer crm_status;

    public Ticket() {
    }

    public Ticket(ReportAuthReq reportAuthReq, UserDTO userDTO) {
        this.user_nick = reportAuthReq.getUserName();
        this.user_tels = reportAuthReq.getUserPhone();
        this.user_emails = reportAuthReq.getUserEmail();
        this.nick = reportAuthReq.getUserName();
        this.tel = reportAuthReq.getUserPhone();
        this.email = reportAuthReq.getUserEmail();
        this.crm_status = 4;
    }

    public String getUser_nick() {
        return user_nick;
    }

    public void setUser_nick(String user_nick) {
        this.user_nick = user_nick;
    }

    public String getUser_tels() {
        return user_tels;
    }

    public void setUser_tels(String user_tels) {
        this.user_tels = user_tels;
    }

    public String getUser_emails() {
        return user_emails;
    }

    public void setUser_emails(String user_emails) {
        this.user_emails = user_emails;
    }

    public String getNick() {
        return nick;
    }

    public void setNick(String nick) {
        this.nick = nick;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public Integer getCrm_status() {
        return crm_status;
    }

    public void setCrm_status(Integer crm_status) {
        this.crm_status = crm_status;
    }
}
