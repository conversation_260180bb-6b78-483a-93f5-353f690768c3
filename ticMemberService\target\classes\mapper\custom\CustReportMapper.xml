<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.mapper.custom.CustReportMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.entity.cust.CustReport" >
    <id column="REPORT_ID" property="reportId" jdbcType="BIGINT" />
    <result column="CUST_ID" property="custId" jdbcType="BIGINT" />
    <result column="CURRENCY" property="currency" jdbcType="VARCHAR" />
    <result column="BU" property="bu" jdbcType="VARCHAR" />
    <result column="REPORT_PRICE" property="reportPrice" jdbcType="DECIMAL" />
    <result column="TAX_RATE" property="taxRate" jdbcType="DECIMAL" />
    <result column="STATE" property="state" jdbcType="TINYINT" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    REPORT_ID, CUST_ID, CURRENCY, BU, REPORT_PRICE, TAX_RATE, STATE, CREATE_DATE, STATE_DATE
  </sql>
  <select id="qryReport" resultMap="BaseResultMap" parameterType="com.sgs.ecom.member.vo.VOCustReport" >
    select 
    <include refid="Base_Column_List" />
    from TB_CUST_REPORT
    where CURRENCY = #{currency,jdbcType=VARCHAR}
    AND CUST_ID = #{custId,jdbcType=BIGINT}
    AND BU = #{bu,jdbcType=VARCHAR}
    AND STATE = 1
    limit 1
  </select>


</mapper>