package com.sgs.ecom.member.enumtool.rsts;

import org.apache.commons.lang3.StringUtils;

public enum SampleStateEnum {
	ONE("one", 1, "固态"),
	TWO("two", 2, "液体/膏体"),
	THREE("three", 3, "粉末"),
		;

	SampleStateEnum(String name, int index, String nameCh) {
		this.name = name;
		this.index = index;
		this.nameCh = nameCh;
	}

	public static SampleStateEnum getEnum(String index) {
		if(StringUtils.isBlank(index)){
			return null;
		}
		for (SampleStateEnum c : SampleStateEnum.values()) {
			if (index.equals(String.valueOf(c.getIndex()))) {
				return c;
			}
		}
		return null;
	}

	public static String getNameCh(String index) {
		for (SampleStateEnum c : SampleStateEnum.values()) {
			if (index.equals(String.valueOf(c.getIndex()))) {
				return c.getNameCh();
			}
		}
		return "";
	}



	private String name;
	private int index;
	private String nameCh;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public int getIndex() {
		return index;
	}

	public void setIndex(int index) {
		this.index = index;
	}

	public String getNameCh() {
		return nameCh;
	}

	public void setNameCh(String nameCh) {
		this.nameCh = nameCh;
	}
}
