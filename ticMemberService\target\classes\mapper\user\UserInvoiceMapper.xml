<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.user.dao.UserInvoiceMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.vo.VOUserInvoice" >
    <id column="INVOICE_ID" property="invoiceId" jdbcType="BIGINT" />
    <id column="INVOICE_UUID" property="invoiceUuid" jdbcType="BIGINT" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="INVOICE_TYPE" property="invoiceType" jdbcType="TINYINT" />
    <result column="INVOICE_TITLE" property="invoiceTitle" jdbcType="VARCHAR" />
    <result column="TAX_NO" property="taxNo" jdbcType="VARCHAR" />
    <result column="REG_ADDRESS" property="regAddress" jdbcType="VARCHAR" />
    <result column="REG_PHONE" property="regPhone" jdbcType="VARCHAR" />
    <result column="BANK_NAME" property="bankName" jdbcType="VARCHAR" />
    <result column="BANK_NUMBER" property="bankNumber" jdbcType="VARCHAR" />
    <result column="IS_DEFAULT" property="isDefault" jdbcType="TINYINT" />
    <result column="STATE" property="state" jdbcType="TINYINT" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
    <result column="FRONT_IMG" property="frontImg" jdbcType="VARCHAR" />
    <result column="BACK_IMG" property="backImg" jdbcType="VARCHAR" />
    <result column="LINK_PERSON" property="linkPerson" jdbcType="VARCHAR" />
    <result column="LINK_PHONE" property="linkPhone" jdbcType="VARCHAR" />
    <result column="LINK_EMAIL" property="linkEmail" jdbcType="VARCHAR" />
    <result column="BOSS_NO" property="bossNo" jdbcType="VARCHAR" />
    <result column="EXTEND_FILES" property="extendFiles" jdbcType="VARCHAR" />
    <result column="IS_FOREIGN" property="isForeign" jdbcType="INTEGER" />
    <result column="COUNTRY" property="country" jdbcType="VARCHAR" />
    <result column="FOREIGN_CITY" property="foreignCity" jdbcType="VARCHAR" />
    <result column="POST_CODE" property="postCode" jdbcType="VARCHAR" />
    <result column="CONTACT" property="contact" jdbcType="VARCHAR" />
  </resultMap>

  <sql id="Base_Column_List" >
    INVOICE_ID,INVOICE_UUID, USER_ID, INVOICE_TYPE, INVOICE_TITLE, TAX_NO, REG_ADDRESS, REG_PHONE,
    BANK_NAME, BANK_NUMBER, IS_DEFAULT, STATE, CREATE_DATE, STATE_DATE,FRONT_IMG,BACK_IMG,
    LINK_PERSON, LINK_PHONE, LINK_EMAIL,BOSS_NO,EXTEND_FILES,IS_FOREIGN,COUNTRY,FOREIGN_CITY,POST_CODE,CONTACT
  </sql>
  <select id="selectVOByPrimaryKey" resultMap="BaseResultMap"  >
    select 
    <include refid="Base_Column_List" />
    from USER_INVOICE
    where INVOICE_ID = #{invoiceId}
  </select>

  <insert id="insertVOSelective" parameterType="com.sgs.ecom.member.vo.VOUserInvoice" >
    <selectKey resultType="java.lang.Long" keyProperty="invoiceId" order="AFTER">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into USER_INVOICE (INVOICE_ID, USER_ID, INVOICE_TYPE, IS_FOREIGN,
      INVOICE_TITLE, TAX_NO, REG_ADDRESS, 
      REG_PHONE, BANK_NAME, BANK_NUMBER, 
      IS_DEFAULT, STATE, CREATE_DATE, 
      STATE_DATE,FRONT_IMG,BACK_IMG,LINK_PERSON,LINK_PHONE,LINK_EMAIL,INVOICE_UUID,BOSS_NO,EXTEND_FILES,COUNTRY,FOREIGN_CITY,POST_CODE,CONTACT)
    values (#{invoiceId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{invoiceType,jdbcType=TINYINT},
      #{isForeign},#{invoiceTitle,jdbcType=VARCHAR},
      #{taxNo,jdbcType=VARCHAR}, #{regAddress,jdbcType=VARCHAR},
      #{regPhone,jdbcType=VARCHAR}, #{bankName,jdbcType=VARCHAR}, #{bankNumber,jdbcType=VARCHAR}, 
      #{isDefault,jdbcType=TINYINT}, #{state,jdbcType=TINYINT}, #{createDate,jdbcType=TIMESTAMP}, 
      #{stateDate,jdbcType=TIMESTAMP}, #{frontImg,jdbcType=VARCHAR}, #{backImg,jdbcType=VARCHAR},
      #{linkPerson,jdbcType=VARCHAR}, #{linkPhone,jdbcType=VARCHAR}, #{linkEmail,jdbcType=VARCHAR},
      #{invoiceUuid,jdbcType=VARCHAR}, #{bossNo,jdbcType=VARCHAR}, #{extendFiles,jdbcType=VARCHAR},
    #{country,jdbcType=VARCHAR}, #{foreignCity,jdbcType=VARCHAR}, #{postCode,jdbcType=VARCHAR}, #{contact,jdbcType=VARCHAR}
    )
  </insert>

  <insert id="insertSelectiveEntity" parameterType="com.sgs.ecom.member.entity.user.UserInvoice" >
    <selectKey resultType="java.lang.Long" keyProperty="invoiceId" order="AFTER">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into USER_INVOICE (INVOICE_ID, USER_ID, INVOICE_TYPE, IS_FOREIGN,
    INVOICE_TITLE, TAX_NO, REG_ADDRESS,
    REG_PHONE, BANK_NAME, BANK_NUMBER,
    IS_DEFAULT, STATE, CREATE_DATE,
    STATE_DATE,FRONT_IMG,BACK_IMG,LINK_PERSON,LINK_PHONE,LINK_EMAIL,INVOICE_UUID,BOSS_NO,EXTEND_FILES,COUNTRY,FOREIGN_CITY,POST_CODE,CONTACT)
    values (#{invoiceId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{invoiceType,jdbcType=TINYINT},
    #{isForeign},#{invoiceTitle,jdbcType=VARCHAR},
    #{taxNo,jdbcType=VARCHAR}, #{regAddress,jdbcType=VARCHAR},
    #{regPhone,jdbcType=VARCHAR}, #{bankName,jdbcType=VARCHAR}, #{bankNumber,jdbcType=VARCHAR},
    #{isDefault,jdbcType=TINYINT}, #{state,jdbcType=TINYINT}, #{createDate,jdbcType=TIMESTAMP},
    #{stateDate,jdbcType=TIMESTAMP}, #{frontImg,jdbcType=VARCHAR}, #{backImg,jdbcType=VARCHAR},
    #{linkPerson,jdbcType=VARCHAR}, #{linkPhone,jdbcType=VARCHAR}, #{linkEmail,jdbcType=VARCHAR},
    #{invoiceUuid,jdbcType=VARCHAR}, #{bossNo,jdbcType=VARCHAR}, #{extendFiles,jdbcType=VARCHAR},
    #{country,jdbcType=VARCHAR}, #{foreignCity,jdbcType=VARCHAR}, #{postCode,jdbcType=VARCHAR}, #{contact,jdbcType=VARCHAR}
    )
  </insert>
  <update id="updateVOByPrimaryKeySelective" parameterType="com.sgs.ecom.member.vo.VOUserInvoice" >
    update USER_INVOICE
    <set >
      <if test="userId != null" >
        USER_ID = #{userId,jdbcType=BIGINT},
      </if>
      <if test="invoiceType != null" >
        INVOICE_TYPE = #{invoiceType,jdbcType=TINYINT},
      </if>
      <if test="invoiceTitle != null" >
        INVOICE_TITLE = #{invoiceTitle,jdbcType=VARCHAR},
      </if>
      <if test="taxNo != null" >
        TAX_NO = #{taxNo,jdbcType=VARCHAR},
      </if>
      <if test="taxNo == null and isForeign ==1 " >
        TAX_NO = null,
      </if>
      <if test="regAddress != null" >
        REG_ADDRESS = #{regAddress,jdbcType=VARCHAR},
      </if>
      <if test="regAddress == null and isForeign ==1 " >
        REG_ADDRESS = null,
      </if>
      <if test="regPhone != null" >
        REG_PHONE = #{regPhone,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null" >
        BANK_NAME = #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="bankName == null and isForeign ==1 " >
        BANK_NAME = null,
      </if>
      <if test="bankNumber != null" >
        BANK_NUMBER = #{bankNumber,jdbcType=VARCHAR},
      </if>
      <if test="bankNumber == null and isForeign ==1 " >
        BANK_NUMBER = null,
      </if>
      <if test="isDefault != null" >
        IS_DEFAULT = #{isDefault,jdbcType=TINYINT},
      </if>
      <if test="state != null" >
        STATE = #{state,jdbcType=TINYINT},
      </if>
      <if test="createDate != null" >
        CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        STATE_DATE = #{stateDate,jdbcType=TIMESTAMP},
      </if>

      <if test="backImg != null" >
        BACK_IMG = #{backImg,jdbcType=VARCHAR},
      </if>
      <if test="backImg == null and isForeign ==1 " >
        BACK_IMG = null,
      </if>
      <if test="frontImg != null" >
        FRONT_IMG = #{frontImg,jdbcType=VARCHAR},
      </if>
      <if test="frontImg == null and isForeign ==1 " >
        FRONT_IMG = null,
      </if>
      <if test="linkPerson != null" >
        LINK_PERSON = #{linkPerson,jdbcType=VARCHAR},
      </if>
      <if test="linkPhone != null" >
        LINK_PHONE = #{linkPhone,jdbcType=VARCHAR},
      </if>
      <if test="linkEmail != null" >
        LINK_EMAIL = #{linkEmail,jdbcType=VARCHAR},
      </if>
      <if test="bossNo != null" >
        BOSS_NO = #{bossNo,jdbcType=VARCHAR},
      </if>
      <if test="extendFiles != null" >
        EXTEND_FILES = #{extendFiles,jdbcType=VARCHAR},
      </if>
      <if test="isForeign != null" >
        IS_FOREIGN = #{isForeign},
      </if>
      <if test="country != null" >
        COUNTRY = #{country},
      </if>
      <if test="country == null and isForeign ==0 " >
        COUNTRY = null,
      </if>
      <if test="foreignCity != null" >
        FOREIGN_CITY = #{foreignCity},
      </if>
      <if test="foreignCity == null and isForeign ==0 " >
        FOREIGN_CITY = null,
      </if>
      <if test="postCode != null" >
        POST_CODE = #{postCode},
      </if>
      <if test="postCode == null and isForeign ==0 " >
        POST_CODE = null,
      </if>
      <if test="contact != null" >
        CONTACT = #{contact},
      </if>
      <if test="contact == null and isForeign ==0 " >
        CONTACT = null,
      </if>
    </set>
    where INVOICE_ID = #{invoiceId,jdbcType=BIGINT}
  </update>


  <!--自定义 -->
  <sql id="sqlListDTO" >
    INVOICE_ID,USER_ID, INVOICE_TYPE, INVOICE_TITLE, TAX_NO, REG_ADDRESS, REG_PHONE,
    BANK_NAME, BANK_NUMBER, IS_DEFAULT, STATE, CREATE_DATE, STATE_DATE,BACK_IMG,FRONT_IMG,
    LINK_PERSON, LINK_PHONE, LINK_EMAIL,INVOICE_UUID,BOSS_NO,EXTEND_FILES,IS_FOREIGN,COUNTRY,FOREIGN_CITY,POST_CODE,CONTACT
  </sql>

  <resultMap id="resultDTO" type="com.sgs.ecom.member.dto.user.UserInvoiceDTO" >
    <id column="INVOICE_ID" property="invoiceId" jdbcType="BIGINT" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="INVOICE_TYPE" property="invoiceType" jdbcType="TINYINT" />
    <result column="INVOICE_TITLE" property="invoiceTitle" jdbcType="VARCHAR" />
    <result column="TAX_NO" property="taxNo" jdbcType="VARCHAR" />
    <result column="REG_ADDRESS" property="regAddress" jdbcType="VARCHAR" />
    <result column="REG_PHONE" property="regPhone" jdbcType="VARCHAR" />
    <result column="BANK_NAME" property="bankName" jdbcType="VARCHAR" />
    <result column="BANK_NUMBER" property="bankNumber" jdbcType="VARCHAR" />
    <result column="IS_DEFAULT" property="isDefault" jdbcType="TINYINT" />
    <result column="BACK_IMG" property="backImg" jdbcType="VARCHAR" />
    <result column="FRONT_IMG" property="frontImg" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="TINYINT" />
    <result column="LINK_PERSON" property="linkPerson" jdbcType="VARCHAR" />
    <result column="LINK_PHONE" property="linkPhone" jdbcType="VARCHAR" />
    <result column="LINK_EMAIL" property="linkEmail" jdbcType="VARCHAR" />
    <result column="INVOICE_UUID" property="invoiceUuid" jdbcType="VARCHAR" />
    <result column="BOSS_NO" property="bossNo" jdbcType="VARCHAR" />
    <result column="EXTEND_FILES" property="otherFileListStr" jdbcType="VARCHAR" />
    <result column="IS_FOREIGN" property="isForeign" jdbcType="INTEGER" />
    <result column="COUNTRY" property="country" jdbcType="VARCHAR" />
    <result column="FOREIGN_CITY" property="foreignCity" jdbcType="VARCHAR" />
    <result column="POST_CODE" property="postCode" jdbcType="VARCHAR" />
    <result column="CONTACT" property="contact" jdbcType="VARCHAR" />
  </resultMap>

  <select id="selectListByMap" resultMap="resultDTO" >
    select
    <include refid="sqlListDTO" />
    from USER_INVOICE
    <include refid="baseQueryWhere"/>
    <include refid="orderBy"/>
  </select>

  <sql id="baseQueryWhere">
    where 1=1
    <if test="userId != null">
      AND USER_ID=#{userId}
    </if>
    <if test="state != null">
      AND STATE=#{state}
    </if>
    <if test="invoiceId != null">
      AND INVOICE_ID=#{invoiceId}
    </if>
    <if test="invoiceType != null">
      AND INVOICE_TYPE=#{invoiceType}
    </if>
    <if test="invoiceTypeList != null ">
      AND INVOICE_TYPE in
      <foreach collection="invoiceTypeList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>

  </sql>

  <select id="updateDefault"  >
   update USER_INVOICE set IS_DEFAULT=0 where IS_DEFAULT=1 and USER_ID=#{userId}
    <if test="invoiceType != null">
      AND INVOICE_TYPE=#{invoiceType}
    </if>
  </select>





  <select id="selectByPrimaryKeyDTO" resultMap="resultDTO"  >
    select
    <include refid="sqlListDTO" />
    from USER_INVOICE
    where INVOICE_ID = #{invoiceId}
  </select>

  <select id="selectByUuidDTO" resultMap="resultDTO"  >
    select
    <include refid="sqlListDTO" />
    from USER_INVOICE
    where INVOICE_UUID = #{uuid}
    limit 1
  </select>

  <select id="selectNumByInvoice"  resultType="int" >
    select count(1) from USER_INVOICE where  USER_ID=#{userId} and STATE=1
  </select>



  <sql id="orderBy">
    <if test="orderBy != null ">
      order by ${orderBy}
    </if>
  </sql>
</mapper>