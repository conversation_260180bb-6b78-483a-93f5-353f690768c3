package com.sgs.ecom.member.bo; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import com.platform.annotation.CharacterVaild; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import com.platform.annotation.BeanAnno; 

public class BOUserInvoice{
 
 	public static final String SEQUENCE = "INVOICE_ID"; 
  
 	public static final String BO_SQL = "USER_INVOICE"; 
 
 	public static final String OWNER ="member";

 	public static final String TAX_NO="taxNo";
 	public static final String BANK_NUMBER="bankNumber";
 	public static final String STATE_DATE="stateDate";
 	public static final String USER_ID="userId";
 	public static final String STATE="state";
 	public static final String INVOICE_ID="invoiceId";
 	public static final String CREATE_DATE="createDate";
 	public static final String REG_ADDRESS="regAddress";
 	public static final String INVOICE_TYPE="invoiceType";
 	public static final String INVOICE_TITLE="invoiceTitle";
 	public static final String IS_DEFAULT="isDefault";
 	public static final String REG_PHONE="regPhone";
 	public static final String BANK_NAME="bankName";

 	@BeanAnno("TAX_NO")
 	private String taxNo;
 	@BeanAnno("BANK_NUMBER")
 	private String bankNumber;
 	@BeanAnno("STATE_DATE")
 	private Timestamp stateDate;
 	@BeanAnno("USER_ID")
 	private long userId;
 	@BeanAnno("STATE")
 	private int state;
 	@BeanAnno("INVOICE_ID")
 	private long invoiceId;
 	@BeanAnno("CREATE_DATE")
 	private Timestamp createDate;
 	@BeanAnno("REG_ADDRESS")
 	private String regAddress;
 	@BeanAnno("INVOICE_TYPE")
 	private int invoiceType;
 	@BeanAnno("INVOICE_TITLE")
 	private String invoiceTitle;
 	@BeanAnno("IS_DEFAULT")
 	private int isDefault;
 	@BeanAnno("REG_PHONE")
 	private String regPhone;
 	@BeanAnno("BANK_NAME")
 	private String bankName;

 	@CharacterVaild(len = 20) 
 	public void setTaxNo(String taxNo){
 		 this.taxNo=taxNo;
 	}
 	public String getTaxNo(){
 		 return this.taxNo;
 	}
 
 	 
 	@CharacterVaild(len = 32) 
 	public void setBankNumber(String bankNumber){
 		 this.bankNumber=bankNumber;
 	}
 	public String getBankNumber(){
 		 return this.bankNumber;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setUserId(long userId){
 		 this.userId=userId;
 	}
 	public long getUserId(){
 		 return this.userId;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setInvoiceId(long invoiceId){
 		 this.invoiceId=invoiceId;
 	}
 	public long getInvoiceId(){
 		 return this.invoiceId;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setRegAddress(String regAddress){
 		 this.regAddress=regAddress;
 	}
 	public String getRegAddress(){
 		 return this.regAddress;
 	}
 
 	 
 	public void setInvoiceType(int invoiceType){
 		 this.invoiceType=invoiceType;
 	}
 	public int getInvoiceType(){
 		 return this.invoiceType;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setInvoiceTitle(String invoiceTitle){
 		 this.invoiceTitle=invoiceTitle;
 	}
 	public String getInvoiceTitle(){
 		 return this.invoiceTitle;
 	}
 
 	 
 	public void setIsDefault(int isDefault){
 		 this.isDefault=isDefault;
 	}
 	public int getIsDefault(){
 		 return this.isDefault;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setRegPhone(String regPhone){
 		 this.regPhone=regPhone;
 	}
 	public String getRegPhone(){
 		 return this.regPhone;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setBankName(String bankName){
 		 this.bankName=bankName;
 	}
 	public String getBankName(){
 		 return this.bankName;
 	}
 
 	 
}