package com.sgs.ecom.member.dto; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import com.platform.annotation.ApiAnno; 
import com.platform.annotation.BeanAnno;
import com.platform.annotation.IsNullAnno;
import com.platform.annotation.UpdateAnno; 

public class UserInfoDTO{
 
 	public static final String CREATE_SQL = "select USER_NICK,LEVEL_NAME,USER_EMAIL,STATE_DATE,USER_ID,STATE,REG_IP,USER_NAME,U_ID,LEVEL_ID,USER_PHONE,CREATE_DATE,BBC_PWD,USER_PWD from USER_INFO"; 
 
 	@UpdateAnno(serviceName={"modUserInfo"})
 	@ApiAnno(serviceName={"qryUserInfoById"})
 	@BeanAnno(value="USER_NICK", getName="getUserNick", setName="setUserNick")
 	private String userNick;
 	@ApiAnno(serviceName={"qryUserInfoById"})
 	@BeanAnno(value="LEVEL_NAME", getName="getLevelName", setName="setLevelName")
 	private String levelName;
 	@UpdateAnno(serviceName={"modUserInfo"})
 	@ApiAnno(serviceName={"qryUserInfoById"})
 	@BeanAnno(value="USER_EMAIL", getName="getUserEmail", setName="setUserEmail")
 	private String userEmail;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private Timestamp stateDate;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="USER_ID", getName="getUserId", setName="setUserId")
 	private long userId;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private long state;
 	@ApiAnno(serviceName={"qryUserInfoById"})
 	@BeanAnno(value="REG_IP", getName="getRegIp", setName="setRegIp")
 	private String regIp;
 	@UpdateAnno(serviceName={"modUserInfo"})
 	@ApiAnno(serviceName={"qryUserInfoById"})
 	@BeanAnno(value="USER_NAME", getName="getUserName", setName="setUserName")
 	private String userName;
 	@IsNullAnno(serviceName={"qryMonthCreditById"})
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="U_ID", getName="getUId", setName="setUId")
 	private long uId;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="LEVEL_ID", getName="getLevelId", setName="setLevelId")
 	private long levelId;
 	@ApiAnno(serviceName={"qryUserInfoById"})
 	@BeanAnno(value="USER_PHONE", getName="getUserPhone", setName="setUserPhone")
 	private String userPhone;
 	@ApiAnno(serviceName={"qryUserInfoById"})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="BBC_PWD", getName="getBbcPwd", setName="setBbcPwd")
 	private String bbcPwd;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="USER_PWD", getName="getUserPwd", setName="setUserPwd")
 	private String userPwd;

 	public void setUserNick(String userNick){
 		 this.userNick=userNick;
 	}
 	public String getUserNick(){
 		 return this.userNick;
 	}
 
 	 
 	public void setLevelName(String levelName){
 		 this.levelName=levelName;
 	}
 	public String getLevelName(){
 		 return this.levelName;
 	}
 
 	 
 	public void setUserEmail(String userEmail){
 		 this.userEmail=userEmail;
 	}
 	public String getUserEmail(){
 		 return this.userEmail;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setUserId(long userId){
 		 this.userId=userId;
 	}
 	public long getUserId(){
 		 return this.userId;
 	}
 
 	 
 	public void setState(long state){
 		 this.state=state;
 	}
 	public long getState(){
 		 return this.state;
 	}
 
 	 
 	public void setRegIp(String regIp){
 		 this.regIp=regIp;
 	}
 	public String getRegIp(){
 		 return this.regIp;
 	}
 
 	 
 	public void setUserName(String userName){
 		 this.userName=userName;
 	}
 	public String getUserName(){
 		 return this.userName;
 	}
 
 	 
 	public void setUId(long uId){
 		 this.uId=uId;
 	}
 	public long getUId(){
 		 return this.uId;
 	}
 
 	 
 	public void setLevelId(long levelId){
 		 this.levelId=levelId;
 	}
 	public long getLevelId(){
 		 return this.levelId;
 	}
 
 	 
 	public void setUserPhone(String userPhone){
 		 this.userPhone=userPhone;
 	}
 	public String getUserPhone(){
 		 return this.userPhone;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setBbcPwd(String bbcPwd){
 		 this.bbcPwd=bbcPwd;
 	}
 	public String getBbcPwd(){
 		 return this.bbcPwd;
 	}
 
 	 
 	public void setUserPwd(String userPwd){
 		 this.userPwd=userPwd;
 	}
 	public String getUserPwd(){
 		 return this.userPwd;
 	}
 
 	 
}