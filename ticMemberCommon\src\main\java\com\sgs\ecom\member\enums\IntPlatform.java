package com.sgs.ecom.member.enums;

public enum IntPlatform {

	PREORDER("PREORDER中", "PREORDER"), SLOTS("SL-OTS", "SL-OTS"), SLCMS("SL-CMS", "SL-CMS");
	
    // 成员变量  
    private String name;  
    private String index;  
    // 构造方法  
    private IntPlatform(String name, String index) {  
        this.name = name;  
        this.index = index;  
    }  

    // 普通方法  
    public static String getName(String index) {  
        for (IntPlatform c : IntPlatform.values()) {  
            if (c.getIndex().equals(index)) {  
                return c.name;  
            }  
        }  
        return null;  
    }  
    // get set 方法  
    public String getName() {  
        return name;  
    }  

    public String getIndex() {  
        return index;  
    }  
}
