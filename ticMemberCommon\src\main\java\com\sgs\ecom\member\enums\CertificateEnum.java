package com.sgs.ecom.member.enums;

public enum CertificateEnum {

    EXISTING_CERTIFICATE("已有证书", "existingCertificate"),
    NUMBER_EMPLOYEES("工厂人数", "numberEmployees"),
    FACTORY("工厂", "factory"),
    DOCUMENTS_PROVIDED("申请者应填写及提供之文件", "documentsProvided");

    // 成员变量
    private String name;
    private String index;
    // 构造方法
    private CertificateEnum(String name, String index) {
        this.name = name;  
        this.index = index;  
    }  

    // 普通方法  
    public static String getName(String index) {  
        for (CertificateEnum c : CertificateEnum.values()) {
            if (c.getIndex().equals(index)) {  
                return c.name;  
            }  
        }  
        return null;  
    }  
    // get set 方法  
    public String getName() {  
        return name;  
    }  

    public String getIndex() {  
        return index;  
    }  
}
