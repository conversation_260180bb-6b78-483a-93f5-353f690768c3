package com.sgs.ecom.member.dto.pdf;

import com.platform.annotation.ApiAnno;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.dto.detail.OrderApplicationFormDTO;
import com.sgs.ecom.member.dto.detail.OrderReportDTO;

/**
 * <AUTHOR>
 */
public class FormHeadDTO {
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private String companyNameCn;
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private String companyAddressCn;
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private String reportLanguage;
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private String reportForm;
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private String reportMethod;
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private String  isRegular;
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private String reportPersonEmail;
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private String reportPersonAddress;
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private String qualifications;
    public FormHeadDTO() {
    }

    public FormHeadDTO(OrderApplicationFormDTO formDTO, OrderReportDTO orderReportDTO, int luaEn) {
        if(luaEn==0){
            this.companyNameCn = formDTO.getCompanyNameCn();
            this.companyAddressCn = formDTO.getCompanyAddressCn();
        }
        if(luaEn==1){
            this.companyNameCn = formDTO.getCompanyNameEn();
            this.companyAddressCn = formDTO.getCompanyAddressEn();
        }
        this.reportForm=orderReportDTO.getReportForm();
        this.reportLanguage=orderReportDTO.getReportLua();
        this.reportMethod=orderReportDTO.getOiqReportMethodShow();
    }

    public String getCompanyNameCn() {
        return companyNameCn;
    }

    public void setCompanyNameCn(String companyNameCn) {
        this.companyNameCn = companyNameCn;
    }

    public String getCompanyAddressCn() {
        return companyAddressCn;
    }

    public void setCompanyAddressCn(String companyAddressCn) {
        this.companyAddressCn = companyAddressCn;
    }

    public String getReportLanguage() {
        return reportLanguage;
    }

    public void setReportLanguage(String reportLanguage) {
        this.reportLanguage = reportLanguage;
    }

    public String getReportForm() {
        return reportForm;
    }

    public void setReportForm(String reportForm) {
        this.reportForm = reportForm;
    }

    public String getReportMethod() {
        return reportMethod;
    }

    public void setReportMethod(String reportMethod) {
        this.reportMethod = reportMethod;
    }

    public String getIsRegular() {
        return isRegular;
    }

    public void setIsRegular(String isRegular) {
        this.isRegular = isRegular;
    }

    public String getReportPersonEmail() {
        return reportPersonEmail;
    }

    public void setReportPersonEmail(String reportPersonEmail) {
        this.reportPersonEmail = reportPersonEmail;
    }

    public String getReportPersonAddress() {
        return reportPersonAddress;
    }

    public void setReportPersonAddress(String reportPersonAddress) {
        this.reportPersonAddress = reportPersonAddress;
    }

    public String getQualifications() {
        return qualifications;
    }

    public void setQualifications(String qualifications) {
        this.qualifications = qualifications;
    }
}
