<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.order.dao.OrderReportMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.vo.VOOrderReport" >
    <id column="REPORT_ID" property="reportId" jdbcType="BIGINT" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="REPORT_LUA" property="reportLua" jdbcType="VARCHAR" />
    <result column="REPORT_LUA_CODE" property="reportLuaCode" jdbcType="VARCHAR" />
    <result column="REPORT_COMPANY_NAME_CN" property="reportCompanyNameCn" jdbcType="VARCHAR" />
    <result column="REPORT_COMPANY_NAME_EN" property="reportCompanyNameEn" jdbcType="VARCHAR" />
    <result column="REPORT_ADDRESS_CN" property="reportAddressCn" jdbcType="VARCHAR" />
    <result column="REPORT_ADDRESS_EN" property="reportAddressEn" jdbcType="VARCHAR" />
    <result column="REPORT_METHOD" property="reportMethod" jdbcType="INTEGER" />
    <result column="REPORT_METHOD_MEMO" property="reportMethodMemo" jdbcType="VARCHAR" />
    <result column="REPORT_FORM" property="reportForm" jdbcType="VARCHAR" />
    <result column="REPORT_FORM_CODE" property="reportFormCode" jdbcType="VARCHAR" />
    <result column="IS_OPEN" property="isOpen" jdbcType="INTEGER" />
    <result column="REPORT_REQUIREMENTS" property="reportRequirements" jdbcType="VARCHAR" />
    <result column="REPORT_PERSON" property="reportPerson" jdbcType="VARCHAR" />
    <result column="PROVINCE" property="province" jdbcType="VARCHAR" />
    <result column="CITY" property="city" jdbcType="VARCHAR" />
    <result column="TOWN" property="town" jdbcType="VARCHAR" />
    <result column="REPORT_AMOUNT" property="reportAmount" jdbcType="DECIMAL" />
    <result column="STATE" property="state" jdbcType="TINYINT" />
    <result column="IS_JUDGE" property="isJudge" jdbcType="TINYINT" />
    <result column="JUDGE_MEMO" property="judgeMemo" jdbcType="VARCHAR" />
    <result column="IS_PICTURE" property="isPicture" jdbcType="TINYINT" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
    <result column="IS_FLOW" property="isFlow" jdbcType="VARCHAR" />
    <result column="DETECTION_SITE_CATEGORY" property="detectionSiteCategory" jdbcType="VARCHAR" />
    <result column="REPORT_REQUIREMENTS_FLG" property="reportRequirementsFlg" jdbcType="TINYINT" />
    <result column="REPORT_TYPE" property="reportType" jdbcType="TINYINT" />
    <result column="REPORT_SEND_CC" property="reportSendCc" jdbcType="VARCHAR" />
    <result column="REPORT_TITLE_TYPE" property="reportTitleType" jdbcType="INTEGER" />
  </resultMap>
  <resultMap id="BaseResult" type="com.sgs.ecom.member.entity.order.OrderReport" >
    <id column="REPORT_ID" property="reportId" jdbcType="BIGINT" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="REPORT_LUA" property="reportLua" jdbcType="VARCHAR" />
    <result column="REPORT_LUA_CODE" property="reportLuaCode" jdbcType="VARCHAR" />
    <result column="REPORT_COMPANY_NAME_CN" property="reportCompanyNameCn" jdbcType="VARCHAR" />
    <result column="REPORT_COMPANY_NAME_EN" property="reportCompanyNameEn" jdbcType="VARCHAR" />
    <result column="REPORT_ADDRESS_CN" property="reportAddressCn" jdbcType="VARCHAR" />
    <result column="REPORT_ADDRESS_EN" property="reportAddressEn" jdbcType="VARCHAR" />
    <result column="REPORT_METHOD" property="reportMethod" jdbcType="INTEGER" />
    <result column="REPORT_METHOD_MEMO" property="reportMethodMemo" jdbcType="VARCHAR" />
    <result column="REPORT_FORM" property="reportForm" jdbcType="VARCHAR" />
    <result column="REPORT_FORM_CODE" property="reportFormCode" jdbcType="VARCHAR" />
    <result column="IS_OPEN" property="isOpen" jdbcType="INTEGER" />
    <result column="REPORT_REQUIREMENTS" property="reportRequirements" jdbcType="VARCHAR" />
    <result column="REPORT_PERSON" property="reportPerson" jdbcType="VARCHAR" />
    <result column="PROVINCE" property="province" jdbcType="VARCHAR" />
    <result column="CITY" property="city" jdbcType="VARCHAR" />
    <result column="TOWN" property="town" jdbcType="VARCHAR" />
    <result column="REPORT_AMOUNT" property="reportAmount" jdbcType="DECIMAL" />
    <result column="STATE" property="state" jdbcType="TINYINT" />
    <result column="IS_JUDGE" property="isJudge" jdbcType="TINYINT" />
    <result column="JUDGE_MEMO" property="judgeMemo" jdbcType="VARCHAR" />
    <result column="IS_PICTURE" property="isPicture" jdbcType="TINYINT" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
    <result column="IS_FLOW" property="isFlow" jdbcType="VARCHAR" />
    <result column="DETECTION_SITE_CATEGORY" property="detectionSiteCategory" jdbcType="VARCHAR" />
    <result column="REPORT_REQUIREMENTS_FLG" property="reportRequirementsFlg" jdbcType="TINYINT" />
    <result column="REPORT_TYPE" property="reportType" jdbcType="TINYINT" />
    <result column="REPORT_SEND_CC" property="reportSendCc" jdbcType="VARCHAR" />
    <result column="REPORT_TITLE_TYPE" property="reportTitleType" jdbcType="INTEGER" />
  </resultMap>

  <resultMap id="BaseResultDTOMap" type="com.sgs.ecom.member.dto.detail.OrderReportDTO" >
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="REPORT_LUA" property="reportLua" jdbcType="VARCHAR" />
    <result column="REPORT_METHOD" property="reportMethod" jdbcType="VARCHAR" />
    <result column="REPORT_METHOD_MEMO" property="reportMethodMemo" jdbcType="VARCHAR" />
    <result column="REPORT_LUA_CODE" property="reportLuaCode" jdbcType="VARCHAR" />
    <result column="REPORT_COMPANY_NAME_CN" property="reportCompanyNameCn" jdbcType="VARCHAR" />
    <result column="REPORT_COMPANY_NAME_EN" property="reportCompanyNameEn" jdbcType="VARCHAR" />
    <result column="REPORT_ADDRESS_CN" property="reportAddressCn" jdbcType="VARCHAR" />
    <result column="REPORT_ADDRESS_EN" property="reportAddressEn" jdbcType="VARCHAR" />
    <result column="PROVINCE" property="province" jdbcType="VARCHAR" />
    <result column="CITY" property="city" jdbcType="VARCHAR" />
    <result column="TOWN" property="town" jdbcType="VARCHAR" />
    <result column="REPORT_SEND_CC" property="reportSendCc" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    REPORT_ID, ORDER_NO, REPORT_LUA, REPORT_LUA_CODE, REPORT_COMPANY_NAME_CN, REPORT_COMPANY_NAME_EN, 
    REPORT_ADDRESS_CN, REPORT_ADDRESS_EN, REPORT_METHOD, REPORT_METHOD_MEMO, REPORT_FORM, 
    REPORT_FORM_CODE, IS_OPEN, REPORT_REQUIREMENTS, REPORT_PERSON, REPORT_AMOUNT, STATE, 
    CREATE_DATE, STATE_DATE,PROVINCE,CITY,TOWN,IS_JUDGE,IS_PICTURE,JUDGE_MEMO,IS_FLOW,DETECTION_SITE_CATEGORY,
    REPORT_REQUIREMENTS_FLG,REPORT_TYPE,REPORT_SEND_CC,REPORT_TITLE_TYPE
  </sql>

  <select id="selectByOrderNo" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from ORDER_REPORT
    <include refid="baseQueryWhere"/>
    limit 1
  </select>


  <insert id="insertVOSelective" parameterType="com.sgs.ecom.member.vo.VOOrderReport" >
    insert into ORDER_REPORT
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="reportId != null" >
        REPORT_ID,
      </if>
      <if test="orderNo != null" >
        ORDER_NO,
      </if>
      <if test="reportLua != null" >
        REPORT_LUA,
      </if>
      <if test="reportLuaCode != null" >
        REPORT_LUA_CODE,
      </if>
      <if test="reportCompanyNameCn != null" >
        REPORT_COMPANY_NAME_CN,
      </if>
      <if test="reportCompanyNameEn != null" >
        REPORT_COMPANY_NAME_EN,
      </if>
      <if test="reportAddressCn != null" >
        REPORT_ADDRESS_CN,
      </if>
      <if test="reportAddressEn != null" >
        REPORT_ADDRESS_EN,
      </if>
      <if test="reportMethod != null" >
        REPORT_METHOD,
      </if>
      <if test="reportMethodMemo != null" >
        REPORT_METHOD_MEMO,
      </if>
      <if test="reportForm != null" >
        REPORT_FORM,
      </if>
      <if test="reportFormCode != null" >
        REPORT_FORM_CODE,
      </if>
      <if test="isOpen != null" >
        IS_OPEN,
      </if>
      <if test="reportRequirements != null" >
        REPORT_REQUIREMENTS,
      </if>
      <if test="reportPerson != null" >
        REPORT_PERSON,
      </if>
      <if test="reportAmount != null" >
        REPORT_AMOUNT,
      </if>
      <if test="state != null" >
        STATE,
      </if>
      <if test="createDate != null" >
        CREATE_DATE,
      </if>
      <if test="stateDate != null" >
        STATE_DATE,
      </if>
      <if test="province != null" >
        PROVINCE,
      </if>
      <if test="city != null" >
        CITY,
      </if>
      <if test="town != null" >
        TOWN,
      </if>
      <if test="isJudge != null" >
        IS_JUDGE,
      </if>
      <if test="judgeMemo != null" >
        JUDGE_MEMO,
      </if>
      <if test="isPicture != null" >
        IS_PICTURE,
      </if>
      <if test="isFlow != null" >
        IS_FLOW,
      </if>
      <if test="detectionSiteCategory != null" >
        DETECTION_SITE_CATEGORY,
      </if>
      <if test="reportRequirementsFlg != null" >
        REPORT_REQUIREMENTS_FLG,
      </if>
      <if test="reportType != null" >
        REPORT_TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="reportId != null" >
        #{reportId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="reportLua != null" >
        #{reportLua,jdbcType=VARCHAR},
      </if>
      <if test="reportLuaCode != null" >
        #{reportLuaCode,jdbcType=VARCHAR},
      </if>
      <if test="reportCompanyNameCn != null" >
        #{reportCompanyNameCn,jdbcType=VARCHAR},
      </if>
      <if test="reportCompanyNameEn != null" >
        #{reportCompanyNameEn,jdbcType=VARCHAR},
      </if>
      <if test="reportAddressCn != null" >
        #{reportAddressCn,jdbcType=VARCHAR},
      </if>
      <if test="reportAddressEn != null" >
        #{reportAddressEn,jdbcType=VARCHAR},
      </if>
      <if test="reportMethod != null" >
        #{reportMethod,jdbcType=INTEGER},
      </if>
      <if test="reportMethodMemo != null" >
        #{reportMethodMemo,jdbcType=VARCHAR},
      </if>
      <if test="reportForm != null" >
        #{reportForm,jdbcType=VARCHAR},
      </if>
      <if test="reportFormCode != null" >
        #{reportFormCode,jdbcType=VARCHAR},
      </if>
      <if test="isOpen != null" >
        #{isOpen,jdbcType=INTEGER},
      </if>
      <if test="reportRequirements != null" >
        #{reportRequirements,jdbcType=VARCHAR},
      </if>
      <if test="reportPerson != null" >
        #{reportPerson,jdbcType=VARCHAR},
      </if>
      <if test="reportAmount != null" >
        #{reportAmount,jdbcType=DECIMAL},
      </if>
      <if test="state != null" >
        #{state,jdbcType=TINYINT},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        #{stateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="province != null" >
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null" >
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="town != null" >
        #{town,jdbcType=VARCHAR},
      </if>
      <if test="isJudge != null" >
        #{isJudge,jdbcType=TINYINT},
      </if>
      <if test="judgeMemo != null" >
        #{judgeMemo,jdbcType=VARCHAR},
      </if>
      <if test="isPicture != null" >
        #{isPicture,jdbcType=TINYINT},
      </if>
      <if test="isFlow != null" >
        #{isFlow,jdbcType=VARCHAR},
      </if>
      <if test="detectionSiteCategory != null" >
        #{detectionSiteCategory,jdbcType=VARCHAR},
      </if>
      <if test="reportRequirementsFlg != null" >
        #{reportRequirementsFlg,jdbcType=TINYINT},
      </if>
      <if test="reportType != null" >
        #{reportType,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>



  <delete id="delReportByMap">
    delete from ORDER_REPORT where ORDER_NO=#{orderNo}
  </delete>

  <resultMap id="resultDTO" type="com.sgs.ecom.member.dto.detail.OrderReportDTO" >
    <id column="REPORT_ID" property="reportId" jdbcType="BIGINT" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="REPORT_COMPANY_NAME_CN" property="reportCompanyNameCn" jdbcType="VARCHAR" />
    <result column="REPORT_COMPANY_NAME_EN" property="reportCompanyNameEn" jdbcType="VARCHAR" />
    <result column="REPORT_ADDRESS_CN" property="reportAddressCn" jdbcType="VARCHAR" />
    <result column="REPORT_ADDRESS_EN" property="reportAddressEn" jdbcType="VARCHAR" />
    <result column="REPORT_LUA" property="reportLua" jdbcType="VARCHAR" />
    <result column="REPORT_LUA_CODE" property="reportLuaCode" jdbcType="VARCHAR" />
    <result column="REPORT_FORM" property="reportForm" jdbcType="VARCHAR" />
    <result column="REPORT_FORM_CODE" property="reportFormCode" jdbcType="VARCHAR" />
    <result column="REPORT_METHOD" property="reportMethod" jdbcType="VARCHAR" />
    <result column="REPORT_METHOD_MEMO" property="reportMethodMemo" jdbcType="VARCHAR" />
    <result column="REPORT_TITLE_TYPE" property="reportTitleType" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="VARCHAR" />
  </resultMap>


  <sql id="reportDTOSql" >
    REPORT_ID,ORDER_NO,REPORT_COMPANY_NAME_CN,REPORT_COMPANY_NAME_EN,REPORT_ADDRESS_CN,REPORT_ADDRESS_EN,
    REPORT_LUA, REPORT_LUA_CODE,  REPORT_FORM, REPORT_FORM_CODE,REPORT_REQUIREMENTS_FLG,REPORT_REQUIREMENTS,
    REPORT_METHOD, REPORT_METHOD_MEMO, REPORT_TITLE_TYPE,STATE,PROVINCE,CITY,TOWN, IS_JUDGE, JUDGE_MEMO,IS_PICTURE,DETECTION_SITE_CATEGORY
  </sql>
  <select id="selectOrderReportByOrderNo" resultMap="resultDTO" >
    select
    <include refid="reportDTOSql" />
    from ORDER_REPORT where  ORDER_NO=#{orderNo} order by REPORT_ID desc limit 1
  </select>



  <select id="selectLastFormByUserId" resultMap="resultDTO" >
    select REPORT_COMPANY_NAME_CN,REPORT_COMPANY_NAME_EN,REPORT_ADDRESS_CN,REPORT_ADDRESS_EN,o.PROVINCE,o.CITY,o.TOWN,IS_JUDGE,IS_PICTURE
    from ORDER_REPORT o,ORDER_BASE_INFO obi
    where o.ORDER_NO=obi.ORDER_NO
    and obi.USER_ID=#{userId}
    and obi.ORDER_TYPE=210000 order by o.STATE_DATE desc limit 1
    </select>

  <select id="selectRoReportListByUserId" resultMap="resultDTO" >
    select t.REPORT_COMPANY_NAME_CN,t.REPORT_COMPANY_NAME_EN,t.REPORT_ADDRESS_CN,t.REPORT_ADDRESS_EN
    from (
      select ort.REPORT_COMPANY_NAME_CN,ort.REPORT_COMPANY_NAME_EN,ort.REPORT_ADDRESS_CN,ort.REPORT_ADDRESS_EN
      from ORDER_REPORT ort,ORDER_BASE_INFO obi  where ort.ORDER_NO=obi.ORDER_NO and ort.STATE=1  and obi.USER_ID=#{userId}
      and obi.ORDER_TYPE=300000 order by obi.CREATE_DATE desc limit 1000 ) t
    group by t.REPORT_COMPANY_NAME_CN ,t.REPORT_COMPANY_NAME_EN,t.REPORT_ADDRESS_CN,t.REPORT_ADDRESS_EN
  </select>
    <select id="qryOrderReportByOrderNo" resultMap="BaseResultDTOMap">
      select
      <include refid="Base_Column_List" />
      from ORDER_REPORT
      <include refid="baseQueryWhere"/>
    </select>
  <select id="qryOrderReport" resultMap ="BaseResult">
    select
    <include refid="reportDTOSql" />
    from ORDER_REPORT where  ORDER_NO=#{orderNo} and state=1
  </select>


  <select id="qryReportListByVO" resultMap ="BaseResultDTOMap">
    select
    <include refid="reportDTOSql" />
    from ORDER_REPORT
    where  state=1
    <if test="orderNoList != null ">
      AND ORDER_NO in
      <foreach collection="orderNoList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </select>





  <sql id="baseQueryWhere">
    where STATE=1
    <if test="orderNo != null">
      AND ORDER_NO=#{orderNo}
    </if>

  </sql>



  <insert id="insertEntity" parameterType="com.sgs.ecom.member.entity.order.OrderReport" >
    insert into ORDER_REPORT
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="reportId != null" >
        REPORT_ID,
      </if>
      <if test="orderNo != null" >
        ORDER_NO,
      </if>
      <if test="reportLua != null" >
        REPORT_LUA,
      </if>
      <if test="reportLuaCode != null" >
        REPORT_LUA_CODE,
      </if>
      <if test="reportCompanyNameCn != null" >
        REPORT_COMPANY_NAME_CN,
      </if>
      <if test="reportCompanyNameEn != null" >
        REPORT_COMPANY_NAME_EN,
      </if>
      <if test="reportAddressCn != null" >
        REPORT_ADDRESS_CN,
      </if>
      <if test="reportAddressEn != null" >
        REPORT_ADDRESS_EN,
      </if>
      <if test="reportMethod != null" >
        REPORT_METHOD,
      </if>
      <if test="reportMethodMemo != null" >
        REPORT_METHOD_MEMO,
      </if>
      <if test="reportForm != null" >
        REPORT_FORM,
      </if>
      <if test="reportFormCode != null" >
        REPORT_FORM_CODE,
      </if>
      <if test="isOpen != null" >
        IS_OPEN,
      </if>
      <if test="reportRequirements != null" >
        REPORT_REQUIREMENTS,
      </if>
      <if test="reportPerson != null" >
        REPORT_PERSON,
      </if>
      <if test="reportSendCc != null" >
        REPORT_SEND_CC,
      </if>
      <if test="reportAmount != null" >
        REPORT_AMOUNT,
      </if>
      <if test="state != null" >
        STATE,
      </if>
      <if test="createDate != null" >
        CREATE_DATE,
      </if>
      <if test="stateDate != null" >
        STATE_DATE,
      </if>
      <if test="province != null" >
        PROVINCE,
      </if>
      <if test="city != null" >
        CITY,
      </if>
      <if test="town != null" >
        TOWN,
      </if>
      <if test="isJudge != null" >
        IS_JUDGE,
      </if>
      <if test="judgeMemo != null" >
        JUDGE_MEMO,
      </if>
      <if test="isPicture != null" >
        IS_PICTURE,
      </if>
      <if test="isFlow != null" >
        IS_FLOW,
      </if>
      <if test="detectionSiteCategory != null" >
        DETECTION_SITE_CATEGORY,
      </if>
      <if test="reportRequirementsFlg != null" >
        REPORT_REQUIREMENTS_FLG,
      </if>
      <if test="reportType != null" >
        REPORT_TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="reportId != null" >
        #{reportId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="reportLua != null" >
        #{reportLua,jdbcType=VARCHAR},
      </if>
      <if test="reportLuaCode != null" >
        #{reportLuaCode,jdbcType=VARCHAR},
      </if>
      <if test="reportCompanyNameCn != null" >
        #{reportCompanyNameCn,jdbcType=VARCHAR},
      </if>
      <if test="reportCompanyNameEn != null" >
        #{reportCompanyNameEn,jdbcType=VARCHAR},
      </if>
      <if test="reportAddressCn != null" >
        #{reportAddressCn,jdbcType=VARCHAR},
      </if>
      <if test="reportAddressEn != null" >
        #{reportAddressEn,jdbcType=VARCHAR},
      </if>
      <if test="reportMethod != null" >
        #{reportMethod,jdbcType=INTEGER},
      </if>
      <if test="reportMethodMemo != null" >
        #{reportMethodMemo,jdbcType=VARCHAR},
      </if>
      <if test="reportForm != null" >
        #{reportForm,jdbcType=VARCHAR},
      </if>
      <if test="reportFormCode != null" >
        #{reportFormCode,jdbcType=VARCHAR},
      </if>
      <if test="isOpen != null" >
        #{isOpen,jdbcType=INTEGER},
      </if>
      <if test="reportRequirements != null" >
        #{reportRequirements,jdbcType=VARCHAR},
      </if>
      <if test="reportPerson != null" >
        #{reportPerson,jdbcType=VARCHAR},
      </if>
      <if test="reportSendCc != null" >
        #{reportSendCc,jdbcType=VARCHAR},
      </if>
      <if test="reportAmount != null" >
        #{reportAmount,jdbcType=DECIMAL},
      </if>
      <if test="state != null" >
        #{state,jdbcType=TINYINT},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        #{stateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="province != null" >
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null" >
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="town != null" >
        #{town,jdbcType=VARCHAR},
      </if>
      <if test="isJudge != null" >
        #{isJudge,jdbcType=TINYINT},
      </if>
      <if test="judgeMemo != null" >
        #{judgeMemo,jdbcType=VARCHAR},
      </if>
      <if test="isPicture != null" >
        #{isPicture,jdbcType=TINYINT},
      </if>
      <if test="isFlow != null" >
        #{isFlow,jdbcType=VARCHAR},
      </if>
      <if test="detectionSiteCategory != null" >
        #{detectionSiteCategory,jdbcType=VARCHAR},
      </if>
      <if test="reportRequirementsFlg != null" >
        #{reportRequirementsFlg,jdbcType=TINYINT},
      </if>
      <if test="reportType != null" >
        #{reportType,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>

  <insert id="copyByOrderNo" parameterType="com.sgs.ecom.member.dto.order.CopyByOrder">
    insert into ORDER_REPORT (
      ORDER_NO,REPORT_LUA,REPORT_LUA_CODE,REPORT_COMPANY_NAME_CN,REPORT_COMPANY_NAME_EN,REPORT_ADDRESS_CN,REPORT_ADDRESS_EN,
      REPORT_METHOD,REPORT_METHOD_MEMO,REPORT_FORM,REPORT_FORM_CODE,IS_OPEN,REPORT_REQUIREMENTS,REPORT_PERSON,REPORT_AMOUNT,STATE,
      CREATE_DATE,STATE_DATE,report_send_cc,PROVINCE,CITY,TOWN,IS_JUDGE,JUDGE_MEMO, IS_PICTURE,IS_FLOW,DETECTION_SITE_CATEGORY,
      REPORT_REQUIREMENTS_FLG,DETECTIONSITE,REPORT_TYPE,REPORT_TITLE_TYPE
    )select
      #{toOrderNo}, #{inquiry.reportLua}, #{inquiry.reportLuaCode},REPORT_COMPANY_NAME_CN,REPORT_COMPANY_NAME_EN,REPORT_ADDRESS_CN,REPORT_ADDRESS_EN,
      REPORT_METHOD,REPORT_METHOD_MEMO, #{inquiry.reportForm}, #{inquiry.reportFormCode} ,IS_OPEN,REPORT_REQUIREMENTS,REPORT_PERSON,REPORT_AMOUNT,STATE,
      CREATE_DATE,STATE_DATE,report_send_cc,PROVINCE,CITY,TOWN,IS_JUDGE,JUDGE_MEMO, IS_PICTURE,IS_FLOW,DETECTION_SITE_CATEGORY,
      REPORT_REQUIREMENTS_FLG,DETECTIONSITE,REPORT_TYPE,REPORT_TITLE_TYPE
     from ORDER_REPORT where ORDER_NO= #{useOrderNo}
  </insert>

</mapper>