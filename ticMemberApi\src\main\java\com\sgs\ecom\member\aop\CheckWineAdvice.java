package com.sgs.ecom.member.aop;

import com.platform.util.SysException;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseController;
import com.sgs.ecom.member.dto.user.UserDTO;
import com.sgs.ecom.member.enumtool.ResultEnumCode;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Aspect
@Component
public class CheckWineAdvice extends ControllerUtil{

    @Pointcut("@annotation(com.sgs.ecom.member.annotation.CheckWine)")
    @Order(10)
    public void checkWine() {

    }



    @Before("checkWine()")
    public void CheckUserToolKitPermission(JoinPoint joinPoint) throws Exception {

            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes())
                    .getRequest();
            String accessToken = request.getHeader("accessToken");

            UserDTO userDTO=getUserDTO(accessToken);
            if(ValidationUtil.isEmpty(userDTO)){
                throw new SysException("9978");
            }
            if(ValidationUtil.isEmpty(userDTO.getAuths())){
                throw new BusinessException(ResultEnumCode.EDIT_UNABLE);
            }
            List<String> prodList=userDTO.getProdList(userDTO);
            if(ValidationUtil.isEmpty(prodList)){
                throw new BusinessException(ResultEnumCode.EDIT_UNABLE);
            }



    }
}
