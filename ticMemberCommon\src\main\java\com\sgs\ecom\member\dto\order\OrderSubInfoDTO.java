package com.sgs.ecom.member.dto.order;

import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.dto.OrderBaseInfoDTO;
import com.sgs.ecom.member.dto.pay.OrderPayDTO;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName OrderPayInfoDTO
 * <AUTHOR>
 * @Date 2024-07-12 14:28
 */
public class OrderSubInfoDTO {

    @ApiAnno(groups={BaseQryFilter.QuerySummary.class})
    private String orderNo;

    @ApiAnno(groups={BaseQryFilter.QuerySummary.class})
    private Integer subUnPayNum;

    @ApiAnno(groups={BaseQryFilter.QuerySummary.class})
    private Integer subPayNum;

    @ApiAnno(groups={BaseQryFilter.QuerySummary.class})
    private BigDecimal subUnPayPrice;

    @ApiAnno(groups={BaseQryFilter.QuerySummary.class})
    private BigDecimal subPayPrice;

    @ApiAnno(groups={BaseQryFilter.QuerySummary.class})
    @BeanAnno(dtocls={OrderBaseInfoDTO.class})
    private List<OrderBaseInfoDTO> subOrders;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getSubUnPayNum() {
        return subUnPayNum;
    }

    public void setSubUnPayNum(Integer subUnPayNum) {
        this.subUnPayNum = subUnPayNum;
    }

    public Integer getSubPayNum() {
        return subPayNum;
    }

    public void setSubPayNum(Integer subPayNum) {
        this.subPayNum = subPayNum;
    }

    public BigDecimal getSubUnPayPrice() {
        return subUnPayPrice;
    }

    public void setSubUnPayPrice(BigDecimal subUnPayPrice) {
        this.subUnPayPrice = subUnPayPrice;
    }

    public BigDecimal getSubPayPrice() {
        return subPayPrice;
    }

    public void setSubPayPrice(BigDecimal subPayPrice) {
        this.subPayPrice = subPayPrice;
    }

    public List<OrderBaseInfoDTO> getSubOrders() {
        return subOrders;
    }

    public void setSubOrders(List<OrderBaseInfoDTO> subOrders) {
        this.subOrders = subOrders;
    }
}
