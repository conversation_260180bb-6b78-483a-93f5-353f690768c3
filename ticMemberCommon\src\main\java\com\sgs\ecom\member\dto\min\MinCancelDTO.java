package com.sgs.ecom.member.dto.min;

public class MinCancelDTO {
	private String order;
	private String applyAt;
	private String confirmAt;
	private int catalog;//6
	private String remark;

	public String getOrder() {
		return order;
	}

	public void setOrder(String order) {
		this.order = order;
	}

	public String getApplyAt() {
		return applyAt;
	}

	public void setApplyAt(String applyAt) {
		this.applyAt = applyAt;
	}

	public String getConfirmAt() {
		return confirmAt;
	}

	public void setConfirmAt(String confirmAt) {
		this.confirmAt = confirmAt;
	}

	public int getCatalog() {
		return catalog;
	}

	public void setCatalog(int catalog) {
		this.catalog = catalog;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
}
