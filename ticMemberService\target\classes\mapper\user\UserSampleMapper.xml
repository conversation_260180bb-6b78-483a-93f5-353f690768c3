<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.user.dao.UserSampleMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.vo.VOUserSample" >
    <id column="SAMPLE_ID" property="sampleId" jdbcType="BIGINT" />
    <result column="SAMPLE_NAME" property="sampleName" jdbcType="VARCHAR" />
    <result column="SAMPLE_NAME_EN" property="sampleNameEn" jdbcType="VARCHAR" />
    <result column="PRODUCT_INFO" property="productInfo" jdbcType="VARCHAR" />
    <result column="PRODUCT_INFO_EN" property="productInfoEn" jdbcType="VARCHAR" />
    <result column="MATERIAL_GRADE" property="materialGrade" jdbcType="VARCHAR" />
    <result column="MATERIAL_GRADE_EN" property="materialGradeEn" jdbcType="VARCHAR" />
    <result column="PRODUCT_BATCH" property="productBatch" jdbcType="VARCHAR" />
    <result column="PRODUCT_BATCH_EN" property="productBatchEn" jdbcType="VARCHAR" />
    <result column="SUPPLIER_NAME" property="supplierName" jdbcType="VARCHAR" />
    <result column="SUPPLIER_NAME_EN" property="supplierNameEn" jdbcType="VARCHAR" />
    <result column="BUYERS_NAME" property="buyersName" jdbcType="VARCHAR" />
    <result column="BUYERS_NAME_EN" property="buyersNameEn" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="REMARK_EN" property="remarkEn" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="TINYINT" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
    <result column="SAMPLE_NO" property="sampleNo" jdbcType="VARCHAR" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="LOT_NO" property="lotNo" jdbcType="VARCHAR" />
    <result column="LOT_NO_EN" property="lotNoEn" jdbcType="VARCHAR" />
    <result column="PRODUCER" property="producer" jdbcType="VARCHAR" />
    <result column="PRODUCER_EN" property="producerEn" jdbcType="VARCHAR" />
    <result column="SAMPLE_SIGN" property="sampleSign" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    SAMPLE_ID, SAMPLE_NAME, SAMPLE_NAME_EN, PRODUCT_INFO, PRODUCT_INFO_EN, MATERIAL_GRADE, 
    MATERIAL_GRADE_EN, PRODUCT_BATCH, PRODUCT_BATCH_EN, SUPPLIER_NAME, SUPPLIER_NAME_EN, 
    BUYERS_NAME, BUYERS_NAME_EN,REMARK , REMARK_EN, STATE, 
    CREATE_DATE, STATE_DATE, SAMPLE_NO, USER_ID,LOT_NO,LOT_NO_EN,PRODUCER,PRODUCER_EN,SAMPLE_SIGN

  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from USER_SAMPLE
    where SAMPLE_ID = #{sampleId,jdbcType=BIGINT}
  </select>

  <select id="selectBySampleNo" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select
    <include refid="Base_Column_List" />
    from USER_SAMPLE
    where SAMPLE_NO = #{sampleNo}
    limit 1
  </select>





  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from USER_SAMPLE
    where SAMPLE_ID = #{sampleId,jdbcType=BIGINT}
  </delete>

  <insert id="insertSelective" parameterType="com.sgs.ecom.member.vo.VOUserSample" >
    insert into USER_SAMPLE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="sampleId != null" >
        SAMPLE_ID,
      </if>
      <if test="sampleName != null" >
        SAMPLE_NAME,
      </if>
      <if test="sampleNameEn != null" >
        SAMPLE_NAME_EN,
      </if>
      <if test="productInfo != null" >
        PRODUCT_INFO,
      </if>
      <if test="productInfoEn != null" >
        PRODUCT_INFO_EN,
      </if>
      <if test="materialGrade != null" >
        MATERIAL_GRADE,
      </if>
      <if test="materialGradeEn != null" >
        MATERIAL_GRADE_EN,
      </if>
      <if test="productBatch != null" >
        PRODUCT_BATCH,
      </if>
      <if test="productBatchEn != null" >
        PRODUCT_BATCH_EN,
      </if>
      <if test="supplierName != null" >
        SUPPLIER_NAME,
      </if>
      <if test="supplierNameEn != null" >
        SUPPLIER_NAME_EN,
      </if>
      <if test="buyersName != null" >
        BUYERS_NAME,
      </if>
      <if test="buyersNameEn != null" >
        BUYERS_NAME_EN,
      </if>
      <if test="remark != null" >
        REMARK,
      </if>
      <if test="remarkEn != null" >
        REMARK_EN,
      </if>
      <if test="state != null" >
        STATE,
      </if>
      <if test="createDate != null" >
        CREATE_DATE,
      </if>
      <if test="stateDate != null" >
        STATE_DATE,
      </if>
      <if test="sampleNo != null" >
        SAMPLE_NO,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
      <if test="lotNo != null" >
        LOT_NO,
      </if>
      <if test="lotNoEn != null" >
        LOT_NO_EN,
      </if>
      <if test="producer != null" >
        PRODUCER,
      </if>
      <if test="producerEn != null" >
        PRODUCER_EN,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="sampleId != null" >
        #{sampleId,jdbcType=BIGINT},
      </if>
      <if test="sampleName != null" >
        #{sampleName,jdbcType=VARCHAR},
      </if>
      <if test="sampleNameEn != null" >
        #{sampleNameEn,jdbcType=VARCHAR},
      </if>
      <if test="productInfo != null" >
        #{productInfo,jdbcType=VARCHAR},
      </if>
      <if test="productInfoEn != null" >
        #{productInfoEn,jdbcType=VARCHAR},
      </if>
      <if test="materialGrade != null" >
        #{materialGrade,jdbcType=VARCHAR},
      </if>
      <if test="materialGradeEn != null" >
        #{materialGradeEn,jdbcType=VARCHAR},
      </if>
      <if test="productBatch != null" >
        #{productBatch,jdbcType=VARCHAR},
      </if>
      <if test="productBatchEn != null" >
        #{productBatchEn,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null" >
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="supplierNameEn != null" >
        #{supplierNameEn,jdbcType=VARCHAR},
      </if>
      <if test="buyersName != null" >
        #{buyersName,jdbcType=VARCHAR},
      </if>
      <if test="buyersNameEn != null" >
        #{buyersNameEn,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="remarkEn != null" >
        #{remarkEn,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        #{state,jdbcType=TINYINT},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        #{stateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="sampleNo != null" >
        #{sampleNo,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="lotNo != null" >
        #{lotNo,jdbcType=VARCHAR},
      </if>
      <if test="lotNoEn != null" >
        #{lotNoEn,jdbcType=VARCHAR},
      </if>
      <if test="producer != null" >
        #{producer,jdbcType=VARCHAR},
      </if>
      <if test="producerEn != null" >
        #{producerEn,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <insert id="insertForeach"  >
    insert into USER_SAMPLE (
    USER_ID,SAMPLE_NO,
    SAMPLE_NAME, SAMPLE_NAME_EN,
    PRODUCT_INFO,PRODUCT_INFO_EN,
    PRODUCT_BATCH,PRODUCT_BATCH_EN,
    MATERIAL_GRADE, MATERIAL_GRADE_EN,
    SUPPLIER_NAME,SUPPLIER_NAME_EN,
    BUYERS_NAME,BUYERS_NAME_EN,
    REMARK,REMARK_EN,
    STATE, CREATE_DATE, STATE_DATE,LOT_NO,
    LOT_NO_EN,PRODUCER,PRODUCER_EN,SAMPLE_SIGN
    )
    values
    <foreach collection ="list" item="sample" index= "index" separator =",">
      (
      #{sample.userId},#{sample.sampleNo},
      #{sample.sampleName},#{sample.sampleNameEn},
      #{sample.productInfo},#{sample.productInfoEn},
      #{sample.productBatch},#{sample.productBatchEn},
      #{sample.materialGrade},#{sample.materialGradeEn},
      #{sample.supplierName},#{sample.supplierNameEn},
      #{sample.buyersName}, #{sample.buyersNameEn},
      #{sample.remark}, #{sample.remarkEn},
      #{sample.state}, #{sample.createDate}, #{sample.stateDate}, #{sample.lotNo},
       #{sample.lotNoEn}, #{sample.producer}, #{sample.producerEn}, #{sample.sampleSign}
      )
    </foreach>
  </insert>

  <insert id="insertBatchUserSample">
    insert into USER_SAMPLE (
    USER_ID,SAMPLE_NO,
    SAMPLE_NAME, SAMPLE_NAME_EN,
    PRODUCT_INFO,PRODUCT_INFO_EN,
    PRODUCT_BATCH,PRODUCT_BATCH_EN,
    MATERIAL_GRADE, MATERIAL_GRADE_EN,
    SUPPLIER_NAME,SUPPLIER_NAME_EN,
    BUYERS_NAME,BUYERS_NAME_EN,
    REMARK,REMARK_EN,
    STATE, CREATE_DATE, STATE_DATE,LOT_NO,
    LOT_NO_EN,PRODUCER,PRODUCER_EN,SAMPLE_SIGN
    )
    values
    <foreach collection ="list" item="sample" index= "index" separator =",">
      (
      #{sample.userId},#{sample.sampleNo},
      #{sample.sampleName},#{sample.sampleNameEn},
      #{sample.productInfo},#{sample.productInfoEn},
      #{sample.productBatch},#{sample.productBatchEn},
      #{sample.materialGrade},#{sample.materialGradeEn},
      #{sample.supplierName},#{sample.supplierNameEn},
      #{sample.buyersName}, #{sample.buyersNameEn},
      #{sample.remark}, #{sample.remarkEn},
      #{sample.state}, #{sample.createDate}, #{sample.stateDate}, #{sample.lotNo},
      #{sample.lotNoEn}, #{sample.producer}, #{sample.producerEn}, #{sample.sampleSign}
      )
    </foreach>
  </insert>


  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.ecom.member.vo.VOUserSample" >
    update USER_SAMPLE
    <set >
      <if test="sampleName != null" >
        SAMPLE_NAME = #{sampleName,jdbcType=VARCHAR},
      </if>
      <if test="sampleNameEn != null" >
        SAMPLE_NAME_EN = #{sampleNameEn,jdbcType=VARCHAR},
      </if>
      <if test="productInfo != null" >
        PRODUCT_INFO = #{productInfo,jdbcType=VARCHAR},
      </if>
      <if test="productInfoEn != null" >
        PRODUCT_INFO_EN = #{productInfoEn,jdbcType=VARCHAR},
      </if>
      <if test="materialGrade != null" >
        MATERIAL_GRADE = #{materialGrade,jdbcType=VARCHAR},
      </if>
      <if test="materialGradeEn != null" >
        MATERIAL_GRADE_EN = #{materialGradeEn,jdbcType=VARCHAR},
      </if>
      <if test="productBatch != null" >
        PRODUCT_BATCH = #{productBatch,jdbcType=VARCHAR},
      </if>
      <if test="productBatchEn != null" >
        PRODUCT_BATCH_EN = #{productBatchEn,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null" >
        SUPPLIER_NAME = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="supplierNameEn != null" >
        SUPPLIER_NAME_EN = #{supplierNameEn,jdbcType=VARCHAR},
      </if>
      <if test="buyersName != null" >
        BUYERS_NAME = #{buyersName,jdbcType=VARCHAR},
      </if>
      <if test="buyersNameEn != null" >
        BUYERS_NAME_EN = #{buyersNameEn,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="remarkEn != null" >
        REMARK_EN = #{remarkEn,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        STATE = #{state,jdbcType=TINYINT},
      </if>
      <if test="createDate != null" >
        CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        STATE_DATE = #{stateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="sampleNo != null" >
        SAMPLE_NO = #{sampleNo,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        USER_ID = #{userId,jdbcType=BIGINT},
      </if>
      <if test="lotNo != null" >
        LOT_NO = #{lotNo,jdbcType=VARCHAR},
      </if>
      <if test="lotNoEn != null" >
        LOT_NO_EN = #{lotNoEn,jdbcType=VARCHAR},
      </if>
      <if test="producer != null" >
        PRODUCER = #{producer,jdbcType=VARCHAR},
      </if>
      <if test="producerEn != null" >
        PRODUCER_EN = #{producerEn,jdbcType=VARCHAR},
      </if>
      <if test="sampleSign != null" >
        SAMPLE_SIGN = #{sampleSign,jdbcType=VARCHAR},
      </if>
    </set>
    where SAMPLE_ID = #{sampleId,jdbcType=BIGINT}
  </update>



  <resultMap id="resultDTO" type="com.sgs.ecom.member.dto.user.UserSampleDTO" >
    <id column="SAMPLE_ID" property="sampleId" jdbcType="BIGINT" />
    <result column="SAMPLE_NAME" property="sampleName" jdbcType="VARCHAR" />
    <result column="SAMPLE_NAME_EN" property="sampleNameEn" jdbcType="VARCHAR" />
    <result column="PRODUCT_INFO" property="productInfo" jdbcType="VARCHAR" />
    <result column="PRODUCT_INFO_EN" property="productInfoEn" jdbcType="VARCHAR" />
    <result column="MATERIAL_GRADE" property="materialGrade" jdbcType="VARCHAR" />
    <result column="MATERIAL_GRADE_EN" property="materialGradeEn" jdbcType="VARCHAR" />
    <result column="PRODUCT_BATCH" property="productBatch" jdbcType="VARCHAR" />
    <result column="PRODUCT_BATCH_EN" property="productBatchEn" jdbcType="VARCHAR" />
    <result column="SUPPLIER_NAME" property="supplierName" jdbcType="VARCHAR" />
    <result column="SUPPLIER_NAME_EN" property="supplierNameEn" jdbcType="VARCHAR" />
    <result column="BUYERS_NAME" property="buyersName" jdbcType="VARCHAR" />
    <result column="BUYERS_NAME_EN" property="buyersNameEn" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="REMARK_EN" property="remarkEn" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="TINYINT" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
    <result column="SAMPLE_NO" property="sampleNo" jdbcType="VARCHAR" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="LOT_NO" property="lotNo" jdbcType="VARCHAR" />
    <result column="LOT_NO_EN" property="lotNoEn" jdbcType="VARCHAR" />
    <result column="PRODUCER" property="producer" jdbcType="VARCHAR" />
    <result column="PRODUCER_EN" property="producerEn" jdbcType="VARCHAR" />
    <result column="SAMPLE_SIGN" property="sampleSign" jdbcType="VARCHAR" />
  </resultMap>

  <sql id="sqlListDTO" >
    SAMPLE_ID, SAMPLE_NAME, SAMPLE_NAME_EN, PRODUCT_INFO, PRODUCT_INFO_EN, MATERIAL_GRADE,
    MATERIAL_GRADE_EN, PRODUCT_BATCH, PRODUCT_BATCH_EN, SUPPLIER_NAME, SUPPLIER_NAME_EN,
    BUYERS_NAME, BUYERS_NAME_EN, REMARK, REMARK_EN, STATE,
    CREATE_DATE, STATE_DATE, SAMPLE_NO, USER_ID,LOT_NO,LOT_NO_EN,PRODUCER,PRODUCER_EN,SAMPLE_SIGN
  </sql>
  <sql id="sqlByKeyListDTO" >
    us.SAMPLE_ID, us.SAMPLE_NAME, us.SAMPLE_NAME_EN, us.PRODUCT_INFO, us.PRODUCT_INFO_EN, us.MATERIAL_GRADE,
    us.MATERIAL_GRADE_EN, us.PRODUCT_BATCH, us.PRODUCT_BATCH_EN, us.SUPPLIER_NAME, us.SUPPLIER_NAME_EN,
    us.BUYERS_NAME, us.BUYERS_NAME_EN, us.REMARK, us.REMARK_EN, us.STATE,
    us.CREATE_DATE, us.STATE_DATE, us.SAMPLE_NO, us.USER_ID,us.LOT_NO,us.LOT_NO_EN,us.PRODUCER,us.PRODUCER_EN,us.SAMPLE_SIGN
  </sql>

  <!--自定义-->
  <select id="selectListByMap" resultMap="resultDTO" >
    select
    <include refid="sqlListDTO" />
    from USER_SAMPLE
    where 1=1 AND STATE=1
    <if test="userId != null">
      AND USER_ID=#{userId}
    </if>
    <if test="sampleSignIsNull != null">
      AND SAMPLE_SIGN IS NULL
    </if>
    order by STATE_DATE DESC
  </select>

  <select id="selectSampleNum"  resultType="int" >
    select count(1) from USER_SAMPLE   where  USER_ID=#{userId} and STATE=1
    <if test="sampleSign != null">
      AND SAMPLE_SIGN=#{sampleSign}
    </if>
  </select>
  <select id="selectListByMapAndKey" resultMap="resultDTO" >
    select
    <include refid="sqlByKeyListDTO" />
    ,usff.SAMPLE_NAME,
    usff.SAMPLE_NAME_EN,
    usff.PRODUCT_INFO,
    usff.PRODUCT_INFO_EN,
    usff.EXTENDED_MODEL_NO,
    usff.EXTENDED_MODEL_NO_EN,
    usff.MATERIAL_GRADE,
    usff.MATERIAL_GRADE_EN,
    usff.LOT_NO,
    usff.LOT_NO_EN
    from USER_SAMPLE us LEFT JOIN
    (
    SELECT
    usf.USER_ID,
    usf.SAMPLE_NO,
    usf.STATE,
    max( ( CASE usf.SAMPLE_KEY WHEN 'sampleName' THEN usf.SAMPLE_VALUE ELSE '' END ) ) SAMPLE_NAME,
    max( ( CASE usf.SAMPLE_KEY WHEN 'sampleNameEn' THEN usf.SAMPLE_VALUE ELSE '' END ) ) SAMPLE_NAME_EN,
    max( ( CASE usf.SAMPLE_KEY WHEN 'productInfo' THEN usf.SAMPLE_VALUE ELSE '' END ) ) PRODUCT_INFO,
    max( ( CASE usf.SAMPLE_KEY WHEN 'productInfoEn' THEN usf.SAMPLE_VALUE ELSE '' END ) ) PRODUCT_INFO_EN,
    max( ( CASE usf.SAMPLE_KEY WHEN 'extendedModelNo' THEN usf.SAMPLE_VALUE ELSE '' END ) ) EXTENDED_MODEL_NO,
    max( ( CASE usf.SAMPLE_KEY WHEN 'extendedModelNoEn' THEN usf.SAMPLE_VALUE ELSE '' END ) ) EXTENDED_MODEL_NO_EN,
    max( ( CASE usf.SAMPLE_KEY WHEN 'materialGrade' THEN usf.SAMPLE_VALUE ELSE '' END ) ) MATERIAL_GRADE,
    max( ( CASE usf.SAMPLE_KEY WHEN 'materialGradeEn' THEN usf.SAMPLE_VALUE ELSE '' END ) ) MATERIAL_GRADE_EN,
    max( ( CASE usf.SAMPLE_KEY WHEN 'lotNo' THEN usf.SAMPLE_VALUE ELSE '' END ) ) LOT_NO,
    max( ( CASE usf.SAMPLE_KEY WHEN 'lotNoEn' THEN usf.SAMPLE_VALUE ELSE '' END ) ) LOT_NO_EN
    FROM
    USER_SAMPLE_FORM usf WHERE usf.STATE =1
    <if test="userId != null">
      AND usf.USER_ID=#{userId}
    </if>
     GROUP BY usf.SAMPLE_NO
    ) usff
    ON us.SAMPLE_NO = usff.SAMPLE_NO
    <include refid="baseQueryWhereByKey"/>
    order by us.STATE_DATE DESC
  </select>
    <select id="qrySampleNum" resultType="java.lang.Integer" parameterType="com.sgs.ecom.member.request.rsts.userSample.VOUserSampleBasic">
      select count(1) from USER_SAMPLE   where  USER_ID=#{userId} and STATE=1
      <if test="sampleSign != null">
        AND SAMPLE_SIGN=#{sampleSign}
      </if>

    </select>


    <sql id="baseQueryWhereByKey">
    WHERE 1=1
		and us.USER_ID = usff.USER_ID
		and usff.STATE =1
    <if test="searchKey != null and searchKey !=''">
      AND (
      usff.SAMPLE_NAME like concat('%',#{searchKey},'%')
      OR usff.SAMPLE_NAME_EN like concat('%',#{searchKey},'%')
      OR usff.PRODUCT_INFO like concat('%',#{searchKey},'%')
      OR usff.PRODUCT_INFO_EN like concat('%',#{searchKey},'%')
      OR usff.EXTENDED_MODEL_NO like concat('%',#{searchKey},'%')
      OR usff.EXTENDED_MODEL_NO_EN like concat('%',#{searchKey},'%')
      OR usff.MATERIAL_GRADE like concat('%',#{searchKey},'%')
      OR usff.MATERIAL_GRADE_EN like concat('%',#{searchKey},'%')
      OR usff.LOT_NO like concat('%',#{searchKey},'%')
      OR usff.LOT_NO_EN like concat('%',#{searchKey},'%')
        )
    </if>
      <if test="userId != null">
        AND us.USER_ID=#{userId}
      </if>
      <if test="sampleSignIsNull != null">
        AND us.SAMPLE_SIGN IS NULL
      </if>

  </sql>

  <sql id="queryReqWhere">
    where 1=1 AND us.STATE=1
    <if test="userId != null">
      AND us.USER_ID=#{userId}
    </if>
    <if test="sampleName != null">
      AND us.SAMPLE_NAME=#{sampleName}
    </if>
    <if test="sampleNameEn != null">
      AND us.SAMPLE_NAME_EN=#{sampleNameEn}
    </if>
    <if test="productInfo != null">
      AND us.PRODUCT_INFO=#{productInfo}
    </if>
    <if test="productInfoEn != null">
      AND us.PRODUCT_INFO_EN=#{productInfoEn}
    </if>
    <if test="productBatch != null">
      AND us.PRODUCT_BATCH=#{productBatch}
    </if>
    <if test="productBatchEn != null">
      AND us.PRODUCT_BATCH_EN=#{productBatchEn}
    </if>
    <if test="materialGrade != null">
      AND us.MATERIAL_GRADE=#{materialGrade}
    </if>
    <if test="materialGradeEn != null">
      AND us.MATERIAL_GRADE_EN=#{materialGradeEn}
    </if>
    <if test="supplierName != null">
      AND us.SUPPLIER_NAME=#{supplierName}
    </if>
    <if test="supplierNameEn != null">
      AND us.SUPPLIER_NAME_EN=#{supplierNameEn}
    </if>
     <if test="buyersName != null">
      AND us.BUYERS_NAME=#{buyersName}
    </if>
     <if test="buyersNameEn != null">
      AND us.BUYERS_NAME_EN=#{buyersNameEn}
    </if>
    <if test="remark != null">
      AND us.REMARK=#{remark}
    </if>
    <if test="remarkEn != null">
      AND us.REMARK_EN=#{remarkEn}
    </if>
    <if test="lotNo != null">
      AND us.LOT_NO=#{lotNo}
    </if>
    <if test="lotNoEn != null">
      AND us.LOT_NO_EN=#{lotNoEn}
    </if>
    <if test="producer != null">
      AND us.PRODUCER=#{producer}
    </if>
    <if test="producerEn != null">
      AND us.PRODUCER_EN=#{producerEn}
    </if>





  </sql>
</mapper>