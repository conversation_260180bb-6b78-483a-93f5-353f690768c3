<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.order.dao.OrderDetailMapper" >

  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.vo.VOOrderDetail" >
    <id column="DETAIL_ID" property="detailId" jdbcType="BIGINT" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="DETAIL_NO" property="detailNo" jdbcType="VARCHAR" />
    <result column="BU" property="bu" jdbcType="VARCHAR" />
    <result column="BU_NAME" property="buName" jdbcType="VARCHAR" />
    <result column="ITEM_ID" property="itemId" jdbcType="INTEGER" />
    <result column="ITEM_NAME" property="itemName" jdbcType="VARCHAR" />
    <result column="TEST_NAME" property="testName" jdbcType="VARCHAR" />
    <result column="ORIGINAL_PRICE" property="originalPrice" jdbcType="DECIMAL" />
    <result column="PRICE" property="price" jdbcType="DECIMAL" />
    <result column="BUY_NUMS" property="buyNums" jdbcType="INTEGER" />
    <result column="DIS_PRICE" property="disPrice" jdbcType="DECIMAL" />
    <result column="TOTAL_PRICE" property="totalPrice" jdbcType="DECIMAL" />
    <result column="STATE" property="state" jdbcType="TINYINT" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
    <result column="MEMO" property="memo" jdbcType="VARCHAR" />
    <result column="TEST_DAYS" property="testDays" jdbcType="DECIMAL" />
    <result column="OTHER_EXPLAIN" property="otherExplain" jdbcType="VARCHAR" />
    <result column="MEMO_EXPLAIN" property="memoExplain" jdbcType="VARCHAR" />
    <result column="TEST_MEMO" property="testMemo" jdbcType="VARCHAR" />
    <result column="QUOTA_DATE" property="quotaDate" jdbcType="VARCHAR" />
    <result column="STANDARD_CODE" property="standardCode" jdbcType="VARCHAR" />
    <result column="SAMPLE_REQUIREMENTS" property="sampleRequirements" jdbcType="VARCHAR" />
    <result column="UNIT" property="unit" jdbcType="VARCHAR" />
    <result column="BUSINESS_LINE" property="businessLine" jdbcType="VARCHAR" />
    <result column="LAB_NAMES" property="labNames" jdbcType="VARCHAR" />
    <result column="PRODUCT_ID" property="productId" jdbcType="BIGINT" />
    <result column="EXTEND_NAME" property="extendName" jdbcType="VARCHAR" />
  </resultMap>
  <resultMap id="BaseResultDTOMap" type="com.sgs.ecom.member.dto.OrderDetailDTO" >
    <id column="DETAIL_ID" property="detailId" jdbcType="BIGINT" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="DETAIL_NO" property="detailNo" jdbcType="VARCHAR" />
    <result column="BU" property="bu" jdbcType="VARCHAR" />
    <result column="ITEM_ID" property="itemId" jdbcType="INTEGER" />
    <result column="ITEM_NAME" property="itemName" jdbcType="VARCHAR" />
    <result column="TEST_NAME" property="testName" jdbcType="VARCHAR" />
    <result column="ORIGINAL_PRICE" property="originalPrice" jdbcType="DECIMAL" />
    <result column="PRICE" property="price" jdbcType="DECIMAL" />
    <result column="BUY_NUMS" property="buyNums" jdbcType="INTEGER" />
    <result column="TOTAL_PRICE" property="totalPrice" jdbcType="DECIMAL" />
    <result column="STATE" property="state" jdbcType="TINYINT" />
    <result column="MEMO" property="memo" jdbcType="VARCHAR" />
    <result column="TEST_DAYS" property="testDays" jdbcType="DECIMAL" />
    <result column="OTHER_EXPLAIN" property="otherExplain" jdbcType="VARCHAR" />
    <result column="MEMO_EXPLAIN" property="memoExplain" jdbcType="VARCHAR" />
    <result column="TEST_MEMO" property="testMemo" jdbcType="VARCHAR" />
    <result column="STANDARD_CODE" property="standardCode" jdbcType="VARCHAR" />
    <result column="SAMPLE_REQUIREMENTS" property="sampleRequirements" jdbcType="VARCHAR" />
    <result column="UNIT" property="unit" jdbcType="VARCHAR" />
    <result column="BUSINESS_LINE" property="businessLine" jdbcType="VARCHAR" />
    <result column="PRODUCT_ID" property="productId" jdbcType="BIGINT" />
    <result column="EXTEND_NAME" property="extendName" jdbcType="VARCHAR" />
    <result column="ITEM_NAME_GROUP" property="itemNameGroup" jdbcType="VARCHAR" />
    <result column="PACKAGE_TYPE" property="packageType" jdbcType="VARCHAR" />
    <result column="LABEL_NAME" property="labelName" jdbcType="VARCHAR" />
    <result column="LOWEST_PRICE" property="lowestPrice" jdbcType="DECIMAL" />
    <result column="SKU_PRICE" property="skuPrice" jdbcType="DECIMAL" />
    <result column="LOWEST_PRICE_MARGIN" property="lowestPriceMargin" jdbcType="DECIMAL" />
  </resultMap>
  <sql id="Base_Column_List" >
    DETAIL_ID, ORDER_NO, DETAIL_NO, BU, BU_NAME, ITEM_ID, ITEM_NAME, TEST_NAME, ORIGINAL_PRICE,
    PRICE, BUY_NUMS, DIS_PRICE, TOTAL_PRICE, STATE, CREATE_DATE, STATE_DATE, MEMO, TEST_DAYS,
    OTHER_EXPLAIN, MEMO_EXPLAIN, TEST_MEMO, QUOTA_DATE, STANDARD_CODE, SAMPLE_REQUIREMENTS,
    UNIT, BUSINESS_LINE, LAB_NAMES,PRODUCT_ID,EXTEND_NAME
  </sql>
  <select id="selectVOByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from ORDER_DETAIL
    where DETAIL_ID = #{detailId,jdbcType=BIGINT}
  </select>





  <insert id="insertVOSelective" parameterType="com.sgs.ecom.member.vo.VOOrderDetail" >
    <selectKey resultType="java.lang.Long" keyProperty="detailId" order="AFTER">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ORDER_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="orderNo != null" >
        ORDER_NO,
      </if>
      <if test="detailNo != null" >
        DETAIL_NO,
      </if>
      <if test="bu != null" >
        BU,
      </if>
      <if test="buName != null" >
        BU_NAME,
      </if>
      <if test="itemId != null" >
        ITEM_ID,
      </if>
      <if test="itemName != null" >
        ITEM_NAME,
      </if>
      <if test="testName != null" >
        TEST_NAME,
      </if>
      <if test="originalPrice != null" >
        ORIGINAL_PRICE,
      </if>
      <if test="price != null" >
        PRICE,
      </if>
      <if test="buyNums != null" >
        BUY_NUMS,
      </if>
      <if test="disPrice != null" >
        DIS_PRICE,
      </if>
      <if test="totalPrice != null" >
        TOTAL_PRICE,
      </if>
      <if test="state != null" >
        STATE,
      </if>
      <if test="createDate != null" >
        CREATE_DATE,
      </if>
      <if test="stateDate != null" >
        STATE_DATE,
      </if>
      <if test="memo != null" >
        MEMO,
      </if>
      <if test="testDays != null" >
        TEST_DAYS,
      </if>
      <if test="otherExplain != null" >
        OTHER_EXPLAIN,
      </if>
      <if test="memoExplain != null" >
        MEMO_EXPLAIN,
      </if>
      <if test="testMemo != null" >
        TEST_MEMO,
      </if>
      <if test="quotaDate != null" >
        QUOTA_DATE,
      </if>
      <if test="standardCode != null" >
        STANDARD_CODE,
      </if>
      <if test="sampleRequirements != null" >
        SAMPLE_REQUIREMENTS,
      </if>
      <if test="unit != null" >
        UNIT,
      </if>
      <if test="businessLine != null" >
        BUSINESS_LINE,
      </if>
      <if test="labNames != null" >
        LAB_NAMES,
      </if>
      <if test="groupNo != null" >
        GROUP_NO,
      </if>
      <if test="cnasLab != null" >
        CNAS_LAB,
      </if>
      <if test="cmaLab != null" >
        CMA_LAB,
      </if>
      <if test="categoryPath != null" >
        CATEGORY_PATH,
      </if>
      <if test="categoryId != null" >
        CATEGORY_ID,
      </if>
      <if test="itemType != null" >
        ITEM_TYPE,
      </if>
      <if test="parentDetailId != null" >
        PARENT_DETAIL_ID,
      </if>
      <if test="isOptional != null" >
        IS_OPTIONAL,
      </if>
      <if test="labelName != null" >
        LABEL_NAME,
      </if>
      <if test="isDefault != null" >
        IS_DEFAULT,
      </if>
      <if test="productId != null" >
          PRODUCT_ID,
      </if>
      <if test="isDetermine != null" >
        IS_DETERMINE,
      </if>
      <if test="itemAlias != null" >
        ITEM_ALIAS,
      </if>
      <if test="urgentType != null" >
        URGENT_TYPE,
      </if>
      <if test="urgentAmount != null" >
        URGENT_AMOUNT,
      </if>
        <if test="extendName != null" >
          EXTEND_NAME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="detailNo != null" >
        #{detailNo,jdbcType=VARCHAR},
      </if>
      <if test="bu != null" >
        #{bu,jdbcType=VARCHAR},
      </if>
      <if test="buName != null" >
        #{buName,jdbcType=VARCHAR},
      </if>
      <if test="itemId != null" >
        #{itemId,jdbcType=INTEGER},
      </if>
      <if test="itemName != null" >
        #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="testName != null" >
        #{testName,jdbcType=VARCHAR},
      </if>
      <if test="originalPrice != null" >
        #{originalPrice,jdbcType=DECIMAL},
      </if>
      <if test="price != null" >
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="buyNums != null" >
        #{buyNums,jdbcType=INTEGER},
      </if>
      <if test="disPrice != null" >
        #{disPrice,jdbcType=DECIMAL},
      </if>
      <if test="totalPrice != null" >
        #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="state != null" >
        #{state,jdbcType=TINYINT},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        #{stateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="memo != null" >
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="testDays != null" >
        #{testDays},
      </if>
      <if test="otherExplain != null" >
        #{otherExplain,jdbcType=VARCHAR},
      </if>
      <if test="memoExplain != null" >
        #{memoExplain,jdbcType=VARCHAR},
      </if>
      <if test="testMemo != null" >
        #{testMemo,jdbcType=VARCHAR},
      </if>
      <if test="quotaDate != null" >
        #{quotaDate,jdbcType=VARCHAR},
      </if>
      <if test="standardCode != null" >
        #{standardCode,jdbcType=VARCHAR},
      </if>
      <if test="sampleRequirements != null" >
        #{sampleRequirements,jdbcType=VARCHAR},
      </if>
      <if test="unit != null" >
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="businessLine != null" >
        #{businessLine,jdbcType=VARCHAR},
      </if>
      <if test="labNames != null" >
        #{labNames,jdbcType=VARCHAR},
      </if>
      <if test="groupNo != null" >
        #{groupNo,jdbcType=VARCHAR},
      </if>
      <if test="cnasLab != null" >
        #{cnasLab,jdbcType=VARCHAR},
      </if>
      <if test="cmaLab != null" >
        #{cmaLab,jdbcType=VARCHAR},
      </if>
      <if test="categoryPath != null" >
        #{categoryPath},
      </if>
      <if test="categoryId != null" >
        #{categoryId},
      </if>
      <if test="itemType != null" >
        #{itemType},
      </if>
      <if test="parentDetailId != null" >
        #{parentDetailId},
      </if>
      <if test="isOptional != null" >
        #{isOptional},
      </if>
      <if test="labelName != null" >
        #{labelName,jdbcType=VARCHAR},
      </if>
      <if test="isDefault != null" >
        #{isDefault},
      </if>
      <if test="productId != null" >
        #{productId,jdbcType=BIGINT},
      </if>
      <if test="isDetermine != null" >
        #{isDetermine,jdbcType=INTEGER},
      </if>
      <if test="itemAlias != null" >
        #{itemAlias},
      </if>
      <if test="urgentType != null" >
        #{urgentType},
      </if>
      <if test="urgentAmount != null" >
        #{urgentAmount},
      </if>
      <if test="extendName != null" >
        #{extendName},
      </if>
    </trim>
  </insert>






  <insert id="insertSelectiveReturnKey" parameterType="com.sgs.ecom.member.entity.order.OrderDetail" >
    <selectKey resultType="java.lang.Long" keyProperty="detailId" order="AFTER">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ORDER_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="orderNo != null" >
        ORDER_NO,
      </if>
      <if test="detailNo != null" >
        DETAIL_NO,
      </if>
      <if test="bu != null" >
        BU,
      </if>
      <if test="buName != null" >
        BU_NAME,
      </if>
      <if test="itemId != null" >
        ITEM_ID,
      </if>
      <if test="itemName != null" >
        ITEM_NAME,
      </if>
      <if test="testName != null" >
        TEST_NAME,
      </if>
      <if test="originalPrice != null" >
        ORIGINAL_PRICE,
      </if>
      <if test="price != null" >
        PRICE,
      </if>
      <if test="buyNums != null" >
        BUY_NUMS,
      </if>
      <if test="disPrice != null" >
        DIS_PRICE,
      </if>
      <if test="totalPrice != null" >
        TOTAL_PRICE,
      </if>
      <if test="state != null" >
        STATE,
      </if>
      <if test="createDate != null" >
        CREATE_DATE,
      </if>
      <if test="stateDate != null" >
        STATE_DATE,
      </if>
      <if test="memo != null" >
        MEMO,
      </if>
      <if test="testDays != null" >
        TEST_DAYS,
      </if>
      <if test="otherExplain != null" >
        OTHER_EXPLAIN,
      </if>
      <if test="memoExplain != null" >
        MEMO_EXPLAIN,
      </if>
      <if test="testMemo != null" >
        TEST_MEMO,
      </if>
      <if test="quotaDate != null" >
        QUOTA_DATE,
      </if>
      <if test="standardCode != null" >
        STANDARD_CODE,
      </if>
      <if test="sampleRequirements != null" >
        SAMPLE_REQUIREMENTS,
      </if>
      <if test="unit != null" >
        UNIT,
      </if>
      <if test="businessLine != null" >
        BUSINESS_LINE,
      </if>
      <if test="labNames != null" >
        LAB_NAMES,
      </if>
      <if test="groupNo != null" >
        GROUP_NO,
      </if>
      <if test="cnasLab != null" >
        CNAS_LAB,
      </if>
      <if test="cmaLab != null" >
        CMA_LAB,
      </if>
      <if test="categoryPath != null" >
        CATEGORY_PATH,
      </if>
      <if test="categoryId != null" >
        CATEGORY_ID,
      </if>
      <if test="itemType != null" >
        ITEM_TYPE,
      </if>
      <if test="parentDetailId != null" >
        PARENT_DETAIL_ID,
      </if>
      <if test="isOptional != null" >
        IS_OPTIONAL,
      </if>
      <if test="labelName != null" >
        LABEL_NAME,
      </if>
      <if test="isDefault != null" >
        IS_DEFAULT,
      </if>
      <if test="productId != null" >
        PRODUCT_ID,
      </if>
      <if test="isDetermine != null" >
        IS_DETERMINE,
      </if>
      <if test="itemAlias != null" >
        ITEM_ALIAS,
      </if>
      <if test="urgentType != null" >
        URGENT_TYPE,
      </if>
      <if test="urgentAmount != null" >
        URGENT_AMOUNT,
      </if>
      <if test="extendName != null" >
        EXTEND_NAME,
      </if>
      <if test="testLineId != null" >
        TEST_LINE_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="detailNo != null" >
        #{detailNo,jdbcType=VARCHAR},
      </if>
      <if test="bu != null" >
        #{bu,jdbcType=VARCHAR},
      </if>
      <if test="buName != null" >
        #{buName,jdbcType=VARCHAR},
      </if>
      <if test="itemId != null" >
        #{itemId,jdbcType=INTEGER},
      </if>
      <if test="itemName != null" >
        #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="testName != null" >
        #{testName,jdbcType=VARCHAR},
      </if>
      <if test="originalPrice != null" >
        #{originalPrice,jdbcType=DECIMAL},
      </if>
      <if test="price != null" >
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="buyNums != null" >
        #{buyNums,jdbcType=INTEGER},
      </if>
      <if test="disPrice != null" >
        #{disPrice,jdbcType=DECIMAL},
      </if>
      <if test="totalPrice != null" >
        #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="state != null" >
        #{state,jdbcType=TINYINT},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        #{stateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="memo != null" >
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="testDays != null" >
        #{testDays},
      </if>
      <if test="otherExplain != null" >
        #{otherExplain,jdbcType=VARCHAR},
      </if>
      <if test="memoExplain != null" >
        #{memoExplain,jdbcType=VARCHAR},
      </if>
      <if test="testMemo != null" >
        #{testMemo,jdbcType=VARCHAR},
      </if>
      <if test="quotaDate != null" >
        #{quotaDate,jdbcType=VARCHAR},
      </if>
      <if test="standardCode != null" >
        #{standardCode,jdbcType=VARCHAR},
      </if>
      <if test="sampleRequirements != null" >
        #{sampleRequirements,jdbcType=VARCHAR},
      </if>
      <if test="unit != null" >
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="businessLine != null" >
        #{businessLine,jdbcType=VARCHAR},
      </if>
      <if test="labNames != null" >
        #{labNames,jdbcType=VARCHAR},
      </if>
      <if test="groupNo != null" >
        #{groupNo,jdbcType=VARCHAR},
      </if>
      <if test="cnasLab != null" >
        #{cnasLab,jdbcType=VARCHAR},
      </if>
      <if test="cmaLab != null" >
        #{cmaLab,jdbcType=VARCHAR},
      </if>
      <if test="categoryPath != null" >
        #{categoryPath},
      </if>
      <if test="categoryId != null" >
        #{categoryId},
      </if>
      <if test="itemType != null" >
        #{itemType},
      </if>
      <if test="parentDetailId != null" >
        #{parentDetailId},
      </if>
      <if test="isOptional != null" >
        #{isOptional},
      </if>
      <if test="labelName != null" >
        #{labelName,jdbcType=VARCHAR},
      </if>
      <if test="isDefault != null" >
        #{isDefault},
      </if>
      <if test="productId != null" >
        #{productId,jdbcType=BIGINT},
      </if>
      <if test="isDetermine != null" >
        #{isDetermine,jdbcType=INTEGER},
      </if>
      <if test="itemAlias != null" >
        #{itemAlias},
      </if>
      <if test="urgentType != null" >
        #{urgentType},
      </if>
      <if test="urgentAmount != null" >
        #{urgentAmount},
      </if>
      <if test="extendName != null" >
        #{extendName},
      </if>
      <if test="testLineId != null" >
        #{testLineId},
      </if>
    </trim>
  </insert>

















  <resultMap id="resultDTO" type="com.sgs.ecom.member.dto.OrderDetailDTO" >
    <id column="DETAIL_ID" property="detailId" jdbcType="BIGINT" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="ITEM_ID" property="itemId" jdbcType="INTEGER" />
    <result column="ITEM_NAME" property="itemName" jdbcType="VARCHAR" />
    <result column="TEST_NAME" property="testName" jdbcType="VARCHAR" />
    <result column="PRICE" property="price" jdbcType="DECIMAL" />
    <result column="ORIGINAL_PRICE" property="originalPrice" jdbcType="DECIMAL" />
    <result column="BUY_NUMS" property="buyNums" jdbcType="INTEGER" />
    <result column="DETAIL_STATE" property="detailState" jdbcType="INTEGER" />
    <result column="MEMO" property="memo" jdbcType="VARCHAR" />
    <result column="TEST_DAYS" property="testDays" jdbcType="DECIMAL" />
    <result column="OTHER_EXPLAIN" property="otherExplain" jdbcType="VARCHAR" />
    <result column="MEMO_EXPLAIN" property="memoExplain" jdbcType="VARCHAR" />

    <result column="STANDARD_CODE" property="standardCode" jdbcType="VARCHAR" />
    <result column="BUSINESS_LINE" property="businessLine" jdbcType="VARCHAR" />
    <result column="LAB_NAMES" property="labName" jdbcType="VARCHAR" />
    <result column="UNIT" property="unit" jdbcType="VARCHAR" />

    <result column="TEST_MEMO" property="testMemo" jdbcType="VARCHAR" />
    <result column="SAMPLE_REQUIREMENTS" property="sampleRequirements" jdbcType="VARCHAR" />

    <result column="CNAS_LAB" property="cnasLab" jdbcType="VARCHAR" />
    <result column="BU" property="bu" jdbcType="VARCHAR" />
    <result column="CMA_LAB" property="cmaLab" jdbcType="VARCHAR" />
    <result column="TOTAL_PRICE" property="totalPrice" jdbcType="DECIMAL" />
    <result column="IS_OPTIONAL" property="isOptional" jdbcType="INTEGER" />
    <result column="LABEL_NAME" property="labelName" jdbcType="VARCHAR" />
    <result column="IS_DEFAULT" property="isDefault" jdbcType="INTEGER" />
    <result column="ITEM_TYPE" property="itemType" jdbcType="INTEGER" />
    <result column="CATEGORY_PATH" property="categoryPath" jdbcType="VARCHAR" />
    <result column="ITEM_ALIAS" property="itemAlias" jdbcType="VARCHAR" />
    <result column="PRODUCT_ID" property="productId" jdbcType="BIGINT" />
    <result column="IS_DETERMINE" property="isDetermine" jdbcType="INTEGER" />
    <result column="URGENT_TYPE" property="urgentType" jdbcType="INTEGER" />
    <result column="URGENT_AMOUNT" property="urgentAmount" jdbcType="DECIMAL" />
    <result column="GROUP_NO" property="groupNo" jdbcType="VARCHAR" />
    <result column="EXTEND_NAME" property="extendName" jdbcType="VARCHAR" />
    <result column="PARENT_DETAIL_ID" property="parentDetailId" jdbcType="INTEGER" />
    <result column="BASE_ORIGINAL_PRICE" property="baseOriginalPrice" jdbcType="DECIMAL" />
    <result column="IS_CS" property="isCs" jdbcType="INTEGER" />
    <result column="TEST_LINE_ID" property="testLineId" jdbcType="VARCHAR" />
  </resultMap>

  <select id="selectListByMap" resultMap="resultDTO" >
    select
    DETAIL_ID, ORDER_NO,ITEM_NAME,ITEM_ID,TEST_NAME,UNIT,TEST_NAME,TEST_MEMO,
    MEMO,MEMO_EXPLAIN,TEST_DAYS,PRICE,BUY_NUMS,OTHER_EXPLAIN,
    BUSINESS_LINE, LAB_NAMES,STANDARD_CODE,UNIT,SAMPLE_REQUIREMENTS,TOTAL_PRICE,BU
    CNAS_LAB,CMA_LAB,IS_OPTIONAL,LABEL_NAME,IS_DEFAULT,CATEGORY_PATH,ITEM_ALIAS,
    ORIGINAL_PRICE,PRODUCT_ID,ITEM_TYPE,IS_DETERMINE,URGENT_TYPE,URGENT_AMOUNT,GROUP_NO,EXTEND_NAME,PARENT_DETAIL_ID,
    BASE_ORIGINAL_PRICE,IS_CS,TEST_LINE_ID
    from ORDER_DETAIL
    <include refid="baseQueryWhere"/>
  </select>

  <select id="selectListByGroupMap" resultMap="resultDTO" >
    select
    DETAIL_ID, ORDER_NO,ITEM_NAME,ITEM_ID,TEST_NAME,UNIT,TEST_NAME,TEST_MEMO,
    MEMO,MEMO_EXPLAIN,TEST_DAYS,PRICE,BUY_NUMS,OTHER_EXPLAIN,
    BUSINESS_LINE, LAB_NAMES,STANDARD_CODE,UNIT,SAMPLE_REQUIREMENTS,TOTAL_PRICE,BU
    CNAS_LAB,CMA_LAB,CATEGORY_PATH,PRODUCT_ID,IS_DETERMINE,URGENT_TYPE,URGENT_AMOUNT,EXTEND_NAME
    from ORDER_DETAIL
    where 1=1
    <if test="parentDetailId != null">
      AND PARENT_DETAIL_ID=#{parentDetailId}
    </if>
    <if test="orderNoList != null ">
      AND ORDER_NO in
      <foreach collection="orderNoList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    group by ORDER_NO,ITEM_ID, ITEM_TYPE
    order by DETAIL_ID asc
  </select>


  <select id="selectSubListByMap" resultMap="resultDTO" >
    select obi.ORDER_NO,
    od.ITEM_NAME,od.ITEM_ID,od.TEST_NAME,od.UNIT,od.TEST_NAME,od.TEST_MEMO,
    od.MEMO,od.MEMO_EXPLAIN,od.TEST_DAYS,od.PRICE,od.ORIGINAL_PRICE,od.BUY_NUMS,od.TOTAL_PRICE,od.OTHER_EXPLAIN,
    od.BUSINESS_LINE, od.LAB_NAMES,od.STANDARD_CODE,od.UNIT,od.SAMPLE_REQUIREMENTS,
    od.CNAS_LAB,od.CMA_LAB,od.PRODUCT_ID,od.ITEM_TYPE,
    os.SAMPLE_NAME,od.URGENT_TYPE,od.URGENT_AMOUNT,EXTEND_NAME
    from ORDER_BASE_INFO obi, ORDER_DETAIL od,ORDER_SAMPLE os,ORDER_SAMPLE_RELATE osr
    where
    obi.order_no=od.order_no and obi.GROUP_NO=od.GROUP_NO
    and obi.order_no=os.order_no and obi.GROUP_NO=os.GROUP_NO
    and obi.order_no=osr.order_no and obi.GROUP_NO=osr.GROUP_NO
    and od.DETAIL_ID=osr.DETAIL_ID
    and os.SAMPLE_NO=osr.SAMPLE_NO
    <if test="orderNoList != null ">
      AND obi.ORDER_NO in
      <foreach collection="orderNoList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </select>







  <sql id="baseQueryWhere">
    where 1=1 and STATE=1
    <if test="orderNo != null">
      AND ORDER_NO=#{orderNo}
    </if>
    <if test="groupNo != null">
      AND GROUP_NO=#{groupNo}
    </if>
    <if test="itemType != null">
      AND ITEM_TYPE=#{itemType}
    </if>
    <if test="productId != null">
      AND PRODUCT_ID=#{productId}
    </if>
    <if test="parentDetailId != null">
      AND PARENT_DETAIL_ID=#{parentDetailId}
    </if>
    <if test="isDefault != null">
      AND IS_DEFAULT=#{isDefault}
    </if>
    <if test="orderNoList != null ">
      AND ORDER_NO in
      <foreach collection="orderNoList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </sql>


  <insert id="copyDetailBySql"  >
insert into ORDER_DETAIL
(ORIGINAL_PRICE,TEST_MEMO,LAB_NAMES,STATE,MEMO_EXPLAIN,ORDER_NO,ITEM_NAME,DETAIL_NO,BU,SAMPLE_REQUIREMENTS,TOTAL_PRICE,
TEST_NAME,BUY_NUMS,TEST_DAYS,BUSINESS_LINE,STANDARD_CODE,OTHER_EXPLAIN,MEMO,UNIT,CMA_LAB,PRICE,STATE_DATE,ITEM_ID,DIS_PRICE,BU_NAME,CREATE_DATE,
GROUP_NO,QUOTA_DATE,CNAS_LAB,IS_OPTIONAL,LABEL_NAME,IS_DEFAULT,PRODUCT_ID,IS_DETERMINE,ITEM_ALIAS,URGENT_TYPE,URGENT_AMOUNT,BASE_ORIGINAL_PRICE )
 select ORIGINAL_PRICE,TEST_MEMO,LAB_NAMES,STATE,MEMO_EXPLAIN,#{newOrderNo},ITEM_NAME,DETAIL_ID,BU,SAMPLE_REQUIREMENTS,TOTAL_PRICE,
 TEST_NAME,BUY_NUMS,TEST_DAYS,BUSINESS_LINE,STANDARD_CODE,OTHER_EXPLAIN,MEMO,UNIT,CMA_LAB,PRICE,now(),ITEM_ID,DIS_PRICE,BU_NAME,now(),
 #{newGroupNo},QUOTA_DATE,CNAS_LAB,IS_OPTIONAL,LABEL_NAME,IS_DEFAULT,PRODUCT_ID,IS_DETERMINE,ITEM_ALIAS,URGENT_TYPE,URGENT_AMOUNT,BASE_ORIGINAL_PRICE
from ORDER_DETAIL   where GROUP_NO = #{oldGroupNo} and ORDER_NO=#{orderNo}
  </insert>



  <resultMap id="BaseRSTSMap" type="com.sgs.ecom.member.vo.VORSTSOrderDetail" >
    <id column="DETAIL_ID" property="detailId" jdbcType="BIGINT" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="GROUP_NO" property="groupNo" jdbcType="VARCHAR" />
    <result column="ITEM_ID" property="itemId" jdbcType="INTEGER" />
    <result column="ITEM_NAME" property="itemName" jdbcType="VARCHAR" />
    <result column="PRICE" property="price" jdbcType="DECIMAL" />
    <result column="BUY_NUMS" property="buyNums" jdbcType="INTEGER" />
    <result column="TOTAL_PRICE" property="totalPrice" jdbcType="DECIMAL" />
    <result column="STATE" property="state" jdbcType="TINYINT" />
    <result column="TEST_NAME" property="testName" jdbcType="VARCHAR" />
    <result column="UNIT" property="unit" jdbcType="VARCHAR" />
    <result column="PARENT_DETAIL_ID" property="parentDetailId" jdbcType="INTEGER" />
    <result column="ITEM_TYPE" property="itemType" jdbcType="INTEGER" />
    <result column="CATEGORY_PATH" property="categoryPath" jdbcType="VARCHAR" />
    <result column="CATEGORY_ID" property="categoryId" jdbcType="BIGINT" />
    <result column="ORIGINAL_PRICE" property="originalPrice" jdbcType="DECIMAL" />
    <result column="TEST_DAYS" property="testDays" jdbcType="DECIMAL" />
    <result column="SAMPLE_REQUIREMENTS" property="sampleRequirements" jdbcType="VARCHAR" />
    <result column="TEST_MEMO" property="testMemo" jdbcType="VARCHAR" />
    <result column="MEMO" property="memo" jdbcType="VARCHAR" />
    <result column="LABEL_NAME" property="labelName" jdbcType="VARCHAR" />
  </resultMap>

  <select id="selectRSTSVOListByMap" resultMap="BaseRSTSMap" >
    select
    DETAIL_ID,ORDER_NO,GROUP_NO,ITEM_ID,ITEM_NAME,PRICE,BUY_NUMS,TOTAL_PRICE,STATE,TEST_NAME,UNIT,
    PARENT_DETAIL_ID,ITEM_TYPE,CATEGORY_PATH,CATEGORY_ID,ORIGINAL_PRICE,TEST_DAYS,SAMPLE_REQUIREMENTS,
    TEST_MEMO,MEMO,PRODUCT_ID,ITEM_TYPE,LABEL_NAME
    from ORDER_DETAIL
    where 1=1
    <if test="orderNo != null">
      AND ORDER_NO=#{orderNo}
    </if>
    <if test="state != null">
      AND STATE=#{state}
    </if>
    <if test="groupNo != null">
      AND GROUP_NO=#{groupNo}
    </if>
    <if test="productId != null">
      AND PRODUCT_ID=#{productId}
    </if>
    <if test="parentDetailId != null">
      AND PARENT_DETAIL_ID=#{parentDetailId}
    </if>
    <if test="orderNoList != null ">
      AND ORDER_NO in
      <foreach collection="orderNoList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="stateList != null ">
      AND STATE in
      <foreach collection="stateList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <include refid="useGroup"/>
    order by DETAIL_ID asc
  </select>

  <sql id="useGroup">
    <if test="useGroup != null">
    GROUP BY ORDER_NO,ITEM_ID,ITEM_TYPE,PARENT_DETAIL_ID
    </if>
  </sql>



  <delete id="delDetailByMap"  >
    delete from ORDER_DETAIL
    where GROUP_NO = #{groupNo} and ORDER_NO=#{orderNo}
  </delete>

  <insert id="insertForeach"  >
    insert into ORDER_DETAIL
    (
    ORDER_NO,CREATE_DATE, STATE_DATE, STATE,ITEM_NAME,
    PRICE,STANDARD_CODE,BUY_NUMS,TOTAL_PRICE,PRODUCT_ID,
    ITEM_TYPE,TEST_NAME,PARENT_DETAIL_ID,EXTEND_NAME
    )
    values
    <foreach collection ="list" item="item" index= "index" separator =",">
      (
      #{item.orderNo}, #{item.createDate},#{item.stateDate},#{item.state},#{item.itemName},
      #{item.price},#{item.standardCode},#{item.buyNums},#{item.totalPrice},#{item.productId},
      #{item.itemType},#{item.testName},#{item.parentDetailId},#{item.extendName}
      )
    </foreach>
  </insert>

  <insert id="insertDetailForeach"  >
    insert into ORDER_DETAIL
    (
    ORDER_NO,CREATE_DATE, STATE,ITEM_NAME,
    PRICE,STANDARD_CODE,BUY_NUMS,TOTAL_PRICE,PRODUCT_ID,
    ITEM_TYPE,TEST_NAME,PARENT_DETAIL_ID,TEST_MEMO,GROUP_NO,IS_DETERMINE,EXTEND_NAME
    )
    values
    <foreach collection ="list" item="item" index= "index" separator =",">
      (
      #{item.orderNo,jdbcType=VARCHAR}, #{item.createDate,jdbcType=VARCHAR},#{item.state},
       #{item.itemName,jdbcType=VARCHAR}, #{item.price},#{item.standardCode,jdbcType=VARCHAR},#{item.buyNums},
       #{item.totalPrice},#{item.productId}, #{item.itemType},#{item.testName,jdbcType=VARCHAR},
       #{item.parentDetailId},#{item.testMemo,jdbcType=VARCHAR},#{item.groupNo,jdbcType=VARCHAR}, #{item.isDetermine },#{item.extendName}
      )
    </foreach>
  </insert>
  <select id="qryListByOrderNoList" resultMap="resultDTO">
    select
    DETAIL_ID, ORDER_NO,ITEM_NAME,ITEM_ID,TEST_NAME,UNIT,TEST_NAME,TEST_MEMO,
    MEMO,MEMO_EXPLAIN,TEST_DAYS,PRICE,BUY_NUMS,OTHER_EXPLAIN,
    BUSINESS_LINE, LAB_NAMES,STANDARD_CODE,UNIT,SAMPLE_REQUIREMENTS,TOTAL_PRICE,BU,
    CNAS_LAB,CMA_LAB,IS_OPTIONAL,LABEL_NAME,IS_DEFAULT,CATEGORY_PATH,ITEM_ALIAS,
    ORIGINAL_PRICE,PRODUCT_ID,ITEM_TYPE,IS_DETERMINE,URGENT_TYPE,URGENT_AMOUNT,GROUP_NO,EXTEND_NAME,PARENT_DETAIL_ID,STATE AS 'DETAIL_STATE'
    from ORDER_DETAIL
    <include refid="baseQueryNewWhere"/>
  </select>
  <select id="qryDetailListByType" resultMap="BaseResultDTOMap">
    select od.ORDER_NO,od.DETAIL_ID ,
    (case
    when od.ITEM_NAME is null or  od.ITEM_NAME = '' then ''
    else concat('【',od.ITEM_NAME,'】')
    end
    )as ITEM_NAME,
    op.quantity as 'BUY_NUMS',
    op.price as 'PRICE',
    op.LOWEST_PRICE,
    op.SKU_PRICE,
    op.LOWEST_PRICE_MARGIN,
    (
    case
    when (INSTR(od.ITEM_NAME ,'自选') &lt;=0  or INSTR(od.ITEM_NAME ,'套餐') &gt;0) and  INSTR(od.ITEM_NAME ,'客服定制') &lt;=0 then 'package'
    when INSTR(od.ITEM_NAME ,'自选') &gt;0 and INSTR(od.ITEM_NAME ,'套餐') &lt;=0  then 'optional'
    when INSTR(od.ITEM_NAME ,'客服定制') &gt;0   then 'csBespoke'
    else  ''
    END
    ) as PACKAGE_TYPE,
    od.LABEL_NAME ,
    (
    select  GROUP_CONCAT(IFNULL(od2.ITEM_NAME,od2.TEST_NAME) separator ';') from order_detail od2 where  od2.ORDER_NO =od.ORDER_NO and od2.PRODUCT_ID =op.PRODUCT_ID   AND od2.ITEM_TYPE =2 and od2.STATE =1 group by op.PRODUCT_ID
    ) as ITEM_NAME_GROUP from
    order_product op,
    order_detail od
    where
    1=1
    AND od.order_no=op.order_no and od.PRODUCT_ID =op.PRODUCT_ID
    <if test="orderNoList != null ">
      AND od.ORDER_NO in
      <foreach collection="orderNoList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="isDefault != null">
      AND IS_DEFAULT=#{isDefault}
    </if>
    and od.STATE =1 and od.ITEM_TYPE =1  group by od.DETAIL_ID
  </select>
    <sql id="baseQueryNewWhere">
    where 1=1
    <if test="orderNo != null">
      AND ORDER_NO=#{orderNo}
    </if>
    <if test="groupNo != null">
      AND GROUP_NO=#{groupNo}
    </if>
    <if test="itemType != null">
      AND ITEM_TYPE=#{itemType}
    </if>
    <if test="productId != null">
      AND PRODUCT_ID=#{productId}
    </if>
    <if test="parentDetailId != null">
      AND PARENT_DETAIL_ID=#{parentDetailId}
    </if>
    <if test="orderNoList != null ">
      AND ORDER_NO in
      <foreach collection="orderNoList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
      <if test="orderAddGroupList != null ">
        AND CONCAT(ORDER_NO,"-",GROUP_NO) in
        <foreach collection="orderAddGroupList" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>

    <if test="state != null">
      AND STATE=#{state}
    </if>
    <if test="stateList != null ">
      AND obi.STATE in
      <foreach collection="stateList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </sql>

  <update id="updateOrderDetailByState">
    update ORDER_DETAIL SET STATE =0  WHERE STATE =1 AND ORDER_NO=#{orderNo}
  </update>


</mapper>