package com.sgs.ecom.member.dto.oiq;

import com.platform.util.ValidationUtil;
import com.sgs.ecom.member.dto.order.OrderLinkDTO;
import com.sgs.ecom.member.entity.order.OrderLink;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class OiqOrderLinkDTO {
    private String linkName;
    private String linkEmail;
    private String linkPhone;

    public OiqOrderLinkDTO() {
    }

    public static String listToStr(Map<String, List<OrderLinkDTO>> linkMap, String key, String str){
        String returnStr="";
        List<OrderLinkDTO> list=linkMap.getOrDefault(key,new ArrayList<>());
        if(!ValidationUtil.isEmpty(list)){
            returnStr=list.stream().map(OrderLinkDTO::getLinkEmail).collect(Collectors.joining(str));
        }
        return returnStr;
    }



    public static Boolean linkComparator(List<OiqOrderLinkDTO> list1,List<OiqOrderLinkDTO> list2){
        if(ValidationUtil.isEmpty(list1) && ValidationUtil.isEmpty(list2)){
            return true;
        }
        if(ValidationUtil.isEmpty(list1) && !ValidationUtil.isEmpty(list2)){
            return false;
        }
        if(!ValidationUtil.isEmpty(list1) && ValidationUtil.isEmpty(list2)){
            return false;
        }
        String returnStr1=list1.stream().map(OiqOrderLinkDTO::getLinkEmail).collect(Collectors.joining(","));
        String returnStr2=list2.stream().map(OiqOrderLinkDTO::getLinkEmail).collect(Collectors.joining(","));
        return returnStr1.equals(returnStr2);
    }


    public String getLinkName() {
        return linkName;
    }

    public void setLinkName(String linkName) {
        this.linkName = linkName;
    }

    public String getLinkEmail() {
        return linkEmail;
    }

    public void setLinkEmail(String linkEmail) {
        this.linkEmail = linkEmail;
    }

    public String getLinkPhone() {
        return linkPhone;
    }

    public void setLinkPhone(String linkPhone) {
        this.linkPhone = linkPhone;
    }
}
