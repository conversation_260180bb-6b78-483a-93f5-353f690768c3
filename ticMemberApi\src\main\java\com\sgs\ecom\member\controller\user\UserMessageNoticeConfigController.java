package com.sgs.ecom.member.controller.user;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.base.BaseBean;
import com.sgs.ecom.member.request.cust.CustReq;
import com.sgs.ecom.member.service.user.interfaces.IUserMessageNoticeConfigService;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import com.sgs.ecom.member.vo.user.VOUserMessageNoticeConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @ClassName UserMessageNoticeConfigController
 * <AUTHOR>
 * @Date 2025/7/25 15:31
 */
@RestController
@RequestMapping("/business/api.v2.user/msgNotice")
@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
public class UserMessageNoticeConfigController extends ControllerUtil {

    @Autowired
    private IUserMessageNoticeConfigService userMessageNoticeConfigService;

    /**
     * @Function: queryMessageNoticeConfig
     * @Description: 查询用户配置
     *
     * @param: token
     * @return: ResultBody
     *
     * @version: 1.0
     * @author: shenyi
     * @date: 2025-07-25
     */
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "query", method = { RequestMethod.POST })
    public ResultBody queryMessageNoticeConfig(@RequestHeader(value="accessToken") String token) throws Exception {
        return ResultBody.newInstance(userMessageNoticeConfigService.queryMessageNoticeConfig(getUserDTO(token).getUserId()));
    }

    /**
     * @Function: setUserMessageNoticeConfig
     * @Description: 设置用户配置
     *
     * @param: token
     * @return: ResultBody
     *
     * @version: 1.0
     * @author: shenyi
     * @date: 2025-07-25
     */
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "set", method = { RequestMethod.POST })
    public ResultBody setUserMessageNoticeConfig(@RequestHeader(value="accessToken") String token,
                                               @RequestBody List<VOUserMessageNoticeConfig> noticeConfigs) throws Exception {
        userMessageNoticeConfigService.setUserMessageNoticeConfig(
                getUserDTO(token).getUserId(), noticeConfigs);
        return ResultBody.success();
    }
}
