package com.sgs.ecom.member.dto.pdf;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sgs.ecom.member.util.serializer.PriceNumFormatSerializer;

import java.math.BigDecimal;

public class PDFAmountDTO {
    //已选项目税后总价 (一口价使用)
    @JsonSerialize(using = PriceNumFormatSerializer.class)
    private BigDecimal total;

    private BigDecimal tax;

    //税的金额
    @JsonSerialize(using = PriceNumFormatSerializer.class)
    private BigDecimal taxAmount;


    public BigDecimal getTotal() {
        return total;
    }

    public void setTotal(BigDecimal total) {
        this.total = total;
    }

    public BigDecimal getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
    }

    public BigDecimal getTax() {
        return tax;
    }

    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }
}
