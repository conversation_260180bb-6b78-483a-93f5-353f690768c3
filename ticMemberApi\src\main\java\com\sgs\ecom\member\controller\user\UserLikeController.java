package com.sgs.ecom.member.controller.user;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.sgs.ecom.member.service.user.interfaces.IUserLikeSV;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import com.sgs.ecom.member.vo.VOUserLike;

@RestController
@RequestMapping("/business/api.v2.user/like")
public class UserLikeController extends ControllerUtil {

	@Resource
	private IUserLikeSV userLikeSV;
	
	/**   
	* @Function: qryLikeByBusi
	* @Description: 根据业务查询用户点赞
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2022-11-17
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2022-11-17  shenyi    v1.0                 新增
	*/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "stat", method = {RequestMethod.POST})
    public ResultBody qryLikeByBusi(@RequestBody VOUserLike userLike,
    	@RequestHeader(value="accessToken") String token,
        @RequestHeader(value="appId") String appId) throws Exception {
    	userLike.setUserId(getUserId(appId, token));
        return ResultBody.newInstance(userLikeSV.qryLikeByBusi(userLike));
    }
    
    /**   
	* @Function: setUserLike
	* @Description: 用户点赞/取消点赞
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2022-11-17
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2022-11-17  shenyi    v1.0                 新增
	*/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "set", method = {RequestMethod.POST})
    public ResultBody setUserLike(@RequestBody VOUserLike userLike,
    	@RequestHeader(value="accessToken") String token,
        @RequestHeader(value="appId") String appId) throws Exception {
    	userLike.setUserId(getUserId(appId, token));
    	userLikeSV.setUserLike(userLike);
        return ResultBody.success();
    }
}
