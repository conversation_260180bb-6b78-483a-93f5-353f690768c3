<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sgs.ecom.member.domain.order.dao.OrderMemoMapper">
    <resultMap id="BaseResultMap" type="com.sgs.ecom.member.dto.order.OrderMemoDTO">
        <id column="ID" jdbcType="BIGINT" property="id"/>
        <result column="ORDER_NO" jdbcType="VARCHAR" property="orderNo"/>
        <result column="MEMO_CODE" jdbcType="VARCHAR" property="memoCode"/>
        <result column="MEMO_INFO" jdbcType="VARCHAR" property="memoInfo"/>
        <result column="STATE" jdbcType="TINYINT" property="state"/>
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="STATE_DATE" jdbcType="TIMESTAMP" property="stateDate"/>
    </resultMap>
    <sql id="Base_Column_List">
    ID, ORDER_NO, MEMO_CODE, MEMO_INFO, STATE, CREATE_DATE, STATE_DATE
  </sql>


    <insert id="insertSelective" parameterType="com.sgs.ecom.member.entity.order.OrderMemo">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into ORDER_MEMO
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID,
            </if>
            <if test="orderNo != null">
                ORDER_NO,
            </if>
            <if test="memoCode != null">
                MEMO_CODE,
            </if>
            <if test="memoInfo != null">
                MEMO_INFO,
            </if>
            <if test="state != null">
                STATE,
            </if>
            <if test="createDate != null">
                CREATE_DATE,
            </if>
            <if test="stateDate != null">
                STATE_DATE,
            </if>
            <if test="personCode != null">
                PERSON_CODE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="memoCode != null">
                #{memoCode,jdbcType=VARCHAR},
            </if>
            <if test="memoInfo != null">
                #{memoInfo,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                #{state,jdbcType=TINYINT},
            </if>
            <if test="createDate != null">
                #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="stateDate != null">
                #{stateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="personCode != null">
                #{personCode},
            </if>
        </trim>
    </insert>


    <!--自定义 -->
    <resultMap id="resultDTO" type="com.sgs.ecom.member.dto.order.OrderMemoDTO">
        <id column="ID" jdbcType="BIGINT" property="id"/>
        <result column="ORDER_NO" jdbcType="VARCHAR" property="orderNo"/>
        <result column="MEMO_CODE" jdbcType="VARCHAR" property="memoCode"/>
        <result column="MEMO_INFO" jdbcType="VARCHAR" property="memoInfo"/>
        <result column="STATE" jdbcType="TINYINT" property="state"/>
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="STATE_DATE" jdbcType="TIMESTAMP" property="stateDate"/>
        <result column="PERSON_CODE" jdbcType="VARCHAR" property="personCode"/>
    </resultMap>
     <sql id="sqlListDTO">
       ID, ORDER_NO, MEMO_CODE, MEMO_INFO, STATE, CREATE_DATE, STATE_DATE,PERSON_CODE
     </sql>


    <select id="selectListByMap" resultMap="resultDTO">
        select
        <include refid="sqlListDTO"/>
        from ORDER_MEMO
        <include refid="baseQueryWhere"/>
        order by ID desc
    </select>


    <sql id="baseQueryWhere">
        <where>
            <if test="orderNo != null">
                AND ORDER_NO=#{orderNo}
            </if>
            <if test="memoCode != null">
                AND MEMO_CODE=#{memoCode}
            </if>
            <if test="memoInfo != null">
                AND MEMO_INFO=#{memoInfo}
            </if>
            <if test="state != null">
                AND state=#{state}
            </if>
        </where>
    </sql>


</mapper>