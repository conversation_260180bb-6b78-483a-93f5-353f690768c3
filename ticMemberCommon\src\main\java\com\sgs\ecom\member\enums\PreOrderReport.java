package com.sgs.ecom.member.enums;

public enum PreOrderReport {

	NEW("New", "201"),  CANCEL("Cancelled", "202"), APPROVED("Approved", "203"),
	DRAFT("Draft", "204"), REWORKED("Reworked", "205"), COMBINED("Combined", "206"),
	REPLACED("Replaced", "207");

    // 成员变量  
    private String name;  
    private String index;  
    // 构造方法  
    private PreOrderReport(String name, String index) {  
        this.name = name;  
        this.index = index;  
    }  

    // 普通方法  
    public static String getName(String index) {  
        for (PreOrderReport c : PreOrderReport.values()) {  
            if (c.getIndex().equals(index)) {  
                return c.name;  
            }  
        }  
        return null;  
    }  
    // get set 方法  
    public String getName() {  
        return name;  
    }  

    public String getIndex() {  
        return index;  
    }  
}
