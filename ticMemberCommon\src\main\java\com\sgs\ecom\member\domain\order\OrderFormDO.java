package com.sgs.ecom.member.domain.order;

import com.sgs.ecom.member.bo.AttrInfo;
import com.sgs.ecom.member.dto.OrderBaseInfoDTO;
import com.sgs.ecom.member.dto.address.ApplicationAddressDTO;
import com.sgs.ecom.member.dto.bbc.TicPdfDTO;
import com.sgs.ecom.member.dto.center.LabDTO;
import com.sgs.ecom.member.dto.detail.OrderApplicationFormDTO;
import com.sgs.ecom.member.dto.detail.OrderInvoiceDTO;
import com.sgs.ecom.member.dto.detail.OrderReportDTO;
import com.sgs.ecom.member.dto.order.*;
import com.sgs.ecom.member.dto.pay.BankDTO;

import java.util.List;

/**
 * <AUTHOR> Zhang
 * @Description :
 * @date 2024/7/4
 */
public class OrderFormDO {
    public OrderFormDTO appendFormData(OrderBaseInfoDTO orderBaseInfoDTO, LabDTO labDTO, OrderReportDTO orderReportDTO,
                                       OrderApplicationFormDTO orderApplicationFormDTO, OrderInvoiceDTO orderInvoiceDTO,
                                       List<OrderSampleDTO> orderSampleDTOList, OrderProductDTO orderProductDTO, AttrInfo attrInfo,
                                       OrderFormDTO orderFormDTO, List<OrderBaseInfoDTO> subOrderDTOList, BankDTO bankDTO, TicPdfDTO ticPdfDTO, List<OrderLinkDTO> orderLinkDTOS, ApplicationAddressDTO applicationAddressDTO) {
        orderFormDTO.setOrderBaseInfo(orderBaseInfoDTO);
        orderFormDTO.setLab(labDTO);
        orderFormDTO.setOrderReport(orderReportDTO);
        orderFormDTO.setOrderApplicationForm(orderApplicationFormDTO);
        orderFormDTO.setOrderInvoice(orderInvoiceDTO);
        orderFormDTO.setOrderProduct(orderProductDTO);
        orderFormDTO.setOrderSampleIList(orderSampleDTOList);
        orderFormDTO.setAttrInfo(attrInfo);
        orderFormDTO.setSubOrderList(subOrderDTOList);
        orderFormDTO.setBank(bankDTO);
        orderFormDTO.setFromPdfInfo(ticPdfDTO);
        orderFormDTO.setOrderLinkList(orderLinkDTOS);
        orderFormDTO.setAddress(applicationAddressDTO);
        return orderFormDTO;
    }
}
