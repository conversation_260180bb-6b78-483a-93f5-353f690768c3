package com.sgs.ecom.member.controller.rsts;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.base.BaseBean;
import com.sgs.ecom.member.request.rsts.ItemSaveReq;
import com.sgs.ecom.member.request.rsts.QryItemReq;
import com.sgs.ecom.member.request.rsts.RstsDetailReq;
import com.sgs.ecom.member.service.util.interfaces.IRSTSService;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.rsts/item")
public class RSTSItemController extends ControllerUtil {

	@Autowired
	private IRSTSService irstsService;

	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "getRstsKey", method = {RequestMethod.POST})
	public ResultBody getRedisKey(@RequestHeader(value="accessToken") String token,
		@RequestBody ItemSaveReq itemSaveReq) throws Exception {
		return ResultBody.newInstance(irstsService.getRSTSRedisKey(itemSaveReq,getUserDTO(token)));
	}

	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "delRstsKey", method = {RequestMethod.POST})
	public ResultBody delRstsKey(@RequestHeader(value="accessToken") String token,
								  @RequestBody ItemSaveReq itemSaveReq) throws Exception {
		irstsService.delRstsKey(itemSaveReq,getUserDTO(token));
		return ResultBody.success();
	}
	/**
	 * @Description :rsts开始申请下一步-new
	 * <AUTHOR> Zhang
	 * @Date  2023/10/12
	 * @param token:
	 * @param rstsDetailReq:
	 * @return: com.sgs.ecom.member.util.ResultBody
	 **/
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "saveStepTwoNew", method = {RequestMethod.POST})
	public ResultBody saveStepTwoNew(
			@RequestHeader(value = "accessToken") String token,
			@RequestBody RstsDetailReq rstsDetailReq) throws Exception {
		irstsService.saveStepTwoNew(rstsDetailReq, getUserDTO(token));
		return ResultBody.success();
	}

	/**
	 * @Description : rsts-查询缓存-new
	 * <AUTHOR> Zhang
	 * @Date  2023/10/13
	 * @param token:
	 * @param qryItemReq:
	 * @return: com.sgs.ecom.member.util.ResultBody
	 **/
//	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "qrySampleItemDTONew", method = {RequestMethod.POST})
	public ResultBody qrySampleItemDTONew(
			@RequestHeader(value = "accessToken") String token,
			@Validated(BaseBean.Query.class)
			@RequestBody QryItemReq qryItemReq) throws Exception {
		return ResultBody.newInstance(irstsService.qrySampleItemDTONew(qryItemReq, getUserDTO(token)));
	}





}
