package com.sgs.ecom.member.domain.order;

import com.alibaba.fastjson.JSON;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseCopyObj;
import com.sgs.ecom.member.entity.order.OrderApplicationAttr;
import com.sgs.ecom.member.enumtool.aplication.OiqFormEnum;
import com.sgs.ecom.member.request.oiq.OiqApplicationReq;
import com.sgs.ecom.member.request.order.OrderApplicationAttrReq;
import com.sgs.ecom.member.util.order.UseDateUtil;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class OrderApplicationAttrDO {

    public static final String DESTINATION_LIST="destinationList";
    public static final String DESTINATION_COUNTRY="destinationCountry";
    public static final String COST_INFORMATION_LIST="costInformationList";
    public static final String GOODS="goods";
    public static final String DOCUMENTS_INFO="documentsInfo";
    public static final String BASE_FORM="baseForm";



    public List<OrderApplicationAttr> reqToAttrList(OiqApplicationReq oiqApplicationReq, String orderNo, String dateStr){
        List<OrderApplicationAttr> list=new ArrayList<>();
        OrderApplicationAttr testImg=portalToForm(OiqFormEnum.TEST_MEMO_IMG.getName(), JSON.toJSONString(oiqApplicationReq.getTestMemoImg()),orderNo,dateStr);
        String json=JSON.toJSONString(oiqApplicationReq.getApplicationAttr());

        Map<String, Object> mapKey = JSON.parseObject(json, Map.class);
        for(String key:mapKey.keySet()){
            if(!ValidationUtil.isEmpty(mapKey.get(key))){
                OrderApplicationAttr attr=repToFormAttr(key,mapKey.get(key).toString(),orderNo,dateStr);
                list.add(attr);
            }
        }
        list.add(testImg);
        return list;
    }







    public OrderApplicationAttr portalToForm(String key,String value,String orderNo,String dateStr){
        OrderApplicationAttr orderApplicationAttr = new OrderApplicationAttr();
        orderApplicationAttr.setOrderNo(orderNo);
        orderApplicationAttr.setState(1);
        orderApplicationAttr.setCreateDate(dateStr);
        orderApplicationAttr.setAttrCode(key);
        orderApplicationAttr.setAttrCode(key);
        orderApplicationAttr.setAttrValue(value);
        orderApplicationAttr.setAttrName(OiqFormEnum.getNameCh(key));
        orderApplicationAttr.setAreaCode(OiqFormEnum.getFormCode(key));
        return orderApplicationAttr;
    }

    public OrderApplicationAttr repToFormAttr(String key,String value,String orderNo,String dateStr){
        if(value==null){
            value="";
        }
        OrderApplicationAttr orderApplicationAttr = new OrderApplicationAttr();
        orderApplicationAttr.setOrderNo(orderNo);
        orderApplicationAttr.setState(1);
        orderApplicationAttr.setCreateDate(dateStr);
        orderApplicationAttr.setAttrCode(key);
        orderApplicationAttr.setAttrCode(key);
        orderApplicationAttr.setAttrValue(value);
        orderApplicationAttr.setAttrName(OiqFormEnum.getNameCh(key));
        orderApplicationAttr.setAreaCode(OiqFormEnum.getFormCode(key));
        return orderApplicationAttr;
    }

    public  List<OrderApplicationAttr> getEntityListByReqList(List<OrderApplicationAttrReq> reqList, String orderNo){
        if(ValidationUtil.isEmpty(reqList)){
            return new ArrayList<>();
        }
        BaseCopyObj baseCopyObj=new BaseCopyObj();
        String dateStr= UseDateUtil.getDateString(new Date());
        List<OrderApplicationAttr> list=new ArrayList<>();
        for(int n=0;n<reqList.size();n++){
            OrderApplicationAttr orderApplicationAttr=new OrderApplicationAttr();
            OrderApplicationAttrReq orderApplicationAttrReq=reqList.get(n);
            baseCopyObj.copyWithNull(orderApplicationAttr,orderApplicationAttrReq);
            if(orderApplicationAttrReq.getAttrText()!=null){
                orderApplicationAttr.setAttrText(JSON.toJSONString(orderApplicationAttrReq.getAttrText()));
            }
            if(StringUtils.isBlank(orderApplicationAttrReq.getAttrValue())){
                orderApplicationAttr.setAttrValue("");
            }
            orderApplicationAttr.setCreateDate(dateStr);
            orderApplicationAttr.setOrderNo(orderNo);
            orderApplicationAttr.setState(1);
            list.add(orderApplicationAttr);
        }
        return list;
    }

    public static List<String> addTfsFormList(){
        List<String> list=new ArrayList<>();
        list.add(DESTINATION_LIST);
        list.add(COST_INFORMATION_LIST);
        list.add(GOODS);
        list.add(DOCUMENTS_INFO);
        return list;
    }










}
