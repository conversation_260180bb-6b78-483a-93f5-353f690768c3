package com.sgs.ecom.member.enumtool.aplication;

public enum  ExpressPayEnum {
    //1寄付现结，2寄付月结
    DAY("day", "1", "寄付现结"),
    MONTH("month", "2", "'寄付月结"),
    ;

    ExpressPayEnum(String name, String index, String nameCh) {
        this.name = name;
        this.index = index;
        this.nameCh = nameCh;
    }

    public static String getName(String index) {
        for (ExpressPayEnum c : ExpressPayEnum.values()) {
            if (c.getIndex().equals(index)) {
                return c.getName();
            }
        }
        return null;
    }

    public static ExpressPayEnum getEnum(String index) {
        for (ExpressPayEnum c : ExpressPayEnum.values()) {
            if (c.getIndex().equals(index)) {
                return c;
            }
        }
        return null;
    }

    public static String getNameCh(String index) {
        for (ExpressPayEnum c : ExpressPayEnum.values()) {
            if (c.getIndex().equals(index)) {
                return c.getNameCh();
            }
        }
        //当不存在枚举的时候返回code 相当于其他
        return null;
    }





    private String name;
    private String index;
    private String nameCh;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }
}
