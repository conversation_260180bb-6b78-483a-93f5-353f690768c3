package com.sgs.ecom.member.domain.order;



import com.sgs.ecom.member.entity.order.OrderLabel;
import com.sgs.ecom.member.enumtool.order.OrderLabelCodeEnum;
import com.sgs.ecom.member.util.order.UseDateUtil;

import java.util.Date;

public class OrderLabelDO {

    public OrderLabel getOrderLabel(String orderNo, String value,OrderLabelCodeEnum orderLabelCodeEnum){
        String dateStr= UseDateUtil.getDateString(new Date());
        OrderLabel orderLabel=new OrderLabel();
        orderLabel.setCreateDate(dateStr);
        orderLabel.setState(1);
        orderLabel.setOrderNo(orderNo);
        orderLabel.setLabelCode(orderLabelCodeEnum.getName());
        orderLabel.setLabelGroup("ORDER");
        orderLabel.setLabelValue(value);
        return orderLabel;
    }
}
