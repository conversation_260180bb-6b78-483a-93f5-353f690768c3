<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.user.dao.UserPromotionAttrMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.entity.UserPromotionAttr" >
    <id column="ATTR_ID" property="attrId" jdbcType="BIGINT" />
    <result column="ACTIVITY_CODE" property="activityCode" jdbcType="VARCHAR" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="ATTR_NAME" property="attrName" jdbcType="VARCHAR" />
    <result column="ATTR_KEY" property="attrKey" jdbcType="VARCHAR" />
    <result column="ATTR_VALUE" property="attrValue" jdbcType="VARCHAR" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    ATTR_ID, ACTIVITY_CODE, USER_ID, ATTR_NAME, ATTR_KEY, ATTR_VALUE, CREATE_DATE
  </sql>
  <insert id="batchInsert" parameterType="java.util.List">
    insert into USER_PROMOTION_ATTR (ACTIVITY_CODE, USER_ID, 
      ATTR_NAME, ATTR_KEY, ATTR_VALUE,STATE)
    values 
      <foreach collection="list" index="index" item="item" separator=",">
      	(#{item.activityCode}, #{item.userId},
      	#{item.attrName}, #{item.attrKey}, #{item.attrValue},1)
      </foreach>
  </insert>

  <update id="delByMap">
    UPDATE USER_PROMOTION_ATTR set STATE=0
    WHERE USER_ID=#{userId} AND ACTIVITY_CODE=#{activityCode}
  </update>


  <resultMap id="resultDTO" type="com.sgs.ecom.member.dto.user.UserPromotionAttrDTO" >
    <result column="ACTIVITY_CODE" property="activityCode" jdbcType="VARCHAR" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="ATTR_NAME" property="attrName" jdbcType="VARCHAR" />
    <result column="ATTR_KEY" property="attrKey" jdbcType="VARCHAR" />
    <result column="ATTR_VALUE" property="attrValue" jdbcType="VARCHAR" />
  </resultMap>


  <select id="selectListByMap" resultMap="resultDTO" >
    select ACTIVITY_CODE,ATTR_NAME,ATTR_KEY,ATTR_VALUE,USER_ID from USER_PROMOTION_ATTR
    where 1=1 AND STATE=1
    <if test="activityList != null ">
      AND (USER_ID,ACTIVITY_CODE) in
      <foreach collection="activityList" index="index" item="item" open="(" separator="," close=")">
        (#{item.userId},#{item.activityCode})
      </foreach>
    </if>
    group by USER_ID,ATTR_KEY
  </select>
</mapper>