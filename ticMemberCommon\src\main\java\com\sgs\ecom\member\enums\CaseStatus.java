package com.sgs.ecom.member.enums;

public enum CaseStatus {

	NEW("受理中", "New"), CLOSE("已完成", "Closed"), CANCEL("已取消", "Cancelled"), 
	PENDING("暂缓处理", "Pending"), TESTING("测试中", "Testing"), REPORTING("测试中", "Reporting");
		
    // 成员变量  
    private String name;  
    private String index;  
    // 构造方法  
    private CaseStatus(String name, String index) {  
        this.name = name;  
        this.index = index;  
    }  

    // 普通方法  
    public static String getName(String index) {  
        for (CaseStatus c : CaseStatus.values()) {  
            if (c.getIndex().equals(index)) {  
                return c.name;  
            }  
        }  
        return null;  
    }  
    // get set 方法  
    public String getName() {  
        return name;  
    }  

    public String getIndex() {  
        return index;  
    }  
}
