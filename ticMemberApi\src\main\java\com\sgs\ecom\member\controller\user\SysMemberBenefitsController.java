package com.sgs.ecom.member.controller.user;

import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.sgs.ecom.member.service.user.interfaces.ISysMemberBenefitsService;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/business/api.v2.user/benefits")
public class SysMemberBenefitsController extends ControllerUtil {

    @Autowired
    private ISysMemberBenefitsService sysMemberBenefitsService;



    /**
     * @Description: 根据用户信息查询用户所匹配的工具包
     * @Author: bowen zhang
     * @Date: 2022/4/27
     * @param token:
     * @return: com.sgs.ecom.member.util.ResultBody
     **/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "qryUserTool", method = { RequestMethod.POST, RequestMethod.GET })
    public ResultBody qryUserTool(@RequestHeader(value="accessToken") String token) throws Exception {
        return ResultBody.newInstance(sysMemberBenefitsService.qryUserTool(getUserDTO(token)));
    }
}
