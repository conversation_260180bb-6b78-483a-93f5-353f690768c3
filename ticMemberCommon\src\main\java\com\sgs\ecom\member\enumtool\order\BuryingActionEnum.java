package com.sgs.ecom.member.enumtool.order;

public enum BuryingActionEnum {
    //详情查看
    DETAIL_BROWSE("detail", "browse"),

            ;



    BuryingActionEnum(String buryingType, String buryingAction) {
        this.buryingType = buryingType;
        this.buryingAction = buryingAction;
    }

    private String buryingType;
    private String buryingAction;


    public String getBuryingType() {
        return buryingType;
    }

    public void setBuryingType(String buryingType) {
        this.buryingType = buryingType;
    }

    public String getBuryingAction() {
        return buryingAction;
    }

    public void setBuryingAction(String buryingAction) {
        this.buryingAction = buryingAction;
    }
}
