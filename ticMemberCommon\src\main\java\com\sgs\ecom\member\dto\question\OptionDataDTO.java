package com.sgs.ecom.member.dto.question;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.dto.question.order.OrderQuestionDTO;
import com.sgs.ecom.member.request.rpc.CenterItemReq;

import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class OptionDataDTO extends BaseQryFilter {

    /**
     * Step0回答
     */
    @ApiAnno(groups={Default.class})
    private List<UserAnswerDTO> initialOptionList;
    @ApiAnno(groups={Default.class})
    private List<UserAnswerDTO> firstOptionList;
    @ApiAnno(groups={Default.class})
    private List<UserAnswerDTO> lastOptionList;
    @ApiAnno(groups={Default.class})
    private List<UserAnswerDTO> secondOptionList;
    @ApiAnno(groups={Default.class})
    private  List<UserAnswerAllDTO> userAnswerAllDTOList;
    @ApiAnno(groups={Default.class})
    private CenterItemReq centerItemReq;
    @ApiAnno(groups={Default.class})
    private OrderQuestionDTO orderQuestionDTO;
    @ApiAnno(groups={Default.class})
    private String orderNo;
}
