package com.sgs.ecom.member.enumtool.aplication;

public enum ReportFormEnum {
    PAPER("paper", "1", "纸质报告"),
    ELECTRON("electron", "2", "电子报告"),
    ELECTRON_PAPER("electronPaper", "3", "电子报告+纸质报告");




    ReportFormEnum(String name, String index, String nameCh) {
        this.name = name;
        this.index = index;
        this.nameCh = nameCh;
    }


    private String name;
    private String index;
    private String nameCh;

    public static String getIndexByNameCh(String nameCh) {
        for (ReportFormEnum c : ReportFormEnum.values()) {
            if (c.getNameCh().equals(nameCh)) {
                return c.getIndex();
            }
        }
        return "";
    }





    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }
}
