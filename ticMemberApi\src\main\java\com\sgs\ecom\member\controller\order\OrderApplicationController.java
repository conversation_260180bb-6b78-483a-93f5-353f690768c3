package com.sgs.ecom.member.controller.order;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.ecom.member.dto.OrderBaseInfoCheckDTO;
import com.sgs.ecom.member.dto.user.UserDTO;
import com.sgs.ecom.member.enumtool.OiqMailEnum;
import com.sgs.ecom.member.enumtool.OiqSmsEnum;
import com.sgs.ecom.member.enumtool.OrderTypeEnum;
import com.sgs.ecom.member.enumtool.event.EventEnum;
import com.sgs.ecom.member.enumtool.send.WechatEnum;
import com.sgs.ecom.member.event.EventApiUtil;
import com.sgs.ecom.member.infrastructure.event.MailEventUtil;
import com.sgs.ecom.member.infrastructure.event.SmsEventUtil;
import com.sgs.ecom.member.request.OrderNoReq;
import com.sgs.ecom.member.request.detail.ApplicationReq;
import com.sgs.ecom.member.request.detail.ExpressReq;
import com.sgs.ecom.member.request.tic.TicApplicationReq;
import com.sgs.ecom.member.service.order.interfaces.IOrderBaseInfoService;
import com.sgs.ecom.member.service.util.interfaces.IOrderApplicationService;
import com.sgs.ecom.member.service.util.interfaces.IPDFService;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.order/application")
public class OrderApplicationController extends ControllerUtil {

    @Autowired
    private IOrderApplicationService orderApplicationService;
    @Autowired
    private IOrderBaseInfoService orderBaseInfoService;
    @Autowired
    private IPDFService pdfService;
    @Autowired
    private EventApiUtil eventApiUtil;
    @Autowired
    private MailEventUtil mailEventUtil;
    @Autowired
    private SmsEventUtil smsEventUtil;

    /**
    *@Function: express
    *@Description 寄送信息
    *@param: [token, orderNoReq]
    *@author: Xiwei_Qiu
    *@date: 2020/12/15
    **/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "express", method = { RequestMethod.POST })
    public ResultBody express (
            @RequestHeader(value="accessToken") String token,
            @RequestBody OrderNoReq orderNoReq)throws Exception{
        return ResultBody.newInstance(orderApplicationService.selectExpressMore(orderNoReq,getUserDTO(token),token));
    }

    /**
     * @Description:寄送样品信息- 查询单订单下多物流信息
     * @Author: bowen zhang
     * @Date: 2022/8/24
     * @param token:
     * @param orderNoReq:
     * @return: com.sgs.ecom.member.util.ResultBody
     **/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "qryExpressList", method = { RequestMethod.POST })
    public ResultBody qryExpressList (
            @RequestHeader(value="accessToken") String token,
            @RequestBody OrderNoReq orderNoReq
    )throws Exception{
        return ResultBody.newInstance(orderApplicationService.qryExpressList(orderNoReq.getOrderNo(),getUserDTO(token),token));
    }


    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "backForm", method = { RequestMethod.POST })
    public ResultBody backForm (
            @RequestHeader(value="accessToken") String token,
            @RequestBody OrderNoReq orderNoReq)throws Exception{
        orderApplicationService.backForm(orderNoReq,getUserDTO(token));
        return ResultBody.success();
    }

    /**
     * @Description: 批量保存样品信息
     * @Author: bowen zhang
     * @Date: 2022/8/24
     * @param token:
     * @param expressReq:
     * @return: com.sgs.ecom.member.util.ResultBody
     **/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "saveBatchExpress", method = { RequestMethod.POST })
    public ResultBody saveBatchExpress (
            @RequestHeader(value="accessToken") String token,
            @RequestBody ExpressReq expressReq)throws Exception{
        orderApplicationService.saveBatchExpress(expressReq,getUserDTO(token),token);
        return ResultBody.success();
    }

    /**
    *@Function: save
    *@Description 保存申请表
    *@param: [token, applicationReq]
    *@version:
    *@author: Xiwei_Qiu
    *@date: 2021/3/9
    **/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "save", method = { RequestMethod.POST })
    public ResultBody save (
            @RequestHeader(value = "system",required = false) String system,
            @RequestHeader(value="accessToken") String token,
            @RequestBody ApplicationReq applicationReq)throws Exception{
        applicationReq.setSystem(system);
        orderApplicationService.save(applicationReq,getUserDTO(token));
        eventApiUtil.saveEvent(applicationReq.getOrderNo(), EventEnum.SAVE_INVOICE_BOSS);
        if(applicationReq.getEventFlg()==1){
            mailEventUtil.sendMail(applicationReq.getEventOrderNo(),applicationReq.getOrderType(), OiqMailEnum.USER_SAVE_FORM,0L);
            smsEventUtil.sendSms(applicationReq.getEventOrderNo(),applicationReq.getOrderType(), OiqSmsEnum.USER_SAVE_FORM);
            eventApiUtil.sendWechatMsg(applicationReq.getEventOrderNo(),EventEnum.SEND_WECHAT_MSG, WechatEnum.USER_SAVE_FORM);
        }


        return ResultBody.success();
    }

    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "checkSaveForm", method = { RequestMethod.POST })
    public ResultBody checkSaveForm (
            @RequestHeader(value="accessToken") String token,
            @RequestBody OrderNoReq orderNoReq)throws Exception{
        return ResultBody.newInstance(orderApplicationService.checkSaveForm(orderNoReq,getUserDTO(token)));
    }


    /**
    *@Function: form
    *@Description 查看申请表详情
    *@param: [token, orderNoReq]
    *
    *@version:
    *@author: Xiwei_Qiu
    *@date: 2021/3/9
    **/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "form", method = { RequestMethod.POST })
    public ResultBody form (
            @RequestHeader(value="accessToken") String token,
            @RequestBody OrderNoReq orderNoReq
    )throws Exception{
        UserDTO userDTO =getUserDTO(token);
        OrderBaseInfoCheckDTO orderBaseInfoCheckDTO=orderBaseInfoService.checkOrderBase(orderNoReq.getOrderNo(),userDTO, OrderTypeEnum.OIQORDER.getIndex());
        return ResultBody.success(orderApplicationService.selectForm(orderBaseInfoCheckDTO,false));
    }



    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "formPDF", method = { RequestMethod.POST })
    public void formPDF (
            @RequestHeader(value="accessToken") String token,
            @RequestBody OrderNoReq orderNoReq,
              HttpServletResponse httpServletResponse
    )throws Exception{
        pdfService.qryFormPDF(orderNoReq,getUserDTO(token),httpServletResponse);
    }


    @RequestMapping(value = "formPDFTest", method = { RequestMethod.GET })
    public void qryFormPDFTest(
            @RequestParam(value="orderNo") String orderNo,
            HttpServletResponse httpServletResponse) throws Exception {
        OrderNoReq orderNoReq=new OrderNoReq();
        orderNoReq.setOrderNo(orderNo);
        pdfService.qryFormPDF(orderNoReq,new UserDTO(12459L),httpServletResponse);

    }

    @RequestMapping(value = "memoPDFTest", method = { RequestMethod.GET })
    public void memoPDFTest(
            @RequestParam(value="orderNo") String orderNo,
            HttpServletResponse httpServletResponse) throws Exception {
        pdfService.qryMemoPDF(orderNo,httpServletResponse);
    }

    /**
     * @Function: saveShopApplicationCache
     * @Description:  保存申请表提交缓存
     *
     * @param: orderNo
     * @return: ResultBody
     *
     * @version: 1.0
     * @author: shenyi
     * @date: 2024-07-05
     *
     * Modification History:
     * Date         Author          Version            Description
     *---------------------------------------------------------*
     * 修改时间      修改人            版本                 修改原因
     * 2024-07-05  shenyi           v1.0                 新增
     */
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "saveCache/{orderNo}", method = { RequestMethod.POST })
    public ResultBody saveShopApplicationCache(@PathVariable String orderNo,
                                               @RequestBody JSONObject jsonData,
                                               @RequestHeader(value="accessToken") String token) throws Exception {
        orderApplicationService.saveShopApplicationCache(orderNo, jsonData, getUserDTO(token));
        return ResultBody.success();
    }

    /**
     * @Function: getShopApplicationCache
     * @Description:  获取申请表提交缓存
     *
     * @param: orderNo
     * @return: ResultBody
     *
     * @version: 1.0
     * @author: shenyi
     * @date: 2024-07-05
     *
     * Modification History:
     * Date         Author          Version            Description
     *---------------------------------------------------------*
     * 修改时间      修改人            版本                 修改原因
     * 2024-07-05  shenyi           v1.0                 新增
     */
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "getCache/{orderNo}", method = { RequestMethod.POST })
    public ResultBody getShopApplicationCache(@PathVariable String orderNo,
                                               @RequestHeader(value="accessToken") String token) throws Exception {
        return ResultBody.newInstance( orderApplicationService.getShopApplicationCache(orderNo, getUserDTO(token)));
    }
}
