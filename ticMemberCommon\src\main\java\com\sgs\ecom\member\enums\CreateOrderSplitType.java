package com.sgs.ecom.member.enums;
/**
 * @Description :订单创建时对赢得三种拆合单配置
 * <AUTHOR>
 * @Date  2024/6/19

 **/
public enum CreateOrderSplitType {

    bySalesAttr("bySalesAttr", 1),
    by<PERSON>ums("byNums", 2),
    marge<PERSON>rder("margeOrder", 3),
    bySalesAttrAndItem("bySalesAttrAndItem", 4);

    // 成员变量
    private String name;
    private int index;
    // 构造方法
    private CreateOrderSplitType(String name, int index) {
        this.name = name;  
        this.index = index;  
    }  
   
    // 普通方法  
    public static String getName(int index) {  
        for (CreateOrderSplitType c : CreateOrderSplitType.values()) {
            if (c.getIndex() == index) {  
                return c.name;  
            }  
        }  
        return null;  
    }  
    // get set 方法  
    public String getName() {  
        return name;  
    }  

    public int getIndex() {  
        return index;  
    }  
}
