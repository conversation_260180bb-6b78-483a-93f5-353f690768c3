package com.sgs.ecom.member.dto.order;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.dto.OrderDetailDTO;
import com.sgs.ecom.member.dto.detail.OrderAttachmentDTO;
import com.sgs.ecom.member.dto.user.UserDTO;

import java.util.ArrayList;
import java.util.List;

public class OrderMoreDTO {
    @ApiAnno(groups={BaseOrderFilter.OiqOrderDetail.class})
    private String orderNo;
    @ApiAnno(groups={BaseOrderFilter.OiqOrderDetail.class})
    private String bu;
    @ApiAnno(groups={BaseOrderFilter.OiqOrderDetail.class})
    private OrderBaseDTO orderBase=new OrderBaseDTO();
    @ApiAnno(groups={BaseOrderFilter.OiqOrderDetail.class})
    private List<OrderAttachmentDTO> reportFileList=new ArrayList<>();
    @ApiAnno(groups={ BaseOrderFilter.OiqOrderDetail.class})
    private List<OrderDetailDTO> orderDetailDTOList=new ArrayList<>();
    private UserDTO userInfo;


    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public OrderBaseDTO getOrderBase() {
        return orderBase;
    }

    public void setOrderBase(OrderBaseDTO orderBase) {
        this.orderBase = orderBase;
    }

    public List<OrderAttachmentDTO> getReportFileList() {
        return reportFileList;
    }

    public void setReportFileList(List<OrderAttachmentDTO> reportFileList) {
        this.reportFileList = reportFileList;
    }

    public List<OrderDetailDTO> getOrderDetailDTOList() {
        return orderDetailDTOList;
    }

    public void setOrderDetailDTOList(List<OrderDetailDTO> orderDetailDTOList) {
        this.orderDetailDTOList = orderDetailDTOList;
    }

    public UserDTO getUserInfo() {
        return userInfo;
    }

    public void setUserInfo(UserDTO userInfo) {
        this.userInfo = userInfo;
    }

    public String getBu() {
        return bu;
    }

    public void setBu(String bu) {
        this.bu = bu;
    }
}

