package com.sgs.ecom.member.enumtool.pay;

import com.sgs.ecom.member.util.check.CheckUtil;

public enum MonthPayEnum {
	NOW_PAY("nowPay", 0, "现付"),
	MONTH_PAY("monthPay", 1, "月结已付款"),
	OIQ_MONTH_PAY("monthPay", 2, "oiq月结付款"),//oiq假崛起
	MONTH_NOT_PAY("monthNotPay", 3, "月结未付款"),//

		;
	MonthPayEnum(String name, int index, String nameCh) {
		this.name = name;
		this.index = index;
		this.nameCh = nameCh;
	}

	public static String getName(int index) {
		for (MonthPayEnum c : MonthPayEnum.values()) {
			if (c.getIndex()==index) {
				return c.getName();
			}
		}
		return null;
	}

	public static MonthPayEnum getEnum(int index) {
		for (MonthPayEnum c : MonthPayEnum.values()) {
			if (c.getIndex()==index) {
				return c;
			}
		}
		return null;
	}

	public static String getNameCh(int index) {
		for (MonthPayEnum c : MonthPayEnum.values()) {
			if (c.getIndex()==index) {
				return c.getNameCh();
			}
		}
		return "";
	}

	public static String getNameChByIndex(String monthPay) {
		if(String.valueOf(MonthPayEnum.NOW_PAY.getIndex()).equals(monthPay)){
			return "现付";
		}else {
			return "月结";
		}
	}


	public static int getIndexBySettleType(Integer SettleType) {
		if(SettleType==null){
			return 0;
		}
		if(CheckUtil.isSame(SettleType,"300002")){
			return 0;
		}
		if(CheckUtil.isSame(SettleType,"300001")){
			return 1;
		}
		return 0;
	}



	private String name;
	private int index;
	private String nameCh;


	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public int getIndex() {
		return index;
	}

	public void setIndex(int index) {
		this.index = index;
	}

	public String getNameCh() {
		return nameCh;
	}

	public void setNameCh(String nameCh) {
		this.nameCh = nameCh;
	}
}
