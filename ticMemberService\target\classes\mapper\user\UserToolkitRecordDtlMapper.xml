<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.user.dao.UserToolkitRecordDtlMapper">
    <resultMap id="BaseResultMap" type="com.sgs.ecom.member.entity.user.UserToolkitRecordDtl">
        <id column="DTL_ID" property="dtlId" jdbcType="BIGINT"/>
        <result column="RECORD_ID" property="recordId" jdbcType="BIGINT"/>
        <result column="TOOL_CODE" property="toolCode" jdbcType="VARCHAR"/>
        <result column="ATTR_NAME" property="attrName" jdbcType="VARCHAR"/>
        <result column="ATTR_CODE" property="attrCode" jdbcType="VARCHAR"/>
        <result column="ATTR_VALUE" property="attrValue" jdbcType="VARCHAR"/>
        <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP"/>
        <result column="ATTR_RATE" property="attrRate" jdbcType="DECIMAL"/>
    </resultMap>
    <resultMap id="ExportResultMap" type="com.sgs.ecom.member.dto.user.UserToolkitExpReportDTO">
        <result column="RECORD_ID" property="recordId" jdbcType="BIGINT"/>
        <result column="TOOL_CODE" property="toolCode" jdbcType="VARCHAR"/>
        <result column="STATE" property="state" jdbcType="INTEGER" />
        <result column="CREATE_DATE" property="createDate" jdbcType="VARCHAR" />
        <result column="USER_ID" property="userId" jdbcType="VARCHAR" />
        <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
        <result column="qrCode" property="qrCode" jdbcType="VARCHAR" />
        <result column="qryType" property="qryType" jdbcType="VARCHAR" />
        <result column="reportType" property="reportType" jdbcType="VARCHAR" />
        <result column="isSuccess" property="isSuccess" jdbcType="VARCHAR" />
        <result column="reportNo" property="reportNo" jdbcType="VARCHAR" />
        <result column="failType" property="failType" jdbcType="VARCHAR" />
        <result column="url" property="url" jdbcType="VARCHAR" />
        <result column="userName" property="userName" jdbcType="VARCHAR" />
        <result column="userPhone" property="userPhone" jdbcType="VARCHAR" />
        <result column="userEmail" property="userEmail" jdbcType="VARCHAR" />
        <result column="companyName" property="companyName" jdbcType="VARCHAR" />
        <result column="fileName" property="fileName" jdbcType="VARCHAR" />
        <result column="frontUrl" property="frontUrl" jdbcType="VARCHAR" />
        <result column="isArtificial" property="isArtificial" jdbcType="VARCHAR" />
        <result column="qrCodePass" property="qrCodePass" jdbcType="VARCHAR" />
        <result column="dssSignatureVerificationTime" property="dssSignatureVerificationTime" jdbcType="VARCHAR" />
        <result column="qrCodeVerificationTime" property="qrCodeVerificationTime" jdbcType="VARCHAR" />
        <result column="getQrCodeTime" property="getQrCodeTime" jdbcType="VARCHAR" />
        <result column="dssFlag" property="dssFlag" jdbcType="VARCHAR" />
        <result column="javaDssFlag" property="javaDssFlag" jdbcType="VARCHAR" />
        <result column="netDssFlag" property="netDssFlag" jdbcType="VARCHAR" />
        <result column="reportVerificationFlag" property="reportVerificationFlag" jdbcType="VARCHAR" />
        <result column="qrCodeVerificationFlag" property="qrCodeVerificationFlag" jdbcType="VARCHAR" />
        <result column="transTimes" property="transTimes" jdbcType="VARCHAR" />
        <result column="firstQrCode" property="firstQrCode" jdbcType="VARCHAR" />
        <result column="scanTimes" property="scanTimes" jdbcType="VARCHAR" />

    </resultMap>
    <sql id="Base_Column_List">
        DTL_ID
        , RECORD_ID, TOOL_CODE, ATTR_NAME, ATTR_CODE, ATTR_VALUE, CREATE_DATE, ATTR_RATE
    </sql>
    <sql id="baseQueryWhere">
        <if test="dtlId != null">
            and DTL_ID = #{dtlId}
        </if>
        <if test="recordId != null">
            and RECORD_ID = #{recordId}
        </if>
        <if test="attrName != null">
            and ATTR_NAME = #{attrName}
        </if>
        <if test="attrCode != null">
            and ATTR_CODE = #{attrCode}
        </if>
        <if test="attrValue != null">
            and ATTR_VALUE = #{attrValue}
        </if>

    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from USER_TOOLKIT_RECORD_DTL
        where DTL_ID = #{dtlId,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from USER_TOOLKIT_RECORD_DTL
        where DTL_ID = #{dtlId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.sgs.ecom.member.entity.user.UserToolkitRecordDtl" keyProperty="id"
            useGeneratedKeys="true">
        insert into USER_TOOLKIT_RECORD_DTL (DTL_ID, RECORD_ID, TOOL_CODE,
                                             ATTR_NAME, ATTR_CODE, ATTR_VALUE,
                                             CREATE_DATE, ATTR_RATE)
        values (#{dtlId,jdbcType=BIGINT}, #{recordId,jdbcType=BIGINT}, #{toolCode,jdbcType=VARCHAR},
                #{attrName,jdbcType=VARCHAR}, #{attrCode,jdbcType=VARCHAR}, #{attrValue,jdbcType=VARCHAR},
                #{createDate,jdbcType=TIMESTAMP}, #{attrRate,jdbcType=DECIMAL})
    </insert>
    <insert id="insertSelective" parameterType="com.sgs.ecom.member.entity.user.UserToolkitRecordDtl">
        <selectKey resultType="java.lang.Long" keyProperty="dtlId" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into USER_TOOLKIT_RECORD_DTL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dtlId != null">
                DTL_ID,
            </if>
            <if test="recordId != null">
                RECORD_ID,
            </if>
            <if test="toolCode != null">
                TOOL_CODE,
            </if>
            <if test="attrName != null">
                ATTR_NAME,
            </if>
            <if test="attrCode != null">
                ATTR_CODE,
            </if>
            <if test="attrValue != null">
                ATTR_VALUE,
            </if>
            <if test="createDate != null">
                CREATE_DATE,
            </if>
            <if test="attrRate != null">
                ATTR_RATE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dtlId != null">
                #{dtlId,jdbcType=BIGINT},
            </if>
            <if test="recordId != null">
                #{recordId,jdbcType=BIGINT},
            </if>
            <if test="toolCode != null">
                #{toolCode,jdbcType=VARCHAR},
            </if>
            <if test="attrName != null">
                #{attrName,jdbcType=VARCHAR},
            </if>
            <if test="attrCode != null">
                #{attrCode,jdbcType=VARCHAR},
            </if>
            <if test="attrValue != null">
                #{attrValue,jdbcType=VARCHAR},
            </if>
            <if test="createDate != null">
                #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="attrRate != null">
                #{attrRate,jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>
    <insert id="insertForeach">

        insert into USER_TOOLKIT_RECORD_DTL ( RECORD_ID, TOOL_CODE,
        ATTR_NAME, ATTR_CODE, ATTR_VALUE,
        CREATE_DATE, ATTR_RATE) values
        <foreach collection="list" item="item" index="index" separator=",">

            ( #{item.recordId}, #{item.toolCode},
            #{item.attrName}, #{item.attrCode}, #{item.attrValue},
            #{item.createDate}, #{item.attrRate})

        </foreach>

    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sgs.ecom.member.entity.user.UserToolkitRecordDtl">
        update USER_TOOLKIT_RECORD_DTL
        <set>
            <if test="recordId != null">
                RECORD_ID = #{recordId,jdbcType=BIGINT},
            </if>
            <if test="toolCode != null">
                TOOL_CODE = #{toolCode,jdbcType=VARCHAR},
            </if>
            <if test="attrName != null">
                ATTR_NAME = #{attrName,jdbcType=VARCHAR},
            </if>
            <if test="attrCode != null">
                ATTR_CODE = #{attrCode,jdbcType=VARCHAR},
            </if>
            <if test="attrValue != null">
                ATTR_VALUE = #{attrValue,jdbcType=VARCHAR},
            </if>
            <if test="createDate != null">
                CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="attrRate != null">
                ATTR_RATE = #{attrRate,jdbcType=DECIMAL},
            </if>
        </set>
        where DTL_ID = #{dtlId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sgs.ecom.member.entity.user.UserToolkitRecordDtl">
        update USER_TOOLKIT_RECORD_DTL
        set RECORD_ID   = #{recordId,jdbcType=BIGINT},
            TOOL_CODE   = #{toolCode,jdbcType=VARCHAR},
            ATTR_NAME   = #{attrName,jdbcType=VARCHAR},
            ATTR_CODE   = #{attrCode,jdbcType=VARCHAR},
            ATTR_VALUE  = #{attrValue,jdbcType=VARCHAR},
            CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
            ATTR_RATE   = #{attrRate,jdbcType=DECIMAL}
        where DTL_ID = #{dtlId,jdbcType=BIGINT}
    </update>

    <select id="selectListByMap" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"></include>
        from
        USER_TOOLKIT_RECORD_DTL
        <where>
            <include refid="baseQueryWhere"></include>
        </where>
    </select>
    <select id="qryExportReportListByMap" resultMap="ExportResultMap">

        SELECT utr.*,
               utrd.qrCode,
               utrd.qryType,
               utrd.reportType,
               utrd.reportType,
               utrd.isSuccess,
               utrd.reportNo,
               utrd.failType,
               utrd.url,
                utr.USER_ID,
               utrd.userName,
               utrd.userPhone,
               utrd.userEmail,
               utrd.companyName,
               utrd.fileName,
               utrd.frontUrl,
               utrd.isArtificial,
               utrd.qrCodePass,
               utrd.dssSignatureVerificationTime,
               utrd.qrCodeVerificationTime,
               utrd.getQrCodeTime,
               utrd.dssFlag,
               utrd.javaDssFlag,
               utrd.netDssFlag,
               utrd.reportVerificationFlag,
               utrd.qrCodeVerificationFlag,
               utrd.transTimes,
               utrd.firstQrCode,
               utrd.isBlacklist,
               utrd.scanTimes
        from user_toolkit_record utr,(SELECT
                                          RECORD_ID,
                                          max(  ( CASE ATTR_CODE WHEN 'qrCode' THEN (CASE WHEN ATTR_VALUE IS NULL THEN ' N/A' ELSE ATTR_VALUE END ) ELSE ' N/A' END  )) qrCode,
                                          max( ( CASE ATTR_CODE WHEN 'qryType' THEN (CASE WHEN ATTR_VALUE IS NULL THEN ' N/A' WHEN ATTR_VALUE =0 THEN '数字验签'  WHEN ATTR_VALUE =1 THEN '二维码返回' WHEN ATTR_VALUE =2 THEN '人工' ELSE ' N/A' END) ELSE ' N/A' END ) ) qryType,
                                          max( ( CASE ATTR_CODE WHEN 'reportType' THEN (CASE WHEN ATTR_VALUE IS NULL THEN ' N/A' WHEN ATTR_VALUE =0 THEN 'pdf'  WHEN ATTR_VALUE =1 THEN '二维码' WHEN ATTR_VALUE =2 THEN '压缩包' ELSE ' N/A' END) ELSE ' N/A' END ) ) reportType,
                                          max( ( CASE ATTR_CODE WHEN 'isSuccess' THEN (CASE WHEN ATTR_VALUE IS NULL THEN ' N/A' WHEN ATTR_VALUE =1 THEN '否'  WHEN ATTR_VALUE =2 THEN '是'  ELSE ' N/A' END) ELSE ' N/A' END ) ) isSuccess,
                                          max( ( CASE ATTR_CODE WHEN 'reportNo' THEN (CASE WHEN ATTR_VALUE IS NULL THEN ' N/A'  ELSE ATTR_VALUE END) ELSE ' N/A' END ) ) reportNo,
                                          max( ( CASE ATTR_CODE WHEN 'failType' THEN (CASE WHEN ATTR_VALUE IS NULL THEN ' N/A' WHEN ATTR_VALUE =1 THEN '报告失效'   WHEN ATTR_VALUE =2 THEN '二维码失效' WHEN ATTR_VALUE =3 THEN '包含黑名单域名' ELSE ' N/A' END) ELSE ' N/A' END ) ) failType,
                                          max( ( CASE ATTR_CODE WHEN 'url' THEN (CASE WHEN ATTR_VALUE IS NULL THEN ' N/A'   ELSE ATTR_VALUE END) ELSE ' N/A' END ) ) url,
                                          max( ( CASE ATTR_CODE WHEN  'userName' THEN (CASE WHEN ATTR_VALUE IS NULL THEN ' N/A'   ELSE ATTR_VALUE END) ELSE ' N/A' END ) ) userName,
                                          max( ( CASE ATTR_CODE WHEN  'userPhone' THEN (CASE WHEN ATTR_VALUE IS NULL THEN ' N/A'   ELSE ATTR_VALUE END) ELSE ' N/A' END ) ) userPhone,
                                          max( ( CASE ATTR_CODE WHEN 'userEmail' THEN (CASE WHEN ATTR_VALUE IS NULL THEN ' N/A'   ELSE ATTR_VALUE END) ELSE ' N/A' END ) ) userEmail,
                                          max( ( CASE ATTR_CODE WHEN 'companyName' THEN (CASE WHEN ATTR_VALUE IS NULL THEN ' N/A'   ELSE ATTR_VALUE END) ELSE ' N/A' END ) ) companyName,
                                          max( ( CASE ATTR_CODE WHEN 'fileName' THEN (CASE WHEN ATTR_VALUE IS NULL THEN ' N/A'   ELSE ATTR_VALUE END) ELSE ' N/A' END ) ) fileName,
                                          max( ( CASE ATTR_CODE WHEN 'frontUrl' THEN (CASE WHEN ATTR_VALUE IS NULL THEN ' N/A'   ELSE ATTR_VALUE END) ELSE ' N/A' END ) ) frontUrl,
                                          max( ( CASE ATTR_CODE WHEN 'isArtificial' THEN (CASE WHEN ATTR_VALUE IS NULL THEN ' N/A' WHEN ATTR_VALUE =0 THEN '否'   WHEN ATTR_VALUE =1 THEN '是' ELSE ' N/A' END) ELSE ' N/A' END ) ) isArtificial,
                                          max( ( CASE ATTR_CODE WHEN 'qrCodePass' THEN (CASE WHEN ATTR_VALUE IS NULL THEN ' N/A' WHEN ATTR_VALUE =2 THEN '否'   WHEN ATTR_VALUE =1 THEN '是' ELSE ' N/A' END) ELSE ' N/A' END ) ) qrCodePass,
                                          max( ( CASE ATTR_CODE WHEN 'dssSignatureVerificationTime' THEN (CASE WHEN ATTR_VALUE IS NULL THEN ' N/A'  ELSE ATTR_VALUE END) ELSE ' N/A' END ) ) dssSignatureVerificationTime,
                                          max( ( CASE ATTR_CODE WHEN 'qrCodeVerificationTime' THEN (CASE WHEN ATTR_VALUE IS NULL THEN ' N/A'  ELSE ATTR_VALUE END) ELSE ' N/A' END ) ) qrCodeVerificationTime,
                                          max( ( CASE ATTR_CODE WHEN 'getQrCodeTime' THEN (CASE WHEN ATTR_VALUE IS NULL THEN ' N/A'  ELSE ATTR_VALUE END) ELSE ' N/A' END ) ) getQrCodeTime,
                                          max( ( CASE ATTR_CODE WHEN 'dssFlag' THEN (CASE WHEN ATTR_VALUE IS NULL THEN ' N/A' WHEN ATTR_VALUE =2 THEN '否'   WHEN ATTR_VALUE =1 THEN '是' ELSE ' N/A' END) ELSE ' N/A' END ) ) dssFlag,
                                          max( ( CASE ATTR_CODE WHEN 'javaDssFlag' THEN (CASE WHEN ATTR_VALUE IS NULL THEN ' N/A' WHEN ATTR_VALUE =2 THEN '否'   WHEN ATTR_VALUE =1 THEN '是' ELSE ' N/A' END) ELSE ' N/A' END ) ) javaDssFlag,
                                          max( ( CASE ATTR_CODE WHEN 'netDssFlag' THEN (CASE WHEN ATTR_VALUE IS NULL THEN ' N/A' WHEN ATTR_VALUE =2 THEN '否'   WHEN ATTR_VALUE =1 THEN '是' ELSE ' N/A' END) ELSE ' N/A' END ) ) netDssFlag,
                                          max( ( CASE ATTR_CODE WHEN 'reportVerificationFlag' THEN (CASE WHEN ATTR_VALUE IS NULL THEN ' N/A' WHEN ATTR_VALUE =2 THEN '否'   WHEN ATTR_VALUE =1 THEN '是' ELSE ' N/A' END) ELSE ' N/A' END ) ) reportVerificationFlag,
                                          max( ( CASE ATTR_CODE WHEN 'qrCodeVerificationFlag' THEN (CASE WHEN ATTR_VALUE IS NULL THEN ' N/A' WHEN ATTR_VALUE =2 THEN '否'   WHEN ATTR_VALUE =1 THEN '是' ELSE ' N/A' END) ELSE ' N/A' END ) ) qrCodeVerificationFlag,
                                          max( ( CASE ATTR_CODE WHEN 'transTimes' THEN (CASE WHEN ATTR_VALUE IS NULL THEN ' N/A'  ELSE ATTR_VALUE END) ELSE ' N/A' END ) ) transTimes,
                                          max( ( CASE ATTR_CODE WHEN 'firstQrCode' THEN (CASE WHEN ATTR_VALUE IS NULL THEN ' N/A'  ELSE ATTR_VALUE END) ELSE ' N/A' END ) ) firstQrCode,
                                          max( ( CASE ATTR_CODE WHEN 'blacklistStr' THEN (CASE WHEN ATTR_VALUE IS NULL or ATTR_VALUE = ''   THEN  '否'  ELSE '是' END) ELSE '否' END ) ) isBlacklist,
                                          max( ( CASE ATTR_CODE WHEN 'scanTimes' THEN (CASE WHEN ATTR_VALUE IS NULL THEN ' N/A'  ELSE ATTR_VALUE END) ELSE ' N/A' END ) ) scanTimes
                                      from user_toolkit_record_dtl
        where 1=1
        <if test="toolCode != null">
            and TOOL_CODE = #{toolCode}
        </if>
        GROUP BY RECORD_ID) utrd
        where
              1=1
        <if test="toolCode != null">
            and utr.TOOL_CODE = #{toolCode}
        </if>
          and utr.RECORD_ID = utrd.RECORD_ID
        <if test="startDate != null and endDate != null and startDate != '' and endDate != ''">
            AND utr.CREATE_DATE between #{startDate} and #{endDate}
        </if>

    </select>
    <select id="qryList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"></include>
        from
        USER_TOOLKIT_RECORD_DTL
        <where>
            <include refid="baseQueryWhere"></include>
        </where>
    </select>

</mapper>