package com.sgs.ecom.member.enumtool.bbc;

public enum DownReportEnum {
	//报告已下载,报告未下载
	SUCCESS("success", 0, "报告未下载"),
	FAIL("fail", 1, "报告已下载"),
	;

	DownReportEnum(String name, int index, String nameCh) {
		this.name = name;
		this.index = index;
		this.nameCh = nameCh;
	}

	public static String getName(int index) {
		for (DownReportEnum c :DownReportEnum.values()) {
			if (c.getIndex()==index) {
				return c.getName();
			}
		}
		return null;
	}

	public static DownReportEnum getEnum(int index) {
		for (DownReportEnum c : DownReportEnum.values()) {
			if (c.getIndex()==index) {
				return c;
			}
		}
		return null;
	}

	public static String getNameCh(int index) {
		for (DownReportEnum c : DownReportEnum.values()) {
			if (c.getIndex()==index) {
				return c.getNameCh();
			}
		}
		return null;
	}

	public static int getIndexByCh(String nameCh) {
		for (DownReportEnum c : DownReportEnum.values()) {
			if (c.getNameCh().equals(nameCh)) {
				return c.getIndex();
			}
		}
		return -1;
	}






	private String name;
	private int index;
	private String nameCh;


	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getNameCh() {
		return nameCh;
	}

	public void setNameCh(String nameCh) {
		this.nameCh = nameCh;
	}

	public int getIndex() {
		return index;
	}

	public void setIndex(int index) {
		this.index = index;
	}
}
