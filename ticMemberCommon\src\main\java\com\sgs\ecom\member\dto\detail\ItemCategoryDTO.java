package com.sgs.ecom.member.dto.detail;

import com.platform.annotation.ApiAnno;
import com.sgs.ecom.member.base.BaseOrderFilter;

/**
 * <AUTHOR>
 */
public class ItemCategoryDTO {
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String categoryPath;

    public String getCategoryPath() {
        return categoryPath;
    }

    public void setCategoryPath(String categoryPath) {
        this.categoryPath = categoryPath;
    }
}
