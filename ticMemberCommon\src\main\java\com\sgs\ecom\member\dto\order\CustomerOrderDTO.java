package com.sgs.ecom.member.dto.order;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.dto.detail.OrderAttachmentDTO;
import com.sgs.ecom.member.enumtool.BaseOrderStateEnum;
import com.sgs.ecom.member.enumtool.TfsStateEnum;
import com.sgs.ecom.member.util.serializer.TimeStringFormatSerializer;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class CustomerOrderDTO {
    @ApiAnno(groups={ BaseOrderFilter.OrderCustomerList.class})
    private String orderNo;
    @ApiAnno(groups={ BaseOrderFilter.OrderCustomerList.class})
    private String relateOrderNo;
    @ApiAnno(groups={ BaseOrderFilter.OrderCustomerList.class})
    private Long orderId;
    @ApiAnno(groups={ BaseOrderFilter.OrderCustomerList.class})
    @JsonSerialize(using = TimeStringFormatSerializer.class)
    private String createDate;
    @ApiAnno(groups={ BaseOrderFilter.OrderCustomerList.class})
    private Integer state;
    @ApiAnno(groups={ BaseOrderFilter.OrderCustomerList.class})
    private String stateShow;
    @ApiAnno(groups={BaseOrderFilter.OrderCustomerList.class})
    private String orderDetailStr;
    @ApiAnno(groups={BaseOrderFilter.OrderCustomerList.class})
    private int orderDetailSize;
    @ApiAnno(groups={BaseOrderFilter.OrderCustomerList.class})
    private List<OrderSampleStrDTO> orderSampleStrDTOList=new ArrayList<>();
    @ApiAnno(groups={BaseOrderFilter.OrderCustomerList.class})
    private String orderType;
    @ApiAnno(groups={BaseOrderFilter.OrderCustomerList.class})
    private List<OrderAttachmentDTO> reportFileList=new ArrayList<>() ;

    private String applicationCompanyNameCn;

    private String reportCompanyNameCn;

    private String applicationCompanyNameEn;

    private String reportCompanyNameEn;
    @ApiAnno(groups={BaseOrderFilter.OrderCustomerList.class})
    private String applicationCompanyNameStr;
    @ApiAnno(groups={BaseOrderFilter.OrderCustomerList.class})
    private String reportCompanyNameStr;

    private String groupNo;
    @ApiAnno(groups={BaseOrderFilter.OrderCustomerList.class})
    private Integer isPayReceived;
    @ApiAnno(groups={BaseOrderFilter.OrderCustomerList.class})
    private Integer payState;
    private String bu;


    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getRelateOrderNo() {
        return relateOrderNo;
    }

    public void setRelateOrderNo(String relateOrderNo) {
        this.relateOrderNo = relateOrderNo;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getStateShow() {
        if("2700".equals(bu)){
            return TfsStateEnum.getNameCh(state);
        }


        return BaseOrderStateEnum.getNameCh(state);

    }

    public void setStateShow(String stateShow) {
        this.stateShow = stateShow;
    }

    public String getOrderDetailStr() {
        return orderDetailStr;
    }

    public void setOrderDetailStr(String orderDetailStr) {
        this.orderDetailStr = orderDetailStr;
    }

    public int getOrderDetailSize() {
        return orderDetailSize;
    }

    public void setOrderDetailSize(int orderDetailSize) {
        this.orderDetailSize = orderDetailSize;
    }

    public List<OrderSampleStrDTO> getOrderSampleStrDTOList() {
        return orderSampleStrDTOList;
    }

    public void setOrderSampleStrDTOList(List<OrderSampleStrDTO> orderSampleStrDTOList) {
        this.orderSampleStrDTOList = orderSampleStrDTOList;
    }

    public String getApplicationCompanyNameCn() {
        return applicationCompanyNameCn;
    }

    public void setApplicationCompanyNameCn(String applicationCompanyNameCn) {
        this.applicationCompanyNameCn = applicationCompanyNameCn;
    }

    public String getReportCompanyNameCn() {
        return reportCompanyNameCn;
    }

    public void setReportCompanyNameCn(String reportCompanyNameCn) {
        this.reportCompanyNameCn = reportCompanyNameCn;
    }

    public String getApplicationCompanyNameEn() {
        return applicationCompanyNameEn;
    }

    public void setApplicationCompanyNameEn(String applicationCompanyNameEn) {
        this.applicationCompanyNameEn = applicationCompanyNameEn;
    }

    public String getReportCompanyNameEn() {
        return reportCompanyNameEn;
    }

    public void setReportCompanyNameEn(String reportCompanyNameEn) {
        this.reportCompanyNameEn = reportCompanyNameEn;
    }

    public String getApplicationCompanyNameStr() {
        if(StringUtils.isBlank(applicationCompanyNameCn)){
          return applicationCompanyNameEn;
        }
        return applicationCompanyNameCn;
    }

    public void setApplicationCompanyNameStr(String applicationCompanyNameStr) {
        this.applicationCompanyNameStr = applicationCompanyNameStr;
    }

    public String getReportCompanyNameStr() {
        if(StringUtils.isBlank(reportCompanyNameCn)){
            return  reportCompanyNameEn;
        }
        return reportCompanyNameCn;
    }

    public void setReportCompanyNameStr(String reportCompanyNameStr) {
        this.reportCompanyNameStr = reportCompanyNameStr;
    }

    public String getGroupNo() {
        return groupNo;
    }

    public void setGroupNo(String groupNo) {
        this.groupNo = groupNo;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public Integer getIsPayReceived() {
        return isPayReceived;
    }

    public void setIsPayReceived(Integer isPayReceived) {
        this.isPayReceived = isPayReceived;
    }

    public Integer getPayState() {
        return payState;
    }

    public void setPayState(Integer payState) {
        this.payState = payState;
    }

    public List<OrderAttachmentDTO> getReportFileList() {
        return reportFileList;
    }

    public void setReportFileList(List<OrderAttachmentDTO> reportFileList) {
        this.reportFileList = reportFileList;
    }

    public String getBu() {
        return bu;
    }

    public void setBu(String bu) {
        this.bu = bu;
    }
}
