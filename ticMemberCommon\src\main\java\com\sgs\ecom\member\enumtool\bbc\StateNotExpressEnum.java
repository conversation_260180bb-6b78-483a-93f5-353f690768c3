package com.sgs.ecom.member.enumtool.bbc;

public enum StateNotExpressEnum {

	BU("INSP,CBE,EHS", 1, "INSP,CBE,EHS","INSP,CBE,EHS"),
	SUB_BU("SL-PX,RSTS-SDS", 2, "SL-PX,RSTS-SDS","SL-PX,RSTS-SDS");


	StateNotExpressEnum(String name, int index, String nameCh, String min) {
		this.name = name;
		this.index = index;
		this.nameCh = nameCh;
		this.min = min;
	}

	public static String getName(int index) {
		for (StateNotExpressEnum c : StateNotExpressEnum.values()) {
			if (c.getIndex()==index) {
				return c.getName();
			}
		}
		return null;
	}
	public static String getNameCh(int index) {
		for (StateNotExpressEnum c : StateNotExpressEnum.values()) {
			if (c.getIndex()==index) {
				return c.getNameCh();
			}
		}
		return "";
	}

	public static StateNotExpressEnum getEnum(String name) {
		for (StateNotExpressEnum c : StateNotExpressEnum.values()) {
			if (c.getName().equals(name)) {
				return c;
			}
		}
		return null;
	}

	public static String getMinByIndex(int index) {
		for (StateNotExpressEnum c : StateNotExpressEnum.values()) {
			if (c.getIndex()==index) {
				if(index==10){
					return "M";
				}
				return c.getMin();
			}
		}
		return "";
	}

	public  static int getEnumStateByMin(String min){
		for (StateNotExpressEnum c : StateNotExpressEnum.values()) {
			if (c.getMin().contains(min)) {
				return c.getIndex();
			}
		}
		return -2;
	}

	private String name;
	private int index;
	private String nameCh;
	private String min;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public int getIndex() {
		return index;
	}

	public void setIndex(int index) {
		this.index = index;
	}

	public String getNameCh() {
		return nameCh;
	}

	public void setNameCh(String nameCh) {
		this.nameCh = nameCh;
	}

	public String getMin() {
		return min;
	}

	public void setMin(String min) {
		this.min = min;
	}
}
