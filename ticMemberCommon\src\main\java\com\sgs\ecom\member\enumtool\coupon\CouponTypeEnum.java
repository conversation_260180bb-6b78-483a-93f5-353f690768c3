package com.sgs.ecom.member.enumtool.coupon;

public enum CouponTypeEnum {
    //1：满减2：折扣3：抵扣、4：兑换、5：满送、6：满免）
    FULL_MINUS("fullMinus", 1, "满减"),
    DISCOUNT("discount", 2, "折扣"),
    DEDUCTION("deduction", 3, "抵扣"),
    EXCHANGE("exchange", 4, "兑换"),
    Full_Delivery("fullDelivery", 5, "满送"),
    full_Free("fullFree", 6, "满免"),
    ;

    CouponTypeEnum(String name, int index, String nameCh) {
        this.name = name;
        this.index = index;
        this.nameCh = nameCh;
    }

    public static CouponTypeEnum getEnum(int index) {

        for (CouponTypeEnum c : CouponTypeEnum.values()) {
            if (c.getIndex()==index) {
                return c;
            }
        }
        return null;
    }



    private String name;
    private int index;
    private String nameCh;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }
}
