package com.sgs.ecom.member.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.dto.bbc.TicAppFormDTO;
import com.sgs.ecom.member.dto.custom.OrderDetailPriceDTO;
import com.sgs.ecom.member.dto.detail.OrderApplicationFormDTO;
import com.sgs.ecom.member.dto.detail.OrderAttachmentDTO;
import com.sgs.ecom.member.dto.detail.OrderInvoiceDTO;
import com.sgs.ecom.member.dto.oiq.TfsOrderDTO;
import com.sgs.ecom.member.dto.order.*;
import com.sgs.ecom.member.dto.detail.OrderReportDTO;
import com.sgs.ecom.member.dto.order.OrderProductDTO;
import com.sgs.ecom.member.dto.order.OrderSampleMoreDTO;
import com.sgs.ecom.member.dto.order.SubOrderDTO;
import com.sgs.ecom.member.dto.order.SubOrderInfoDTO;
import com.sgs.ecom.member.dto.pay.OrderPayDTO;
import com.sgs.ecom.member.dto.pay.PayRefundAlipayDTO;
import com.sgs.ecom.member.dto.portal.PortalInquiryBaseDTO;
import com.sgs.ecom.member.enumtool.aplication.TestCycleEnum;
import com.sgs.ecom.member.enumtool.order.OrderRefundStateEnum;
import com.sgs.ecom.member.request.tic.*;
import com.sgs.ecom.member.util.order.OrderUtil;
import com.sgs.ecom.member.util.order.UseDateUtil;
import com.sgs.ecom.member.util.serializer.PriceNumFormatSerializer;
import com.sgs.ecom.member.util.serializer.TimeStringFormatSerializer;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class OrderBaseInfoMoreDTO extends OrderBaseInfoCheckDTO {

 	public static final String CREATE_SQL = "select LAB_NAME,CREATE_DATE,ORDER_ID,STATE,DISCOUNT_AMOUNT,ORDER_TYPE,REAL_AMOUNT,ORDER_NO from ORDER_BASE_INFO";

	//时间相关数据
	@JsonSerialize(using = TimeStringFormatSerializer.class)
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class,OrderDetail.class,InquiryDetail.class,ShareDetail.class,RstsDetail.class,TicDetail.class})
	private String createDate;
	@JsonSerialize(using = TimeStringFormatSerializer.class)
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
	private String stateDate;
	@JsonSerialize(using = TimeStringFormatSerializer.class)
	@ApiAnno(groups={Default.class,InquiryDetail.class,ShareDetail.class})
	private String offerDate;
	@JsonSerialize(using = TimeStringFormatSerializer.class)
	@ApiAnno(groups={Default.class,InquiryDetail.class,ShareDetail.class})
	private String orderExpDate;
	@JsonSerialize(using = TimeStringFormatSerializer.class)
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class,RstsDetail.class,TicDetail.class})
	private String applySubmitDate;
	@JsonSerialize(using = TimeStringFormatSerializer.class)
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
	public String timeDateShow;
	//预计出具报告数据
	@JsonSerialize(using = TimeStringFormatSerializer.class)
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,RstsDetail.class,TicDetail.class})
	private String deadlineTime;
	@ApiAnno(groups={Default.class,TicDetail.class})
	private String deadlineTimeShow;
	@ApiAnno(groups={Default.class,TicDetail.class})
	private String optDeadlineTime;//操作报告日期的时间
	@JsonSerialize(using = TimeStringFormatSerializer.class)
	@ApiAnno(groups={OrderDetail.class,Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class,RstsDetail.class})
	private String confirmOrderDate;





 	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
 	private String labNameValue;






	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
	private int userSex;

	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
	private String userPhone;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
	private String userName;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
	private String userEmail;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
	private String csName;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
	private String csNameEn;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
	private String companyName;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
	private String province;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
	private String city;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class})
	private String csPhone;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
	private String csEmail;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class,RstsDetail.class,TicDetail.class})
	private int hisState;
	@ApiAnno(groups={Default.class,TicDetail.class})
	private String checkDate;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class,RstsDetail.class})
	private int isUrgent;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
 	private String categoryPath;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
	private int isRead;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,OrderDetail.class})
	private Integer isRemind;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,OrderList.class,OrderDetail.class,TicDetail.class})
	private int refundState;

	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
	private String businessLine;

	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
	private List<OrderDetailDTO> orderDetailDTOList;
	@ApiAnno(groups={Default.class,InquiryDetail.class,ShareDetail.class})
	private List<OrderDetailDTO> orderDetailOptionalList;


	@ApiAnno(groups={Default.class,OiqOrderDetail.class,TicDetail.class,PortalOrderDetail.class})
	private List<OrderPayDTO> orderPayDTOList=new ArrayList<>();

	@ApiAnno(groups={Default.class,OiqOrderDetail.class,RstsDetail.class,TicDetail.class,PortalOrderDetail.class})
	private List<OrderPayDTO> orderRefundPayDTOList=new ArrayList<>();


	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
	private String sampleRequirements;//样品准备要求
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
	private String recommendReason;//推荐理由
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
	private String recommendReasonImage;//推荐理由
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
	private String reportLuaValue;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
	private String reportMemo;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
	private String reportLuaCodeValue;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
	private String reportFormValue;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
	private String testCycleMemo;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
	private String reportFormCodeValue;

	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
	private String productName;
	@ApiAnno(groups={Default.class,InquiryDetail.class,ShareDetail.class})
	private String toOrder;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,RstsDetail.class,TicDetail.class})
	private Integer payMethod;

	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
	private Integer isDetermine=0;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
	private String determine;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
	private String closeReason;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
	private int reasonFlg=0;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
	private OrderDetailPriceDTO orderDetailPriceDTO;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
	private List<OrderAttributeDTO> specialNote=new ArrayList<>();
	@ApiAnno(groups={OiqOrderDetail.class,PortalOrderDetail.class})
	private List<CustomerIdDTO> customerList=new ArrayList<>();

	//订单新加参数
	@ApiAnno(groups={Default.class,OiqOrderDetail.class})
	private int totalNums;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,TicDetail.class})
	private int subState;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class})
	private String productImg;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,})
	private String relateOrderNo;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,TicDetail.class,RstsDetail.class})
	private int payState;
	@ApiAnno(groups={Default.class,TicDetail.class})
	private OrderInvoiceDTO monthInvoice;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,TicDetail.class})
	private int proState;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,RstsDetail.class,TicDetail.class})
	private List<OrderAttachmentDTO> reportFileList=new ArrayList<>();

	@ApiAnno(groups={Default.class,TicDetail.class})
	private List<OrderAttachmentDTO> allFileList;

	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,TicDetail.class,RstsDetail.class})
	private List<OrderAttachmentDTO> invoiceFileList=new ArrayList<>();

	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class,RstsDetail.class,TicDetail.class})
	private int isDelete;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,TicDetail.class})
	private int isPayReceived;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class})
	private int isInvoice;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,TicDetail.class})
	private int invoiceType;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
	private String operatorCode;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class,PortalOrderDetail.class})
	private String sampleNameStr;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class,PortalOrderDetail.class})
	private String itemNameStr;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
	private String testCycleLog;
	@ApiAnno(groups={InquiryDetail.class,ShareDetail.class,OrderDetail.class,Default.class,OiqOrderDetail.class,PortalOrderDetail.class})
	private int priceType;
	@ApiAnno(groups={InquiryDetail.class,ShareDetail.class,OrderDetail.class,Default.class,OiqOrderDetail.class,PortalOrderDetail.class})
	private int newTemplate=1;
	@ApiAnno(groups={OrderDetail.class,Default.class,OiqOrderDetail.class,PortalOrderDetail.class,PortalOrderDetail.class})
	private PortalInquiryBaseDTO inquiryBaseDTO;
	@ApiAnno(groups={PortalOrderDetail.class,InquiryDetail.class,OiqOrderDetail.class})
	private List<OrderAttachmentDTO> quotationFileList=new ArrayList<>();
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class,OiqOrderDetail.class,PortalOrderDetail.class,ShareDetail.class})
	private BigDecimal taxRates;

	@ApiAnno(groups={OrderDetail.class,Default.class,OiqOrderDetail.class,PortalOrderDetail.class,TicDetail.class})
	private int subOrderFlg=0;
	@ApiAnno(groups={OrderDetail.class,Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
	private int waitFlg=0;
	@ApiAnno(groups={OrderDetail.class,Default.class,OiqOrderDetail.class,PortalOrderDetail.class,RstsDetail.class,TicDetail.class})
	private List<SubOrderDTO> subOrderDTOList ;
	@ApiAnno(groups={OrderDetail.class,Default.class,OiqOrderDetail.class,PortalOrderDetail.class,RstsDetail.class,TicDetail.class})
	private SubOrderInfoDTO subOrderInfoDTO=new SubOrderInfoDTO();

	@ApiAnno(groups={TicDetail.class,RstsDetail.class})
	private int monthPay;
	@ApiAnno(groups={TicDetail.class})
	private String monthPayShow;


	@ApiAnno(groups={OrderDetail.class,Default.class,OiqOrderDetail.class,PortalOrderDetail.class,RstsDetail.class,TicDetail.class})
	private BigDecimal differenceStatistics;

	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class,TicDetail.class})
	@BeanAnno(value="DISCOUNT_AMOUNT", getName="getDiscountAmount", setName="setDiscountAmount")
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	private BigDecimal discountAmount=BigDecimal.ZERO;
	@ApiAnno(groups={Default.class,TicDetail.class})
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	private BigDecimal shopDisAmount=BigDecimal.ZERO;
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class,RstsDetail.class,TicDetail.class})
	private BigDecimal orderAmount;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class,RstsDetail.class,TicDetail.class})
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	private BigDecimal serviceAmount;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class,RstsDetail.class})
	private BigDecimal urgentAmount;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
	private BigDecimal testCycle;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
	private BigDecimal discountAmountNum;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
	private BigDecimal newDiscountAmountNum;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
	private BigDecimal urgentAmountNum;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class})
	private String formUrl;
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	@ApiAnno(groups={RstsList.class,RstsDetail.class})
	private BigDecimal platformAmount;

	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class,RstsDetail.class,TicDetail.class})
	private BigDecimal realAmount;

	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class})
	private  String isUrgentShow;

	@ApiAnno(groups={RstsDetail.class})
	private String promoInfo;
	@ApiAnno(groups={RstsDetail.class})
	private String salesCode;
	@ApiAnno(groups={RstsDetail.class})
	private String salesPhone;
	@ApiAnno(groups={RstsDetail.class,InquiryDetail.class,OiqOrderDetail.class,ShareDetail.class,PortalOrderDetail.class})
	private String currency;
	@ApiAnno(groups={InquiryDetail.class,OiqOrderDetail.class,ShareDetail.class,PortalOrderDetail.class})
	private String currencyMark;
	@ApiAnno(groups={InquiryDetail.class,OiqOrderDetail.class})
	private BigDecimal exchangeRate;

	@JsonSerialize(using = PriceNumFormatSerializer.class)
	@ApiAnno(groups={RstsDetail.class})
	private BigDecimal rstsAmount;
	@ApiAnno(groups={RstsDetail.class})
	private int industryField;
	@ApiAnno(groups={RstsDetail.class})
	private OrderOperatorLogDTO lastConfirmLog;
	@ApiAnno(groups={RstsDetail.class})
	private String sampleCode;
	@ApiAnno(groups={RstsDetail.class})
	private String sampleId;
	@ApiAnno(groups={RstsDetail.class})
	private String toPayAmount;
	@ApiAnno(groups={RstsDetail.class})
	private String offPayAmount;
	@ApiAnno(groups={RstsDetail.class})
	private String onlinePayAmount;


	private Long custIdReq;


	@ApiAnno(groups={TicDetail.class})
	private List<OrderProductDTO> orderProductDTOList;
	@ApiAnno(groups={TicDetail.class})
	private String storeId;
	@ApiAnno(groups={TicDetail.class})
	private String storeName;
	@ApiAnno(groups={TicDetail.class})
	private OrderApplicationFormDTO orderApplicationFormDTO;
	@ApiAnno(groups={TicDetail.class})
	private List<OrderAttachmentDTO> checkFileList;
	@ApiAnno(groups={TicDetail.class})
	private TicAppFormDTO ticAppFormDTO;
	@ApiAnno(groups={TicDetail.class})
	private int downLoad;
	@ApiAnno(groups={TicDetail.class})
	private List<PayRefundAlipayDTO> isTimeList;
	@ApiAnno(groups={TicDetail.class})
	private int offFlg;//补充支付凭证
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class,RstsDetail.class,TicDetail.class})
	private List<OrderSampleMoreDTO> orderSampleReqList = new ArrayList<>();

	@ApiAnno(groups={Default.class,TicDetail.class})
	private TicReportReq ticReportReq; //报告信息
	@ApiAnno(groups={Default.class,TicDetail.class})
	private TicRegisterReq ticRegisterReq;//申请方信息
	@ApiAnno(groups={Default.class,TicDetail.class})
	private TicOtherReq ticOtherReq;
	@ApiAnno(groups={Default.class,TicDetail.class})
	private TicTestReq ticTestReq;
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,TicDetail.class})
	private BigDecimal orderAmountPrice;//合计金额 orderAmount-storeAmount-couponAmount

	@ApiAnno(groups={Default.class,TicDetail.class})
	private List<TicAttrInfo>  attrInfo;


	@ApiAnno(groups={Default.class,OiqOrderDetail.class,TicDetail.class})
	private int  isRefund;//客户端是否能显示发起申请退款的按钮 0-否 1-是
	@ApiAnno(groups={Default.class,OiqOrderDetail.class,TicDetail.class})
	private Integer isElectron; //是否是上海开票显示电子 0-否 1-是
	@ApiAnno(groups={RstsDetail.class})
	private String stageTime;

	@ApiAnno(groups={Default.class,TicDetail.class})
	private int submitType ; // 0-默认  1-未保存  2-保存未生成附件 3-提交已生成附件
	@ApiAnno(groups={ShareDetail.class,OiqOrderDetail.class,PortalOrderDetail.class})
	private TfsOrderDTO tfsOrder;

	//分享使用
	@ApiAnno(groups={ShareDetail.class})
	private List<OrderAttributeDTO> labList;
	@ApiAnno(groups={ShareDetail.class})
	private List<OrderAttributeDTO> reportLuaList;
	@ApiAnno(groups={ShareDetail.class})
	private List<OrderAttributeDTO> reportFormList;
	@ApiAnno(groups={ShareDetail.class})
	private List<OrderAttributeDTO> discountAmountNumList;

	@ApiAnno(groups={Default.class,TicDetail.class})
	private Integer downInvoiceLoad;//发票下载标记 0-未出票 1-已出票未下载 2-已出票已下载
	@ApiAnno(groups={BaseQryFilter.Default.class,TicDetail.class})
	private  OrderReportDTO orderReportDTO;
	@ApiAnno(groups={BaseQryFilter.Default.class,TicDetail.class})
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	private BigDecimal csDiscountAmount;//客服优惠金额
	@ApiAnno(groups={Default.class,TicDetail.class})
	private List<OrderLinkDTO> orderLinks;//学员信息
	@ApiAnno(groups={Default.class, TicDetail.class})
	private boolean multiType;	//订单的样品申请表类型标识

	public List<OrderLinkDTO> getOrderLinks() {
		return orderLinks;
	}

	public void setOrderLinks(List<OrderLinkDTO> orderLinks) {
		this.orderLinks = orderLinks;
	}

	public BigDecimal getCsDiscountAmount() {
		return csDiscountAmount;
	}

	public void setCsDiscountAmount(BigDecimal csDiscountAmount) {
		this.csDiscountAmount = csDiscountAmount;
	}

	public OrderReportDTO getOrderReportDTO() {
		return orderReportDTO;
	}

	public void setOrderReportDTO(OrderReportDTO orderReportDTO) {
		this.orderReportDTO = orderReportDTO;
	}

	public Integer getDownInvoiceLoad() {
		return downInvoiceLoad;
	}

	public void setDownInvoiceLoad(Integer downInvoiceLoad) {
		this.downInvoiceLoad = downInvoiceLoad;
	}

	public String getOptDeadlineTime() {
		return optDeadlineTime;
	}

	public void setOptDeadlineTime(String optDeadlineTime) {
		this.optDeadlineTime = optDeadlineTime;
	}

	public String getDeadlineTimeShow() {
		if(ValidationUtil.isEmpty(deadlineTime))
			return "";

		return UseDateUtil.getDeadTimeShow(deadlineTime);
	}

	public int getSubmitType() {
		return submitType;
	}

	public void setSubmitType(int submitType) {
		this.submitType = submitType;
	}

	public Integer getIsElectron() {
		return isElectron;
	}

	public void setIsElectron(Integer isElectron) {
		this.isElectron = isElectron;
	}

	public List<OrderAttachmentDTO> getAllFileList() {
		return allFileList;
	}

	public void setAllFileList(List<OrderAttachmentDTO> allFileList) {
		this.allFileList = allFileList;
	}

	public int getIsRefund() {
		return isRefund;
	}

	public void setIsRefund(int isRefund) {
		this.isRefund = isRefund;
	}

	public BigDecimal getDifferenceStatistics() {
		return differenceStatistics;
	}

	public void setDifferenceStatistics(BigDecimal differenceStatistics) {
		this.differenceStatistics = differenceStatistics;
	}

	public List<TicAttrInfo> getAttrInfo() {
		return attrInfo;
	}

	public void setAttrInfo(List<TicAttrInfo> attrInfo) {
		this.attrInfo = attrInfo;
	}

	public TicTestReq getTicTestReq() {
		return ticTestReq;
	}

	public void setTicTestReq(TicTestReq ticTestReq) {
		this.ticTestReq = ticTestReq;
	}

	public TicOtherReq getTicOtherReq() {
		return ticOtherReq;
	}

	public void setTicOtherReq(TicOtherReq ticOtherReq) {
		this.ticOtherReq = ticOtherReq;
	}

	public List<OrderSampleMoreDTO> getOrderSampleReqList() {
		return orderSampleReqList;
	}



	public void setOrderSampleReqList(List<OrderSampleMoreDTO> orderSampleReqList) {
		this.orderSampleReqList = orderSampleReqList;
	}

	public TicReportReq getTicReportReq() {
		return ticReportReq;
	}

	public void setTicReportReq(TicReportReq ticReportReq) {
		this.ticReportReq = ticReportReq;
	}

	public TicRegisterReq getTicRegisterReq() {
		return ticRegisterReq;
	}

	public void setTicRegisterReq(TicRegisterReq ticRegisterReq) {
		this.ticRegisterReq = ticRegisterReq;
	}

	public BigDecimal getShopDisAmount() {
		return shopDisAmount;
	}

	public void setShopDisAmount(BigDecimal shopDisAmount) {
		this.shopDisAmount = shopDisAmount;
	}



	public int getProState() {
		return proState;
	}

	public void setProState(int proState) {
		this.proState = proState;
	}

	public List<OrderPayDTO> getOrderRefundPayDTOList() {
		return orderRefundPayDTOList;
	}

	public void setOrderRefundPayDTOList(List<OrderPayDTO> orderRefundPayDTOList) {
		this.orderRefundPayDTOList = orderRefundPayDTOList;
	}

	public List<OrderDetailDTO> getOrderDetailDTOList() {
		return orderDetailDTOList;
	}

	public void setOrderDetailDTOList(List<OrderDetailDTO> orderDetailDTOList) {
		this.orderDetailDTOList = orderDetailDTOList;
	}



	public String getCategoryPath() {
		return categoryPath;
	}

	public void setCategoryPath(String categoryPath) {
		this.categoryPath = categoryPath;
	}



	public int getIsRead() {
		return isRead;
	}

	public void setIsRead(int isRead) {
		this.isRead = isRead;
	}

	public String getOfferDate() {
		return offerDate;
	}

	public void setOfferDate(String offerDate) {
		this.offerDate = offerDate;
	}


	public String getOrderExpDate() {
		return orderExpDate;
	}

	public void setOrderExpDate(String orderExpDate) {
		this.orderExpDate = orderExpDate;
	}

	public String getUserPhone() {
		return userPhone;
	}

	public void setUserPhone(String userPhone) {
		this.userPhone = userPhone;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getUserEmail() {
		return userEmail;
	}

	public void setUserEmail(String userEmail) {
		this.userEmail = userEmail;
	}

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public int getHisState() {
		return hisState;
	}

	public void setHisState(int hisState) {
		this.hisState = hisState;
	}

	public int getUserSex() {
		return userSex;
	}

	public void setUserSex(int userSex) {
		this.userSex = userSex;
	}



	public String getCsEmail() {
		return csEmail;
	}

	public void setCsEmail(String csEmail) {
		this.csEmail = csEmail;
	}




	@JsonSerialize(using = PriceNumFormatSerializer.class)
	public BigDecimal getDiscountAmount() {
		return discountAmount;
	}

	public void setDiscountAmount(BigDecimal discountAmount) {
		this.discountAmount = discountAmount;
	}
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	public BigDecimal getOrderAmount() {
		return orderAmount;
	}

	public void setOrderAmount(BigDecimal orderAmount) {
		this.orderAmount = orderAmount;
	}
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	public BigDecimal getServiceAmount() {
		return serviceAmount;
	}

	@JsonSerialize(using = PriceNumFormatSerializer.class)
	public BigDecimal getUrgentAmount() {
		return urgentAmount;
	}

	public void setUrgentAmount(BigDecimal urgentAmount) {
		this.urgentAmount = urgentAmount;
	}

	public void setServiceAmount(BigDecimal serviceAmount) {
		this.serviceAmount = serviceAmount;
	}

	public BigDecimal getTestCycle() {
		return testCycle;
	}

	public void setTestCycle(BigDecimal testCycle) {
		this.testCycle = testCycle;
	}

	public String getSampleRequirements() {
		return sampleRequirements;
	}

	public void setSampleRequirements(String sampleRequirements) {
		this.sampleRequirements = sampleRequirements;
	}

	public String getRecommendReason() {
		return recommendReason;
	}

	public void setRecommendReason(String recommendReason) {
		this.recommendReason = recommendReason;
	}

	public String getLabNameValue() {
		return labNameValue;
	}

	public void setLabNameValue(String labNameValue) {
		this.labNameValue = labNameValue;
	}

	public String getReportLuaValue() {
		return reportLuaValue;
	}

	public void setReportLuaValue(String reportLuaValue) {
		this.reportLuaValue = reportLuaValue;
	}

	public String getReportFormValue() {
		return reportFormValue;
	}

	public void setReportFormValue(String reportFormValue) {
		this.reportFormValue = reportFormValue;
	}

	public String getBusinessLine() {
		return businessLine;
	}

	public void setBusinessLine(String businessLine) {
		this.businessLine = businessLine;
	}

	public int getIsUrgent() {
		return isUrgent;
	}

	public void setIsUrgent(int isUrgent) {
		this.isUrgent = isUrgent;
	}

	public BigDecimal getDiscountAmountNum() {
		return discountAmountNum;
	}

	public void setDiscountAmountNum(BigDecimal discountAmountNum) {
		this.discountAmountNum = discountAmountNum;
	}

	public BigDecimal getUrgentAmountNum() {
		return urgentAmountNum;
	}

	public void setUrgentAmountNum(BigDecimal urgentAmountNum) {
		this.urgentAmountNum = urgentAmountNum;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public String getCsName() {
		return csName;
	}

	public void setCsName(String csName) {
		this.csName = csName;
	}

	public String getCsNameEn() {
		return csNameEn;
	}

	public void setCsNameEn(String csNameEn) {
		this.csNameEn = csNameEn;
	}

	public String getReportLuaCodeValue() {
		return reportLuaCodeValue;
	}

	public void setReportLuaCodeValue(String reportLuaCodeValue) {
		this.reportLuaCodeValue = reportLuaCodeValue;
	}


	public int getTotalNums() {
		return totalNums;
	}

	public void setTotalNums(int totalNums) {
		this.totalNums = totalNums;
	}

	public int getSubState() {
		return subState;
	}

	public void setSubState(int subState) {
		this.subState = subState;
	}

	public String getProductImg() {
		return productImg;
	}

	public void setProductImg(String productImg) {
		this.productImg = productImg;
	}

	public String getRelateOrderNo() {
		return relateOrderNo;
	}

	public void setRelateOrderNo(String relateOrderNo) {
		this.relateOrderNo = relateOrderNo;
	}

	public int getPayState() {
		return payState;
	}

	public void setPayState(int payState) {
		this.payState = payState;
	}

	public int getIsDelete() {
		return isDelete;
	}

	public void setIsDelete(int isDelete) {
		this.isDelete = isDelete;
	}

	public int getIsPayReceived() {
		return isPayReceived;
	}

	public void setIsPayReceived(int isPayReceived) {
		this.isPayReceived = isPayReceived;
	}





	public String getTimeDateShow() {
		return timeDateShow;
	}

	public void setTimeDateShow(String timeDateShow) {
		this.timeDateShow = timeDateShow;
	}

	public List<OrderAttachmentDTO> getReportFileList() {
		return reportFileList;
	}

	public void setReportFileList(List<OrderAttachmentDTO> reportFileList) {
		this.reportFileList = reportFileList;
	}

	public List<OrderAttachmentDTO> getInvoiceFileList() {
		return invoiceFileList;
	}

	public void setInvoiceFileList(List<OrderAttachmentDTO> invoiceFileList) {
		this.invoiceFileList = invoiceFileList;
	}

	public int getIsInvoice() {
		return isInvoice;
	}

	public String getDeadlineTime() {
		return deadlineTime;
	}

	public void setDeadlineTime(String deadlineTime) {
		this.deadlineTime = deadlineTime;
	}

	public void setIsInvoice(int isInvoice) {
		this.isInvoice = isInvoice;
	}

	public String getReportFormCodeValue() {
		return reportFormCodeValue;
	}

	public void setReportFormCodeValue(String reportFormCodeValue) {
		this.reportFormCodeValue = reportFormCodeValue;
	}

	public String getTestCycleMemo() {
		return testCycleMemo;
	}

	public void setTestCycleMemo(String testCycleMemo) {
		this.testCycleMemo = testCycleMemo;
	}

	public String getOperatorCode() {
		return operatorCode;
	}

	public void setOperatorCode(String operatorCode) {
		this.operatorCode = operatorCode;
	}

	public String getRecommendReasonImage() {
		return recommendReasonImage;
	}

	public void setRecommendReasonImage(String recommendReasonImage) {
		this.recommendReasonImage = recommendReasonImage;
	}

	public String getFormUrl() {
		return formUrl;
	}

	public void setFormUrl(String formUrl) {
		this.formUrl = formUrl;
	}

	@JsonSerialize(using = PriceNumFormatSerializer.class)
	public BigDecimal getRealAmount() {
		return realAmount;
	}

	public void setRealAmount(BigDecimal realAmount) {
		this.realAmount = realAmount;
	}


	public String getCreateDate() {
		return createDate;
	}

	public void setCreateDate(String createDate) {
		this.createDate = createDate;
	}

	public String getStateDate() {
		return stateDate;
	}

	public void setStateDate(String stateDate) {
		this.stateDate = stateDate;
	}

	public String getApplySubmitDate() {
		return applySubmitDate;
	}

	public void setApplySubmitDate(String applySubmitDate) {
		this.applySubmitDate = applySubmitDate;
	}

	public String getSampleNameStr() {
		return sampleNameStr;
	}

	public void setSampleNameStr(String sampleNameStr) {
		this.sampleNameStr = sampleNameStr;
	}

	public String getIsUrgentShow() {
		return TestCycleEnum.getNameCh(String.valueOf(isUrgent));
	}

	public String getToOrder() {
		return toOrder;
	}

	public void setToOrder(String toOrder) {
		this.toOrder = toOrder;
	}

	public Integer getPayMethod() {
		return payMethod;
	}

	public void setPayMethod(Integer payMethod) {
		this.payMethod = payMethod;
	}

	public int getRefundState() {
		return refundState;
	}

	public void setRefundState(int refundState) {
		this.refundState = refundState;
	}

	@ApiAnno(groups={Default.class,OiqOrderDetail.class,PortalOrderDetail.class,InquiryDetail.class,ShareDetail.class,OrderList.class,OrderDetail.class,TicDetail.class})
	private String refundStateShow;

	public String getRefundStateShow() {
		return OrderRefundStateEnum.getNameCh(refundState);
	}

	public Integer getIsDetermine() {
		return isDetermine;
	}

	public void setIsDetermine(Integer isDetermine) {
		this.isDetermine = isDetermine;
	}

	public String getDetermine() {
		return determine;
	}

	public void setDetermine(String determine) {
		this.determine = determine;
	}

	public String getTestCycleLog() {
		return testCycleLog;
	}

	public void setTestCycleLog(String testCycleLog) {
		this.testCycleLog = testCycleLog;
	}

	public String getCloseReason() {
		return closeReason;
	}

	public void setCloseReason(String closeReason) {
		this.closeReason = closeReason;
	}

	public int getReasonFlg() {
		return reasonFlg;
	}

	public void setReasonFlg(int reasonFlg) {
		this.reasonFlg = reasonFlg;
	}

	public OrderDetailPriceDTO getOrderDetailPriceDTO() {
		return orderDetailPriceDTO;
	}

	public void setOrderDetailPriceDTO(OrderDetailPriceDTO orderDetailPriceDTO) {
		this.orderDetailPriceDTO = orderDetailPriceDTO;
	}

	public int getSubOrderFlg() {
		return subOrderFlg;
	}

	public void setSubOrderFlg(int subOrderFlg) {
		this.subOrderFlg = subOrderFlg;
	}

	public List<SubOrderDTO> getSubOrderDTOList() {
		return subOrderDTOList;
	}

	public void setSubOrderDTOList(List<SubOrderDTO> subOrderDTOList) {
		this.subOrderDTOList = subOrderDTOList;
	}

	public SubOrderInfoDTO getSubOrderInfoDTO() {
		return subOrderInfoDTO;
	}

	public void setSubOrderInfoDTO(SubOrderInfoDTO subOrderInfoDTO) {
		this.subOrderInfoDTO = subOrderInfoDTO;
	}

	public int getWaitFlg() {
		return waitFlg;
	}

	public void setWaitFlg(int waitFlg) {
		this.waitFlg = waitFlg;
	}

	public List<OrderPayDTO> getOrderPayDTOList() {
		return orderPayDTOList;
	}

	public void setOrderPayDTOList(List<OrderPayDTO> orderPayDTOList) {
		this.orderPayDTOList = orderPayDTOList;
	}

	public BigDecimal getPlatformAmount() {
		return platformAmount;
	}

	public void setPlatformAmount(BigDecimal platformAmount) {
		this.platformAmount = platformAmount;
	}

	public List<OrderDetailDTO> getOrderDetailOptionalList() {
		return orderDetailOptionalList;
	}

	public void setOrderDetailOptionalList(List<OrderDetailDTO> orderDetailOptionalList) {
		this.orderDetailOptionalList = orderDetailOptionalList;
	}

	public int getInvoiceType() {
		return invoiceType;
	}

	public void setInvoiceType(int invoiceType) {
		this.invoiceType = invoiceType;
	}

	public List<OrderAttributeDTO> getSpecialNote() {
		return specialNote;
	}

	public void setSpecialNote(List<OrderAttributeDTO> specialNote) {
		this.specialNote = specialNote;
	}

	public String getConfirmOrderDate() {
		return confirmOrderDate;
	}

	public void setConfirmOrderDate(String confirmOrderDate) {
		this.confirmOrderDate = confirmOrderDate;
	}

	public BigDecimal getRstsAmount() {
		return rstsAmount;
	}

	public void setRstsAmount(BigDecimal rstsAmount) {
		this.rstsAmount = rstsAmount;
	}

	public List<OrderProductDTO> getOrderProductDTOList() {
		return orderProductDTOList;
	}

	public void setOrderProductDTOList(List<OrderProductDTO> orderProductDTOList) {
		this.orderProductDTOList = orderProductDTOList;
	}

	public String getStoreId() {
		return storeId;
	}

	public void setStoreId(String storeId) {
		this.storeId = storeId;
	}

	public String getStoreName() {
		return storeName;
	}

	public void setStoreName(String storeName) {
		this.storeName = storeName;
	}

	public OrderApplicationFormDTO getOrderApplicationFormDTO() {
		return orderApplicationFormDTO;
	}

	public void setOrderApplicationFormDTO(OrderApplicationFormDTO orderApplicationFormDTO) {
		this.orderApplicationFormDTO = orderApplicationFormDTO;
	}

	public List<OrderAttachmentDTO> getCheckFileList() {
		return checkFileList;
	}

	public void setCheckFileList(List<OrderAttachmentDTO> checkFileList) {
		this.checkFileList = checkFileList;
	}

	public TicAppFormDTO getTicAppFormDTO() {
		return ticAppFormDTO;
	}

	public void setTicAppFormDTO(TicAppFormDTO ticAppFormDTO) {
		this.ticAppFormDTO = ticAppFormDTO;
	}

	public int getDownLoad() {
		return downLoad;
	}

	public void setDownLoad(int downLoad) {
		this.downLoad = downLoad;
	}

	public List<PayRefundAlipayDTO> getIsTimeList() {
		return isTimeList;
	}

	public void setIsTimeList(List<PayRefundAlipayDTO> isTimeList) {
		this.isTimeList = isTimeList;
	}

	public int getOffFlg() {
		if(payMethod!=null && payMethod==300000 && isPayReceived==0 && refundState!=1 &&
			(getState()==11 || getState()==13|| getState()==14||getState()==80)  ){
			return 1;
		}
		return 0 ;
	}

	public void setOffFlg(int offFlg) {
		this.offFlg = offFlg;
	}

	public String getCheckDate() {
		return checkDate;
	}

	public void setCheckDate(String checkDate) {
		this.checkDate = checkDate;
	}

	public String getReportMemo() {
		return reportMemo;
	}

	public void setReportMemo(String reportMemo) {
		this.reportMemo = reportMemo;
	}

	public int getMonthPay() {
		return monthPay;
	}

	public void setMonthPay(int monthPay) {
		this.monthPay = monthPay;
	}

	public String getMonthPayShow() {
		if(monthPay==1){
			return "月结 ：已付款";
		}
		if(monthPay==3){
			return "月结 ：待付款";
		}
		return "";
	}

	public OrderInvoiceDTO getMonthInvoice() {
		return monthInvoice;
	}

	public void setMonthInvoice(OrderInvoiceDTO monthInvoice) {
		this.monthInvoice = monthInvoice;
	}

	public BigDecimal getOrderAmountPrice() {
		return OrderUtil.toZero(orderAmount).subtract(OrderUtil.toZero(discountAmount)).subtract(OrderUtil.toZero(shopDisAmount));
	}

	public void setOrderAmountPrice(BigDecimal orderAmountPrice) {
		this.orderAmountPrice = orderAmountPrice;
	}

	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public Integer getIsRemind() {
		if(isRemind!=null && isRemind==2){
			isRemind=1;
		}

		return isRemind;
	}

	public void setIsRemind(Integer isRemind) {
		this.isRemind = isRemind;
	}

	public int getIndustryField() {
		return industryField;
	}

	public void setIndustryField(int industryField) {
		this.industryField = industryField;
	}

	public String getSampleCode() {
		return sampleCode;
	}

	public void setSampleCode(String sampleCode) {
		this.sampleCode = sampleCode;
	}

	public String getSampleId() {
		return sampleId;
	}

	public void setSampleId(String sampleId) {
		this.sampleId = sampleId;
	}

	public int getPriceType() {
		return priceType;
	}

	public void setPriceType(int priceType) {
		this.priceType = priceType;
	}

	public BigDecimal getNewDiscountAmountNum() {
		return newDiscountAmountNum;
	}

	public void setNewDiscountAmountNum(BigDecimal newDiscountAmountNum) {
		this.newDiscountAmountNum = newDiscountAmountNum;
	}

	public int getNewTemplate() {
		return newTemplate;
	}

	public void setNewTemplate(int newTemplate) {
		this.newTemplate = newTemplate;
	}

	public PortalInquiryBaseDTO getInquiryBaseDTO() {
		return inquiryBaseDTO;
	}

	public void setInquiryBaseDTO(PortalInquiryBaseDTO inquiryBaseDTO) {
		this.inquiryBaseDTO = inquiryBaseDTO;
	}

	public String getItemNameStr() {
		return itemNameStr;
	}

	public void setItemNameStr(String itemNameStr) {
		this.itemNameStr = itemNameStr;
	}

	public String getCsPhone() {
		return csPhone;
	}

	public void setCsPhone(String csPhone) {
		this.csPhone = csPhone;
	}

	public Long getCustIdReq() {
		return custIdReq;
	}

	public void setCustIdReq(Long custIdReq) {
		this.custIdReq = custIdReq;
	}

	public List<OrderAttachmentDTO> getQuotationFileList() {
		return quotationFileList;
	}

	public void setQuotationFileList(List<OrderAttachmentDTO> quotationFileList) {
		this.quotationFileList = quotationFileList;
	}

	public String getStageTime() {
		return stageTime;
	}

	public void setStageTime(String stageTime) {
		this.stageTime = stageTime;
	}

	public OrderOperatorLogDTO getLastConfirmLog() {
		return lastConfirmLog;
	}

	public void setLastConfirmLog(OrderOperatorLogDTO lastConfirmLog) {
		this.lastConfirmLog = lastConfirmLog;
	}

	public String getToPayAmount() {
		return toPayAmount;
	}

	public void setToPayAmount(String toPayAmount) {
		this.toPayAmount = toPayAmount;
	}

	public String getOffPayAmount() {
		return offPayAmount;
	}

	public void setOffPayAmount(String offPayAmount) {
		this.offPayAmount = offPayAmount;
	}

	public String getOnlinePayAmount() {
		return onlinePayAmount;
	}

	public void setOnlinePayAmount(String onlinePayAmount) {
		this.onlinePayAmount = onlinePayAmount;
	}



	public List<OrderAttributeDTO> getReportLuaList() {
		return reportLuaList;
	}

	public void setReportLuaList(List<OrderAttributeDTO> reportLuaList) {
		this.reportLuaList = reportLuaList;
	}

	public List<OrderAttributeDTO> getReportFormList() {
		return reportFormList;
	}

	public void setReportFormList(List<OrderAttributeDTO> reportFormList) {
		this.reportFormList = reportFormList;
	}

	public List<OrderAttributeDTO> getDiscountAmountNumList() {
		return discountAmountNumList;
	}

	public void setDiscountAmountNumList(List<OrderAttributeDTO> discountAmountNumList) {
		this.discountAmountNumList = discountAmountNumList;
	}

	public List<OrderAttributeDTO> getLabList() {
		return labList;
	}

	public void setLabList(List<OrderAttributeDTO> labList) {
		this.labList = labList;
	}

	public BigDecimal getExchangeRate() {
		return exchangeRate;
	}

	public void setExchangeRate(BigDecimal exchangeRate) {
		this.exchangeRate = exchangeRate;
	}

	public String getCurrencyMark() {
		return currencyMark;
	}

	public void setCurrencyMark(String currencyMark) {
		this.currencyMark = currencyMark;
	}

	public List<CustomerIdDTO> getCustomerList() {
		return customerList;
	}

	public void setCustomerList(List<CustomerIdDTO> customerList) {
		this.customerList = customerList;
	}

	public BigDecimal getTaxRates() {
		return taxRates;
	}

	public void setTaxRates(BigDecimal taxRates) {
		this.taxRates = taxRates;
	}

	public boolean isMultiType() {
		return multiType;
	}

	public void setMultiType(boolean multiType) {
		this.multiType = multiType;
	}

	public TfsOrderDTO getTfsOrder() {
		return tfsOrder;
	}

	public void setTfsOrder(TfsOrderDTO tfsOrder) {
		this.tfsOrder = tfsOrder;
	}
}