package com.sgs.ecom.member.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.util.serializer.PriceNumFormatSerializer;

import java.math.BigDecimal;

public class OrderBaseOtherDTO {
    private BigDecimal discountAmountNum;
    private BigDecimal newDiscountAmountNum;
    private BigDecimal urgentAmountNum;
    private BigDecimal reportLua=BigDecimal.ZERO;
    private BigDecimal reportForm=BigDecimal.ZERO;
    private BigDecimal labName=BigDecimal.ZERO;
    private int memberFlg;
    public BigDecimal getDiscountAmountNum() {
        return discountAmountNum;
    }

    public void setDiscountAmountNum(BigDecimal discountAmountNum) {
        this.discountAmountNum = discountAmountNum;
    }

    public BigDecimal getUrgentAmountNum() {
        return urgentAmountNum;
    }

    public BigDecimal getNewDiscountAmountNum() {
        return newDiscountAmountNum;
    }

    public void setNewDiscountAmountNum(BigDecimal newDiscountAmountNum) {
        this.newDiscountAmountNum = newDiscountAmountNum;
    }

    public void setUrgentAmountNum(BigDecimal urgentAmountNum) {
        this.urgentAmountNum = urgentAmountNum;
    }
    @JsonSerialize(using = PriceNumFormatSerializer.class)
    public BigDecimal getReportLua() {
        return reportLua;
    }

    public void setReportLua(BigDecimal reportLua) {
        this.reportLua = reportLua;
    }
    @JsonSerialize(using = PriceNumFormatSerializer.class)
    public BigDecimal getReportForm() {
        return reportForm;
    }

    public void setReportForm(BigDecimal reportForm) {
        this.reportForm = reportForm;
    }
    @JsonSerialize(using = PriceNumFormatSerializer.class)
    public BigDecimal getLabName() {
        return labName;
    }

    public void setLabName(BigDecimal labName) {
        this.labName = labName;
    }

    public int getMemberFlg() {
        return memberFlg;
    }

    public void setMemberFlg(int memberFlg) {
        this.memberFlg = memberFlg;
    }


}
