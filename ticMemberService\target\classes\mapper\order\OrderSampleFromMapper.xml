<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.order.dao.OrderSampleFromMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.bo.OrderSampleFrom" >
    <id column="FORM_ID" property="formId" jdbcType="BIGINT" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="SAMPLE_NO" property="sampleNo" jdbcType="VARCHAR" />
    <result column="GROUP_NO" property="groupNo" jdbcType="VARCHAR" />
    <result column="SAMPLE_KEY_NAME" property="sampleKeyName" jdbcType="VARCHAR" />
    <result column="SAMPLE_KEY" property="sampleKey" jdbcType="VARCHAR" />
    <result column="SAMPLE_VALUE" property="sampleValue" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="TINYINT" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
    <result column="IS_REPORT_SHOW" property="isReportShow" jdbcType="TINYINT" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="ENUM_CONFIG" property="enumConfig" jdbcType="VARCHAR" />
    <result column="SAMPLE_EXPLAIN" property="sampleExplain" jdbcType="VARCHAR" />
    <result column="SAMPLE_EXPLAIN_EN" property="sampleExplainEn" jdbcType="VARCHAR" />
    <result column="AREA_CODE" property="areaCode" jdbcType="VARCHAR" />
    <result column="LUA" property="lua" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    FORM_ID, ORDER_NO, SAMPLE_NO, GROUP_NO, SAMPLE_KEY_NAME, SAMPLE_KEY, SAMPLE_VALUE, 
    STATE, CREATE_DATE, STATE_DATE,IS_REPORT_SHOW,remark,ENUM_CONFIG,SAMPLE_EXPLAIN,SAMPLE_EXPLAIN_EN,AREA_CODE,LUA
  </sql>





  <!--自定义 -->
  <resultMap id="resultDTO" type="com.sgs.ecom.member.dto.order.OrderSampleFromDTO" >
    <id column="FORM_ID" property="formId" jdbcType="BIGINT" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="SAMPLE_NO" property="sampleNo" jdbcType="VARCHAR" />
    <result column="GROUP_NO" property="groupNo" jdbcType="VARCHAR" />
    <result column="SAMPLE_KEY_NAME" property="sampleKeyName" jdbcType="VARCHAR" />
    <result column="SAMPLE_KEY" property="sampleKey" jdbcType="VARCHAR" />
    <result column="SAMPLE_VALUE" property="sampleValue" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="TINYINT" />
    <result column="SORT_SHOW" property="sortShow" jdbcType="INTEGER" />
    <result column="IS_REPORT_SHOW" property="isReportShow" jdbcType="TINYINT" />
    <result column="SAMPLE_EXPLAIN" property="sampleExplain" jdbcType="VARCHAR" />
    <result column="SAMPLE_EXPLAIN_EN" property="sampleExplainEn" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="ENUM_CONFIG" property="enumConfig" jdbcType="VARCHAR" />
    <result column="AREA_CODE" property="areaCode" jdbcType="VARCHAR" />
    <result column="LUA" property="lua" jdbcType="VARCHAR" />
    <result column="SAMPLE_GROUP" property="sampleGroup" jdbcType="VARCHAR" />
    <result column="REMARK_ENUM_CONFIG" property="remarkEnumConfig" jdbcType="VARCHAR" />
  </resultMap>

  <sql id="sqlListDTO" >
    FORM_ID, ORDER_NO, SAMPLE_NO, GROUP_NO, SAMPLE_KEY_NAME, SAMPLE_KEY, SAMPLE_VALUE,
        STATE,SORT_SHOW,IS_REPORT_SHOW,SAMPLE_EXPLAIN,remark,ENUM_CONFIG,SAMPLE_EXPLAIN,SAMPLE_EXPLAIN_EN,AREA_CODE,LUA,
        SAMPLE_GROUP,REMARK_ENUM_CONFIG
  </sql>

  <select id="selectListByMap" resultMap="resultDTO" >
    select
    <include refid="sqlListDTO" />
    from ORDER_SAMPLE_FROM
    <include refid="baseQueryWhere"/>
  </select>
  <sql id="baseQueryWhere">
   <where>
    <if test="orderNo != null">
      AND ORDER_NO=#{orderNo}
    </if>
    <if test="groupNo != null">
      AND GROUP_NO=#{groupNo}
    </if>
     <if test="state != null">
       AND STATE=#{state}
     </if>
     <if test="sampleKeyList != null">
       AND SAMPLE_KEY in
       <foreach collection="sampleKeyList" index="index" item="item" open="(" separator="," close=")">
         #{item}
       </foreach>
     </if>

   </where>
  </sql>

  <select id="selectListByVO" resultMap="resultDTO" >
    select
    <include refid="sqlListDTO" />
    from ORDER_SAMPLE_FROM
    <include refid="baseQueryVOWhere"/>
  </select>
  <sql id="baseQueryVOWhere">
    <where>
      <if test="orderNo != null">
        AND ORDER_NO=#{orderNo}
      </if>
      <if test="groupNo != null">
        AND GROUP_NO=#{groupNo}
      </if>
      <if test="state != null">
        AND STATE=#{state}
      </if>
    </where>
  </sql>

  <insert id="insertForeach"  >
    insert into ORDER_SAMPLE_FROM
    (ORDER_NO, SAMPLE_NO, GROUP_NO, SAMPLE_KEY_NAME, SAMPLE_KEY, SAMPLE_VALUE,  STATE,SORT_SHOW,IS_REPORT_SHOW,SAMPLE_EXPLAIN,remark,ENUM_CONFIG,SAMPLE_EXPLAIN_EN,AREA_CODE,LUA)
    values
    <foreach collection ="list" item="item" index= "index" separator =",">
      (
      #{item.orderNo}, #{item.sampleNo},#{item.groupNo},#{item.sampleKeyName},#{item.sampleKey},
       #{item.sampleValue},#{item.state},#{item.sortShow},#{item.isReportShow},#{item.sampleExplain},#{item.remark},#{item.enumConfig},#{item.sampleExplainEn},#{item.areaCode},#{item.lua}
      )
    </foreach>
  </insert>

  <insert id="insertForeachAddCenter"  >
    insert into ORDER_SAMPLE_FROM
    (ORDER_NO, SAMPLE_NO, GROUP_NO, SAMPLE_KEY_NAME, SAMPLE_KEY, SAMPLE_VALUE,  STATE,SORT_SHOW,IS_REPORT_SHOW,SAMPLE_EXPLAIN,remark,ENUM_CONFIG,
     SAMPLE_EXPLAIN_EN,AREA_CODE,LUA,SAMPLE_GROUP,REMARK_ENUM_CONFIG)
    values
    <foreach collection ="list" item="item" index= "index" separator =",">
      (
      #{item.orderNo}, #{item.sampleNo},#{item.groupNo},#{item.sampleKeyName},#{item.sampleKey},
      #{item.sampleValue},#{item.state},#{item.sortShow},#{item.isReportShow},#{item.sampleExplain},#{item.remark},#{item.enumConfig},#{item.sampleExplainEn},#{item.areaCode},
      #{item.lua},#{item.sampleGroup},#{item.remarkEnumConfig}
      )
    </foreach>
  </insert>

  <insert id="copySampleFormBySql"  >
    insert into ORDER_SAMPLE_FROM
    (ORDER_NO, SAMPLE_NO, GROUP_NO, SAMPLE_KEY_NAME, SAMPLE_KEY, SAMPLE_VALUE,  STATE,SORT_SHOW,IS_REPORT_SHOW,SAMPLE_EXPLAIN,remark,ENUM_CONFIG,SAMPLE_EXPLAIN_EN,AREA_CODE,LUA,SAMPLE_GROUP)
    select #{newOrderNo},SAMPLE_NO,#{newGroupNo},SAMPLE_KEY_NAME,SAMPLE_KEY,SAMPLE_VALUE,STATE,SORT_SHOW,IS_REPORT_SHOW,SAMPLE_EXPLAIN,remark,ENUM_CONFIG,SAMPLE_EXPLAIN_EN,AREA_CODE,LUA,SAMPLE_GROUP
    from ORDER_SAMPLE_FROM where GROUP_NO = #{oldGroupNo} and ORDER_NO=#{orderNo} and state=1
  </insert>
  <update id="updateForm">
    update ORDER_SAMPLE_FROM set STATE=0 where ORDER_NO=#{orderNo}
  </update>
</mapper>