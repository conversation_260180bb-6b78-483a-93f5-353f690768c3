package com.sgs.ecom.member.bo;


import javax.persistence.Id;

public class OrderSampleFrom {
    public static  final String SAMPLE_NAME="SAMPLE_NAME";
    public static  final String PRODUCT_INFO="PRODUCT_INFO";
    public static  final String PRODUCT_BATCH="PRODUCT_BATCH";
    public static  final String MATERIAL_GRADE="MATERIAL_GRADE";
    public static  final String REMARK="REMARK";

    @Id
    private Long formId;

    private String orderNo;

    private String sampleNo;

    private String groupNo;

    private String sampleKeyName;

    private String sampleKey;

    private String sampleValue;

    private int state;

    private String createDate;

    private String stateDate;

    private int sortShow;
    private Integer isReportShow;

    private String  sampleExplain;
    private String  remark;
    private String  enumConfig;

    private String sampleExplainEn;
    private String areaCode;
    private String lua;
    private String sampleGroup;
    private String remarkEnumConfig;

    public String getLua() {
        return lua;
    }

    public void setLua(String lua) {
        this.lua = lua;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getSampleExplainEn() {
        return sampleExplainEn;
    }

    public void setSampleExplainEn(String sampleExplainEn) {
        this.sampleExplainEn = sampleExplainEn;
    }

    public String getEnumConfig() {
        return enumConfig;
    }

    public void setEnumConfig(String enumConfig) {
        this.enumConfig = enumConfig;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getIsReportShow() {
        return isReportShow;
    }

    public void setIsReportShow(Integer isReportShow) {
        this.isReportShow = isReportShow;
    }

    public Long getFormId() {
        return formId;
    }

    public void setFormId(Long formId) {
        this.formId = formId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo == null ? null : orderNo.trim();
    }

    public String getSampleNo() {
        return sampleNo;
    }

    public void setSampleNo(String sampleNo) {
        this.sampleNo = sampleNo == null ? null : sampleNo.trim();
    }

    public String getGroupNo() {
        return groupNo;
    }

    public void setGroupNo(String groupNo) {
        this.groupNo = groupNo == null ? null : groupNo.trim();
    }

    public String getSampleKeyName() {
        return sampleKeyName;
    }

    public void setSampleKeyName(String sampleKeyName) {
        this.sampleKeyName = sampleKeyName == null ? null : sampleKeyName.trim();
    }

    public String getSampleKey() {
        return sampleKey;
    }

    public void setSampleKey(String sampleKey) {
        this.sampleKey = sampleKey == null ? null : sampleKey.trim();
    }

    public String getSampleValue() {
        return sampleValue;
    }

    public void setSampleValue(String sampleValue) {
        this.sampleValue = sampleValue == null ? null : sampleValue.trim();
    }

    public void setState(int state) {
        this.state = state;
    }


    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getStateDate() {
        return stateDate;
    }

    public void setStateDate(String stateDate) {
        this.stateDate = stateDate;
    }

    public int getState() {
        return state;
    }

    public int getSortShow() {
        return sortShow;
    }

    public void setSortShow(int sortShow) {
        this.sortShow = sortShow;
    }

    public String getSampleExplain() {
        return sampleExplain;
    }

    public void setSampleExplain(String sampleExplain) {
        this.sampleExplain = sampleExplain;
    }

    public String getSampleGroup() {
        return sampleGroup;
    }

    public void setSampleGroup(String sampleGroup) {
        this.sampleGroup = sampleGroup;
    }

    public String getRemarkEnumConfig() {
        return remarkEnumConfig;
    }

    public void setRemarkEnumConfig(String remarkEnumConfig) {
        this.remarkEnumConfig = remarkEnumConfig;
    }
}