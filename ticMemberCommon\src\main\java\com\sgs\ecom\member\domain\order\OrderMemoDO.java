package com.sgs.ecom.member.domain.order;

import com.platform.util.ValidationUtil;
import com.sgs.base.BaseCopyObj;
import com.sgs.ecom.member.constant.BaseConstant;
import com.sgs.ecom.member.dto.user.UserDTO;
import com.sgs.ecom.member.entity.order.OrderMemo;

import javax.validation.Valid;
import java.util.Date;

public class OrderMemoDO extends OrderMemo {


    private BaseCopyObj baseCopy = new BaseCopyObj();



    public OrderMemo addOtherOrderMemo(String orderNo, UserDTO userDTO) {
        OrderMemo orderMemo =  initOrderMemo(orderNo,userDTO);
        orderMemo.setMemoCode("formMemo");
        orderMemo.setMemoInfo(BaseConstant.HL_NB_MEMO_NAME);
        orderMemo.setPersonCode("系统");
        return orderMemo;
    }

    private OrderMemo initOrderMemo(String orderNo, UserDTO userDTO) {
        OrderMemo orderMemo = new OrderMemoDO();
        orderMemo.setOrderNo(orderNo);
        orderMemo.setState(1);
        orderMemo.setCreateDate(new Date());
        orderMemo.setStateDate(new Date());
        return orderMemo;
    }
}
