package com.sgs.ecom.member.dto.pdf;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseCopyObj;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.dto.OrderBaseOtherDTO;
import com.sgs.ecom.member.dto.base.BaseOrderDTO;
import com.sgs.ecom.member.util.serializer.PriceNumFormatSerializer;

import java.math.BigDecimal;

/**quotation
 * <AUTHOR>
 */
public class QuotationDTO {
    @ApiAnno(groups={BaseOrderFilter.WordQuotation.class})
    private BigDecimal discountNum;
    @ApiAnno(groups={BaseOrderFilter.WordQuotation.class})
    private String orderNo;
    @ApiAnno(groups={BaseOrderFilter.WordQuotation.class})
    private String realAmount;
    @ApiAnno(groups={BaseOrderFilter.WordQuotation.class})
    private String stateDate;
    @ApiAnno(groups={BaseOrderFilter.WordQuotation.class})
    private String sysPersonDTOStr;
    @ApiAnno(groups={BaseOrderFilter.WordQuotation.class})
    private String testCycleMemo;
    @ApiAnno(groups={BaseOrderFilter.WordQuotation.class})
    private String testCycleName;
    @ApiAnno(groups={BaseOrderFilter.WordQuotation.class})
    private String total;
    @JsonSerialize(using = PriceNumFormatSerializer.class)
    @ApiAnno(groups={BaseOrderFilter.WordQuotation.class})
    private BigDecimal taxAmount;
    @ApiAnno(groups={BaseOrderFilter.WordQuotation.class})
    private BigDecimal discountAmountNum;
    @ApiAnno(groups={BaseOrderFilter.WordQuotation.class})
    private BigDecimal newDiscountAmountNum;
    @ApiAnno(groups={BaseOrderFilter.WordQuotation.class})
    private BigDecimal urgentAmountNum;
    @ApiAnno(groups={BaseOrderFilter.WordQuotation.class})
    private BigDecimal reportLua=BigDecimal.ZERO;
    @ApiAnno(groups={BaseOrderFilter.WordQuotation.class})
    private BigDecimal reportForm=BigDecimal.ZERO;
    @ApiAnno(groups={BaseOrderFilter.WordQuotation.class})
    private BigDecimal labName=BigDecimal.ZERO;
    @ApiAnno(groups={BaseOrderFilter.WordQuotation.class})
    private String sampleStr;
    @ApiAnno(groups={BaseOrderFilter.WordQuotation.class})
    private String sampleCategoryName;
    @ApiAnno(groups={BaseOrderFilter.WordQuotation.class})
    private BigDecimal taxRates;
    private Integer priceType;
    private BaseOrderDTO orderDTO;




    public static void addOther(QuotationDTO quotationDTO,OrderBaseOtherDTO orderBaseOtherDTO){
        BaseCopyObj baseCopyObj=new BaseCopyObj();
        baseCopyObj.copyWithNull(quotationDTO,orderBaseOtherDTO);
    }

    public BigDecimal getDiscountNum() {
        return discountNum;
    }

    public void setDiscountNum(BigDecimal discountNum) {
        this.discountNum = discountNum;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getRealAmount() {
        return realAmount;
    }

    public void setRealAmount(String realAmount) {
        this.realAmount = realAmount;
    }

    public String getStateDate() {
        return stateDate;
    }

    public void setStateDate(String stateDate) {
        this.stateDate = stateDate;
    }

    public String getSysPersonDTOStr() {
        return sysPersonDTOStr;
    }

    public void setSysPersonDTOStr(String sysPersonDTOStr) {
        this.sysPersonDTOStr = sysPersonDTOStr;
    }

    public String getTestCycleMemo() {
        return testCycleMemo;
    }

    public void setTestCycleMemo(String testCycleMemo) {
        this.testCycleMemo = testCycleMemo;
    }

    public String getTestCycleName() {
        return testCycleName;
    }

    public void setTestCycleName(String testCycleName) {
        this.testCycleName = testCycleName;
    }


    public String getTotal() {
        return total;
    }

    public void setTotal(String total) {
        this.total = total;
    }

    public BigDecimal getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
    }

    public BigDecimal getDiscountAmountNum() {
        return discountAmountNum;
    }

    public void setDiscountAmountNum(BigDecimal discountAmountNum) {
        this.discountAmountNum = discountAmountNum;
    }

    public BigDecimal getNewDiscountAmountNum() {
        return newDiscountAmountNum;
    }

    public void setNewDiscountAmountNum(BigDecimal newDiscountAmountNum) {
        this.newDiscountAmountNum = newDiscountAmountNum;
    }

    public BigDecimal getUrgentAmountNum() {
        return urgentAmountNum;
    }

    public void setUrgentAmountNum(BigDecimal urgentAmountNum) {
        this.urgentAmountNum = urgentAmountNum;
    }

    public BigDecimal getReportLua() {
        return reportLua;
    }

    public void setReportLua(BigDecimal reportLua) {
        this.reportLua = reportLua;
    }

    public BigDecimal getReportForm() {
        return reportForm;
    }

    public void setReportForm(BigDecimal reportForm) {
        this.reportForm = reportForm;
    }

    public BigDecimal getLabName() {
        return labName;
    }

    public void setLabName(BigDecimal labName) {
        this.labName = labName;
    }

    public String getSampleStr() {
        return sampleStr;
    }

    public void setSampleStr(String sampleStr) {
        this.sampleStr = sampleStr;
    }

    public String getSampleCategoryName() {
        return sampleCategoryName;
    }

    public void setSampleCategoryName(String sampleCategoryName) {
        this.sampleCategoryName = sampleCategoryName;
    }

    public BigDecimal getTaxRates() {
        return taxRates;
    }

    public void setTaxRates(BigDecimal taxRates) {
        this.taxRates = taxRates;
    }

    public Integer getPriceType() {
        return priceType;
    }

    public void setPriceType(Integer priceType) {
        this.priceType = priceType;
    }

    public BaseOrderDTO getOrderDTO() {
        return orderDTO;
    }

    public void setOrderDTO(BaseOrderDTO orderDTO) {
        this.orderDTO = orderDTO;
    }
}
