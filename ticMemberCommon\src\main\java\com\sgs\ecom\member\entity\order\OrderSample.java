package com.sgs.ecom.member.entity.order;



public class OrderSample {

    private String createDate;

    private String productBatch;

    private String groupNo;

    private String sampleName;

    private String stateDate;

    private String sampleNameEn;

    private Integer state;

    private String orderNo;

    private String productInfo;

    private String materialGrade;

    private String remark;

    private Long sampleId;

    private String sampleNo;
    private String sampleNameCn;



    private String supplierName;
    private String buyersName;

    private String remarkEn;
    private String buyersNameEn;
    private String supplierNameEn;

    private String productBatchEn;
    private String productInfoEn;
    private String materialGradeEn;
    private String lotNo;
    private String lotNoEn;
    private String producer;
    private String producerEn;
    private String userSampleNo;

    private String sampleShapeCode;

    private String sampleCategoryCode;



    public void setCreateDate(String createDate){
        this.createDate=createDate;
    }
    public String getCreateDate(){
        return this.createDate;
    }


    public void setProductBatch(String productBatch){
        this.productBatch=productBatch;
    }
    public String getProductBatch(){
        return this.productBatch;
    }


    public void setGroupNo(String groupNo){
        this.groupNo=groupNo;
    }
    public String getGroupNo(){
        return this.groupNo;
    }


    public void setSampleName(String sampleName){
        this.sampleName=sampleName;
    }
    public String getSampleName(){
        return this.sampleName;
    }


    public void setStateDate(String stateDate){
        this.stateDate=stateDate;
    }
    public String getStateDate(){
        return this.stateDate;
    }


    public void setSampleNameEn(String sampleNameEn){
        this.sampleNameEn=sampleNameEn;
    }
    public String getSampleNameEn(){
        return this.sampleNameEn;
    }


    public void setState(Integer state){
        this.state=state;
    }
    public Integer getState(){
        return this.state;
    }


    public void setOrderNo(String orderNo){
        this.orderNo=orderNo;
    }
    public String getOrderNo(){
        return this.orderNo;
    }


    public void setProductInfo(String productInfo){
        this.productInfo=productInfo;
    }
    public String getProductInfo(){
        return this.productInfo;
    }


    public void setMaterialGrade(String materialGrade){
        this.materialGrade=materialGrade;
    }
    public String getMaterialGrade(){
        return this.materialGrade;
    }


    public void setRemark(String remark){
        this.remark=remark;
    }
    public String getRemark(){
        return this.remark;
    }


    public void setSampleId(Long sampleId){
        this.sampleId=sampleId;
    }
    public Long getSampleId(){
        return this.sampleId;
    }

    public String getSampleNo() {
        return sampleNo;
    }

    public void setSampleNo(String sampleNo) {
        this.sampleNo = sampleNo;
    }

    public String getSampleNameCn() {
        return sampleNameCn;
    }

    public void setSampleNameCn(String sampleNameCn) {
        this.sampleNameCn = sampleNameCn;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getBuyersName() {
        return buyersName;
    }

    public void setBuyersName(String buyersName) {
        this.buyersName = buyersName;
    }

    public String getRemarkEn() {
        return remarkEn;
    }

    public void setRemarkEn(String remarkEn) {
        this.remarkEn = remarkEn;
    }

    public String getBuyersNameEn() {
        return buyersNameEn;
    }

    public void setBuyersNameEn(String buyersNameEn) {
        this.buyersNameEn = buyersNameEn;
    }

    public String getSupplierNameEn() {
        return supplierNameEn;
    }

    public void setSupplierNameEn(String supplierNameEn) {
        this.supplierNameEn = supplierNameEn;
    }

    public String getProductBatchEn() {
        return productBatchEn;
    }

    public void setProductBatchEn(String productBatchEn) {
        this.productBatchEn = productBatchEn;
    }

    public String getProductInfoEn() {
        return productInfoEn;
    }

    public void setProductInfoEn(String productInfoEn) {
        this.productInfoEn = productInfoEn;
    }

    public String getMaterialGradeEn() {
        return materialGradeEn;
    }

    public void setMaterialGradeEn(String materialGradeEn) {
        this.materialGradeEn = materialGradeEn;
    }

    public String getLotNo() {
        return lotNo;
    }

    public void setLotNo(String lotNo) {
        this.lotNo = lotNo;
    }

    public String getLotNoEn() {
        return lotNoEn;
    }

    public void setLotNoEn(String lotNoEn) {
        this.lotNoEn = lotNoEn;
    }

    public String getProducer() {
        return producer;
    }

    public void setProducer(String producer) {
        this.producer = producer;
    }

    public String getProducerEn() {
        return producerEn;
    }

    public void setProducerEn(String producerEn) {
        this.producerEn = producerEn;
    }

    public String getUserSampleNo() {
        return userSampleNo;
    }

    public void setUserSampleNo(String userSampleNo) {
        this.userSampleNo = userSampleNo;
    }

    public String getSampleShapeCode() {
        return sampleShapeCode;
    }

    public void setSampleShapeCode(String sampleShapeCode) {
        this.sampleShapeCode = sampleShapeCode;
    }

    public String getSampleCategoryCode() {
        return sampleCategoryCode;
    }

    public void setSampleCategoryCode(String sampleCategoryCode) {
        this.sampleCategoryCode = sampleCategoryCode;
    }
}
