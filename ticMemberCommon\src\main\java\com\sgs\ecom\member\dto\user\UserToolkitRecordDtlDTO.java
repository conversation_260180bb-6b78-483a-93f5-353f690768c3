package com.sgs.ecom.member.dto.user;
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import com.platform.util.json.TimeFormatSerializer; 
import com.platform.annotation.ApiAnno; 
import com.platform.annotation.BeanAnno; 
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.vo.VOUserToolkitRecordDtl;

public class UserToolkitRecordDtlDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select ATTR_VALUE,CREATE_DATE,ATTR_RATE,DTL_ID,ATTR_NAME,RECORD_ID,TOOL_CODE,ATTR_CODE from USER_TOOLKIT_RECORD_DTL"; 
 
 
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="ATTR_VALUE", getName="getAttrValue", setName="setAttrValue")
 	private String attrValue;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="ATTR_RATE", getName="getAttrRate", setName="setAttrRate")
 	private BigDecimal attrRate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="DTL_ID", getName="getDtlId", setName="setDtlId")
 	private long dtlId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="ATTR_NAME", getName="getAttrName", setName="setAttrName")
 	private String attrName;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="RECORD_ID", getName="getRecordId", setName="setRecordId")
 	private long recordId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="TOOL_CODE", getName="getToolCode", setName="setToolCode")
 	private String toolCode;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="ATTR_CODE", getName="getAttrCode", setName="setAttrCode")
 	private String attrCode;

	//成分含量/号型规则集合/洗涤方式集合
	List<UserToolkitRecordDtlDTO> items;


	public List<UserToolkitRecordDtlDTO> getItems() {
		return items;
	}

	public void setItems(List<UserToolkitRecordDtlDTO> items) {
		this.items = items;
	}

	public void setAttrValue(String attrValue){
 		 this.attrValue=attrValue;
 	}
 	public String getAttrValue(){
 		 return this.attrValue;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setAttrRate(BigDecimal attrRate){
 		 this.attrRate=attrRate;
 	}
 	public BigDecimal getAttrRate(){
 		 return this.attrRate;
 	}
 
 	 
 	public void setDtlId(long dtlId){
 		 this.dtlId=dtlId;
 	}
 	public long getDtlId(){
 		 return this.dtlId;
 	}
 
 	 
 	public void setAttrName(String attrName){
 		 this.attrName=attrName;
 	}
 	public String getAttrName(){
 		 return this.attrName;
 	}
 
 	 
 	public void setRecordId(long recordId){
 		 this.recordId=recordId;
 	}
 	public long getRecordId(){
 		 return this.recordId;
 	}
 
 	 
 	public void setToolCode(String toolCode){
 		 this.toolCode=toolCode;
 	}
 	public String getToolCode(){
 		 return this.toolCode;
 	}
 
 	 
 	public void setAttrCode(String attrCode){
 		 this.attrCode=attrCode;
 	}
 	public String getAttrCode(){
 		 return this.attrCode;
 	}
 
 	 
}