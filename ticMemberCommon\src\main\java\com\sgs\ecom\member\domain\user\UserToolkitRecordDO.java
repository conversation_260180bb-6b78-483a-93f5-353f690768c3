package com.sgs.ecom.member.domain.user;
 
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseCopyObj;
import com.sgs.ecom.member.dto.user.UserDTO;
import com.sgs.ecom.member.entity.user.UserToolkitRecord;
import com.sgs.ecom.member.enums.UserToolKitType;
import com.sgs.ecom.member.request.report.ReportAuthReq;
import com.sgs.ecom.member.request.report.ReportAuthRsp;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.sql.Timestamp;
import java.util.Date;

public class UserToolkitRecordDO extends UserToolkitRecord {
	private BaseCopyObj baseCopy = new BaseCopyObj();


	public UserToolkitRecord addUserToolkitRecord(ReportAuthReq reportAuthReq, String qrcode, UserDTO userDTO, int type, String userToolKitNo, UserToolKitType reportAuthenticity, ReportAuthRsp reportAuthRsp) {
		UserToolkitRecord userToolkitRecord= init(userDTO,type,userToolKitNo,reportAuthenticity,reportAuthReq);
		userToolkitRecord.setShowMode(reportAuthRsp.isSuccess()?"2":"1");//针对报告验真这个字段主要用于记录验证是否成功 1-失败 2-成功，针对水洗标不是这个用途
		return userToolkitRecord;
	}

	private UserToolkitRecord init(UserDTO userDTO, int type, String userToolKitNo, UserToolKitType reportAuthenticity,ReportAuthReq reportAuthReq) {
		UserToolkitRecord userToolkitRecord = new UserToolkitRecordDO();
		userToolkitRecord.setUserId(userDTO.getUserId());
		userToolkitRecord.setToolCode(reportAuthenticity.getIndex());
		userToolkitRecord.setMakeNo(userToolKitNo);
		userToolkitRecord.setMakeType(String.valueOf(type));
		userToolkitRecord.setMakeTitle(reportAuthReq.getFileName());
		userToolkitRecord.setState(1);
		userToolkitRecord.setCreateDate(new Timestamp(new Date().getTime()));
		userToolkitRecord.setStateDate(new Timestamp(new Date().getTime()));
		return userToolkitRecord;
	}
}