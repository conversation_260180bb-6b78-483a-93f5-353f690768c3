package com.sgs.ecom.member.enums;
/**
 * @Description :返回给前端的标识位
 * <AUTHOR>
 * @Date  2024/6/19

 **/
public enum SplitOrderType {

    NEW_SPLIT("NEW_SPLIT", 1),
    OLD_SPLIT("OLD_SPLIT", 2);

    // 成员变量
    private String name;
    private int index;
    // 构造方法
    private SplitOrderType(String name, int index) {
        this.name = name;  
        this.index = index;  
    }  
   
    // 普通方法  
    public static String getName(int index) {  
        for (SplitOrderType c : SplitOrderType.values()) {
            if (c.getIndex() == index) {  
                return c.name;  
            }  
        }  
        return null;  
    }  
    // get set 方法  
    public String getName() {  
        return name;  
    }  

    public int getIndex() {  
        return index;  
    }  
}
