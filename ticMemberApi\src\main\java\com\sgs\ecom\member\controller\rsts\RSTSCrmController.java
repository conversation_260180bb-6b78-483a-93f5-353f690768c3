package com.sgs.ecom.member.controller.rsts;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.util.SysException;
import com.sgs.ecom.member.dto.user.UserDTO;
import com.sgs.ecom.member.request.LogReq;
import com.sgs.ecom.member.request.rsts.RstsUserReq;
import com.sgs.ecom.member.service.custom.interfaces.ICustInfoSV;
import com.sgs.ecom.member.service.order.interfaces.IOrderLogService;
import com.sgs.ecom.member.service.rsts.interfaces.IRSTSApplicationService;
import com.sgs.ecom.member.service.user.interfaces.IUserInfoService;
import com.sgs.ecom.member.service.util.interfaces.IOrderApplicationService;
import com.sgs.ecom.member.service.util.interfaces.IOrderService;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.rsts/crm")
public class RSTSCrmController extends ControllerUtil {

    @Autowired
    private ICustInfoSV custInfoSV;
    @Autowired
    private IUserInfoService userInfoService;
    @Autowired
    private IOrderService orderService;
    @Autowired
    private IOrderLogService orderLogService;
    @Autowired
    private IRSTSApplicationService irstsApplicationService;
    @Autowired
    private IOrderApplicationService orderApplicationService;

    @HystrixCommand
    @RequestMapping(value = "qryCustCs", method = { RequestMethod.POST })
    public ResultBody qryBelongCustCsByUser(@RequestBody RstsUserReq rstsUserReq) throws Exception {
        UserDTO userDTO=userInfoService.selectUserDTOByUserKey(rstsUserReq.getUserKey());
        return ResultBody.newInstance(custInfoSV.qryBelongCustCsByUser(userDTO,rstsUserReq.getCustId()));
    }




    @HystrixCommand
    @RequestMapping(value = "qryDetail", method = {RequestMethod.POST})
    public ResultBody updateDetail(
            @RequestBody RstsUserReq rstsUserReq) throws Exception {
        UserDTO userDTO=userInfoService.selectUserDTOByUserKey(rstsUserReq.getUserKey());
        return ResultBody.success(orderService.qryDetail(rstsUserReq.getOrderNo(), userDTO,rstsUserReq.getCustId(),""));
    }

    @HystrixCommand
    @RequestMapping(value = "logList", method = {RequestMethod.POST})
    public ResultBody list(
            @RequestBody LogReq logReq) throws Exception {
        UserDTO userDTO=userInfoService.selectUserDTOByUserKey(logReq.getUserKey());
        return ResultBody.newInstance(orderLogService.qryLogList(logReq, userDTO));
    }

    /**
     * @Function: form
     * @author: Xiwei_Qiu
     * @date: 2021/3/9
     **/
    @HystrixCommand
    @RequestMapping(value = "form", method = {RequestMethod.POST})
    public ResultBody form(
            @RequestBody RstsUserReq rstsUserReq) throws Exception {
        UserDTO userDTO=userInfoService.selectUserDTOByUserKey(rstsUserReq.getUserKey());
        return ResultBody.newInstance(irstsApplicationService.selectRSTSDetail(rstsUserReq, userDTO));
    }


    @HystrixCommand
    @RequestMapping(value = "express", method = { RequestMethod.POST })
    public ResultBody express (@RequestBody RstsUserReq rstsUserReq)throws Exception{
        UserDTO userDTO=userInfoService.selectUserDTOByUserKey(rstsUserReq.getUserKey());
        return ResultBody.newInstance(orderApplicationService.selectExpressMore(rstsUserReq,userDTO,null));
    }

    @HystrixCommand
    @RequestMapping(value = "expressList", method = { RequestMethod.POST })
    public ResultBody expressList(@RequestBody RstsUserReq rstsUserReq) throws Exception {
        UserDTO userDTO=userInfoService.selectUserDTOByUserKey(rstsUserReq.getUserKey());
        return ResultBody.newInstance(orderApplicationService.selectExpressList(rstsUserReq.getOrderNo(),userDTO));
    }
}
