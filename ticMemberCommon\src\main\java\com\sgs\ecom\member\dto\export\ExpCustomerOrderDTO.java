package com.sgs.ecom.member.dto.export;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.enumtool.BaseOrderStateEnum;
import com.sgs.ecom.member.enumtool.TfsStateEnum;
import org.apache.commons.lang.StringUtils;

public class ExpCustomerOrderDTO {
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String orderNo;
    private String bu;

    private String applicationCompanyNameCn;
    private String reportCompanyNameCn;
    private String applicationCompanyNameEn;
    private String reportCompanyNameEn;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String applicationCompanyNameStr;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String reportCompanyNameStr;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String orderDetailStr;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String orderSampleStr;
    private Integer state;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String stateShow;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String reportNoStr;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getApplicationCompanyNameStr() {
        if(StringUtils.isBlank(applicationCompanyNameCn)){
            return applicationCompanyNameEn;
        }
        return applicationCompanyNameCn;
    }
    public void setApplicationCompanyNameStr(String applicationCompanyNameStr) {
        this.applicationCompanyNameStr = applicationCompanyNameStr;
    }

    public String getReportCompanyNameStr() {
        if(StringUtils.isBlank(reportCompanyNameCn)){
            return  reportCompanyNameEn;
        }
        return reportCompanyNameCn;
    }

    public void setReportCompanyNameStr(String reportCompanyNameStr) {
        this.reportCompanyNameStr = reportCompanyNameStr;
    }

    public String getOrderDetailStr() {
        return orderDetailStr;
    }

    public void setOrderDetailStr(String orderDetailStr) {
        this.orderDetailStr = orderDetailStr;
    }

    public String getOrderSampleStr() {
        return orderSampleStr;
    }

    public void setOrderSampleStr(String orderSampleStr) {
        this.orderSampleStr = orderSampleStr;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getStateShow() {
        if(StringUtils.isNotBlank(bu) &&"2700".equals(bu)){
            return TfsStateEnum.getNameCh(state);
        }

        return BaseOrderStateEnum.getNameCh(state);
    }

    public void setStateShow(String stateShow) {
        this.stateShow = stateShow;
    }

    public String getReportNoStr() {
        return reportNoStr;
    }

    public void setReportNoStr(String reportNoStr) {
        this.reportNoStr = reportNoStr;
    }

    public String getApplicationCompanyNameCn() {
        return applicationCompanyNameCn;
    }

    public void setApplicationCompanyNameCn(String applicationCompanyNameCn) {
        this.applicationCompanyNameCn = applicationCompanyNameCn;
    }

    public String getReportCompanyNameCn() {
        return reportCompanyNameCn;
    }

    public void setReportCompanyNameCn(String reportCompanyNameCn) {
        this.reportCompanyNameCn = reportCompanyNameCn;
    }

    public String getApplicationCompanyNameEn() {
        return applicationCompanyNameEn;
    }

    public void setApplicationCompanyNameEn(String applicationCompanyNameEn) {
        this.applicationCompanyNameEn = applicationCompanyNameEn;
    }

    public String getReportCompanyNameEn() {
        return reportCompanyNameEn;
    }

    public void setReportCompanyNameEn(String reportCompanyNameEn) {
        this.reportCompanyNameEn = reportCompanyNameEn;
    }

    public String getBu() {
        return bu;
    }

    public void setBu(String bu) {
        this.bu = bu;
    }
}
