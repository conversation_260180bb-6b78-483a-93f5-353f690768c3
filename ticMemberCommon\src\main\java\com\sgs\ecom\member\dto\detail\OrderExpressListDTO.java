package com.sgs.ecom.member.dto.detail;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.enumtool.aplication.ExpressCodeEnum;

public class OrderExpressListDTO {
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String expressCode;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String expressNo;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String expressType;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String expressCodeShow;

    public String getExpressType() {
        return expressType;
    }

    public void setExpressType(String expressType) {
        this.expressType = expressType;
    }

    public String getExpressCode() {
        return expressCode;
    }

    public void setExpressCode(String expressCode) {
        this.expressCode = expressCode;
    }

    public String getExpressNo() {
        return expressNo;
    }

    public void setExpressNo(String expressNo) {
        this.expressNo = expressNo;
    }

    public String getExpressCodeShow() {
        expressCodeShow= ExpressCodeEnum.getNameCh(expressCode);
        return expressCodeShow==null?"":expressCodeShow;
    }
    public void setExpressCodeShow(String expressCodeShow) {
        this.expressCodeShow = expressCodeShow;
    }
}
