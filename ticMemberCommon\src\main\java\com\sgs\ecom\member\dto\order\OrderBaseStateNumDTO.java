package com.sgs.ecom.member.dto.order;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class OrderBaseStateNumDTO {

    private int state;
    private int num;
    private Integer orderType;


    public static Map<Integer,Integer> getStateMapByQryMap(Map<String, List<OrderBaseStateNumDTO>> map,Integer orderType){
        if(map.containsKey(orderType)){
            List<OrderBaseStateNumDTO> list=map.get(orderType);
            return  list.stream().collect(Collectors.toMap(E->E.getState(), E->E.getNum(), (key1, key2) -> key2));
        }
        return new HashMap<>();
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }
}
