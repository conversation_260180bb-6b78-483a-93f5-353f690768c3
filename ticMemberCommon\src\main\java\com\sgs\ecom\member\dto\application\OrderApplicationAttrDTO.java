package com.sgs.ecom.member.dto.application;

import com.alibaba.fastjson.JSON;
import com.platform.annotation.ApiAnno;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.vo.VOUserInfo;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
public class OrderApplicationAttrDTO {
	//type 0-输入框 1-前缀方形勾选框 2-圆形前缀勾选
	//isMust 0-否 1-是


	private String attrKey;
	@ApiAnno(groups={BaseQryFilter.Default.class, BaseOrderFilter.OrderApplicationOther.class,BaseOrderFilter.OrderToOther.class,BaseOrderFilter.OiqFormInfo.class})
	private String attrValue;
	@ApiAnno(groups={BaseQryFilter.Default.class, BaseOrderFilter.OrderApplicationOther.class,BaseOrderFilter.OrderToOther.class,BaseOrderFilter.OiqFormInfo.class})
	private String attrName;
	private String orderNo;
	@ApiAnno(groups={BaseQryFilter.Default.class, BaseOrderFilter.OrderApplicationOther.class,BaseOrderFilter.OrderToOther.class,BaseOrderFilter.OiqFormInfo.class})
	private String areaCode;
	@ApiAnno(groups={BaseQryFilter.Default.class, BaseOrderFilter.OrderApplicationOther.class,BaseOrderFilter.OrderToOther.class,BaseOrderFilter.OiqFormInfo.class})
	private String attrCode;
	private Integer type;
	private Integer isMust;
	private int state;
	@ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
	private String classifyType;
	@ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
	private String enumConfig;

	private String  attrTextString;
	@ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
	private Object attrText;

	private String createDate;

	public OrderApplicationAttrDTO() {
	}

	public OrderApplicationAttrDTO(String attrValue) {
		this.attrValue = attrValue;
	}

	public String getCreateDate() {
		return createDate;
	}

	public void setCreateDate(String createDate) {
		this.createDate = createDate;
	}

	public String getEnumConfig() {
		return enumConfig;
	}

	public void setEnumConfig(String enumConfig) {
		this.enumConfig = enumConfig;
	}

	public String getClassifyType() {
		return classifyType;
	}

	public void setClassifyType(String classifyType) {
		this.classifyType = classifyType;
	}

	public String getAttrKey() {
		return attrKey;
	}

	public void setAttrKey(String attrKey) {
		this.attrKey = attrKey;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Integer getIsMust() {
		return isMust;
	}

	public void setIsMust(Integer isMust) {
		this.isMust = isMust;
	}

	public String getAttrValue() {
		return attrValue;
	}

	public void setAttrValue(String attrValue) {
		this.attrValue = attrValue;
	}


	public String getAttrName() {
		return attrName;
	}

	public void setAttrName(String attrName) {
		this.attrName = attrName;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getAreaCode() {
		return areaCode;
	}

	public void setAreaCode(String areaCode) {
		this.areaCode = areaCode;
	}

	public String getAttrCode() {
		return attrCode;
	}

	public void setAttrCode(String attrCode) {
		this.attrCode = attrCode;
	}

	public int getState() {
		return state;
	}

	public void setState(int state) {
		this.state = state;
	}

	public String getAttrTextString() {
		return attrTextString;
	}

	public void setAttrTextString(String attrTextString) {
		this.attrTextString = attrTextString;
	}

	public Object getAttrText() {
		if(StringUtils.isNotBlank(attrTextString)){
			return JSON.parseObject(attrTextString, Object.class);
		}

		return attrText;
	}

	public void setAttrText(JSONObject attrText) {
		this.attrText = attrText;
	}


}
