package com.sgs.ecom.member.entity.user;

import java.sql.Timestamp;

public class UserToolkitRecord {
    private Long recordId;

    private Long userId;

    private String toolCode;

    private String makeNo;

    private String makeType;

    private String makeTitle;

    private Integer state;

    private Timestamp createDate;

    private Timestamp stateDate;

    private String showMode;

    public String getShowMode() {
        return showMode;
    }

    public void setShowMode(String showMode) {
        this.showMode = showMode;
    }

    public Long getRecordId() {
        return recordId;
    }

    public void setRecordId(Long recordId) {
        this.recordId = recordId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getToolCode() {
        return toolCode;
    }

    public void setToolCode(String toolCode) {
        this.toolCode = toolCode == null ? null : toolCode.trim();
    }

    public String getMakeNo() {
        return makeNo;
    }

    public void setMakeNo(String makeNo) {
        this.makeNo = makeNo == null ? null : makeNo.trim();
    }

    public String getMakeType() {
        return makeType;
    }

    public void setMakeType(String makeType) {
        this.makeType = makeType == null ? null : makeType.trim();
    }

    public String getMakeTitle() {
        return makeTitle;
    }

    public void setMakeTitle(String makeTitle) {
        this.makeTitle = makeTitle == null ? null : makeTitle.trim();
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public Timestamp getStateDate() {
        return stateDate;
    }

    public void setStateDate(Timestamp stateDate) {
        this.stateDate = stateDate;
    }
}