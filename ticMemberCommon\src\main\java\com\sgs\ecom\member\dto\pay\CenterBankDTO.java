package com.sgs.ecom.member.dto.pay;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;

public class CenterBankDTO {
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String accountNo;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String accountName;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String bankNo;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String bankName;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String bankAddress;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String bankTel;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String buCode;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String bankBranchNo;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String sealImg;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String sealImgUrl;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String base64Url;
    //美元的
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String accountNameEn;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String bankAddressEn;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String bankNameEn;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String bankNoEn;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String bankTelEn;


    public String getAccountNameEn() {
        return accountNameEn;
    }

    public void setAccountNameEn(String accountNameEn) {
        this.accountNameEn = accountNameEn;
    }

    public String getBankAddressEn() {
        return bankAddressEn;
    }

    public void setBankAddressEn(String bankAddressEn) {
        this.bankAddressEn = bankAddressEn;
    }

    public String getBankNameEn() {
        return bankNameEn;
    }

    public void setBankNameEn(String bankNameEn) {
        this.bankNameEn = bankNameEn;
    }

    public String getBankNoEn() {
        return bankNoEn;
    }

    public void setBankNoEn(String bankNoEn) {
        this.bankNoEn = bankNoEn;
    }

    public String getBankTelEn() {
        return bankTelEn;
    }

    public void setBankTelEn(String bankTelEn) {
        this.bankTelEn = bankTelEn;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getBankNo() {
        return bankNo;
    }

    public void setBankNo(String bankNo) {
        this.bankNo = bankNo;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankAddress() {
        return bankAddress;
    }

    public void setBankAddress(String bankAddress) {
        this.bankAddress = bankAddress;
    }

    public String getBankTel() {
        return bankTel;
    }

    public void setBankTel(String bankTel) {
        this.bankTel = bankTel;
    }

    public String getBuCode() {
        return buCode;
    }

    public void setBuCode(String buCode) {
        this.buCode = buCode;
    }

    public String getBankBranchNo() {
        return bankBranchNo;
    }

    public void setBankBranchNo(String bankBranchNo) {
        this.bankBranchNo = bankBranchNo;
    }

    public String getSealImg() {
        return sealImg;
    }

    public void setSealImg(String sealImg) {
        this.sealImg = sealImg;
    }

    public String getInvoiceName() {
        return accountName;
    }


    public String getSealImgUrl() {
        return sealImgUrl;
    }

    public void setSealImgUrl(String sealImgUrl) {
        this.sealImgUrl = sealImgUrl;
    }

    public String getBase64Url() {
        return base64Url;
    }

    public void setBase64Url(String base64Url) {
        this.base64Url = base64Url;
    }
}
