package com.sgs.ecom.member.enumtool.bbc;

public enum AddressEnum {
	CONTACT_PERSON("contactPerson", "联系人",1),
	PHONE("phone", "电话",2),
	PROVINCE("province", "省",3),
	CITY("city", "市",4),
	ADDRESS("address", "详细地址",5),
		;

	AddressEnum(String name,  String nameCh,int sortShow) {
		this.name = name;
		this.nameCh = nameCh;
	}

	private String name;
	private String nameCh;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}


	public String getNameCh() {
		return nameCh;
	}

	public void setNameCh(String nameCh) {
		this.nameCh = nameCh;
	}
}
