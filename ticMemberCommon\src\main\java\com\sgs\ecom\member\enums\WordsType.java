package com.sgs.ecom.member.enums;

public enum WordsType {

	COMPONENT("成分热词", "COMPONENT");
		
    // 成员变量  
    private String name;  
    private String index;  
    // 构造方法  
    private WordsType(String name, String index) {  
        this.name = name;  
        this.index = index;  
    }  

    // 普通方法  
    public static String getName(String index) {  
        for (WordsType c : WordsType.values()) {  
            if (c.getIndex().equals(index)) {  
                return c.name;  
            }  
        }  
        return null;  
    }  
    // get set 方法  
    public String getName() {  
        return name;  
    }  

    public String getIndex() {  
        return index;  
    }  
}
