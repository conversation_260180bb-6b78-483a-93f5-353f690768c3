package com.sgs.ecom.member.dto.application;

import com.sgs.ecom.member.dto.center.EnumDTO;

import java.math.BigDecimal;

public class TestCycleDTO {
	private EnumDTO enumDTO;
	private String testCycleCode;
	private int del;//要减的天数
	private BigDecimal urgentNum;//已经除以100了

	public EnumDTO getEnumDTO() {
		return enumDTO;
	}

	public void setEnumDTO(EnumDTO enumDTO) {
		this.enumDTO = enumDTO;
	}

	public String getTestCycleCode() {
		return testCycleCode;
	}

	public void setTestCycleCode(String testCycleCode) {
		this.testCycleCode = testCycleCode;
	}

	public int getDel() {
		return del;
	}

	public void setDel(int del) {
		this.del = del;
	}

	public BigDecimal getUrgentNum() {
		return urgentNum;
	}

	public void setUrgentNum(BigDecimal urgentNum) {
		this.urgentNum = urgentNum;
	}
}
