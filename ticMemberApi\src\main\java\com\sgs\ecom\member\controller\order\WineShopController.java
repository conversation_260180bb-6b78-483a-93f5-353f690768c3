package com.sgs.ecom.member.controller.order;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.ecom.member.annotation.CheckWine;
import com.sgs.ecom.member.request.OrderBaseInfoReq;
import com.sgs.ecom.member.request.wine.WineListReq;
import com.sgs.ecom.member.service.wine.interfaces.WineShopSV;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.order/wineShop")
public class WineShopController extends ControllerUtil {

    @Autowired
    private WineShopSV wineShopSV;

    @CheckWine
//    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "qryList", method = {RequestMethod.POST})
    public ResultBody qryList(
            @RequestHeader(value = "accessToken") String token,
            @RequestBody WineListReq wineListReq) throws Exception {
        return ResultBody.newInstance(wineShopSV.getPageList(wineListReq, getUserDTO(token)));
    }

    @CheckWine
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "getStateNum", method = {RequestMethod.POST})
    public ResultBody getStateNum(
            @RequestHeader(value = "accessToken") String token,
            @RequestBody WineListReq wineListReq) throws Exception {
        return ResultBody.newInstance(wineShopSV.getStateNum(wineListReq, getUserDTO(token)));
    }

    /**
     * @Description : 酒管系统订单导出
     * <AUTHOR> Zhang
     * @Date  2023/3/2
     * @param token:
     * @param wineListReq:
     * @param res:
     * @return: void
     **/
    @CheckWine
//    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "expList", method = {RequestMethod.POST})
    public void expList(
            @RequestHeader(value = "accessToken") String token,
            @RequestBody WineListReq wineListReq, HttpServletResponse res) throws Exception {
        wineShopSV.expList(wineListReq,  getUserDTO(token), res);
    }
}
