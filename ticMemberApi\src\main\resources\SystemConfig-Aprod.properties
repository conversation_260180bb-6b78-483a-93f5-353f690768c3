#\u9a8c\u7b7e\u5bc6\u94a5
pcode.tic=Sgs123!
pcode.member=Sgs123!
pid.mzy=sakuw87Sius7pa9s
pid.member=CGz2RFHATB3XAsvg
pid.platform=CnjSOp0lKTaIIznz
pid.sgs=Z0zCnRE3IaY9Kzem
pid.sl=SWIj8ONZfUaj8EkB
pid.mall=Z0zCnRE3IaY9Kzem
pid.tic=aq2s2ASdsqoa9U80
pid.order=kPhG6so9xOHY3QsO
pid.leads=saUqq8AHsS7kQH8s

#SGS interal int
#sl.ots=https://cnots.sgs.net/ots/Services/CaseService.asmx/
#sl.cms=https://cnapp.sgs.net/FrameWorkApi/cmsApi/api/v1/
#preorder.ots=https://cnapp.sgs.net/preorder2api/order/getOrderRelateInfoForTIC

#sso
sso.url=https://gate.sgsonline.com.cn/ticSso/

#Common Service
upfile.url=https://cnapp.sgs.net/FrameWorkApi/file/doUpload
downfile.url=https://cnapp.sgs.net/FrameWorkApi/file/download
uppubfile.url=https://cnapp.sgs.net/FrameWorkApi/file/uploadPublicFile/
upprivatefile.url=https://cnapp.sgs.net/FrameWorkApi/file/doUpload


#tic
score.url=http://10.168.129.143:8011/api/v1/order/updateNgoScore

#ticMember/
#temp_path=/home/<USER>/temp/
temp_path=/mnt/datadisk1/temp/oiq/
base_temp=WEB-INF/
template_path=temp/templates
dashboard_img=temp/images/dashboard.png
pointer_red_img=temp/images/pointer-red.png
pointer_yel_img=temp/images/pointer-yel.png
pointer_gre_img=temp/images/pointer-gre.png
logo_img=temp/images/logo.png
line_img=temp/images/line.png
printer_img=temp/images/printer.png


emailA=<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
emailB=<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>


##\u652f\u4ed8\u76f8\u5173
#\u8bf7\u6c42\u7f51\u5173\u5730\u5740
