package com.sgs.ecom.member.enumtool.order;

/**
 * <AUTHOR>
 */

public enum OrderLabelCodeEnum {
    FIRST_INQUIRY("FIRST_INQUIRY",  "首次询价单"),
    TAILS("TAILS",  "提交申请表的小尾巴"),
    ;
    private String name;
    private String nameCh;

    OrderLabelCodeEnum(String name, String nameCh) {
        this.name = name;
        this.nameCh = nameCh;
    }


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }
}
