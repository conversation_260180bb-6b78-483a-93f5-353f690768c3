package com.sgs.ecom.member.dto.custom;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;

public class StateAndNumDTO {
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int state;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int num;

    public StateAndNumDTO(){

    }

    public StateAndNumDTO(int state, int num) {
        this.state = state;
        this.num = num;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

}
