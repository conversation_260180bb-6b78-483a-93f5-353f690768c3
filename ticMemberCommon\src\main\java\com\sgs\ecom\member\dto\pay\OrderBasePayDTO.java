package com.sgs.ecom.member.dto.pay;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.dto.order.OrderProductDTO;
import com.sgs.ecom.member.util.serializer.PriceNumFormatSerializer;
import com.sgs.ecom.member.util.serializer.TimeStringFormatSerializer;

import java.math.BigDecimal;
import java.util.List;

public class OrderBasePayDTO {
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private  Long orderId;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String orderNo;
    @JsonSerialize(using = TimeStringFormatSerializer.class)
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String createDate;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String orderType;
    @JsonSerialize(using = PriceNumFormatSerializer.class)
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BigDecimal realAmount;//订单实付金额
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int payState;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int subState;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private Long userId;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String userName;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String reportForm;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String reportLua;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String categoryPath;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int refundState;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int state;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String groupNo;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private Long labId;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String relateOrderNo;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String payMethod;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String bu;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String toOrderNo;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private List<OrderProductDTO> orderProductDTOList;
    private List<Long> labIdList;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int monthPay;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int isPayReceived;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String operatorCode;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int payType;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String accountName;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String currency;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String currencyMark;

    @JsonSerialize(using = PriceNumFormatSerializer.class)
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BigDecimal totalPrice;//单价*数量
    @JsonSerialize(using = PriceNumFormatSerializer.class)
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BigDecimal orderAmount;//订单应付金额

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String model;//返回是否时新拆单规则
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private boolean ticFlg; //true -子单 false -子单

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String recommendReason;//补充订单说明
    @JsonSerialize(using = PriceNumFormatSerializer.class)
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BigDecimal  shopDisAmount;
    @JsonSerialize(using = PriceNumFormatSerializer.class)
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BigDecimal  csDiscountAmount;
    @JsonSerialize(using = PriceNumFormatSerializer.class)
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BigDecimal  discountAmount;
    @JsonSerialize(using = PriceNumFormatSerializer.class)
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BigDecimal  serviceAmount;

    public BigDecimal getShopDisAmount() {
        return shopDisAmount;
    }

    public void setShopDisAmount(BigDecimal shopDisAmount) {
        this.shopDisAmount = shopDisAmount;
    }

    public BigDecimal getCsDiscountAmount() {
        return csDiscountAmount;
    }

    public void setCsDiscountAmount(BigDecimal csDiscountAmount) {
        this.csDiscountAmount = csDiscountAmount;
    }

    public BigDecimal getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount;
    }

    public BigDecimal getServiceAmount() {
        return serviceAmount;
    }

    public void setServiceAmount(BigDecimal serviceAmount) {
        this.serviceAmount = serviceAmount;
    }

    public String getRecommendReason() {
        return recommendReason;
    }

    public void setRecommendReason(String recommendReason) {
        this.recommendReason = recommendReason;
    }

    public boolean isTicFlg() {
        return ticFlg;
    }

    public void setTicFlg(boolean ticFlg) {
        this.ticFlg = ticFlg;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public int getPayType() {
        return payType;
    }

    public void setPayType(int payType) {
        this.payType = payType;
    }

    public String getOperatorCode() {
        return operatorCode;
    }

    public void setOperatorCode(String operatorCode) {
        this.operatorCode = operatorCode;
    }

    public int getIsPayReceived() {
        return isPayReceived;
    }

    public void setIsPayReceived(int isPayReceived) {
        this.isPayReceived = isPayReceived;
    }

    public int getMonthPay() {
		return monthPay;
	}

	public void setMonthPay(int monthPay) {
		this.monthPay = monthPay;
	}

	public List<Long> getLabIdList() {
        return labIdList;
    }

    public void setLabIdList(List<Long> labIdList) {
        this.labIdList = labIdList;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public BigDecimal getRealAmount() {
        return realAmount;
    }

    public void setRealAmount(BigDecimal realAmount) {
        this.realAmount = realAmount;
    }

    public int getPayState() {
        return payState;
    }

    public void setPayState(int payState) {
        this.payState = payState;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getReportForm() {
        return reportForm;
    }

    public void setReportForm(String reportForm) {
        this.reportForm = reportForm;
    }

    public String getReportLua() {
        return reportLua;
    }

    public void setReportLua(String reportLua) {
        this.reportLua = reportLua;
    }

    public String getCategoryPath() {
        return categoryPath;
    }

    public void setCategoryPath(String categoryPath) {
        this.categoryPath = categoryPath;
    }

    public int getSubState() {
        return subState;
    }

    public void setSubState(int subState) {
        this.subState = subState;
    }

    public int getRefundState() {
        return refundState;
    }

    public void setRefundState(int refundState) {
        this.refundState = refundState;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public String getGroupNo() {
        return groupNo;
    }

    public void setGroupNo(String groupNo) {
        this.groupNo = groupNo;
    }

    public Long getLabId() {
        return labId;
    }

    public void setLabId(Long labId) {
        this.labId = labId;
    }

    public String getRelateOrderNo() {
        return relateOrderNo;
    }

    public void setRelateOrderNo(String relateOrderNo) {
        this.relateOrderNo = relateOrderNo;
    }

    public String getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(String payMethod) {
        this.payMethod = payMethod;
    }

    public String getToOrderNo() {
        return toOrderNo;
    }

    public void setToOrderNo(String toOrderNo) {
        this.toOrderNo = toOrderNo;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public List<OrderProductDTO> getOrderProductDTOList() {
        return orderProductDTOList;
    }

    public void setOrderProductDTOList(List<OrderProductDTO> orderProductDTOList) {
        this.orderProductDTOList = orderProductDTOList;
    }

    public String getBu() {
        return bu;
    }

    public void setBu(String bu) {
        this.bu = bu;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getCurrencyMark() {
        return currencyMark;
    }

    public void setCurrencyMark(String currencyMark) {
        this.currencyMark = currencyMark;
    }
}
