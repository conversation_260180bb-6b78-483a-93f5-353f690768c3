package com.sgs.ecom.member.dto.center;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.base.BaseOrderFilter;
import org.apache.commons.lang.StringUtils;

public class LabDTO {
    @ApiAnno(groups={BaseQryFilter.Default.class, BaseOrderFilter.LabCode.class})
    private Long labId;
    @ApiAnno(groups={BaseQryFilter.Default.class, BaseOrderFilter.LabCode.class})
    private String labName;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String province;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String city;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String town;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String labAddress;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String linkPerson;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String linkPhone;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String postcode;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String accountNo;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String subBu;
    @ApiAnno(groups={BaseQryFilter.Default.class, BaseOrderFilter.LabCode.class})
    private String businessLineId;
    @ApiAnno(groups={BaseQryFilter.Default.class, BaseOrderFilter.LabCode.class})
    private String businessLineName;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String alipayChannel;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String wxpayChannel;

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private Long categoryId;

    @ApiAnno(groups={BaseQryFilter.Default.class, BaseOrderFilter.LabCode.class})
    private String labAddressShow;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String catName;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String categoryName;
    @ApiAnno(groups={BaseQryFilter.Default.class, BaseOrderFilter.LabCode.class})
    private String labCode;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String wechatNo;//微信支付账号

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String alipayNo;//支付宝支付账号

    @ApiAnno(groups={BaseQryFilter.Default.class, BaseOrderFilter.LabCode.class})
    private String platformCode;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String paymentChannelSource;

    //1 表示无实验室
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private Integer labFlg;

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public String getWechatNo() {
        return wechatNo;
    }

    public void setWechatNo(String wechatNo) {
        this.wechatNo = wechatNo;
    }

    public String getAlipayNo() {
        return alipayNo;
    }

    public void setAlipayNo(String alipayNo) {
        this.alipayNo = alipayNo;
    }

    public Long getLabId() {
        return labId;
    }

    public void setLabId(Long labId) {
        this.labId = labId;
    }

    public String getLabName() {
        return labName;
    }

    public void setLabName(String labName) {
        this.labName = labName;
    }



    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }



    public String getLabAddress() {
        return labAddress;
    }

    public void setLabAddress(String labAddress) {
        this.labAddress = labAddress;
    }

    public String getLinkPerson() {
        return linkPerson;
    }

    public void setLinkPerson(String linkPerson) {
        this.linkPerson = linkPerson;
    }

    public String getLinkPhone() {
        return linkPhone;
    }

    public void setLinkPhone(String linkPhone) {
        this.linkPhone = linkPhone;
    }

    public String getPostcode() {
        return postcode;
    }

    public void setPostcode(String postcode) {
        this.postcode = postcode;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getSubBu() {
        return subBu;
    }

    public void setSubBu(String subBu) {
        this.subBu = subBu;
    }

    public String getBusinessLineId() {
        return businessLineId;
    }

    public void setBusinessLineId(String businessLineId) {
        this.businessLineId = businessLineId;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getTown() {
        return town;
    }

    public void setTown(String town) {
        this.town = town;
    }

    public String getLabAddressShow() {

        StringBuilder stringBuilder=new StringBuilder();
        if(StringUtils.isNotBlank(province)){
            stringBuilder.append(province);
        }
        if(StringUtils.isNotBlank(city) && !"上海市".equals(city)){
            stringBuilder.append(city);
        }
        if(StringUtils.isNotBlank(town)){
            stringBuilder.append(town);
        }
        if(StringUtils.isNotBlank(labAddress)){
            stringBuilder.append(labAddress);
        }
        return stringBuilder.toString();
    }

    public String getAlipayChannel() {
        return alipayChannel;
    }

    public void setAlipayChannel(String alipayChannel) {
        this.alipayChannel = alipayChannel;
    }

    public void setLabAddressShow(String labAddressShow) {
        this.labAddressShow = labAddressShow;
    }

    public String getWxpayChannel() {
        return wxpayChannel;
    }

    public void setWxpayChannel(String wxpayChannel) {
        this.wxpayChannel = wxpayChannel;
    }

    public String getCatName() {
        return catName;
    }

    public void setCatName(String catName) {
        this.catName = catName;
    }

    public String getLabCode() {
        return labCode;
    }

    public void setLabCode(String labCode) {
        this.labCode = labCode;
    }

    public String getPlatformCode() {
        return platformCode;
    }

    public void setPlatformCode(String platformCode) {
        this.platformCode = platformCode;
    }

    public String getBusinessLineName() {
        return businessLineName;
    }

    public void setBusinessLineName(String businessLineName) {
        this.businessLineName = businessLineName;
    }

    public Integer getLabFlg() {
        return labFlg;
    }

    public void setLabFlg(Integer labFlg) {
        this.labFlg = labFlg;
    }

    public String getPaymentChannelSource() {
        return paymentChannelSource;
    }

    public void setPaymentChannelSource(String paymentChannelSource) {
        this.paymentChannelSource = paymentChannelSource;
    }
}
