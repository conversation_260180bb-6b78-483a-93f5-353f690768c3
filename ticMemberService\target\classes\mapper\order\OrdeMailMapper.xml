<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.order.dao.OrderMailMapper">

    <resultMap id="rstsMailDTO" type="com.sgs.ecom.member.dto.send.RSTSMailDTO" >
        <result column="ORDER_ID" property="orderId" jdbcType="BIGINT" />
        <result column="USER_ID" property="userId" jdbcType="BIGINT" />
        <result column="CS_CODE" property="csCode" jdbcType="VARCHAR" />
        <result column="CS_EMAIL" property="csEmail" jdbcType="VARCHAR" />
        <result column="IS_TEST" property="isTest" jdbcType="INTEGER" />
        <result column="CUST_ID" property="custId" jdbcType="BIGINT" />
        <result column="CURRENCY" property="currency" jdbcType="VARCHAR" />
        <result column="REAL_AMOUNT" property="realAmount" jdbcType="DECIMAL" />
        <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
        <result column="SALES_CODE" property="salesCode" jdbcType="VARCHAR" />
        <result column="LINK_PERSON" property="linkPerson" jdbcType="INTEGER" />
        <result column="LINK_PHONE" property="linkPhone" jdbcType="VARCHAR" />
        <result column="LINK_EMAIL" property="linkEmail" jdbcType="VARCHAR" />
        <result column="FROM_SOURCE" property="fromSource" jdbcType="VARCHAR" />

    </resultMap>


    <select id="selectRstsMailDTOByOrderId" resultMap="rstsMailDTO">
        select obi.ORDER_ID,obi.ORDER_NO,obi.CS_CODE,obi.CS_EMAIL,obi.IS_TEST,obi.CUST_ID,
               obi.USER_ID,obi.CURRENCY,obi.REAL_AMOUNT,
               obi.SALES_CODE,oaf.LINK_PERSON,oaf.LINK_PHONE,oaf.LINK_EMAIL,obi.FROM_SOURCE
        from ORDER_BASE_INFO obi,ORDER_APPLICATION_FORM oaf
        where obi.ORDER_TYPE=300000 and obi.ORDER_NO=oaf.ORDER_NO  and oaf.STATE=1
        and obi.ORDER_ID= #{orderId}
    </select>




</mapper>