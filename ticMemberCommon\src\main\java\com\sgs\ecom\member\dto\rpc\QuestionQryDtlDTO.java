package com.sgs.ecom.member.dto.rpc;

import com.sgs.ecom.member.dto.center.CenterCategory;

import java.util.ArrayList;
import java.util.List;

public class QuestionQryDtlDTO {
    private String questionId;
    private String state;

    private String itemId;
    private String itemName;
    private String lineCode;
    private String bu;
    private List<CenterCategory> catagoryItem=new ArrayList<>();
    public String getQuestionId() {
        return questionId;
    }

    public void setQuestionId(String questionId) {
        this.questionId = questionId;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getItemId() {
        return itemId;
    }

    public void setItemId(String itemId) {
        this.itemId = itemId;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getBu() {
        return bu;
    }

    public void setBu(String bu) {
        this.bu = bu;
    }

    public String getLineCode() {
        return lineCode;
    }

    public void setLineCode(String lineCode) {
        this.lineCode = lineCode;
    }

    public List<CenterCategory> getCatagoryItem() {
        return catagoryItem;
    }

    public void setCatagoryItem(List<CenterCategory> catagoryItem) {
        this.catagoryItem = catagoryItem;
    }
}
