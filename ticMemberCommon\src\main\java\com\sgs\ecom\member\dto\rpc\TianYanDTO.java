package com.sgs.ecom.member.dto.rpc;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.base.BaseOrderFilter;
import lombok.Data;

@Data
public class TianYanDTO extends BaseQryFilter {

    /**
     * 主键
     */
    private Long id;
    /**
     * 税号
     */
    @ApiAnno(groups={Default.class})
    private String creditCode;
    /**
     * 公司名称
     */
    @ApiAnno(groups={Default.class})
    private String name;
    /**
     * 企业状态
     */
    private String regStatus;
    /**
     * 注册地址
     */
    @ApiAnno(groups={Default.class})
    private String regLocation;

}
