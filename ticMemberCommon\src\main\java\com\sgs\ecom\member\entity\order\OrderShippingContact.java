package com.sgs.ecom.member.entity.order;

import lombok.Data;

import javax.persistence.Id;

/**
 * <AUTHOR>
 */
@Data
public class OrderShippingContact {
    /**
     * 主键
     */
    @Id
    private Long contactId;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 联系方
     */
    private String contactType;
    /**
     * 国家
     */
    private String contactCountry;
    /**
     * 城市
     */
    private String contactCity;
    /**
     * 地址
     */
    private String contactAddress;
    /**
     * 邮编
     */
    private String contactZipCode;
    /**
     * 联系人
     */
    private String contactName;
    /**
     * 联系电话
     */
    private String contactPhone;
    /**
     * 联系邮箱
     */
    private String contactEmail;
    /**
     * 联系FXINo
     */
    private String contactFxiNo;
    /**
     * 纳税号
     */
    private String contactTaxNo;
    /**
     * 交易号
     */
    private String contactTransNo;
    /**
     * 账号
     */
    private String accountNumber;
    /**
     * 状态
     */
    private Integer state;
    /**
     * 创建时间
     */
    private String createDate;
    /**
     * 状态时间
     */
    private String stateDate;
}
