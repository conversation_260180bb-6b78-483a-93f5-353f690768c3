<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.user.dao.UserCouponDtlMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.vo.VOUserCouponDtl" >
    <id column="DETAIL_ID" property="detailId" jdbcType="BIGINT" />
    <result column="COUPON_NO" property="couponNo" jdbcType="VARCHAR" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="DISCOUNT_AMOUNT" property="discountAmount" jdbcType="DECIMAL" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
     DETAIL_ID,COUPON_NO,ORDER_NO,DISCOUNT_AMOUNT,CREATE_DATE
  </sql>




  <insert id="insertForeach"  >
    insert into USER_COUPON_DTL
    ( COUPON_NO,ORDER_NO,DISCOUNT_AMOUNT,CREATE_DATE)
    values
    <foreach collection ="list" item="item" index= "index" separator =",">
      (
      #{item.couponNo}, #{item.orderNo},#{item.discountAmount},#{item.createDate}
      )
    </foreach>
  </insert>





</mapper>