package com.sgs.ecom.member.bo; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import com.platform.annotation.CharacterVaild; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import java.util.Date; 
import com.platform.util.json.DateFormatSerializer; 
import com.platform.annotation.BeanAnno; 

public class BOUserCourse{
 
 	public static final String SEQUENCE = "COURSE_ID"; 
  
 	public static final String BO_SQL = "USER_COURSE"; 
 
 	public static final String OWNER ="member";

 	public static final String COURSE_TIME="courseTime";
 	public static final String CREATE_DATE="createDate";
 	public static final String EXP_DATE="expDate";
 	public static final String SKU_ID="skuId";
 	public static final String COURSE_ID="courseId";
 	public static final String STATE_DATE="stateDate";
 	public static final String USER_ID="userId";
 	public static final String STORE_ID="storeId";
 	public static final String STATE="state";
 	public static final String COURSE_CONTENT="courseContent";
 	public static final String COURSE_NAME="courseName";

 	@BeanAnno("COURSE_TIME")
 	private String courseTime;
 	@BeanAnno("CREATE_DATE")
 	private Timestamp createDate;
 	@BeanAnno("EXP_DATE")
 	private Date expDate;
 	@BeanAnno("SKU_ID")
 	private long skuId;
 	@BeanAnno("COURSE_ID")
 	private long courseId;
 	@BeanAnno("STATE_DATE")
 	private Timestamp stateDate;
 	@BeanAnno("USER_ID")
 	private long userId;
 	@BeanAnno("STORE_ID")
 	private long storeId;
 	@BeanAnno("STATE")
 	private long state;
 	@BeanAnno("COURSE_CONTENT")
 	private String courseContent;
 	@BeanAnno("COURSE_NAME")
 	private String courseName;

 	@CharacterVaild(len = 30) 
 	public void setCourseTime(String courseTime){
 		 this.courseTime=courseTime;
 	}
 	public String getCourseTime(){
 		 return this.courseTime;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setExpDate(Date expDate){
 		 this.expDate=expDate;
 	}
 	@JsonSerialize(using = DateFormatSerializer.class) 
 	public Date getExpDate(){
 		 return this.expDate;
 	}
 
 	 
 	public void setSkuId(long skuId){
 		 this.skuId=skuId;
 	}
 	public long getSkuId(){
 		 return this.skuId;
 	}
 
 	 
 	public void setCourseId(long courseId){
 		 this.courseId=courseId;
 	}
 	public long getCourseId(){
 		 return this.courseId;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setUserId(long userId){
 		 this.userId=userId;
 	}
 	public long getUserId(){
 		 return this.userId;
 	}
 
 	 
 	public void setStoreId(long storeId){
 		 this.storeId=storeId;
 	}
 	public long getStoreId(){
 		 return this.storeId;
 	}
 
 	 
 	public void setState(long state){
 		 this.state=state;
 	}
 	public long getState(){
 		 return this.state;
 	}
 
 	 
 	@CharacterVaild(len = 512) 
 	public void setCourseContent(String courseContent){
 		 this.courseContent=courseContent;
 	}
 	public String getCourseContent(){
 		 return this.courseContent;
 	}
 
 	 
 	@CharacterVaild(len = 128) 
 	public void setCourseName(String courseName){
 		 this.courseName=courseName;
 	}
 	public String getCourseName(){
 		 return this.courseName;
 	}
 
 	 
}