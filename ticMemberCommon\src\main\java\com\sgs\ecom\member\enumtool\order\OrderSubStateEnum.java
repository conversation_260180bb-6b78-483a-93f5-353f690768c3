package com.sgs.ecom.member.enumtool.order;

public enum OrderSubStateEnum {
    NO_OPERATOR("noOperator", 0, "未操作"),
    ORDER_AWAIT_AUDIT("orderAwaitAudit", 64, "已开单"),//子状态，不算入到订单主状态中
    ORDER_IMPORT_STATUS("orderAwaitAudit", 65, "已import订单"),//soda已import订单
    BACK("back", 70, "退回申请表"),
    BACK_CONFIRM("refund", 71, "退回后提交未处理"),
    BACK_SUCCESS("success", 72, "退回后成功"),

    PORTAL_FLG("portalFlg", 98, "用于区分小门户的待提交申请表状态"),

    QUESTION_FLG("questionFlg", 99, "oiq修改问卷保存的时候修改子状态"),
    ;
    OrderSubStateEnum(String name, int index, String nameCh) {
        this.name = name;
        this.index = index;
        this.nameCh = nameCh;
    }

    public static String getName(int index) {
        for (OrderSubStateEnum c : OrderSubStateEnum.values()) {
            if (c.getIndex()==index) {
                return c.getName();
            }
        }
        return null;
    }

    public static OrderSubStateEnum getEnum(int index) {
        for (OrderSubStateEnum c : OrderSubStateEnum.values()) {
            if (c.getIndex()==index) {
                return c;
            }
        }
        return null;
    }

    public static String getNameCh(int index) {
        for (OrderSubStateEnum c :OrderSubStateEnum.values()) {
            if (c.getIndex()==index) {
                return c.getNameCh();
            }
        }
        return null;
    }





    private String name;
    private int index;
    private String nameCh;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }
}
