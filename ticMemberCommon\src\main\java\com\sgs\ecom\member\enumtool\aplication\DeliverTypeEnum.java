package com.sgs.ecom.member.enumtool.aplication;

public enum DeliverTypeEnum {
   //投递方式10样品自寄,11样品上门取件,20发票快递
   my("my", "10", "自行邮寄"),
    sf("sf", "11", "顺丰上门取件"),
    mySend("mySend", "12", "上门送样"),
    invoice("invoice", "20", "快递"),

    BACK("back", "30", "退回的样品地址"),
    REPORT("REPORT", "31", "报告邮寄的样品地址"),


    BUY("buy", "100", "买家"),
    FACTORY("factory", "101", "工厂"),
    SUPPLIER("supplier", "102", "供应商"),
    DETECTION("detection", "103", "检测地址"),
    TEACHING_MATERIAL("teachingMaterial", "104", "教材寄送"),
    CERTIFICATE("certificate", "105", "证书寄送"),


    ;

    DeliverTypeEnum(String name, String index, String nameCh) {
        this.name = name;
        this.index = index;
        this.nameCh = nameCh;
    }

    public static String getName(String index) {
        for (DeliverTypeEnum c : DeliverTypeEnum.values()) {
            if (c.getIndex().equals(index)) {
                return c.getName();
            }
        }
        return null;
    }

    public static DeliverTypeEnum getEnum(String index) {
        for (DeliverTypeEnum c : DeliverTypeEnum.values()) {
            if (c.getIndex().equals(index)) {
                return c;
            }
        }
        return null;
    }

    public static String getNameCh(String index) {
        for (DeliverTypeEnum c : DeliverTypeEnum.values()) {
            if (c.getIndex().equals(index)) {
                return c.getNameCh();
            }
        }
        return null;
    }

    public static Integer getMinCode(String index) {
        if(DeliverTypeEnum.my.getIndex().equals(index)){
            return 1;
        }
        if(DeliverTypeEnum.sf.getIndex().equals(index)){
            return 2;
        }
        if(DeliverTypeEnum.mySend.getIndex().equals(index)){
            return 0;
        }
        return -1;
    }




    private String name;
    private String index;
    private String nameCh;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }




}
