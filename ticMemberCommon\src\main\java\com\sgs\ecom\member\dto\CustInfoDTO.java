package com.sgs.ecom.member.dto; 
 
import java.sql.Timestamp;
import java.util.Map;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.annotation.IsNullAnno;
import com.platform.util.json.TimeFormatSerializer;
import com.sgs.base.BaseQryFilter.QueryDtl; 

public class CustInfoDTO{
 
 	public static final String CREATE_SQL = "select IS_FREEZE,CUST_ID,PROVICE,CREDIT_AMOUNT,STATE_DATE,STATE,memo,COMPANY_NAME,CREATE_DATE,COUNTRY,CITY,TOWN,ADDRESS,CUST_CODE,COMPANY_NAME_EN from TB_CUST_INFO"; 
 
 
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="IS_FREEZE", getName="getIsFreeze", setName="setIsFreeze")
 	private int isFreeze;
	@ApiAnno(groups={QueryDtl.class} ,serviceName={})
 	@BeanAnno(value="CUST_ID", getName="getCustId", setName="setCustId")
 	private Long custId;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="PROVICE", getName="getProvice", setName="setProvice")
 	private String provice;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="CREDIT_AMOUNT", getName="getCreditAmount", setName="setCreditAmount")
 	private double creditAmount;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private Timestamp stateDate;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="MEMO", getName="getMemo", setName="setMemo")
 	private String memo;
 	@ApiAnno(groups={QueryDtl.class} ,serviceName={})
 	@BeanAnno(value="COMPANY_NAME", getName="getCompanyName", setName="setCompanyName")
 	private String companyName;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="COUNTRY", getName="getCountry", setName="setCountry")
 	private String country;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="CITY", getName="getCity", setName="setCity")
 	private String city;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="TOWN", getName="getTown", setName="setTown")
 	private String town;
 	@ApiAnno(groups={QueryDtl.class} ,serviceName={})
 	@BeanAnno(value="ADDRESS", getName="getAddress", setName="setAddress")
 	private String address;
 	@ApiAnno(groups={QueryDtl.class} ,serviceName={})
 	@BeanAnno(value="ADDRESS_EN", getName="getAddressEn", setName="setAddressEn")
 	private String addressEn;
 	@IsNullAnno(serviceName={"addBalanceByCust","subtractBalanceByCust"})
 	@ApiAnno(groups={QueryDtl.class})
 	@BeanAnno(value="CUST_CODE", getName="getCustCode", setName="setCustCode")
 	private String custCode;
 	@ApiAnno(groups={QueryDtl.class} ,serviceName={})
 	@BeanAnno(value="COMPANY_NAME_EN", getName="getCompanyNameEn", setName="setCompanyNameEn")
 	private String companyNameEn;
 	@ApiAnno(groups={QueryDtl.class} ,serviceName={})
 	@BeanAnno(value="TAX_NO", getName="getTaxNo", setName="setTaxNo")
 	private String taxNo;
 	@ApiAnno(groups={QueryDtl.class} ,serviceName={})
 	@BeanAnno(value="REG_PHONE", getName="getRegPhone", setName="setRegPhone")
 	private String regPhone;
 	@ApiAnno(groups={QueryDtl.class} ,serviceName={})
 	@BeanAnno(value="BANK_NAME", getName="getBankName", setName="setBankName")
 	private String bankName;
 	@ApiAnno(groups={QueryDtl.class} ,serviceName={})
 	@BeanAnno(value="BANK_NUMBER", getName="getBankNumber", setName="setBankNameNumber")
 	private String bankNumber;
	@ApiAnno(groups={QueryDtl.class} ,serviceName={})
	private String bossNo;
	@ApiAnno(groups={QueryDtl.class} ,serviceName={})
	private String vbaAccount;
	@ApiAnno(groups={QueryDtl.class} ,serviceName={})
	private Integer settleType;
	@ApiAnno(groups={QueryDtl.class} ,serviceName={})
	private String belongArea;
 	@BeanAnno(value="BUSI_CODE", getName="getBusiCode", setName="setBusiCode")
 	private String busiCode;
 	@ApiAnno(groups={QueryDtl.class})
 	@BeanAnno(value="REPORT_PRICE", getName="getReportPrice", setName="setReportPrice")
 	private double reportPrice;
 	@ApiAnno(groups={QueryDtl.class})
 	@BeanAnno(value="BELONG_LAB_CODE", getName="getBelongLabCode", setName="setBelongLabCode")
 	private String belongLabCode;
 	@BeanAnno(value="IS_DELETE", getName="getIsDelete", setName="setIsDelete")
 	private int isDelete;
	@ApiAnno(groups={QueryDtl.class})
	private String invoiceTitle;
	@ApiAnno(groups={QueryDtl.class})
	private String regAddress;
 	@ApiAnno(groups={QueryDtl.class})
 	@BeanAnno(value="CURRENCY", getName="getCurrency", setName="setCurrency")
 	private String currency;
	@ApiAnno(groups={QueryDtl.class})
	private Map<String,String> vbaMap;



 	public void setIsFreeze(int isFreeze){
 		 this.isFreeze=isFreeze;
 	}
 	public int getIsFreeze(){
 		 return this.isFreeze;
 	}
 
 	 
 	public void setCustId(Long custId){
 		 this.custId=custId;
 	}
 	public Long getCustId(){
 		 return this.custId;
 	}
 
 	 
 	public void setProvice(String provice){
 		 this.provice=provice;
 	}
 	public String getProvice(){
 		 return this.provice;
 	}
 
 	 
 	public void setCreditAmount(double creditAmount){
 		 this.creditAmount=creditAmount;
 	}
 	public double getCreditAmount(){
 		 return this.creditAmount;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setMemo(String memo){
 		 this.memo=memo;
 	}
 	public String getMemo(){
 		 return this.memo;
 	}
 
 	 
 	public void setCompanyName(String companyName){
 		 this.companyName=companyName;
 	}
 	public String getCompanyName(){
 		 return this.companyName;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setCountry(String country){
 		 this.country=country;
 	}
 	public String getCountry(){
 		 return this.country;
 	}
 
 	 
 	public void setCity(String city){
 		 this.city=city;
 	}
 	public String getCity(){
 		 return this.city;
 	}
 
 	 
 	public void setTown(String town){
 		 this.town=town;
 	}
 	public String getTown(){
 		 return this.town;
 	}
 
 	 
 	public void setAddress(String address){
 		 this.address=address;
 	}
 	public String getAddress(){
 		 return this.address;
 	}
 
 	 
 	public void setCustCode(String custCode){
 		 this.custCode=custCode;
 	}
 	public String getCustCode(){
 		 return this.custCode;
 	}
 
 	 
 	public void setCompanyNameEn(String companyNameEn){
 		 this.companyNameEn=companyNameEn;
 	}
 	public String getCompanyNameEn(){
 		 return this.companyNameEn;
 	}
	public String getTaxNo() {
		return taxNo;
	}
	public void setTaxNo(String taxNo) {
		this.taxNo = taxNo;
	}
	public String getRegPhone() {
		return regPhone;
	}
	public void setRegPhone(String regPhone) {
		this.regPhone = regPhone;
	}
	public String getBankName() {
		return bankName;
	}
	public void setBankName(String bankName) {
		this.bankName = bankName;
	}
	public String getBankNumber() {
		return bankNumber;
	}
	public void setBankNumber(String bankNumber) {
		this.bankNumber = bankNumber;
	}
	public String getAddressEn() {
		return addressEn;
	}
	public void setAddressEn(String addressEn) {
		this.addressEn = addressEn;
	}

	public String getBossNo() {
		return bossNo;
	}

	public void setBossNo(String bossNo) {
		this.bossNo = bossNo;
	}

	public String getVbaAccount() {
		return vbaAccount;
	}

	public void setVbaAccount(String vbaAccount) {
		this.vbaAccount = vbaAccount;
	}

	public Integer getSettleType() {
		return settleType;
	}

	public void setSettleType(Integer settleType) {
		this.settleType = settleType;
	}

	public String getBelongArea() {
		return belongArea;
	}

	public void setBelongArea(String belongArea) {
		this.belongArea = belongArea;
	}
	public String getBusiCode() {
		return busiCode;
	}
	public void setBusiCode(String busiCode) {
		this.busiCode = busiCode;
	}
	public double getReportPrice() {
		return reportPrice;
	}
	public void setReportPrice(double reportPrice) {
		this.reportPrice = reportPrice;
	}
	public String getBelongLabCode() {
		return belongLabCode;
	}
	public void setBelongLabCode(String belongLabCode) {
		this.belongLabCode = belongLabCode;
	}
	public int getIsDelete() {
		return isDelete;
	}
	public void setIsDelete(int isDelete) {
		this.isDelete = isDelete;
	}

	public String getInvoiceTitle() {
		return invoiceTitle;
	}

	public void setInvoiceTitle(String invoiceTitle) {
		this.invoiceTitle = invoiceTitle;
	}

	public String getRegAddress() {
		return regAddress;
	}

	public void setRegAddress(String regAddress) {
		this.regAddress = regAddress;
	}
	public String getCurrency() {
		return currency;
	}
	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public Map<String, String> getVbaMap() {
		return vbaMap;
	}

	public void setVbaMap(Map<String, String> vbaMap) {
		this.vbaMap = vbaMap;
	}
}