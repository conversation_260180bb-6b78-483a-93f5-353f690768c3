package com.sgs.ecom.member.dto.user;

import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.request.base.BaseFileReq;
import com.sgs.ecom.member.util.UtilTools;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class UserSampleFromDTO extends BaseQryFilter {

 	public static final String CREATE_SQL = "select CREATE_DATE,GROUP_NO,STATE_DATE,SAMPLE_NO,STATE,ORDER_NO,SAMPLE_KEY_NAME,FORM_ID,SAMPLE_VALUE,SAMPLE_KEY from ORDER_SAMPLE_FROM";
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="SAMPLE_NO", getName="getSampleNo", setName="setSampleNo")
 	private String sampleNo;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(groups={Default.class})
 	private Long userId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="SAMPLE_KEY_NAME", getName="getSampleKeyName", setName="setSampleKeyName")
 	private String sampleKeyName;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="FORM_ID", getName="getFormId", setName="setFormId")
 	private long formId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="SAMPLE_VALUE", getName="getSampleValue", setName="setSampleValue")
 	private String sampleValue;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="SAMPLE_KEY", getName="getSampleKey", setName="setSampleKey")
 	private String sampleKey;
	@ApiAnno(groups={Default.class})
	private int sortShow;
	@ApiAnno(groups={Default.class})
	private List<BaseFileReq> fileReqList=new ArrayList<>();
	@ApiAnno(groups={Default.class})
	private int type;
	@ApiAnno(groups={Default.class})
	private String areaCode;
	@ApiAnno(groups={Default.class})
	private String sampleValueRemark;
	@ApiAnno(groups={Default.class})
	private String enumConfig;

	@ApiAnno(groups={Default.class})
	private String sampleValueShow;//如果是枚举的字段进行翻译
	@ApiAnno(groups={Default.class})
	private String sampleExplain;
	@ApiAnno(groups={Default.class})
	private String sampleExplainEn;

	@ApiAnno(groups={Default.class})
	private String areaName;

	@ApiAnno(groups={Default.class})
	private String areaNameEn;

	@ApiAnno(groups={Default.class})
	private String sampleKeyNameEn;

	@ApiAnno(groups={Default.class})
	private String remark;

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getAreaName() {
		return areaName;
	}

	public void setAreaName(String areaName) {
		this.areaName = areaName;
	}

	public String getAreaNameEn() {
		return areaNameEn;
	}

	public void setAreaNameEn(String areaNameEn) {
		this.areaNameEn = areaNameEn;
	}

	public String getSampleKeyNameEn() {
		return sampleKeyNameEn;
	}

	public void setSampleKeyNameEn(String sampleKeyNameEn) {
		this.sampleKeyNameEn = sampleKeyNameEn;
	}

	public String getSampleExplain() {
		return sampleExplain;
	}

	public void setSampleExplain(String sampleExplain) {
		this.sampleExplain = sampleExplain;
	}

	public String getSampleExplainEn() {
		return sampleExplainEn;
	}

	public void setSampleExplainEn(String sampleExplainEn) {
		this.sampleExplainEn = sampleExplainEn;
	}

	public String getSampleValueShow() {
		return sampleValueShow;
	}

	public void setSampleValueShow(String sampleValueShow) {
		this.sampleValueShow = sampleValueShow;
	}

	public String getEnumConfig() {
		return enumConfig;
	}

	public void setEnumConfig(String enumConfig) {
		this.enumConfig = enumConfig;
	}

	public String getSampleValueRemark() {
		return sampleValueRemark;
	}

	public void setSampleValueRemark(String sampleValueRemark) {
		this.sampleValueRemark = sampleValueRemark;
	}

	public String getAreaCode() {
		return areaCode;
	}

	public void setAreaCode(String areaCode) {
		this.areaCode = areaCode;
	}
	public UserSampleFromDTO() {
	}

	public UserSampleFromDTO(String sampleKeyName, String sampleValue, int sortShow) {
		this.sampleKeyName = sampleKeyName;
		this.sampleValue = sampleValue;
		this.sortShow = sortShow;
	}



 
 	 
 	public void setSampleNo(String sampleNo){
 		 this.sampleNo=sampleNo;
 	}
 	public String getSampleNo(){
 		 return this.sampleNo;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}


	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public void setSampleKeyName(String sampleKeyName){
 		 this.sampleKeyName=sampleKeyName;
 	}
 	public String getSampleKeyName(){
 		 return this.sampleKeyName;
 	}
 
 	 
 	public void setFormId(long formId){
 		 this.formId=formId;
 	}
 	public long getFormId(){
 		 return this.formId;
 	}
 
 	 
 	public void setSampleValue(String sampleValue){
 		 this.sampleValue=sampleValue;
 	}
 	public String getSampleValue(){
 		 return this.sampleValue;
 	}
 
 	 
 	public void setSampleKey(String sampleKey){
 		 this.sampleKey=sampleKey;
 	}
 	public String getSampleKey(){
 		 return this.sampleKey;
 	}

	public int getSortShow() {
		return sortShow;
	}

	public void setSortShow(int sortShow) {
		this.sortShow = sortShow;
	}

	public List<BaseFileReq> getFileReqList() {
		return fileReqList;
	}

	public void setFileReqList(List<BaseFileReq> fileReqList) {
		this.fileReqList = fileReqList;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}
}