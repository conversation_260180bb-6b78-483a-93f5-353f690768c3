<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd padding: 10px 0 0 0; margin: 0;">
<html xmlns="http://www.w3.org/1999/xhtml padding: 10px 0 0 0; margin: 0;">
<head>
  <meta charset="UTF-8"></meta>
  <meta name="viewport"
        content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"></meta>
  <meta http-equiv="X-UA-Compatible" content="ie=edge"></meta>
  <title>Document</title>
  <style>
    * {
      box-sizing: border-box;
      word-wrap: break-word;
    }
    p {
      margin: 0;
    }
    .clearfix:after {
      content: " ";
      display: block;
      height: 0;
      clear: both;
      visibility: hidden;
    }

    .clearfix {
      *+height: 1%;
    }
    .float-left {
      float: left;
    }
    .float-right {
      float: right;
    }
    table {
      border-top: 1px solid #333;
      border-right: 1px solid #333;
    }
    td {
      border-bottom: 1px solid #333;
      border-left: 1px solid #333;
    }
  </style>
</head>
<body style="font-family: SimSun;">
<div style="margin-bottom: 20px;position: relative" class="clearfix">
  <img style="width: 20%;" src="${sqb2}" alt="" class="float-left"></img>
  <div style="width: 60%; position: absolute; left:200px;bottom: 20px;">
    <p style="text-align:center">
      <span style="font-size:16px;font-weight: bold">TESTING APPLICATION FORM</span>
    </p>
    <p style="text-align:center">
      <span style="font-size:16px;font-weight: bold">OIQ测试申请表</span>
    </p>
  </div>
  <div style="width: 20%;margin-top: 24px;" class="float-right">
    <p>
      <span style="font-size:10px">
        <#if luaType==1>No.: SHTC-IND-P-7.1-01-F07</#if>
        <#if luaType==2>No.: SHTC-IND-P-7.1-01-F08</#if>
        <#if luaType==0>No.: SHTC-IND-P-7.1-01-F09</#if>
      </span>
    </p>
    <p>
      <span style="font-size:10px">Version.:A/01</span>
    </p>
    <p>
      <span style="font-size:10px">Issue Date: Feb 1st, 2021</span>
    </p>

  </div>
</div>


<table width="100%" style="width: 100%;" cellspacing="0">
  <col width="5%"/>
  <col width="9%"/>
  <col width="8%"/>
  <col width="10%"/>
  <col width="5%" />
  <col width="10%" />
  <col width="10%" />
  <col width="5%" />
  <col width="5%" />
  <col width="10%" />
  <col width="5%" />
  <col width="9%" />
  <col  width="9%"/>
  <tr style=";height:10px" class="firstRow">
    <td colspan="13" style="border: 1px solid #333; padding: 0px 7px;">
      <p style="margin-top:4px;margin-right:0;margin-bottom:   4px;margin-left:0;line-height:16px">
        <span style="font-size:12px;color:black;font-weight: bold">订单编号：</span>
        <span style="font-size:12px;color:#333">${(orderFormPDFDTO.orderNo)!''}</span>
      </p>
    </td>
  </tr>

  <#if luaType!=0>
    <tr>
      <td colspan="1" rowspan="3" style="border: 1px solid #333; padding: 0px 7px;text-align:center;border-right:none;border-top: none;">
        <p> <span style="font-size:12px;font-weight: bold">★</span></p>
        <p><span style="font-size:12px;font-weight: bold">申</span></p>
        <p><span style="font-size:12px;font-weight: bold">请</span></p>
        <p><span style="font-size:12px;font-weight: bold">方</span></p>
      </td>
      <td colspan="3" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;border-top: none;border-bottom: none;">
      <span style="font-size:12px;">
	   <#if luaType==1>申请公司名称</#if>
        <#if luaType==2>Company Name</#if>
	  </span>
      </td>
      <td colspan="9" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;border-bottom: none;border-top:none;border-left: none;">
      <span style="font-size:12px;color:#333;">
	   <#if luaType==1> ${(orderApplicationFormDTO.companyNameCn)!''}</#if>
        <#if luaType==2> ${(orderApplicationFormDTO.companyNameEn)!''}</#if>
	  </span>
      </td>
    </tr>
    <tr>
      <td colspan="3" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;">
      <span style="font-size:12px;">
	  <#if luaType==1> 申请公司地址</#if>
        <#if luaType==2>Company Address</#if>
	  </span>
      </td>
      <td colspan="9" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;border-left: none;">
      <span style="font-size:12px;color:#333;">
	  <#if luaType==1>${(orderApplicationFormDTO.province)!''}${(orderApplicationFormDTO.city)!''}${(orderApplicationFormDTO.town)!''}${(orderApplicationFormDTO.companyAddressCn)!''}</#if>
        <#if luaType==2> ${(orderApplicationFormDTO.companyAddressEn)!''}</#if>
	  </span>
      </td>
    </tr>
    <tr>
      <td colspan="1" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;border-top: none;">
        <span style="font-size:12px;">联系人</span>
      </td>
      <td rowspan="1" valign="null" align="null" style="border: 1px solid #333; padding: 0px 7px;border-top: none;border-left:none;" colspan="2">
        <span style="font-size:12px;color:#333 ;">${(orderApplicationFormDTO.linkPerson)!''}</span>
      </td>
      <td colspan="2" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;border-right: none;border-top: none;border-left:none;">
        <span style="font-size:12px;">电话</span>
      </td>
      <td colspan="3" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;border-top: none;">
        <span style="font-size:12px;color:#333;">${(orderApplicationFormDTO.linkPhone)!''}</span>
      </td>
      <td colspan="1" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;border-left: none;border-top: none;">
        <span style="font-size:12px;">电子邮箱</span>
      </td>
      <td colspan="3" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;border-left: none;border-top: none;">
        <span style="font-size:12px;color:#333 ;">${(orderApplicationFormDTO.linkEmail)!''}</span>
      </td>
    </tr>

    <tr style=";height:23px">
      <td colspan="2"
          style="border-right: 1px solid #333; border-bottom: 1px solid #333; border-left: 1px solid #333; border-image: initial; border-top: none; padding: 0px 7px;">
        <p><span style="font-size:12px;">★</span>
          <span style="font-size:12px;font-weight: bold">报告信息</span></p>
      </td>
      <td colspan="2"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:20px">
          <span style="font-size:12px;">报告语言</span>
        </p>
      </td>
      <td colspan="5"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:20px">
          <img src="${sqb1}" style="border: 1px solid #333;vertical-align: text-bottom;"></img>
          <span style="font-size:12px;">${orderFormPDFDTO.reportLua}</span></p>
      </td>
      <td
              style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:20px"><span style="font-size:12px;">报告形式</span></p>
      </td>
      <td colspan="3"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:20px">
          <img src="${sqb1}" style="border: 1px solid #333;vertical-align: text-bottom;"></img>
          <span style="font-size:12px;">${orderFormPDFDTO.reportForm}</span> </p>
      </td>
    </tr>


    <tr style=";height:23px">
      <td colspan="2"
          style="border-right: 1px solid #333; border-bottom: 1px solid #333; border-left: 1px solid #333; border-image: initial; border-top: none; padding: 0px 7px;">
        <p><span style="font-size:12px;">★</span><br></br>
          <span style="font-size:12px;font-weight: bold">报告出具方式</span></p>
      </td>

      <td colspan="11"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:20px">
          <span style="font-size:12px;">${(orderApplicationFormDTO.reportMethodShow)!''}</span> </p>
      </td>
    </tr>




    <tr style=";page-break-inside:avoid;height:18px">
      <td rowspan="${n}"
          style="border-right: 1px solid #333; border-bottom: 1px solid #333; border-left: 1px solid #333; border-image: initial; border-top: none; padding: 0px 7px;text-align:center">
        <p style=""> <span style="font-size:12px;font-weight: bold">报</span></p>
        <p><span style="font-size:12px;font-weight: bold">告</span></p>
        <p><span style="font-size:12px;font-weight: bold">所</span></p>
        <p><span style="font-size:12px;font-weight: bold">需</span></p>
        <p><span style="font-size:12px;font-weight: bold">信</span></p>
        <p><span style="font-size:12px;font-weight: bold">息</span></p>
      </td>
      <td colspan="12"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <span style="font-size:12px;">注：以下信息，如出中文报告请用中文填写，如出英文报告请用英文填写。</span>
        </p>
      </td>
    </tr>
    <tr style=";page-break-inside:avoid;height:18px">
      <td colspan="2" rowspan="${nEn}"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <span style="font-size:12px;">★</span><span style="font-size:12px;" >
            报告抬头:
            <#if luaType==1>公司名称</#if>
            <#if luaType==2>Company Name</#if>
          </span>
        </p>
      </td>

      <td colspan="10"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px ">
	  <span style="font-size:12px;color:#333;">
	<#if luaType==1>
      ${(orderReportDTO.reportCompanyNameCn)!''}
    </#if>
        <#if luaType==2> ${(orderReportDTO.reportCompanyNameEn)!''}</#if>
	  </span>
        </p>
      </td>
    </tr>
    <tr style=";page-break-inside:avoid;height:18px">
      <td colspan="2" rowspan="${nEn}" style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <span style="font-size:12px;">★</span><span style="font-size:12px;">报告地址:
            <#if luaType==1>公司地址</#if>
            <#if luaType==2>Company Address</#if>
          </span>
        </p>
      </td>
      <td colspan="10"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
        <span style="font-size:12px;color:#333;">
		<#if luaType==1>
          ${(orderReportDTO.reportAddressCn)!''}
        </#if>
          <#if luaType==2> ${(orderReportDTO.reportAddressEn)!''}</#if>
		</span>
        </p>
      </td>
    </tr>
    <#if orderSampleDTOList?has_content>
      <#list orderSampleDTOList as orderSample>

        <#list orderSample.samplePDF as aa>
          <tr style=";page-break-inside:avoid;height:5px">
            <#list aa as moreSample>
              <td colspan="2"
                  style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
                <p style="line-height:16px"><span style="font-size:12px;">
                 <#if moreSample.sortShow==-2><span style="font-size:12px;">★</span></#if>
                    ${(moreSample.sampleKeyName)!''}</span> </p>
              </td>
              <td colspan="4"
                  style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
                <p style="line-height:16px">
                  <span style="font-size:12px;color:#333;">${(moreSample.sampleValue)!''}</span>
                </p>
              </td>
            </#list>
          </tr>
        </#list>

      </#list>
    </#if>
  </#if>




  <#if luaType==0>
    <tr>
      <td colspan="1" rowspan="3" style="border: 1px solid #333; padding: 0px 7px;text-align:center;border-right:none;border-top: none;">
        <p> <span style="font-size:12px;font-weight: bold">★</span></p>
        <p><span style="font-size:12px;font-weight: bold">申</span></p>
        <p><span style="font-size:12px;font-weight: bold">请</span></p>
        <p><span style="font-size:12px;font-weight: bold">方</span></p>
      </td>
      <td colspan="2" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;border-top: none;border-bottom: none;">
        <span style="font-size:12px;">申请公司名称 </span>
      </td>
      <td colspan="4" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;border-bottom: none;border-top:none;border-left: none;border-right: none;">
      <span style="font-size:12px;color:#333;">
	  ${(orderApplicationFormDTO.companyNameCn)!''}
	  </span>
      </td>
      <td colspan="2" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;border-top: none;border-bottom: none;">
        <span style="font-size:12px;">Company Name</span>
      </td>
      <td colspan="4" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;border-bottom: none;border-top:none;border-left: none;">
      <span style="font-size:12px;color:#333;">
	  ${(orderApplicationFormDTO.companyNameEn)!''}
	  </span>
      </td>
    </tr>
    <tr>
      <td colspan="2" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;">
        <span style="font-size:12px;">申请公司地址</span>
      </td>
      <td colspan="4" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;border-left: none;border-right: none;">
      <span style="font-size:12px;color:#333;">
        ${(orderApplicationFormDTO.province)!''}${(orderApplicationFormDTO.city)!''}${(orderApplicationFormDTO.town)!''}${(orderApplicationFormDTO.companyAddressCn)!''}
	  </span>
      </td>
      <td colspan="2" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;">
        <span style="font-size:12px;">Company Address</span>
      </td>
      <td colspan="4" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;border-left: none;">
      <span style="font-size:12px;color:#333;">
	   ${(orderApplicationFormDTO.companyAddressEn)!''}
	  </span>
      </td>
    </tr>
    <tr>
      <td colspan="1" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;border-top: none;">
        <span style="font-size:12px;">联系人</span>
      </td>
      <td rowspan="1" valign="null" align="null" style="border: 1px solid #333; padding: 0px 7px;border-top: none;border-left:none;" colspan="2">
        <span style="font-size:12px;color:#333;">${(orderApplicationFormDTO.linkPerson)!''}</span>
      </td>
      <td colspan="2" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;border-right: none;border-top: none;border-left:none;">
        <span style="font-size:12px;">电话</span>
      </td>
      <td colspan="3" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;border-top: none;">
        <span style="font-size:12px;color:#333;">${(orderApplicationFormDTO.linkPhone)!''}</span>
      </td>
      <td colspan="1" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;border-left: none;border-top: none;">
        <span style="font-size:12px;">电子邮箱</span>
      </td>
      <td colspan="3" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;border-left: none;border-top: none;">
        <span style="font-size:12px;color:#333;">${(orderApplicationFormDTO.linkEmail)!''}</span>
      </td>
    </tr>

    <tr style=";height:23px">
      <td colspan="2"
          style="border-right: 1px solid #333; border-bottom: 1px solid #333; border-left: 1px solid #333; border-image: initial; border-top: none; padding: 0px 7px;">
        <p><span style="font-size:12px;">★</span>
          <span style="font-size:12px;font-weight: bold">报告信息</span></p>
      </td>
      <td colspan="2"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:20px">
          <span style="font-size:12px;">报告语言</span>
        </p>
      </td>
      <td colspan="5"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:20px">
          <img src="${sqb1}" style="border: 1px solid #333;vertical-align: text-bottom;"></img>
          <span style="font-size:12px;">${orderFormPDFDTO.reportLua}</span></p>
      </td>
      <td
              style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:20px"><span style="font-size:12px;">报告形式</span></p>
      </td>
      <td colspan="3"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:20px">
          <img src="${sqb1}" style="border: 1px solid #333;vertical-align: text-bottom;"></img>
          <span style="font-size:12px;">${orderFormPDFDTO.reportForm}</span> </p>
      </td>
    </tr>

    <tr style=";height:23px">
      <td colspan="2"
          style="border-right: 1px solid #333; border-bottom: 1px solid #333; border-left: 1px solid #333; border-image: initial; border-top: none; padding: 0px 7px;">
        <p><span style="font-size:12px;">★</span>
          <span style="font-size:12px;font-weight: bold">报告出具方式</span></p>
      </td>

      <td colspan="11"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:20px">
          <span style="font-size:12px;">${(orderApplicationFormDTO.reportMethodShow)!''}</span> </p>
      </td>
    </tr>

    <tr style=";page-break-inside:avoid;height:18px">
      <td rowspan="${n}"
          style="border-right: 1px solid #333; border-bottom: 1px solid #333; border-left: 1px solid #333; border-image: initial; border-top: none; padding: 0px 7px;text-align:center">
        <p style=""> <span style="font-size:12px;font-weight: bold">报</span></p>
        <p><span style="font-size:12px;font-weight: bold">告</span></p>
        <p><span style="font-size:12px;font-weight: bold">所</span></p>
        <p><span style="font-size:12px;font-weight: bold">需</span></p>
        <p><span style="font-size:12px;font-weight: bold">信</span></p>
        <p><span style="font-size:12px;font-weight: bold">息</span></p>
      </td>
      <td colspan="12"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <span style="font-size:12px;">注：以下信息，如出中文报告请用中文填写，如出英文报告请用英文填写。</span>
        </p>
      </td>
    </tr>
    <tr style=";page-break-inside:avoid;height:18px">
      <td colspan="2" rowspan="${nEn}"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <span style="font-size:12px;">★</span><span style="font-size:12px;" >报告抬头</span>
        </p>
      </td>
      <td colspan="4"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <span style="font-size:12px;">公司名称(中文)</span>
        </p>
      </td>
      <td colspan="6"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px ">
          <span style="font-size:12px;color:#333;">
            ${(orderReportDTO.reportCompanyNameCn)!''}</span>
        </p>
      </td>
    </tr>
    <tr style=";page-break-inside:avoid;height:19px">
      <td colspan="4"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <span style="font-size:12px">Company Name(英文大写)</span>
        </p>
      </td>
      <td colspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <span style="font-size:12px;color:#333;">${(orderReportDTO.reportCompanyNameEn)!''}</span>
      </td>
    </tr>
    <tr style=";page-break-inside:avoid;height:18px">
      <td colspan="2" rowspan="${nEn}" style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <span style="font-size:12px;">★</span><span style="font-size:12px;">报告地址</span>
        </p>
      </td>
      <td colspan="4"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <span style="font-size:12px;">公司地址</span><span style="font-size:12px">(</span><span
                  style="font-size:12px;">中文</span><span style="font-size:12px">)</span><span
                  style="font-size:12px;"></span>
        </p>
      </td>
      <td colspan="6"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <span style="font-size:12px;color:#333;">
           ${(orderReportDTO.reportAddressCn)!''}</span>
        </p>
      </td>
    </tr>
    <tr style=";page-break-inside:avoid;height:23px">
      <td colspan="4"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <span style="font-size:12px;">Address(英文大写)</span>
        </p>
      </td>
      <td colspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <span style="font-size:12px;color:#333;">${(orderReportDTO.reportAddressEn)!''}</span>
      </td>
    </tr>
    <#if orderSampleDTOList?has_content>
      <#list orderSampleDTOList as orderSample>

        <#list orderSample.samplePDF as aa>
          <tr style=";page-break-inside:avoid;height:5px">
            <#list aa as moreSample>
              <td colspan="2"
                  style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
                <p style="line-height:16px"><span style="font-size:12px;">
                 <#if moreSample.sortShow==-2><span style="font-size:12px;">★</span></#if>
                    ${(moreSample.sampleKeyName)!''}</span> </p>
              </td>
              <td colspan="4"
                  style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
                <p style="line-height:16px">
                  <span style="font-size:12px;color:#333;">${(moreSample.sampleValue)!''}</span>
                </p>
              </td>
            </#list>
          </tr>
        </#list>

      </#list>
    </#if>
  </#if>







  <tr style=";page-break-inside:avoid;height:4px">
    <td rowspan="5"
        style="border-right: 1px solid #333; border-bottom: 1px solid #333; border-left: 1px solid #333; border-image: initial; border-top: none; text-align: center">
      <p style="text-align:center">
        <span style="font-size:12px;">★</span>
      </p>
      <p> <span style="font-size:12px;font-weight: bold">发&nbsp;付</span></p>
      <p> <span style="font-size:12px;font-weight: bold">票&nbsp;款</span></p>
      <p><span style="font-size:12px;font-weight: bold">信&nbsp;方</span></p>
      <p><span style="font-size:12px;font-weight: bold">息&nbsp;与</span></p>
      <p style="text-align:center"><br/></p>
    </td>
    <#if isForeign==0>
    <td colspan="2" valign="top"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="margin-top:1px;margin-right:0;margin-bottom:   1px;margin-left:0;line-height:16px">
        <span style="font-size:12px;">
            <#if orderApplicationFormDTO.invoice.invoiceType==2>
              发票抬头
            </#if>
          <#if orderApplicationFormDTO.invoice.invoiceType!=2>
            公司名称
          </#if>
        </span><span style="font-size:12px">&nbsp;&nbsp; </span>
      </p>
    </td>
    <td colspan="6" valign="top"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px; position: relative;">
      <p style="line-height:16px">
        <span style="font-size:12px;color:#333;">
          ${(orderApplicationFormDTO.invoice.invoiceTitle)!''}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          <b style="position: absolute; bottom: 2px; right: 1px;"><#if orderApplicationFormDTO.invoice.bossFlg==1> <em>[${(orderApplicationFormDTO.invoice.bossTag)!''}]</em> </#if>
          </b>

        </span>
      </p>
    </td>
    <td colspan="2" valign="top"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="margin-top:1px;margin-right:0;margin-bottom:   1px;margin-left:0;line-height:16px">
        <span style="font-size:12px;">开户行</span>
      </p>
    </td>
    <td colspan="2" valign="top"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="margin-top:1px;margin-right:0;margin-bottom:   1px;margin-left:0;line-height:16px">
        <span style="font-size:12px;color:#333;">${(orderApplicationFormDTO.invoice.bankName)!''}</span>
      </p>
    </td>
  </tr>
  <tr style=";page-break-inside:avoid;height:4px">
    <td colspan="2" valign="top"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="line-height:16px">
        <span style="font-size:12px;">公司地址</span>
      </p>
    </td>
    <td colspan="6" valign="top"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="line-height:16px">
        <span style="font-size:12px;color:#333;">${(orderApplicationFormDTO.invoice.regAddress)!''}</span>
      </p>
    </td>
    <td colspan="2" valign="top"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="line-height:16px">
        <span style="font-size:12px;">电话</span>
      </p>
    </td>
    <td colspan="2" valign="top"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="line-height:16px">
        <span style="font-size:12px;color:#333;">${(orderApplicationFormDTO.invoice.regPhone)!''}</span>
      </p>
    </td>
  </tr>
  <tr style=";page-break-inside:avoid;height:15px">
    <td colspan="2" valign="top"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="line-height:16px">
        <span style="font-size:12px;">税号</span>
      </p>
    </td>
    <td colspan="6" valign="top"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="line-height:16px">
        <span style="font-size:12px;color:#333;">${(orderApplicationFormDTO.invoice.taxNo)!''} <#if  orderApplicationFormDTO.invoice.isForeign ==1>(境外公司)</#if></span>
      </p>
    </td>
    <td colspan="2" valign="top"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="line-height:16px">
        <span style="font-size:12px;">账号</span>
      </p>
    </td>
    <td colspan="2" valign="top"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="line-height:16px">
        <span style="font-size:12px;color:#333;">${(orderApplicationFormDTO.invoice.bankNumber)!''}</span>
      </p>
    </td>
  </tr>
  </#if>


  <#if isForeign==1>
    <td colspan="2" valign="top"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="margin-top:1px;margin-right:0;margin-bottom:   1px;margin-left:0;line-height:16px">
        <span style="font-size:12px;">发票抬头</span><span style="font-size:12px">&nbsp;&nbsp; </span>
      </p>
    </td>
    <td colspan="6" valign="top"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px; position: relative;">
      <p style="line-height:16px">
        <span style="font-size:12px;color:#333;">${(orderApplicationFormDTO.invoice.invoiceTitle)!''}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          <b style="position: absolute; bottom: 2px; right: 1px;"><#if orderApplicationFormDTO.invoice.bossFlg==1> <em>[${(orderApplicationFormDTO.invoice.bossTag)!''}]</em> </#if></b>
        </span>
      </p>
    </td>
    <td colspan="2" valign="top"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="margin-top:1px;margin-right:0;margin-bottom:   1px;margin-left:0;line-height:16px">
        <span style="font-size:12px;">国家及地区</span>
      </p>
    </td>
    <td colspan="2" valign="top"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="margin-top:1px;margin-right:0;margin-bottom:   1px;margin-left:0;line-height:16px">
        <span style="font-size:12px;color:#333;">${(orderApplicationFormDTO.invoice.country)!''}</span>
      </p>
    </td>
    </tr>
    <tr style=";page-break-inside:avoid;height:4px">
      <td colspan="2" valign="top"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <span style="font-size:12px;">城市</span>
        </p>
      </td>
      <td colspan="6" valign="top"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <span style="font-size:12px;color:#333;">${(orderApplicationFormDTO.invoice.foreignCity)!''}</span>
        </p>
      </td>
      <td colspan="2" valign="top"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <span style="font-size:12px;">邮编</span>
        </p>
      </td>
      <td colspan="2" valign="top"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <span style="font-size:12px;color:#333;">${(orderApplicationFormDTO.invoice.postCode)!''}</span>
        </p>
      </td>
    </tr>



    <tr style=";page-break-inside:avoid;height:15px">
      <td colspan="2" valign="top"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <span style="font-size:12px;">地址</span>
        </p>
      </td>
      <td colspan="10" valign="top"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <span style="font-size:12px;color:#333;">${(orderApplicationFormDTO.invoice.regAddress)!''} </span>
        </p>
      </td>
    </tr>
    <tr style=";page-break-inside:avoid;height:15px">
      <td colspan="2" valign="top"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <span style="font-size:12px;">联系人</span>
        </p>
      </td>
      <td colspan="6" valign="top"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <span style="font-size:12px;color:#333;">${(orderApplicationFormDTO.invoice.contact)!''} </span>
        </p>
      </td>
      <td colspan="2" valign="top"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <span style="font-size:12px;">电话</span>
        </p>
      </td>
      <td colspan="2" valign="top"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <span style="font-size:12px;color:#333;">${(orderApplicationFormDTO.invoice.regPhone)!''}</span>
        </p>
      </td>
    </tr>
  </#if>



  <tr style=";page-break-inside:avoid;height:4px">
    <td colspan="2"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="margin-top:1px;margin-right:0;margin-bottom:   1px;margin-left:0;line-height:16px">
        <span style="font-size:12px;">付款方联系人</span>
      </p>
    </td>
    <td colspan="2"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="line-height:16px">
        <span style="font-size:12px;color:#333;"> ${(orderApplicationFormDTO.receiveName)!''}</span>
      </p>
    </td>
    <td colspan="1"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="line-height:16px">
        <span style="font-size:12px;color:black">手机</span>
      </p>
    </td>
    <td colspan="3"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="line-height:16px">
        <span style="font-size:12px;color:#333;">   ${(orderApplicationFormDTO.receivePhone)!''}</span>
      </p>
    </td>
    <td colspan="2"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="line-height:16px">
        <span style="font-size:12px;">电子邮箱</span>
      </p>
    </td>
    <td colspan="2"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="line-height:16px">
        <span style="font-size:12px;color:#333;">  ${(orderApplicationFormDTO.receiveEmail)!''}</span>
      </p>
    </td>
  </tr>
  <tr style=";page-break-inside:avoid;height:4px">
    <td colspan="2"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="margin-top:1px;margin-right:0;margin-bottom:   1px;margin-left:0;line-height:16px">
        <span style="font-size:12px;">发票类型</span>
      </p>
    </td>
    <td colspan="3"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="line-height:16px">
        <span style="font-size:12px;color:#333;">${(orderApplicationFormDTO.invoice.invoiceTypeShow)!''}</span>
      </p>
    </td>
    <td colspan="3"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="line-height:16px">
        <span style="font-size:12px;">发票邮寄地址</span>
      </p>
    </td>
    <td colspan="4"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="line-height:16px">
    <span style="font-size:12px;color:#333;">
         ${(invoiceAddressStr)!''}
    </span>
      </p>
    </td>
  </tr>
</table>
<table width="100%" style="width: 100%;" cellspacing="0">
  <col width="35%" />
  <col width="17%" />
  <col width="19%" />
  <col width="7%" />
  <col width="22%" />
  <col width="7%" />
  <#if useDetermine==0>
    <tr style=";height:4px">
      <td style="border-right: 1px solid #333; border-bottom: 1px solid #333; border-left: 1px solid #333; border-image: initial; border-top: none; padding: 0px 7px;">
        <p style="margin-top:3px;margin-right:0;margin-bottom:3px;margin-left:0;text-align:center">
          <span style="font-size:12px;">★</span>
          <span style="font-size:12px;font-weight: bold">测试项目</span>
        </p>
      </td>
      <td style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="text-align:center">
          <span style="font-size:12px;font-weight: bold">测试样品</span>
        </p>
      </td>
      <td style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="text-align:center">
          <span style="font-size:12px;font-weight: bold">测试方法</span>
        </p>
      </td>
      <td style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="margin-top:3px;margin-right:0;margin-bottom:3px;margin-left:0;text-align:center">
          <span style="font-size:12px;font-weight: bold">是否判定</span>
        </p>
      </td>
      <td style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="margin-top:3px;margin-right:0;margin-bottom:3px;margin-left:0;text-align:center;">
          <span style="font-size:12px;font-weight: bold">测试备注</span>
        </p>
      </td>
    </tr>
    <#if orderDetailDTOList?has_content>
      <#list orderDetailDTOList as orderDetail>
        <tr style=";height:20px">
          <td style="border-right: 1px solid #333; border-bottom: 1px solid #333; border-left: 1px solid #333; border-image: initial; border-top: none; padding: 0px 7px;">
            <p>
              <span style="font-size:12px;color:#333"> ${(orderDetail.itemName)!''}</span>
            </p>
          </td>
          <td style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
            <p>
    <span style="font-size:12px;color:#333;">
     <#if orderDetail.orderSampleDTOList?? && (orderDetail.orderSampleDTOList?size > 0)  >
       <#list orderDetail.orderSampleDTOList as orderSample>
         <span style="font-size: 13px;color:#878787;">
              ${(orderSample.row)!''}#${(orderSample.sampleNameShow)!''}
         </span>
       </#list>
     </#if>
    </span>
            </p>
          </td>
          <td style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
            <p>
              <span style="font-size:12px;color:#333;">${(orderDetail.standardCode)!''}</span>
            </p>
          </td>
          <td style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
            <p style="line-height:20px">
              <#if orderDetail.isDetermine==0>
                <span style="font-size:12px;color:#333;">否</span>
              </#if>
              <#if orderDetail.isDetermine==1>
                <span style="font-size:12px;color:#333;">是</span>
              </#if>
            </p>
          </td>
          <td style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
            <p style="line-height:20px">
              <span style="font-size:12px;color:#333;">${(orderDetail.testMemo)!''}</span>
            </p>
          </td>

        </tr>
      </#list>
    </#if>
  </#if>

</table>


<table width="100%" style="width: 100%;" cellspacing="0">
  <tr style=";height:10px" class="firstRow">
    <td width="100" colspan="13" style="border: 1px solid #333; padding: 0px 7px;">
      <p style="margin-top:4px;margin-right:0;margin-bottom:   4px;margin-left:0;line-height:16px">
        <span style="font-size:12px;letter-spacing:-0;font-weight: bold">测试备注:</span>
        <span style="font-size:12px;color:#333;">${(orderApplicationFormDTO.testMemo)!''}</span>
      </p>
    </td>
  </tr>



  <tr style=";height:23px">
    <td width="100" colspan="13"
        style="border-right: 1px solid #333; border-bottom: 1px solid #333; border-left: 1px solid #333; border-image: initial; border-top: none; padding: 0px 7px;">
      <p>
        <span style="font-size:12px;">★</span>
        <span style="font-size:12px;letter-spacing:-0;font-weight: bold">服务周期要求：</span>

        <span style="font-size:12px;color:#333"> ${(orderFormPDFDTO.isUrgentShow)!''}</span>

        <span style="font-size:12px;color:#333 ;">
          ${(orderFormPDFDTO.testCycleMemo)!''}
        </span>
      </p>
    </td>

  </tr>





  <tr style=";height:18px">
    <td width="19" colspan="3" rowspan="2"
        style="border-right: 1px solid #333; border-bottom: 1px solid #333; border-left: 1px solid #333; border-image: initial; border-top: none; padding: 0px 7px;">
      <p style="text-align:justify;text-justify:inter-ideograph;line-height:12px">
        <span style="font-size:12px;">★</span>
        <span style="font-size:12px;font-weight: bold">样品退回</span>
      </p>
      <p style="margin-top:3px;margin-right:0;margin-bottom:   3px;margin-left:0">
        <span style="font-size:11px;">(</span><span
                style="font-size:10px;">不适用化学测试</span><span
                style="font-size:   11px;">)</span>
      </p>
    </td>
    <td width="80" colspan="10" valign="bottom"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="margin-top:3px;margin-right:0;margin-bottom:   3px;margin-left:0">
        <img src="${sqb1}" style="border: 1px solid #333;vertical-align: text-bottom;"></img>
        <#if orderApplicationFormDTO.isRefundSample==1>
          <span style="font-size:12px;">退样（退回快递,顺丰到付）</span>
        </#if>
        <#if orderApplicationFormDTO.isRefundSample==0>
          <span style="font-size:12px;">不需要退回</span>
        </#if>
      </p>
    </td>
  </tr>
  <tr style=";height:13px">
    <td width="18" colspan="4" valign="bottom"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="margin-top:3px;margin-right:0;margin-bottom:   3px;margin-left:0">
        <span style="font-size:12px;">样品退回地址</span>
      </p>
    </td>
    <td width="62" colspan="6" valign="bottom"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="margin-top:3px;margin-right:0;margin-bottom:   3px;margin-left:0">
    <span style="font-size:12px;color:#333;">
        <#if orderApplicationFormDTO.isRefundSample==1>
          ${(backStr)!''}
        </#if>
      <#if orderApplicationFormDTO.isRefundSample==0>
        不需要退回地址
      </#if>
    </span>
      </p>
    </td>
  </tr>

  <tr style=";height:20px">
    <td width="19" colspan="3" rowspan="2"
        style="border-right: 1px solid #333; border-bottom: 1px solid #333; border-left: 1px solid #333; border-image: initial; border-top: none; padding: 0px 7px;">
      <p style="text-align:justify;text-justify:inter-ideograph;line-height:12px">
        <span style="font-size:12px;">★</span>
        <span style="font-size:12px;font-weight: bold">报告交付</span>
      </p>
    </td>
    <td width="18" colspan="4" valign="bottom"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="margin-top:3px;margin-right:0;margin-bottom:   3px;margin-left:0">
        <span style="font-size:12px;">报告发送邮箱</span>
      </p>
    </td>
    <td width="62" colspan="6" valign="bottom"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="margin-top:3px;margin-right:0;margin-bottom:   3px;margin-left:0">
        <span style="font-size:12px;color:#333;">${(email)!''}</span>
      </p>
    </td>
  </tr>
  <tr style=";height:5px">
    <td width="18" colspan="4" valign="bottom"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="margin-top:3px;margin-right:0;margin-bottom:   3px;margin-left:0">
        <span style="font-size:12px;">报告邮寄地址</span>
      </p>
    </td>
    <td width="62" colspan="6" valign="bottom"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="margin-top:3px;margin-right:0;margin-bottom:   3px;margin-left:0">
    <span style="font-size:12px;color:#333">
        ${(reportStr)!''}
	 </span>
      </p>
    </td>
  </tr>
  <tr style=";height:15px">
    <td width="100" colspan="13" valign="top"
        style="border-right: 1px solid #333; border-bottom: 1px solid #333; border-left: 1px solid #333; border-image: initial; border-top: none; padding: 0px 7px;">
      <p style="margin-top:3px;margin-right:0;margin-bottom:   3px;margin-left:0">
        <span style="font-size:12px;font-weight: bold">提示信息：</span>
      </p>
      <p style="line-height:13px">
        <span style="font-size:12px"> 1.  </span><span style="font-size:12px;">如需CMA或CNAS盖章，请提前告知工程师。</span>
      </p>

      <p style="line-height:13px">
        <span style="font-size:12px">2. </span><span style="font-size:12px;">实验室的评价仅基于实验室活动的实际值，未将实验室活动的结果不确定度影响计入。</span>
      </p>
      <p style="line-height:13px">
        <span style="font-size:12px">3. </span><span style="font-size:12px;">若未指明测试标准</span><span
                style="font-size:12px">/</span><span style="font-size:12px;">产品标准及年代号，则默认为客户接受</span><span
                style="font-size:12px">SGS</span><span style="font-size:12px;">推荐的测试标准</span><span
                style="font-size:12px">/</span><span style="font-size:12px;">产品标准。</span>
      </p>
      <p style="line-height:13px">
        <span style="font-size:12px">4. </span><span style="font-size:12px;">若未告知不接受分包，则视为接受。</span><span
                style="font-size:12px">SGS</span><span style="font-size:12px;">将分包到认可的检测机构。</span>
      </p>
      <p style="line-height:13px">
        <span style="font-size:12px"> 5.  </span><span style="font-size:12px;">测试后样品会有不同程度的损坏/损耗，余样保留时间30天，逾期销毁。</span>
      </p>
      <p style="line-height:13px">
        <span style="font-size:12px"> 6.  </span><span style="font-size:12px;">报告正式发布后，如需进行修改，根据修改的测试内容和项目另行评估收费。</span>
      </p>
      <p style="line-height:13px">
        &nbsp;
      </p>
    </td>
  </tr>
  <tr style=";height:15px">
    <td width="100" colspan="13" valign="top"
        style="border-right: 1px solid #333; border-bottom: 1px solid #333; border-left: 1px solid #333; border-image: initial; border-top: none; padding: 0px 7px;">
      <p style="margin-top:3px;margin-right:0;margin-bottom:   3px;margin-left:0">
        <span style="font-size:12px;">我们申请做以上测试并同意</span><span style="font-size:12px">SGS</span><span
                style="font-size:12px;">通用服务条款上的所有条款。（请务必在此位置签名或盖章）</span>
      </p>
      <p>
        <span style="font-size:12px;">申请人签名</span><span style="font-size:12px">/</span><span
                style="font-size:12px;">盖章：</span><span style="font-size:12px"> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span
                style="font-size:12px;">日期</span> <span style="font-size:12px;">：</span>
      </p>
      <p>

      </p>
    </td>
  </tr>
  <tr>
    <td width="51" style="border:none"></td>
    <td width="70" style="border:none"></td>
    <td width="30" style="border:none"></td>
    <td width="70" style="border:none"></td>
    <td width="10" style="border:none"></td>
    <td width="60" style="border:none"></td>
    <td width="0" style="border:none"></td>
    <td width="40" style="border:none"></td>
    <td width="70" style="border:none"></td>
    <td width="110" style="border:none"></td>
    <td width="1" style="border:none"></td>
    <td width="218" style="border:none"></td>
    <td width="44" style="border:none"></td>
  </tr>
</table>
<p style="text-align: center;margin-top: 30px;">
  <span style="text-align: center; text-indent: 20px; font-size: 10px;">本公司测试申请和测试报告签发均依据本公司的</span><span
          style="text-align: center; text-indent: 20px; font-size: 10px;">”</span><span
          style="text-align: center; text-indent: 20px; font-size: 10px; ">检验和测试服务通用条款</span><span
          style="text-align: center; text-indent: 20px; font-size: 10px;">”</span><span
          style="text-align: center; text-indent: 20px; font-size: 10px; ">，详细内容见：</span><span
          style="text-align: center; text-indent: 20px; font-size: 11px; "><ins>https://www.sgsgroup.com.cn/zh-cn/terms-and-conditions</ins></span>
</p>
</body>
</html>
