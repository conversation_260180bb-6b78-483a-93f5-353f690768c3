<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.mapper.question.UserQuOptionMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.vo.VOUserQuOption" >
    <id column="ANSWER_ID" property="answerId" jdbcType="BIGINT" />
    <result column="REPLY_ID" property="replyId" jdbcType="BIGINT" />
    <result column="QUESTION_ID" property="questionId" jdbcType="BIGINT" />
    <result column="PART_NO" property="partNo" jdbcType="VARCHAR" />
    <result column="SUBJECT_NO" property="subjectNo" jdbcType="VARCHAR" />
    <result column="OPTION_NO" property="optionNo" jdbcType="VARCHAR" />
    <result column="OPTION_INFO" property="optionInfo" jdbcType="VARCHAR" />
    <result column="OPTION_INFO_EN" property="optionInfoEn" jdbcType="VARCHAR" />
    <result column="OPTION_SCORE" property="optionScore" jdbcType="INTEGER" />
    <result column="SORT_SHOW" property="sortShow" jdbcType="INTEGER" />
    <result column="FINAL_SCORE" property="finalScore" jdbcType="INTEGER" />
    <result column="STATE" property="state" jdbcType="TINYINT" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
    <result column="STANDARD_CODE" property="standardCode" jdbcType="VARCHAR" />
    <result column="OPTION_VAL" property="optionVal" jdbcType="VARCHAR" />
    <result column="OPTION_TEXT" property="optionText" jdbcType="VARCHAR" />
    <result column="RELATE_PART_NO" property="relatePartNo" jdbcType="VARCHAR" />
    <result column="OPTION_FILE" property="optionFile" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    ANSWER_ID, REPLY_ID, QUESTION_ID, PART_NO, SUBJECT_NO, OPTION_NO, OPTION_INFO, OPTION_INFO_EN, 
    OPTION_SCORE, SORT_SHOW, FINAL_SCORE, STATE, CREATE_DATE, STATE_DATE, STANDARD_CODE, 
    OPTION_VAL, OPTION_TEXT,RELATE_PART_NO,OPTION_FILE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from TB_USER_QU_OPTION
    where ANSWER_ID = #{answerId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from TB_USER_QU_OPTION
    where ANSWER_ID = #{answerId,jdbcType=BIGINT}
  </delete>

  <insert id="insertSelective" parameterType="com.sgs.ecom.member.vo.VOUserQuOption" >
    insert into TB_USER_QU_OPTION
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="answerId != null" >
        ANSWER_ID,
      </if>
      <if test="replyId != null" >
        REPLY_ID,
      </if>
      <if test="questionId != null" >
        QUESTION_ID,
      </if>
      <if test="partNo != null" >
        PART_NO,
      </if>
      <if test="subjectNo != null" >
        SUBJECT_NO,
      </if>
      <if test="optionNo != null" >
        OPTION_NO,
      </if>
      <if test="optionInfo != null" >
        OPTION_INFO,
      </if>
      <if test="optionInfoEn != null" >
        OPTION_INFO_EN,
      </if>
      <if test="optionScore != null" >
        OPTION_SCORE,
      </if>
      <if test="sortShow != null" >
        SORT_SHOW,
      </if>
      <if test="finalScore != null" >
        FINAL_SCORE,
      </if>
      <if test="state != null" >
        STATE,
      </if>
      <if test="createDate != null" >
        CREATE_DATE,
      </if>
      <if test="stateDate != null" >
        STATE_DATE,
      </if>
      <if test="standardCode != null" >
        STANDARD_CODE,
      </if>
      <if test="optionVal != null" >
        OPTION_VAL,
      </if>
      <if test="optionText != null" >
        OPTION_TEXT,
      </if>
      <if test="relatePartNo != null" >
        RELATE_PART_NO,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="answerId != null" >
        #{answerId,jdbcType=BIGINT},
      </if>
      <if test="replyId != null" >
        #{replyId,jdbcType=BIGINT},
      </if>
      <if test="questionId != null" >
        #{questionId,jdbcType=BIGINT},
      </if>
      <if test="partNo != null" >
        #{partNo,jdbcType=VARCHAR},
      </if>
      <if test="subjectNo != null" >
        #{subjectNo,jdbcType=VARCHAR},
      </if>
      <if test="optionNo != null" >
        #{optionNo,jdbcType=VARCHAR},
      </if>
      <if test="optionInfo != null" >
        #{optionInfo,jdbcType=VARCHAR},
      </if>
      <if test="optionInfoEn != null" >
        #{optionInfoEn,jdbcType=VARCHAR},
      </if>
      <if test="optionScore != null" >
        #{optionScore,jdbcType=INTEGER},
      </if>
      <if test="sortShow != null" >
        #{sortShow,jdbcType=INTEGER},
      </if>
      <if test="finalScore != null" >
        #{finalScore,jdbcType=INTEGER},
      </if>
      <if test="state != null" >
        #{state,jdbcType=TINYINT},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        #{stateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="standardCode != null" >
        #{standardCode,jdbcType=VARCHAR},
      </if>
      <if test="optionVal != null" >
        #{optionVal,jdbcType=VARCHAR},
      </if>
      <if test="optionText != null" >
        #{optionText,jdbcType=VARCHAR},
      </if>
      <if test="relatePartNo != null" >
        #{relatePartNo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.ecom.member.vo.VOUserQuOption" >
    update TB_USER_QU_OPTION
    <set >
      <if test="replyId != null" >
        REPLY_ID = #{replyId,jdbcType=BIGINT},
      </if>
      <if test="questionId != null" >
        QUESTION_ID = #{questionId,jdbcType=BIGINT},
      </if>
      <if test="partNo != null" >
        PART_NO = #{partNo,jdbcType=VARCHAR},
      </if>
      <if test="subjectNo != null" >
        SUBJECT_NO = #{subjectNo,jdbcType=VARCHAR},
      </if>
      <if test="optionNo != null" >
        OPTION_NO = #{optionNo,jdbcType=VARCHAR},
      </if>
      <if test="optionInfo != null" >
        OPTION_INFO = #{optionInfo,jdbcType=VARCHAR},
      </if>
      <if test="optionInfoEn != null" >
        OPTION_INFO_EN = #{optionInfoEn,jdbcType=VARCHAR},
      </if>
      <if test="optionScore != null" >
        OPTION_SCORE = #{optionScore,jdbcType=INTEGER},
      </if>
      <if test="sortShow != null" >
        SORT_SHOW = #{sortShow,jdbcType=INTEGER},
      </if>
      <if test="finalScore != null" >
        FINAL_SCORE = #{finalScore,jdbcType=INTEGER},
      </if>
      <if test="state != null" >
        STATE = #{state,jdbcType=TINYINT},
      </if>
      <if test="createDate != null" >
        CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        STATE_DATE = #{stateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="standardCode != null" >
        STANDARD_CODE = #{standardCode,jdbcType=VARCHAR},
      </if>
      <if test="optionVal != null" >
        OPTION_VAL = #{optionVal,jdbcType=VARCHAR},
      </if>
      <if test="optionText != null" >
        OPTION_TEXT = #{optionText,jdbcType=VARCHAR},
      </if>
    </set>
    where ANSWER_ID = #{answerId,jdbcType=BIGINT}
  </update>

  <!--自定义-->
  <update id="updateBatchByMap" >
    update TB_USER_QU_OPTION
    <set >
      <if test="state != null" >
        STATE = #{state,jdbcType=TINYINT},
      </if>
    </set>
    where 1=1
    <if test="questionId != null">
      AND QUESTION_ID=#{questionId}
    </if>
    <if test="partNo != null">
      AND PART_NO=#{partNo}
    </if>
    <if test="replyId != null">
      AND REPLY_ID=#{replyId}
    </if>
    <if test="replyId != null">
      AND REPLY_ID=#{replyId}
    </if>
    <if test="subjectNo != null" >
      AND SUBJECT_NO = #{subjectNo,jdbcType=VARCHAR}
    </if>
    <if test="optionNo != null" >
      AND OPTION_NO = #{optionNo,jdbcType=VARCHAR}
    </if>
    <if test="relatePartNo != null" >
      AND RELATE_PART_NO is null
    </if>

  </update>








  <resultMap id="resultQuestionOptionNameDTO" type="com.sgs.ecom.member.dto.question.QuestionOptionNameDTO" >
    <result column="QUESTION_TITLE" property="questionTitle" jdbcType="VARCHAR" />
    <result column="PART_NO" property="partNo" jdbcType="VARCHAR" />
    <result column="SUBJECT_NO" property="subjectNo" jdbcType="VARCHAR" />
    <result column="QUESTION_TYPE" property="questionType" jdbcType="VARCHAR" />
    <result column="OPTION_NO" property="optionNo" jdbcType="VARCHAR" />
    <result column="OPTION_INFO" property="optionInfo" jdbcType="VARCHAR" />
    <result column="OPTION_TEXT" property="optionText" jdbcType="VARCHAR" />
    <result column="OPTION_FILE" property="optionFile" jdbcType="VARCHAR" />
    <result column="POSITION" property="position" jdbcType="VARCHAR" />
    <result column="RELATE_PART_NO" property="relatePartNo" jdbcType="VARCHAR" />
  </resultMap>

  <select id="getQuestionOptionNameDTO" resultMap="resultQuestionOptionNameDTO" >
    select uqo.SUBJECT_NO,uqo.OPTION_NO,uqo.OPTION_INFO,uqs.QUESTION_TITLE ,uqs.PART_NO
    from
    TB_USER_QU_OPTION as uqo ,
    TB_USER_QU_SUBJECT as uqs,
    TB_USER_QU_PART as uqp
    where uqo.SUBJECT_NO=uqs.SUBJECT_NO
    and uqp.PART_NO=uqp.PART_NO
    and uqs.PART_NO=uqp.PART_NO
    and uqp.ORDER_NO=#{orderNo}
    and uqp.position=#{position}
    and uqo.REPLY_ID=uqp.REPLY_ID
    and uqo.state=1
    order by uqs.SORT_SHOW desc, uqo.SORT_SHOW desc

  <!--  and uqo.state=1 and uqs.state=1
    <if test="replyId != null">
      AND uqo.REPLY_ID=#{replyId}
    </if>
    <if test="questionId != null">
      AND uqo.QUESTION_ID=#{questionId}
    </if>

    <if test="partNo != null">
      AND uqo.PART_NO=#{partNo}
    </if>
    <if test="partNoList != null and partNoList.size()>0">
      AND uqo.PART_NO in
      <foreach collection="partNoList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>-->



  </select>


  <select id="selectPositionOption" resultMap="resultQuestionOptionNameDTO" >
    select uqo.SUBJECT_NO,uqo.OPTION_NO,uqo.OPTION_INFO,uqo.OPTION_TEXT,uqo.OPTION_FILE,
    uqs.QUESTION_TITLE ,uqs.PART_NO,uqs.QUESTION_TYPE,
    uqp.POSITION,uqo.RELATE_PART_NO
    from
    TB_USER_QU_OPTION as uqo ,
    TB_USER_QU_SUBJECT as uqs,
    TB_USER_QU_PART as uqp,
    TB_USER_QUESTION as uq
    where 1=1
    and uq.ORDER_NO=uqp.ORDER_NO
    and uqo.REPLY_ID=uq.REPLY_ID
    and uqo.REPLY_ID=uqs.REPLY_ID
    and uqo.REPLY_ID=uqp.REPLY_ID
    and uqo.SUBJECT_NO=uqs.SUBJECT_NO
    and uqo.PART_NO=uqp.PART_NO
    and uqo.PART_NO=uqs.PART_NO
    and uq.ORDER_NO=#{orderNo}
    and uq.state=1
    <if test="isOriginal != null">
      AND uq.IS_ORIGINAL=#{isOriginal}
    </if>
    <if test="position != null">
      AND uqp.position=#{position}
    </if>
    <if test="useHide != null">
      AND uqs.IS_HIDE=0
    </if>
    <if test="relatePartNo != null">
      AND uqo.RELATE_PART_NO is not null and uqo.RELATE_PART_NO!=""
    </if>
    and uqo.REPLY_ID=uqp.REPLY_ID
    and uqo.state=1
    and uqs.state=1
    order by uqp.PART_ID desc,uqs.SORT_SHOW asc,uqo.SORT_SHOW asc
  </select>


  <insert id="insertForeach"  >
    insert into TB_USER_QU_OPTION (
        REPLY_ID,
        QUESTION_ID,
        PART_NO,
        SUBJECT_NO,
        OPTION_NO,
        OPTION_INFO,
        OPTION_INFO_EN,
        OPTION_SCORE,
        SORT_SHOW,
        FINAL_SCORE,
        STATE,
        CREATE_DATE,
        STATE_DATE,
        STANDARD_CODE,
        OPTION_VAL,
        OPTION_TEXT,
        OPTION_FILE,
        RELATE_PART_NO)
    values
    <foreach collection ="list" item="option" index= "index" separator =",">
    (
          #{option.replyId,jdbcType=BIGINT},
          #{option.questionId,jdbcType=BIGINT},
          #{option.partNo,jdbcType=VARCHAR},
          #{option.subjectNo,jdbcType=VARCHAR},
          #{option.optionNo,jdbcType=VARCHAR},
          #{option.optionInfo,jdbcType=VARCHAR},
          #{option.optionInfoEn,jdbcType=VARCHAR},
          #{option.optionScore,jdbcType=INTEGER},
          #{option.sortShow,jdbcType=INTEGER},
          #{option.finalScore,jdbcType=INTEGER},
          #{option.state,jdbcType=TINYINT},
          #{option.createDate,jdbcType=TIMESTAMP},
          #{option.stateDate,jdbcType=TIMESTAMP},
          #{option.standardCode,jdbcType=VARCHAR},
          #{option.optionVal,jdbcType=VARCHAR},
          #{option.optionText,jdbcType=VARCHAR},
          #{option.optionFile,jdbcType=VARCHAR},
          #{option.relatePartNo,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>


  <select id="selectListByMap" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from TB_USER_QU_OPTION
    <include refid="baseQueryWhere"/>
  </select>

  <sql id="baseQueryWhere">
    where 1=1
    <if test="questionId != null">
      AND QUESTION_ID=#{questionId}
    </if>
    <if test="partNo != null">
      AND PART_NO=#{partNo}
    </if>
    <if test="partNoList != null ">
      AND PART_NO in
      <foreach collection="partNoList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="replyId != null">
      AND REPLY_ID=#{replyId}
    </if>
    <if test="state != null">
      AND  STATE = #{state}
    </if>
  </sql>
  <sql id="orderBy">
    <if test="orderBy != null and partNoList != null">
      order by
      field(
      PART_NO,
      <foreach collection="partNoList" index="index" item="item"  separator="," >
        #{item}
      </foreach>
      )
    </if>
  </sql>


</mapper>