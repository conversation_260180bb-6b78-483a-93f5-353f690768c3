package com.sgs.ecom.member.dto.detail;
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import java.sql.Timestamp;
import java.util.StringJoiner;

import com.platform.util.json.TimeFormatSerializer; 
import com.platform.annotation.ApiAnno; 
import com.platform.annotation.BeanAnno; 
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.dto.OrderBaseInfoMoreDTO;
import com.sgs.ecom.member.dto.user.UserAddressDTO;
import com.sgs.ecom.member.dto.user.UserCompanyDTO;
import com.sgs.ecom.member.dto.user.UserInvoiceDTO;
import com.sgs.ecom.member.enumtool.aplication.ReportMethodEnum;
import com.sgs.ecom.member.vo.VOUserInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class OrderApplicationFormDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select STATE_DATE,STATE,LINK_PHONE,FORM_TEMPLATE,CREATE_DATE,LINK_PERSON,COMPANY_ADDRESS_EN,TEST_CYCLE,COMPANY_NAME_CN,COMPANY_ADDRESS_CN,LINK_EMAIL,FORM_ID,COMPANY_NAME_EN from ORDER_APPLICATION_FORM"; 
 
 
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
	@JsonSerialize(using = TimeFormatSerializer.class)
	private Timestamp stateDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(groups={Default.class,BaseOrderFilter.OrderToOther.class,BaseOrderFilter.WordForm.class})
 	@BeanAnno(value="LINK_PHONE", getName="getLinkPhone", setName="setLinkPhone")
 	private String linkPhone;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="FORM_TEMPLATE", getName="getFormTemplate", setName="setFormTemplate")
 	private String formTemplate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
	@JsonSerialize(using = TimeFormatSerializer.class)
 	private Timestamp createDate;
 	@ApiAnno(groups={Default.class,BaseOrderFilter.OrderToOther.class,BaseOrderFilter.WordForm.class})
 	@BeanAnno(value="LINK_PERSON", getName="getLinkPerson", setName="setLinkPerson")
 	private String linkPerson;
 	@ApiAnno(groups={Default.class, BaseOrderFilter.OrderApplicationOther.class,BaseOrderFilter.OrderToOther.class,BaseOrderFilter.WordForm.class})
 	@BeanAnno(value="COMPANY_ADDRESS_EN", getName="getCompanyAddressEn", setName="setCompanyAddressEn")
 	private String companyAddressEn;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="TEST_CYCLE", getName="getTestCycle", setName="setTestCycle")
 	private Integer testCycle;
 	@ApiAnno(groups={Default.class, BaseOrderFilter.OrderApplicationOther.class,BaseOrderFilter.OrderToOther.class,BaseOrderFilter.WordForm.class})
 	@BeanAnno(value="COMPANY_NAME_CN", getName="getCompanyNameCn", setName="setCompanyNameCn")
 	private String companyNameCn;
 	@ApiAnno(groups={Default.class, BaseOrderFilter.OrderApplicationOther.class,BaseOrderFilter.OrderToOther.class,BaseOrderFilter.WordForm.class})
 	@BeanAnno(value="COMPANY_ADDRESS_CN", getName="getCompanyAddressCn", setName="setCompanyAddressCn")
 	private String companyAddressCn;
 	@ApiAnno(groups={Default.class,BaseOrderFilter.OrderToOther.class,BaseOrderFilter.WordForm.class})
 	@BeanAnno(value="LINK_EMAIL", getName="getLinkEmail", setName="setLinkEmail")
 	private String linkEmail;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="FORM_ID", getName="getFormId", setName="setFormId")
 	private Long formId;
 	@ApiAnno(groups={Default.class, BaseOrderFilter.OrderApplicationOther.class,BaseOrderFilter.OrderToOther.class,BaseOrderFilter.WordForm.class})
 	@BeanAnno(value="COMPANY_NAME_EN", getName="getCompanyNameEn", setName="setCompanyNameEn")
 	private String companyNameEn;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OrderToOther.class})
	private Integer isRefundSample;
	@ApiAnno(groups={Default.class})
	private String reportSendCc;
	@ApiAnno(groups={Default.class})
	private UserAddressDTO backAddress;
	@ApiAnno(groups={Default.class})
	private UserAddressDTO reportAddress;
	@ApiAnno(groups={Default.class})
	private UserAddressDTO invoiceAddress;
	@ApiAnno(groups={Default.class})
	private UserAddressDTO buyAddress;
	@ApiAnno(groups={Default.class})
	private UserAddressDTO factoryAddress;
	@ApiAnno(groups={Default.class})
	private UserAddressDTO supplierAddress;
	@ApiAnno(groups={Default.class})
	private UserAddressDTO detectionAddress;
	@ApiAnno(groups={Default.class})
	private UserAddressDTO teachingMaterialAddress;//ACA客户-教材寄送地址
	@ApiAnno(groups={Default.class})
	private UserAddressDTO certificateAddress;//ACA客户-证书寄送地址
	@ApiAnno(groups={Default.class})
	private UserInvoiceDTO invoice;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OrderToOther.class,BaseOrderFilter.WordForm.class})
	private String testMemo;
	@ApiAnno(groups={Default.class})
	private String testMemoImg;
	@ApiAnno(groups={Default.class})
	private int isTransfer;

	//税号
	@ApiAnno(groups={Default.class})
	private String taxNo;
	//公司电话
	@ApiAnno(groups={Default.class})
	private String regPhone;
	//开户银行
	@ApiAnno(groups={Default.class})
	private String bankName;
	@ApiAnno(groups={Default.class})
	//银行账户
	private String bankNumber;
	@ApiAnno(groups={Default.class})
	//营业执照图片
	private String img;


	@ApiAnno(groups={Default.class})
	private String receiveName;
	@ApiAnno(groups={Default.class})
	private String receivePhone;
	@ApiAnno(groups={Default.class})
	private String receiveEmail;

	@ApiAnno(groups={Default.class})
	private int reportMethod;
	@ApiAnno(groups={Default.class})
	private String reportMethodValue;
	@ApiAnno(groups={Default.class})
	private String reportMethodShow;
	@ApiAnno(groups={Default.class})
	private String reportCompanyNameCn;
	@ApiAnno(groups={Default.class})
	private String reportCompanyNameEn;
	@ApiAnno(groups={Default.class})
	private String reportAddressCn;
	@ApiAnno(groups={Default.class})
	private String reportAddressEn;
	@ApiAnno(groups={Default.class})
	private String province;
	private String city;
	private String town;
	private String orderNo;
	@ApiAnno(groups={Default.class})
	private String labCode;
	@ApiAnno(groups={Default.class})
	private String reportAddressStr;
	@ApiAnno(groups={Default.class})
	private String backAddressStr;
	@ApiAnno(groups={Default.class})
	private String csFormMemoStr;//客服备注
	@ApiAnno(groups={Default.class})
	private String testMemoFileName;//测试备注的附件字符串

	private String companyAddressCnSame;



	// 检测地址(申请表打印用)
	private String detectionAddressStr;
	// 最终买家地址(申请表打印用)
	private String buyAddressStr;
	// 最终买家地址英文(申请表打印用)
	private String buyAddressStrEn;
	// 最终买家联系人(申请表打印用)
	private String buyContactStr;
	// 最终买家联系人英文(申请表打印用)
	private String buyContactStrEn;
	// 供应商地址(申请表打印用)
	private String supplierAddressStr;
	// 供应商地址英文(申请表打印用)
	private String supplierAddressStrEn;
	// 供应商联系人(申请表打印用)
	private String supplierContactStr;
	// 供应商联系人英文(申请表打印用)
	private String supplierContactStrEn;
	// 工厂地址(申请表打印用)
	private String factoryAddressStr;
	// 工厂地址英文(申请表打印用)
	private String factoryAddressStrEn;
	// 工厂联系人(申请表打印用)
	private String factoryContactStr;
	// 工厂联系人英文(申请表打印用)
	private String factoryContactStrEn;

	public OrderApplicationFormDTO(OrderBaseInfoMoreDTO orderBaseInfoMoreDTO, VOUserInfo voUserInfo, UserCompanyDTO userCompanyDTO) {
		this.linkPerson = voUserInfo.getUserNick();
		this.linkPhone = voUserInfo.getUserPhone();
		this.linkEmail = voUserInfo.getUserEmail();
		this.province = userCompanyDTO.getProvice();
		this.city = userCompanyDTO.getCity();
		this.town = userCompanyDTO.getTown();
		this.companyNameCn = userCompanyDTO.getCompanyName();
		this.companyNameEn = userCompanyDTO.getCompanyNameEn();
		this.companyAddressCn = userCompanyDTO.getAddress();
		this.companyAddressEn = userCompanyDTO.getCompanyAddrEn();
		this.orderNo = orderBaseInfoMoreDTO.getOrderNo();
	}

	public OrderApplicationFormDTO(OrderBaseInfoMoreDTO orderBaseInfoMoreDTO) {
		this.linkPerson = orderBaseInfoMoreDTO.getUserName();
		this.linkPhone = orderBaseInfoMoreDTO.getUserPhone();
		this.linkEmail = orderBaseInfoMoreDTO.getUserEmail();
		this.province = orderBaseInfoMoreDTO.getProvince();
		this.city = orderBaseInfoMoreDTO.getCity();
		this.town = orderBaseInfoMoreDTO.getTown();
		this.companyNameCn = orderBaseInfoMoreDTO.getCompanyName();
		this.companyNameEn = orderBaseInfoMoreDTO.getCompanyNameEn();
		this.companyAddressCn = orderBaseInfoMoreDTO.getCompanyAddressCn();
		this.companyAddressEn = orderBaseInfoMoreDTO.getCompanyAddressEn();
		this.orderNo = orderBaseInfoMoreDTO.getOrderNo();
	}

 	public String getCompanyAddressCn(){
		StringJoiner stringJoiner=new StringJoiner("");
		if(StringUtils.isNotBlank(province)){
			stringJoiner.add(province);
		}
		if(StringUtils.isNotBlank(city)){
			stringJoiner.add(city);
		}
		if(StringUtils.isNotBlank(town)){
			stringJoiner.add(town);
		}
		if(StringUtils.isNotBlank(companyAddressCn)){
			stringJoiner.add(companyAddressCn);
		}
		return stringJoiner.toString();
 	}

	public String getReportMethodShow() {
 		if(ReportMethodEnum.OTHER.getIndex()==reportMethod){
			return reportMethodValue;
		}
		return ReportMethodEnum.getNameCh(reportMethod);
	}


	public String getCompanyAddressCnSame() {
		this.companyAddressCnSame=companyAddressCn;
		return companyAddressCnSame;
	}


}