package com.sgs.ecom.member.enumtool.rsts;

public enum CommonCustCode {

	RSTS_COMMON("RSTS_COMMON", "1302"),
    TEST_COMMON("TEST_COMMON", "test");
	
    // 成员变量  
    private String name;  
    private String index;  
    // 构造方法  
    private CommonCustCode(String name, String index) {  
        this.name = name;  
        this.index = index;  
    }  

    // 普通方法  
    public static String getName(String index) {  
        for (CommonCustCode c : CommonCustCode.values()) {  
            if (c.getIndex().equals(index)) {  
                return c.name;  
            }  
        }  
        return null;  
    }  
    // get set 方法  
    public String getName() {  
        return name;  
    }  

    public String getIndex() {  
        return index;  
    }   
}
