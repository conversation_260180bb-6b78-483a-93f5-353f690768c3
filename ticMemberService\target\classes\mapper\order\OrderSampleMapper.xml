<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.order.dao.OrderSampleMapper" >

  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.vo.VOOrderSample" >
    <id column="SAMPLE_ID" property="sampleId" jdbcType="BIGINT" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="SAMPLE_NAME" property="sampleName" jdbcType="VARCHAR" />
    <result column="SAMPLE_NAME_EN" property="sampleNameEn" jdbcType="VARCHAR" />
    <result column="SAMPLE_NAME_CN" property="sampleNameCn" jdbcType="VARCHAR" />
    <result column="PRODUCT_INFO" property="productInfo" jdbcType="VARCHAR" />
    <result column="PRODUCT_INFO_EN" property="productInfoEn" jdbcType="VARCHAR" />
    <result column="PRODUCT_BATCH" property="productBatch" jdbcType="VARCHAR" />
    <result column="PRODUCT_BATCH_EN" property="productBatchEn" jdbcType="VARCHAR" />
    <result column="MATERIAL_GRADE" property="materialGrade" jdbcType="VARCHAR" />
    <result column="MATERIAL_GRADE_EN" property="materialGradeEn" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="INTEGER" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
    <result column="GROUP_NO" property="groupNo" jdbcType="VARCHAR" />
    <result column="SAMPLE_NO" property="sampleNo" jdbcType="VARCHAR" />
    <result column="SUPPLIER_NAME" property="supplierName" jdbcType="VARCHAR" />
    <result column="BUYERS_NAME" property="buyersName" jdbcType="VARCHAR" />
    <result column="SUPPLIER_NAME_EN" property="supplierNameEn" jdbcType="VARCHAR" />
    <result column="BUYERS_NAME_EN" property="buyersNameEn" jdbcType="VARCHAR" />
    <result column="REMARK_EN" property="remarkEn" jdbcType="VARCHAR" />
    <result column="LOT_NO" property="lotNo" jdbcType="VARCHAR" />
    <result column="LOT_NO_EN" property="lotNoEn" jdbcType="VARCHAR" />
    <result column="PRODUCER" property="producer" jdbcType="VARCHAR" />
    <result column="PRODUCER_EN" property="producerEn" jdbcType="VARCHAR" />
  </resultMap>

  <resultMap id="resultSampleDTO" type="com.sgs.ecom.member.dto.order.OrderSampleDTO" >
    <id column="SAMPLE_ID" property="sampleId" jdbcType="BIGINT" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="SAMPLE_NAME" property="sampleName" jdbcType="VARCHAR" />
    <result column="SAMPLE_NAME_EN" property="sampleNameEn" jdbcType="VARCHAR" />
    <result column="SAMPLE_NAME_CN" property="sampleNameCn" jdbcType="VARCHAR" />
    <result column="PRODUCT_INFO" property="productInfo" jdbcType="VARCHAR" />
    <result column="PRODUCT_INFO_EN" property="productInfoEn" jdbcType="VARCHAR" />
    <result column="PRODUCT_BATCH" property="productBatch" jdbcType="VARCHAR" />
    <result column="PRODUCT_BATCH_EN" property="productBatchEn" jdbcType="VARCHAR" />
    <result column="MATERIAL_GRADE" property="materialGrade" jdbcType="VARCHAR" />
    <result column="MATERIAL_GRADE_EN" property="materialGradeEn" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="INTEGER" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
    <result column="GROUP_NO" property="groupNo" jdbcType="VARCHAR" />
    <result column="SAMPLE_NO" property="sampleNo" jdbcType="VARCHAR" />
    <result column="SUPPLIER_NAME" property="supplierName" jdbcType="VARCHAR" />
    <result column="BUYERS_NAME" property="buyersName" jdbcType="VARCHAR" />
    <result column="SUPPLIER_NAME_EN" property="supplierNameEn" jdbcType="VARCHAR" />
    <result column="BUYERS_NAME_EN" property="buyersNameEn" jdbcType="VARCHAR" />
    <result column="REMARK_EN" property="remarkEn" jdbcType="VARCHAR" />
    <result column="LOT_NO" property="lotNo" jdbcType="VARCHAR" />
    <result column="LOT_NO_EN" property="lotNoEn" jdbcType="VARCHAR" />
    <result column="PRODUCER" property="producer" jdbcType="VARCHAR" />
    <result column="PRODUCER_EN" property="producerEn" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    SAMPLE_ID, ORDER_NO, SAMPLE_NAME, SAMPLE_NAME_EN,SAMPLE_NAME_CN, PRODUCT_INFO, PRODUCT_BATCH, MATERIAL_GRADE,
    REMARK, STATE, CREATE_DATE, STATE_DATE,GROUP_NO,SAMPLE_NO,PRODUCT_INFO_EN,MATERIAL_GRADE_EN,PRODUCT_BATCH_EN,
    SUPPLIER_NAME,BUYERS_NAME,SUPPLIER_NAME_EN,BUYERS_NAME_EN,REMARK_EN,LOT_NO,LOT_NO_EN,PRODUCER,PRODUCER_EN
  </sql>


  <select id="selectVOListByMap" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from ORDER_SAMPLE
    <include refid="baseQueryWhere"/>
    order by SAMPLE_ID asc
  </select>




  <insert id="insertSelective" parameterType="com.sgs.ecom.member.vo.VOOrderSample" >
    insert into ORDER_SAMPLE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="sampleId != null" >
        SAMPLE_ID,
      </if>
      <if test="orderNo != null" >
        ORDER_NO,
      </if>
      <if test="sampleName != null" >
        SAMPLE_NAME,
      </if>
      <if test="sampleNameEn != null" >
        SAMPLE_NAME_EN,
      </if>
      <if test="sampleNameCn != null" >
        SAMPLE_NAME_CN,
      </if>
      <if test="productInfo != null" >
        PRODUCT_INFO,
      </if>
      <if test="productInfoEn != null" >
        PRODUCT_INFO_EN,
      </if>
      <if test="productBatch != null" >
        PRODUCT_BATCH,
      </if>
      <if test="productBatchEn != null" >
        PRODUCT_BATCH_EN,
      </if>
      <if test="materialGrade != null" >
        MATERIAL_GRADE,
      </if>
      <if test="materialGradeEn != null" >
        MATERIAL_GRADE_EN,
      </if>
      <if test="supplierName != null" >
        SUPPLIER_NAME,
      </if>
      <if test="supplierNameEn != null" >
        SUPPLIER_NAME_EN,
      </if>
      <if test="buyersName != null" >
        BUYERS_NAME,
      </if>
      <if test="buyersNameEn != null" >
        BUYERS_NAME_EN,
      </if>
      <if test="remark != null" >
        REMARK,
      </if>
      <if test="remarkEn != null" >
        REMARK_EN,
      </if>
      <if test="state != null" >
        STATE,
      </if>
      <if test="createDate != null" >
        CREATE_DATE,
      </if>
      <if test="stateDate != null" >
        STATE_DATE,
      </if>
      <if test="groupNo != null" >
        GROUP_NO,
      </if>
      <if test="sampleNo != null" >
        SAMPLE_NO,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="sampleId != null" >
        #{sampleId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="sampleName != null" >
        #{sampleName,jdbcType=VARCHAR},
      </if>
      <if test="sampleNameEn != null" >
        #{sampleNameEn,jdbcType=VARCHAR},
      </if>
      <if test="sampleNameCn != null" >
        #{sampleNameCn,jdbcType=VARCHAR},
      </if>
      <if test="productInfo != null" >
        #{productInfo,jdbcType=VARCHAR},
      </if>
      <if test="productInfoEn != null" >
        #{productInfoEn,jdbcType=VARCHAR},
      </if>
      <if test="productBatch != null" >
        #{productBatch,jdbcType=VARCHAR},
      </if>
      <if test="productBatchEn != null" >
        #{productBatchEn,jdbcType=VARCHAR},
      </if>
      <if test="materialGrade != null" >
        #{materialGrade,jdbcType=VARCHAR},
      </if>
      <if test="materialGradeEn != null" >
        #{materialGradeEn,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null" >
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="supplierNameEn != null" >
        #{supplierNameEn,jdbcType=VARCHAR},
      </if>
      <if test="buyersName != null" >
        #{buyersName,jdbcType=VARCHAR},
      </if>
      <if test="buyersNameEn != null" >
        #{buyersNameEn,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="remarkEn != null" >
        #{remarkEn,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        #{state,jdbcType=INTEGER},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        #{stateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="groupNo != null" >
        #{groupNo,jdbcType=VARCHAR},
      </if>
      <if test="sampleNo != null" >
        #{sampleNo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>



  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.ecom.member.vo.VOOrderSample" >
    update ORDER_SAMPLE
    <set >
      <if test="orderNo != null" >
        ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="sampleName != null" >
        SAMPLE_NAME = #{sampleName,jdbcType=VARCHAR},
      </if>
      <if test="sampleNameEn != null" >
        SAMPLE_NAME_EN = #{sampleNameEn,jdbcType=VARCHAR},
      </if>
      <if test="sampleNameCn != null" >
        SAMPLE_NAME_CN = #{sampleNameCn,jdbcType=VARCHAR},
      </if>
      <if test="productInfo != null" >
        PRODUCT_INFO = #{productInfo,jdbcType=VARCHAR},
      </if>
      <if test="productInfoEn != null" >
        PRODUCT_INFO_EN = #{productInfoEn,jdbcType=VARCHAR},
      </if>
      <if test="productBatch != null" >
        PRODUCT_BATCH = #{productBatch,jdbcType=VARCHAR},
      </if>
      <if test="productBatchEn != null" >
        PRODUCT_BATCH_EN = #{productBatchEn,jdbcType=VARCHAR},
      </if>
      <if test="materialGrade != null" >
        MATERIAL_GRADE = #{materialGrade,jdbcType=VARCHAR},
      </if>
      <if test="materialGradeEn != null" >
        MATERIAL_GRADE_EN = #{materialGradeEn,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="remarkEn != null" >
        REMARK_EN = #{remarkEn,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null" >
        SUPPLIER_NAME = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="supplierNameEn != null" >
        SUPPLIER_NAME_EN = #{supplierNameEn,jdbcType=VARCHAR},
      </if>
      <if test="buyersName != null" >
        BUYERS_NAME = #{buyersName,jdbcType=VARCHAR},
      </if>
      <if test="buyersNameEn != null" >
        BUYERS_NAME_EN = #{buyersNameEn,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        STATE = #{state,jdbcType=INTEGER},
      </if>
      <if test="createDate != null" >
        CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        STATE_DATE = #{stateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="groupNo != null" >
        GROUP_NO = #{groupNo,jdbcType=VARCHAR},
      </if>
    </set>
    where SAMPLE_ID = #{sampleId,jdbcType=BIGINT}
  </update>
  <update id="updateFromByOrderNo">
    update ORDER_SAMPLE set STATE = 0 WHERE ORDER_NO = #{orderNo}
  </update>


  <!--自定义 -->
  <resultMap id="resultDTO" type="com.sgs.ecom.member.dto.order.OrderSampleDTO" >
    <id column="SAMPLE_ID" property="sampleId" jdbcType="BIGINT" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="SAMPLE_NAME" property="sampleName" jdbcType="VARCHAR" />
    <result column="SAMPLE_NAME_EN" property="sampleNameEn" jdbcType="VARCHAR" />
    <result column="SAMPLE_NAME_CN" property="sampleNameCn" jdbcType="VARCHAR" />
    <result column="PRODUCT_INFO" property="productInfo" jdbcType="VARCHAR" />
    <result column="PRODUCT_BATCH" property="productBatch" jdbcType="VARCHAR" />
    <result column="MATERIAL_GRADE" property="materialGrade" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="INTEGER" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
    <result column="GROUP_NO" property="groupNo" jdbcType="VARCHAR" />
    <result column="SAMPLE_NO" property="sampleNo" jdbcType="VARCHAR" />
    <result column="row" property="row" jdbcType="VARCHAR" />
    <result column="SUPPLIER_NAME" property="supplierName" jdbcType="VARCHAR" />
    <result column="BUYERS_NAME" property="buyersName" jdbcType="VARCHAR" />
    <result column="SUPPLIER_NAME_EN" property="supplierNameEn" jdbcType="VARCHAR" />
    <result column="BUYERS_NAME_EN" property="buyersNameEn" jdbcType="VARCHAR" />
    <result column="REMARK_EN" property="remarkEn" jdbcType="VARCHAR" />
    <result column="DETAIL_ID" property="detailId" jdbcType="BIGINT" />
  </resultMap>

  <sql id="sqlListDTO" >
    SAMPLE_ID, ORDER_NO, SAMPLE_NAME, SAMPLE_NAME_EN,SAMPLE_NAME_CN, PRODUCT_INFO, PRODUCT_BATCH, MATERIAL_GRADE,
    REMARK, STATE, CREATE_DATE, STATE_DATE,GROUP_NO,SAMPLE_NO,SUPPLIER_NAME,BUYERS_NAME,SUPPLIER_NAME_EN,BUYERS_NAME_EN,REMARK_EN
  </sql>

  <select id="selectListByMap" resultMap="resultDTO" >
    select
    @i:= @i+ 1 AS `row` ,
    <include refid="sqlListDTO" />
    from ORDER_SAMPLE,(SELECT @i:=0) as ii
    <include refid="baseQueryWhere"/>
    order by SAMPLE_ID asc
  </select>


  <insert id="copySampleBySql"  >
 insert into ORDER_SAMPLE(STATE_DATE,STATE,ORDER_NO,PRODUCT_INFO,CREATE_DATE,PRODUCT_BATCH,GROUP_NO,SAMPLE_NAME,SAMPLE_NAME_EN,SAMPLE_NAME_CN,SAMPLE_NO,MATERIAL_GRADE,REMARK)
 select now(),STATE,#{newOrderNo},PRODUCT_INFO,now(),PRODUCT_BATCH,#{newGroupNo},SAMPLE_NAME,SAMPLE_NAME_EN,SAMPLE_NAME_CN,SAMPLE_NO,MATERIAL_GRADE,REMARK
 from ORDER_SAMPLE where GROUP_NO = #{oldGroupNo} and ORDER_NO=#{orderNo} ORDER BY SAMPLE_ID asc
  </insert>


  <delete id="delSampleByMap">
    delete from ORDER_SAMPLE where order_no=#{orderNo} and GROUP_NO=#{groupNo}
  </delete>


  <insert id="insertForeach"  >
    insert into ORDER_SAMPLE (
    ORDER_NO,SAMPLE_NO,GROUP_NO,
    SAMPLE_NAME, SAMPLE_NAME_EN,SAMPLE_NAME_CN,
    PRODUCT_INFO,PRODUCT_INFO_EN,
    PRODUCT_BATCH,PRODUCT_BATCH_EN,
    MATERIAL_GRADE, MATERIAL_GRADE_EN,
    SUPPLIER_NAME,SUPPLIER_NAME_EN,
    BUYERS_NAME,BUYERS_NAME_EN,
    LOT_NO,LOT_NO_EN,PRODUCER,PRODUCER_EN,
    REMARK,REMARK_EN,
    STATE, CREATE_DATE, STATE_DATE
    )
    values
    <foreach collection ="list" item="sample" index= "index" separator =",">
      (
      #{sample.orderNo},#{sample.sampleNo},#{sample.groupNo},
      #{sample.sampleName},#{sample.sampleNameEn},#{sample.sampleNameCn},
      #{sample.productInfo},#{sample.productInfoEn},
      #{sample.productBatch},#{sample.productBatchEn},
      #{sample.materialGrade},#{sample.materialGradeEn},
      #{sample.supplierName},#{sample.supplierNameEn},
      #{sample.buyersName}, #{sample.buyersNameEn},
      #{sample.lotNo}, #{sample.lotNoEn}, #{sample.producer}, #{sample.producerEn},
      #{sample.remark}, #{sample.remarkEn},
      #{sample.state}, #{sample.createDate}, #{sample.stateDate}

      )
    </foreach>
  </insert>

  <!--自定义样品数据重新组装查询 -->
  <resultMap id="resultMoreDTO" type="com.sgs.ecom.member.dto.order.OrderSampleMoreDTO" >
    <id column="SAMPLE_ID" property="sampleId" jdbcType="BIGINT" />
    <result column="row" property="row" jdbcType="VARCHAR" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="SAMPLE_NAME" property="sampleName" jdbcType="VARCHAR" />
    <result column="SAMPLE_NAME_EN" property="sampleNameEn" jdbcType="VARCHAR" />
    <result column="SAMPLE_NAME_CN" property="sampleNameCn" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="INTEGER" />
    <result column="GROUP_NO" property="groupNo" jdbcType="VARCHAR" />
    <result column="SAMPLE_NO" property="sampleNo" jdbcType="VARCHAR" />
    <result column="SAMPLE_CATEGORY_CODE" property="sampleCategoryCode" jdbcType="VARCHAR" />
    <result column="SAMPLE_SHAPE_CODE" property="sampleShapeCode" jdbcType="VARCHAR" />
  </resultMap>

  <sql id="sqlMoreListDTO" >
   ORDER_NO,SAMPLE_NAME,SAMPLE_NAME_EN,SAMPLE_NAME_CN,STATE,SAMPLE_NO,SAMPLE_ID,GROUP_NO,SAMPLE_CATEGORY_CODE,SAMPLE_SHAPE_CODE
  </sql>
  <select id="selectMoreListByMap" resultMap="resultMoreDTO" >
    select
    @i:= @i+ 1 AS `row` ,
    <include refid="sqlMoreListDTO" />
    from ORDER_SAMPLE,(SELECT @i:=0) as ii
    <include refid="baseQueryWhere"/>
    order by ORDER_SHOW,SAMPLE_ID asc
  </select>

  <select id="selectMoreListByVO" resultMap="resultMoreDTO" >
    select
    @i:= @i+ 1 AS `row` ,
    <include refid="sqlMoreListDTO" />
    from ORDER_SAMPLE,(SELECT @i:=0) as ii
    <include refid="baseQueryVOWhere"/>
    order by ORDER_SHOW,SAMPLE_ID asc
  </select>


  <resultMap id="resultStrDTO" type="com.sgs.ecom.member.dto.order.OrderSampleStrDTO" >
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="SAMPLE_NO" property="sampleNo" jdbcType="VARCHAR" />
    <result column="SAMPLE_NAME_CN" property="sampleNameCn" jdbcType="VARCHAR" />
    <result column="SAMPLE_NAME_EN" property="sampleNameEn" jdbcType="VARCHAR" />
    <result column="sampleAttrStr" property="sampleAttrStr" jdbcType="VARCHAR" />
  </resultMap>
  <select id="qryOrderSampleStrList" resultMap="resultStrDTO" >
    select
    if(obi.c="CHI" or obi.c="LUA" , if(obi.bu=1900,t.sampleName ,os.SAMPLE_NAME_CN),"") SAMPLE_NAME_CN,
    if(obi.c="EN" or obi.c="LUA",if(obi.bu=1900,t.sampleNameEn,os.SAMPLE_NAME_EN),"") SAMPLE_NAME_EN,
    os.ORDER_NO,os.SAMPLE_NO
    ,  GROUP_CONCAT(if(SAMPLE_VALUE='',null,SAMPLE_VALUE)  ORDER BY SORT_SHOW asc  separator '|' )  as sampleAttrStr
    from ORDER_SAMPLE_FROM osf ,ORDER_SAMPLE os ,
    (select case REPORT_LUA_CODE when "ZN" then "CHI" when "EN" then "EN" else "LUA" end as c,ORDER_NO,GROUP_NO,bu
    from ORDER_BASE_INFO obi
    where 1=1
    <if test="orderNoList != null ">
      AND obi.ORDER_NO in
      <foreach collection="orderNoList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    ) as  obi,
    (select osff.order_no,osff.group_no,osff.SAMPLE_NO, max( CASE osff.SAMPLE_KEY WHEN 'SAMPLE_NUMBER' THEN osff.SAMPLE_VALUE ELSE '' end) sampleName,
    max( CASE osff.SAMPLE_KEY WHEN 'SAMPLE_NUMBER_EN' THEN osff.SAMPLE_VALUE ELSE '' end) sampleNameEn
    from order_sample_from osff where osff.state=1
    <if test="orderNoList != null ">
      AND osff.ORDER_NO in
      <foreach collection="orderNoList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    group by osff.ORDER_NO,osff.SAMPLE_NO) t
    where osf.ORDER_NO=obi.ORDER_NO and osf.GROUP_NO=obi.GROUP_NO
    and osf.SAMPLE_NO=t.SAMPLE_NO
    and obi.order_no=t.order_no
    and osf.SAMPLE_NO=os.SAMPLE_NO
    and os.ORDER_NO=obi.ORDER_NO and os.GROUP_NO=obi.GROUP_NO
    and obi.group_no=t.group_no
    and os.STATE=1
    and osf.state=1
    and (obi.c="LUA" or (obi.c!="LUA" and obi.c=osf.lua ))
    <if test="orderNoList != null ">
      AND obi.ORDER_NO in
      <foreach collection="orderNoList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    group by os.ORDER_NO,os.SAMPLE_NO
    order by os.SAMPLE_ID asc
  </select>

  <sql id="baseQueryWhere">
    where 1=1
    <if test="orderNo != null">
      AND ORDER_NO=#{orderNo}
    </if>
    <if test="groupNo != null">
      AND GROUP_NO=#{groupNo}
    </if>
    <if test="sampleNo != null">
      AND SAMPLE_NO=#{sampleNo}
    </if>
    <if test="state != null">
      AND STATE=#{state}
    </if>
    <if test="orderNoList != null ">
      AND ORDER_NO in
      <foreach collection="orderNoList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </sql>


  <sql id="baseQueryVOWhere">
    <where>
      <if test="orderNo != null">
        AND ORDER_NO=#{orderNo}
      </if>
      <if test="groupNo != null">
        AND GROUP_NO=#{groupNo}
      </if>
      <if test="orderNoList != null ">
        AND ORDER_NO in
        <foreach collection="orderNoList" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
  </where>
  </sql>


  <insert id="insertForeachEntity"  >
    insert into ORDER_SAMPLE (
    ORDER_NO,SAMPLE_NO,GROUP_NO,
    SAMPLE_NAME, SAMPLE_NAME_EN,SAMPLE_NAME_CN,
    STATE, CREATE_DATE, STATE_DATE,SAMPLE_SHAPE_CODE,SAMPLE_CATEGORY_CODE
    )
    values
    <foreach collection ="list" item="sample" index= "index" separator =",">
      (
      #{sample.orderNo},#{sample.sampleNo},#{sample.groupNo},
      #{sample.sampleName},#{sample.sampleNameEn},#{sample.sampleNameCn},
      #{sample.state}, #{sample.createDate}, #{sample.stateDate}, #{sample.sampleShapeCode}, #{sample.sampleCategoryCode}
      )
    </foreach>
  </insert>

  <update id="updateEntity" parameterType="com.sgs.ecom.member.entity.order.OrderSample" >
    update ORDER_SAMPLE
    <set >
      <if test="orderNo != null" >
        ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="sampleName != null" >
        SAMPLE_NAME = #{sampleName,jdbcType=VARCHAR},
      </if>
      <if test="sampleNameEn != null" >
        SAMPLE_NAME_EN = #{sampleNameEn,jdbcType=VARCHAR},
      </if>
      <if test="sampleNameCn != null" >
        SAMPLE_NAME_CN = #{sampleNameCn,jdbcType=VARCHAR},
      </if>
      <if test="productInfo != null" >
        PRODUCT_INFO = #{productInfo,jdbcType=VARCHAR},
      </if>
      <if test="productInfoEn != null" >
        PRODUCT_INFO_EN = #{productInfoEn,jdbcType=VARCHAR},
      </if>
      <if test="productBatch != null" >
        PRODUCT_BATCH = #{productBatch,jdbcType=VARCHAR},
      </if>
      <if test="productBatchEn != null" >
        PRODUCT_BATCH_EN = #{productBatchEn,jdbcType=VARCHAR},
      </if>
      <if test="materialGrade != null" >
        MATERIAL_GRADE = #{materialGrade,jdbcType=VARCHAR},
      </if>
      <if test="materialGradeEn != null" >
        MATERIAL_GRADE_EN = #{materialGradeEn,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="remarkEn != null" >
        REMARK_EN = #{remarkEn,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null" >
        SUPPLIER_NAME = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="supplierNameEn != null" >
        SUPPLIER_NAME_EN = #{supplierNameEn,jdbcType=VARCHAR},
      </if>
      <if test="buyersName != null" >
        BUYERS_NAME = #{buyersName,jdbcType=VARCHAR},
      </if>
      <if test="buyersNameEn != null" >
        BUYERS_NAME_EN = #{buyersNameEn,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        STATE = #{state,jdbcType=INTEGER},
      </if>
      <if test="createDate != null" >
        CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        STATE_DATE = #{stateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="groupNo != null" >
        GROUP_NO = #{groupNo,jdbcType=VARCHAR},
      </if>
    </set>
    where SAMPLE_ID = #{sampleId,jdbcType=BIGINT}
  </update>

  <resultMap id="resultSample" type="com.sgs.ecom.member.dto.custom.OrderSampleNameDTO" >
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="SAMPLE_NAME" property="orderSampleName" jdbcType="VARCHAR" />
  </resultMap>

  <select id="selectFirstSample" resultMap="resultSample" >
    select oa.ORDER_NO ,oa.SAMPLE_NAME from ORDER_SAMPLE_RELATE osr,ORDER_SAMPLE oa,order_base_info obi
    where  osr.GROUP_NO=oa.GROUP_NO and obi.order_no=oa.order_no and obi.group_no=oa.group_no
      and osr.SAMPLE_NO=oa.SAMPLE_NO and oa.STATE=1
      <if test="orderNoList != null ">
        AND obi.ORDER_NO in
        <foreach collection="orderNoList" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      and osr.DETAIL_ID in (
      select min(DETAIL_ID) from order_detail od,order_base_info obi
       where od.order_no=obi.order_no  and obi.group_no=od.GROUP_NO
      <if test="orderNoList != null ">
        AND obi.ORDER_NO in
        <foreach collection="orderNoList" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
     group by obi.ORDER_NO
    )
  </select>
  <select id="qryListByOrderNoList" resultMap="resultSampleDTO">
    select
    <include refid="Base_Column_List" />
    from ORDER_SAMPLE
    <include refid="baseQueryWhere"/>
    order by SAMPLE_ID asc

  </select>
  <select id="qryPackageAndSampleRelation" resultMap="resultDTO">
    select concat(os.ROW_NUM,' ',concat(IFNULL(os.SAMPLE_NAME,'') , ' ',IFNULL(os.SAMPLE_NAME_EN,'') ))as SAMPLE_NAME  ,osr.DETAIL_ID,os.ORDER_NO,os.GROUP_NO,os.SAMPLE_NO  from
    (select concat((@i:=@i+1),'#')  as ROW_NUM,os1.* from order_sample os1,(select   @i:=0) as it
    where 1=1
    and os1.state= 1
    <if test="orderNoList != null ">
      AND os1.ORDER_NO in
      <foreach collection="orderNoList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>

    order by SAMPLE_ID)as os ,
    order_sample_relate osr where
    os.STATE =1 and osr.STATE =1
    and os.ORDER_NO = osr.ORDER_NO
    and os.GROUP_NO =osr.GROUP_NO
    and os.SAMPLE_NO  = osr.SAMPLE_NO
    <if test="orderNoList != null ">
      AND os.ORDER_NO in
      <foreach collection="orderNoList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    order by os.SAMPLE_ID

  </select>

</mapper>