<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.mapper.custom.CustInvoiceMapper" >

  <resultMap id="QryResultMap" type="com.sgs.ecom.member.dto.cust.CustInvoiceDTO" >
    <id column="CUST_ID" property="custId" jdbcType="BIGINT" />
    <id column="INVOICE_ID" property="invoiceId" jdbcType="BIGINT" />
    <result column="TAX_NO" property="taxNo" jdbcType="VARCHAR" />
    <result column="REG_PHONE" property="regPhone" jdbcType="VARCHAR" />
    <result column="BANK_NAME" property="bankName" jdbcType="VARCHAR" />
    <result column="BANK_NUMBER" property="bankNumber" jdbcType="VARCHAR" />
    <result column="INVOICE_TITLE" property="invoiceTitle" jdbcType="VARCHAR" />
    <result column="REG_ADDRESS" property="regAddress" jdbcType="VARCHAR" />
    <result column="CUSTOMER_NUMBER" property="customerNumber" jdbcType="VARCHAR" />
  </resultMap>

  <select id="qryCustInvoiceListByCustId" resultMap="QryResultMap" parameterType="java.lang.Long" >
    select cin.INVOICE_ID,cin.CUST_ID, cin.TAX_NO,cin.REG_PHONE,cin.BANK_NAME,cin.BANK_NUMBER,
    cin.INVOICE_TITLE,cin.REG_ADDRESS,cin.CUSTOMER_NUMBER
    from TB_CUST_INVOICE cin
    where cin.CUST_ID = #{custId,jdbcType=BIGINT} and state=1
  </select>

</mapper>