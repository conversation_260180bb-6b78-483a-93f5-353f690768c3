package com.sgs.ecom.member.controller.tic;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.ecom.member.enumtool.event.EventEnum;
import com.sgs.ecom.member.event.EventApiUtil;
import com.sgs.ecom.member.request.OrderNoReq;
import com.sgs.ecom.member.request.tic.TicApplicationReq;
import com.sgs.ecom.member.service.tic.interfaces.ITicApplicationService;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.tic/application")
public class TicApplicationController extends ControllerUtil {

	@Autowired
	private ITicApplicationService ticApplicationService;
	@Autowired
	private EventApiUtil eventApiUtil;

	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "save", method = { RequestMethod.POST })
	public ResultBody save (
		@RequestHeader(value="accessToken") String token,
		@RequestBody TicApplicationReq ticApplicationReq)throws Exception{
		ticApplicationService.save(ticApplicationReq,getUserDTO(token),token);
		eventApiUtil.saveEvent(ticApplicationReq.getOrderNo(), EventEnum.SAVE_INVOICE_BOSS);
		return ResultBody.success();
	}

	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "checkDate", method = { RequestMethod.POST })
	public ResultBody save (
		@RequestHeader(value="accessToken") String token,
		@RequestBody OrderNoReq orderNoReq)throws Exception{
		return ResultBody.newInstance(ticApplicationService.checkDate(orderNoReq,getUserDTO(token)));
	}

	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "qryLab", method = { RequestMethod.POST })
	public ResultBody qryLab (
		@RequestHeader(value="accessToken") String token,
		@RequestBody OrderNoReq orderNoReq)throws Exception{
		return ResultBody.newInstance(ticApplicationService.qryLab(orderNoReq,getUserDTO(token)));
	}


	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "qryStore", method = { RequestMethod.POST })
	public ResultBody qryStore (
			@RequestHeader(value="accessToken") String token,
			@RequestBody OrderNoReq orderNoReq)throws Exception{
		return ResultBody.newInstance(ticApplicationService.qryStore(orderNoReq,getUserDTO(token)));
	}

	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "qryBank", method = { RequestMethod.POST })
	public ResultBody qryBank (
			@RequestHeader(value="accessToken") String token,
			@RequestBody OrderNoReq orderNoReq)throws Exception{
		return ResultBody.newInstance(ticApplicationService.qryBank(orderNoReq,getUserDTO(token),token));
	}

}
