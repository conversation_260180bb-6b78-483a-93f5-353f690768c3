<?xml version="1.0" encoding="UTF-8"?><project-modules id="moduleCoreId" project-version="1.5.0">
    <wb-module deploy-name="ticMemberApi">
        <wb-resource deploy-path="/" source-path="/target/m2e-wtp/web-resources"/>
        <wb-resource deploy-path="/WEB-INF/classes" source-path="/src/main/java"/>
        <wb-resource deploy-path="/WEB-INF/classes" source-path="/src/main/resources"/>
        <wb-resource deploy-path="/" source-path="/src/main/webapp"/>
        <dependent-module archiveName="ticMemberCommon-1.1.jar" deploy-path="/WEB-INF/lib" handle="module:/resource/ticMemberCommon/ticMemberCommon">
            <dependency-type>uses</dependency-type>
        </dependent-module>
        <dependent-module archiveName="ticMemberSys-1.1.jar" deploy-path="/WEB-INF/lib" handle="module:/resource/ticMemberSys/ticMemberSys">
            <dependency-type>uses</dependency-type>
        </dependent-module>
        <dependent-module archiveName="ticMemberService-1.1.jar" deploy-path="/WEB-INF/lib" handle="module:/resource/ticMemberService/ticMemberService">
            <dependency-type>uses</dependency-type>
        </dependent-module>
        <dependent-module archiveName="platform-1.2.6.jar" deploy-path="/WEB-INF/lib" handle="module:/resource/platform/platform">
            <dependency-type>uses</dependency-type>
        </dependent-module>
        <property name="context-root" value="ticMemberApi"/>
        <property name="java-output-path" value="/ticMemberApi/target/classes"/>
    </wb-module>
</project-modules>
