package com.sgs.ecom.member.entity.sobot;

import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.sgs.ecom.member.base.BaseOrderFilter;

import javax.validation.groups.Default;
import java.util.List;

public class SobotTicket {
	@ApiAnno(groups={BaseOrderFilter.Default.class})
	@BeanAnno(alias="companyid")
	private String companyId;
	@ApiAnno(groups={BaseOrderFilter.Default.class})
	@BeanAnno(alias="userid")
	private String userId;
	@ApiAnno(groups={BaseOrderFilter.Default.class})
	@BeanAnno(alias="user_tels")
	private String userTels;
	@ApiAnno(groups={BaseOrderFilter.Default.class})
	@BeanAnno(alias="user_emails")
	private String userEmails;
	@ApiAnno(groups={BaseOrderFilter.Default.class})
	@BeanAnno(alias="ticket_content")
	private String ticketContent;
	@ApiAnno(groups={BaseOrderFilter.Default.class})
	@BeanAnno(alias="ticket_typeid")
	private String ticketTypeId;
	@ApiAnno(groups={BaseOrderFilter.Default.class})
	@BeanAnno(alias="ticket_status")
	private String ticketStatus;
	@ApiAnno(groups={BaseOrderFilter.Default.class})
	@BeanAnno(alias="ticket_level")
	private String ticketLevel;
	@ApiAnno(groups={BaseOrderFilter.Default.class})
	@BeanAnno(alias="create_agentid")
	private String createAgentId;
	@ApiAnno(groups={BaseOrderFilter.Default.class})
	@BeanAnno(alias="create_agent_name")
	private String createAgentName;
	@ApiAnno(groups={BaseOrderFilter.Default.class})
	@BeanAnno(alias="ticket_from")
	private String ticketFrom;
	@ApiAnno(groups={BaseOrderFilter.Default.class})
	@BeanAnno(alias="ticket_title")
	private String ticketTitle;
	@ApiAnno(groups={BaseOrderFilter.Default.class})
	@BeanAnno(alias="file_str")
	private String fileStr;
	@ApiAnno(groups={BaseOrderFilter.Default.class})
	@BeanAnno(alias="extend_fields")
	private List<SobotExtendField> extendFields;

	public List<SobotExtendField> getExtendFields() {
		return extendFields;
	}

	public void setExtendFields(List<SobotExtendField> extendFields) {
		this.extendFields = extendFields;
	}

	public String getCompanyId() {
		return companyId;
	}

	public void setCompanyId(String companyId) {
		this.companyId = companyId;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getUserTels() {
		return userTels;
	}

	public void setUserTels(String userTels) {
		this.userTels = userTels;
	}

	public String getUserEmails() {
		return userEmails;
	}

	public void setUserEmails(String userEmails) {
		this.userEmails = userEmails;
	}

	public String getTicketContent() {
		return ticketContent;
	}

	public void setTicketContent(String ticketContent) {
		this.ticketContent = ticketContent;
	}

	public String getTicketTypeId() {
		return ticketTypeId;
	}

	public void setTicketTypeId(String ticketTypeId) {
		this.ticketTypeId = ticketTypeId;
	}

	public String getTicketStatus() {
		return ticketStatus;
	}

	public void setTicketStatus(String ticketStatus) {
		this.ticketStatus = ticketStatus;
	}

	public String getTicketLevel() {
		return ticketLevel;
	}

	public void setTicketLevel(String ticketLevel) {
		this.ticketLevel = ticketLevel;
	}

	public String getCreateAgentId() {
		return createAgentId;
	}

	public void setCreateAgentId(String createAgentId) {
		this.createAgentId = createAgentId;
	}

	public String getCreateAgentName() {
		return createAgentName;
	}

	public void setCreateAgentName(String createAgentName) {
		this.createAgentName = createAgentName;
	}

	public String getTicketFrom() {
		return ticketFrom;
	}

	public void setTicketFrom(String ticketFrom) {
		this.ticketFrom = ticketFrom;
	}

	public String getTicketTitle() {
		return ticketTitle;
	}

	public void setTicketTitle(String ticketTitle) {
		this.ticketTitle = ticketTitle;
	}

	public String getFileStr() {
		return fileStr;
	}

	public void setFileStr(String fileStr) {
		this.fileStr = fileStr;
	}


}
