package com.sgs.ecom.member.entity.order;

import javax.persistence.Id;
import java.math.BigDecimal;

public class OrderExpress {
    
    
    @Id
    private Long expressId;

    private Integer goodsType;
   

    private String receiptPerson;
   

    private Long userId;
   

    private Integer state;
   

    private String orderNo;
   

    private String expressCode;
   

    private String preExpressDate;
   

    private String receiptTown;
   

    private Long labId;
   

    private Integer isCs;
   
    
    private String sendProvice;
   

    private String labName;
   

    private String goodsName;
   

    private Integer packageNums;
   

    private Integer isShow;

    private String sendTown;
   

    private Integer deliverType;
   

    private String monthlyCard;
   

    private String memo;
   

    private String sendAddr;
   

    private String sendCity;
   

    private String stateDate;
   

    private String receiptCompany;
   

    private String sendCompany;
   

    private String receiptEmail;
   

    private String sendPerson;
   

    private String receiptProvice;
   

    private String expressType;
   

    private String createDate;
   

    private BigDecimal goodsPrice;

    private String expressNo;
   

    private String receiptPhone;
   

    private Integer payMethond;
   

    private String receiptAddr;
   

    private String sendPhone;
   

    private String receiptCity;
   

    private Long addressId;

    public Long getExpressId() {
        return expressId;
    }

    public void setExpressId(Long expressId) {
        this.expressId = expressId;
    }

    public Integer getGoodsType() {
        return goodsType;
    }

    public void setGoodsType(Integer goodsType) {
        this.goodsType = goodsType;
    }

    public String getReceiptPerson() {
        return receiptPerson;
    }

    public void setReceiptPerson(String receiptPerson) {
        this.receiptPerson = receiptPerson;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getExpressCode() {
        return expressCode;
    }

    public void setExpressCode(String expressCode) {
        this.expressCode = expressCode;
    }

    public String getPreExpressDate() {
        return preExpressDate;
    }

    public void setPreExpressDate(String preExpressDate) {
        this.preExpressDate = preExpressDate;
    }

    public String getReceiptTown() {
        return receiptTown;
    }

    public void setReceiptTown(String receiptTown) {
        this.receiptTown = receiptTown;
    }

    public Long getLabId() {
        return labId;
    }

    public void setLabId(Long labId) {
        this.labId = labId;
    }

    public Integer getIsCs() {
        return isCs;
    }

    public void setIsCs(Integer isCs) {
        this.isCs = isCs;
    }

    public String getSendProvice() {
        return sendProvice;
    }

    public void setSendProvice(String sendProvice) {
        this.sendProvice = sendProvice;
    }

    public String getLabName() {
        return labName;
    }

    public void setLabName(String labName) {
        this.labName = labName;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public Integer getPackageNums() {
        return packageNums;
    }

    public void setPackageNums(Integer packageNums) {
        this.packageNums = packageNums;
    }

    public Integer getIsShow() {
        return isShow;
    }

    public void setIsShow(Integer isShow) {
        this.isShow = isShow;
    }

    public String getSendTown() {
        return sendTown;
    }

    public void setSendTown(String sendTown) {
        this.sendTown = sendTown;
    }

    public Integer getDeliverType() {
        return deliverType;
    }

    public void setDeliverType(Integer deliverType) {
        this.deliverType = deliverType;
    }

    public String getMonthlyCard() {
        return monthlyCard;
    }

    public void setMonthlyCard(String monthlyCard) {
        this.monthlyCard = monthlyCard;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getSendAddr() {
        return sendAddr;
    }

    public void setSendAddr(String sendAddr) {
        this.sendAddr = sendAddr;
    }

    public String getSendCity() {
        return sendCity;
    }

    public void setSendCity(String sendCity) {
        this.sendCity = sendCity;
    }

    public String getStateDate() {
        return stateDate;
    }

    public void setStateDate(String stateDate) {
        this.stateDate = stateDate;
    }

    public String getReceiptCompany() {
        return receiptCompany;
    }

    public void setReceiptCompany(String receiptCompany) {
        this.receiptCompany = receiptCompany;
    }

    public String getSendCompany() {
        return sendCompany;
    }

    public void setSendCompany(String sendCompany) {
        this.sendCompany = sendCompany;
    }

    public String getReceiptEmail() {
        return receiptEmail;
    }

    public void setReceiptEmail(String receiptEmail) {
        this.receiptEmail = receiptEmail;
    }

    public String getSendPerson() {
        return sendPerson;
    }

    public void setSendPerson(String sendPerson) {
        this.sendPerson = sendPerson;
    }

    public String getReceiptProvice() {
        return receiptProvice;
    }

    public void setReceiptProvice(String receiptProvice) {
        this.receiptProvice = receiptProvice;
    }

    public String getExpressType() {
        return expressType;
    }

    public void setExpressType(String expressType) {
        this.expressType = expressType;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public BigDecimal getGoodsPrice() {
        return goodsPrice;
    }

    public void setGoodsPrice(BigDecimal goodsPrice) {
        this.goodsPrice = goodsPrice;
    }

    public String getExpressNo() {
        return expressNo;
    }

    public void setExpressNo(String expressNo) {
        this.expressNo = expressNo;
    }

    public String getReceiptPhone() {
        return receiptPhone;
    }

    public void setReceiptPhone(String receiptPhone) {
        this.receiptPhone = receiptPhone;
    }

    public Integer getPayMethond() {
        return payMethond;
    }

    public void setPayMethond(Integer payMethond) {
        this.payMethond = payMethond;
    }

    public String getReceiptAddr() {
        return receiptAddr;
    }

    public void setReceiptAddr(String receiptAddr) {
        this.receiptAddr = receiptAddr;
    }

    public String getSendPhone() {
        return sendPhone;
    }

    public void setSendPhone(String sendPhone) {
        this.sendPhone = sendPhone;
    }

    public String getReceiptCity() {
        return receiptCity;
    }

    public void setReceiptCity(String receiptCity) {
        this.receiptCity = receiptCity;
    }

    public Long getAddressId() {
        return addressId;
    }

    public void setAddressId(Long addressId) {
        this.addressId = addressId;
    }
}
