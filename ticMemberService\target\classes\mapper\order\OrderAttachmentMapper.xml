<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.order.dao.OrderAttachmentMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.vo.VOOrderAttachment" >
    <id column="ATTACHMENT_ID" property="attachmentId" jdbcType="BIGINT" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="GROUP_NO" property="groupNo" jdbcType="VARCHAR" />
    <result column="ATT_TYPE" property="attType" jdbcType="VARCHAR" />
    <result column="UPLOAD_TYPE" property="uploadType" jdbcType="TINYINT" />
    <result column="CLOUD_ID" property="cloudId" jdbcType="VARCHAR" />
    <result column="FILE_ID" property="fileId" jdbcType="VARCHAR" />
    <result column="FILE_NAME" property="fileName" jdbcType="VARCHAR" />
    <result column="FILE_URL" property="fileUrl" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="TINYINT" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
    <result column="DOWN_LOAD" property="downLoad" jdbcType="TINYINT" />
  </resultMap>
  <sql id="Base_Column_List" >
    ATTACHMENT_ID, ORDER_NO, GROUP_NO, ATT_TYPE, UPLOAD_TYPE, CLOUD_ID, FILE_ID, FILE_NAME, 
    FILE_URL, STATE, CREATE_DATE, STATE_DATE, DOWN_LOAD
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from ORDER_ATTACHMENT
    where ATTACHMENT_ID = #{attachmentId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from ORDER_ATTACHMENT
    where ATTACHMENT_ID = #{attachmentId,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" parameterType="com.sgs.ecom.member.vo.VOOrderAttachment" >
    insert into ORDER_ATTACHMENT
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="attachmentId != null" >
        ATTACHMENT_ID,
      </if>
      <if test="orderNo != null" >
        ORDER_NO,
      </if>
      <if test="groupNo != null" >
        GROUP_NO,
      </if>
      <if test="attType != null" >
        ATT_TYPE,
      </if>
      <if test="uploadType != null" >
        UPLOAD_TYPE,
      </if>
      <if test="cloudId != null" >
        CLOUD_ID,
      </if>
      <if test="fileId != null" >
        FILE_ID,
      </if>
      <if test="fileName != null" >
        FILE_NAME,
      </if>
      <if test="fileUrl != null" >
        FILE_URL,
      </if>
      <if test="state != null" >
        STATE,
      </if>
      <if test="createDate != null" >
        CREATE_DATE,
      </if>
      <if test="stateDate != null" >
        STATE_DATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="attachmentId != null" >
        #{attachmentId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="groupNo != null" >
        #{groupNo,jdbcType=VARCHAR},
      </if>
      <if test="attType != null" >
        #{attType,jdbcType=VARCHAR},
      </if>
      <if test="uploadType != null" >
        #{uploadType,jdbcType=TINYINT},
      </if>
      <if test="cloudId != null" >
        #{cloudId,jdbcType=VARCHAR},
      </if>
      <if test="fileId != null" >
        #{fileId,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null" >
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileUrl != null" >
        #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        #{state,jdbcType=TINYINT},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        #{stateDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.ecom.member.vo.VOOrderAttachment" >
    update ORDER_ATTACHMENT
    <set >
      <if test="orderNo != null" >
        ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="groupNo != null" >
        GROUP_NO = #{groupNo,jdbcType=VARCHAR},
      </if>
      <if test="attType != null" >
        ATT_TYPE = #{attType,jdbcType=VARCHAR},
      </if>
      <if test="uploadType != null" >
        UPLOAD_TYPE = #{uploadType,jdbcType=TINYINT},
      </if>
      <if test="cloudId != null" >
        CLOUD_ID = #{cloudId,jdbcType=VARCHAR},
      </if>
      <if test="fileId != null" >
        FILE_ID = #{fileId,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null" >
        FILE_NAME = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileUrl != null" >
        FILE_URL = #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        STATE = #{state,jdbcType=TINYINT},
      </if>
      <if test="createDate != null" >
        CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        STATE_DATE = #{stateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="downLoad != null" >
        DOWN_LOAD = #{downLoad,jdbcType=TINYINT},
      </if>
    </set>
    where ATTACHMENT_ID = #{attachmentId,jdbcType=BIGINT}
  </update>



  <!-- 自定义-->

  <resultMap id="resultDTO" type="com.sgs.ecom.member.dto.detail.OrderAttachmentDTO" >
    <result column="FILE_ID" property="fileId" jdbcType="VARCHAR" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="CLOUD_ID" property="cloudId" jdbcType="VARCHAR" />
    <result column="FILE_NAME" property="fileName" jdbcType="VARCHAR" />
    <result column="FILE_URL" property="fileUrl" jdbcType="VARCHAR" />
    <result column="ATTACHMENT_ID" property="attachmentId" jdbcType="BIGINT" />
    <result column="ATT_TYPE" property="attType" jdbcType="VARCHAR" />
    <result column="CREATE_DATE" property="createDate" jdbcType="VARCHAR" />
    <result column="GROUP_NO" property="groupNo" jdbcType="VARCHAR" />
    <result column="DOWN_LOAD" property="downLoad" jdbcType="TINYINT" />
    <result column="STATE" property="state" jdbcType="TINYINT" />
    <result column="UPLOAD_TYPE" property="uploadType" jdbcType="TINYINT" />
    <result column="REPORT_NO" property="reportNo" jdbcType="TINYINT" />
  </resultMap>

  <select id="qryAttachmentByOrder" resultMap="resultDTO" >
    select
    ATTACHMENT_ID,ORDER_NO,FILE_ID,CLOUD_ID,FILE_NAME,ATT_TYPE,CREATE_DATE,FILE_URL,DOWN_LOAD,GROUP_NO,STATE
    from ORDER_ATTACHMENT
    where STATE = 1
    and ORDER_NO in
    <foreach collection="orderNos" index="index" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    AND ATT_TYPE in
    <foreach collection="attrTypes" index="index" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <select id="selectListByMap" resultMap="resultDTO" >
    select
    ATTACHMENT_ID,ORDER_NO,FILE_ID,CLOUD_ID,FILE_NAME,ATT_TYPE,CREATE_DATE,FILE_URL,DOWN_LOAD,GROUP_NO,STATE,UPLOAD_TYPE,REPORT_NO
    from ORDER_ATTACHMENT
    <include refid="baseQueryWhere"/>
  </select>


  <sql id="baseQueryWhere">
   <where>
     <if test="orderNo != null">
       AND ORDER_NO=#{orderNo}
     </if>
     <if test="groupNo != null">
       AND GROUP_NO=#{groupNo}
     </if>
     <if test="state != null">
       AND STATE=#{state}
     </if>
     <if test="attType != null">
       AND ATT_TYPE=#{attType}
     </if>
     <if test="downLoad != null">
       AND DOWN_LOAD=#{downLoad}
     </if>
     <if test="sampleKeyList != null ">
       AND ATT_TYPE in
       <foreach collection="sampleKeyList" index="index" item="item" open="(" separator="," close=")">
         #{item}
       </foreach>
     </if>

     <if test="orderNoList != null ">
       AND ORDER_NO in
       <foreach collection="orderNoList" index="index" item="item" open="(" separator="," close=")">
         #{item}
       </foreach>
     </if>
     <if test="attrTypeList != null ">
       AND ATT_TYPE in
       <foreach collection="attrTypeList" index="index" item="item" open="(" separator="," close=")">
         #{item}
       </foreach>
     </if>
     <if test="downReport != null">
         AND ORDER_NO  not in(
         select DISTINCT ORDER_NO from ORDER_ATTACHMENT where ATT_TYPE=10 and DOWN_LOAD=0
         ) and ATT_TYPE=10
     </if>
   </where>
  </sql>



  <update id="delByOrderNo" >
    update ORDER_ATTACHMENT set STATE=0 where 1=1
    <if test="attrTypeList != null ">
      AND ATT_TYPE in
      <foreach collection="attrTypeList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    and ORDER_NO=#{orderNo}
  </update>

  <insert id="insertForeach"  >
    insert into ORDER_ATTACHMENT (
    ORDER_NO, GROUP_NO, ATT_TYPE, UPLOAD_TYPE, CLOUD_ID,
    FILE_ID, FILE_NAME, FILE_URL, STATE, CREATE_DATE,
    STATE_DATE
    )
    values
    <foreach collection ="list" item="item" index= "index" separator =",">
      (
      #{item.orderNo}, #{item.groupNo}, #{item.attType}, #{item.uploadType}, #{item.cloudId},
      #{item.fileId}, #{item.fileName}, #{item.fileUrl}, #{item.state}, #{item.createDate},
      #{item.stateDate}
      )
    </foreach>
  </insert>




  <update id="updateAttachmentByOrderNo" parameterType="java.lang.String" >
    update ORDER_ATTACHMENT set DOWN_LOAD = 1
   where 1=1
    <if test="orderNo != null">
      AND ORDER_NO=#{orderNo}
    </if>
    <if test="fileId != null">
      AND FILE_ID=#{fileId}
    </if>
    <if test="attType != null">
      AND ATT_TYPE=#{attType}
    </if>
  </update>

  <select id="selectCount" resultType="java.lang.Integer">
    select
    count(1)
    from ORDER_ATTACHMENT
    <include refid="baseQueryWhere"/>
  </select>
    <select id="qryList" resultMap="resultDTO">
      select
      <include refid="Base_Column_List" />
      from ORDER_ATTACHMENT
      <include refid="baseQueryWhere"/>
    </select>


</mapper>