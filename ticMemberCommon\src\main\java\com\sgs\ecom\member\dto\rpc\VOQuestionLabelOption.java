package com.sgs.ecom.member.dto.rpc;

import com.sgs.base.BaseBean;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * VOQuestionLabelOption
 *
 * @version: 1.0
 * @author: 标签选项VO
 * @throws Exception
 * @date: 2025/5/13
 * <p>
 * Modification History: Date         Author          Version            Description
 * ---------------------------------------------------------* 修改时间         修改人         版本 * 修改原因
 * 2025/5/13      标签VO         v1.0               新增
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class VOQuestionLabelOption extends BaseBean {

    private static final long serialVersionUID = 1L;

    /**
     * 选项编码
     */
    @Size(min = 0, max = 20, message = "选项编码参数长度超出范围", groups = {Insert.class,
        Update.class})
    private String optionCode;

    /**
     * 选项名称
     */
    @NotNull(message = "选项名称不可为空", groups = {Insert.class, Update.class})
    @Size(min = 0, max = 255, message = "选项名称参数长度超出范围", groups = {Insert.class,
        Update.class})
    private String optionName;

    /**
     * 是否允许填空( 1：是 0：否)
     */
    private int isFill;

    /**
     * 排序
     */
    private int sortShow;
}
