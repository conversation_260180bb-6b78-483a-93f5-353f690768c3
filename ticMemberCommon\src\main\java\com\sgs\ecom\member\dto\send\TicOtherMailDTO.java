package com.sgs.ecom.member.dto.send;

import com.platform.util.ValidationUtil;
import com.sgs.ecom.member.dto.acct.MonthCustMoreDTO;
import com.sgs.ecom.member.dto.pay.PaymentDTO;
import com.sgs.ecom.member.dto.pay.PaymentSendDTO;
import com.sgs.ecom.member.request.FileReq;
import com.sgs.ecom.member.request.report.ReportAuthReq;

import java.util.List;
import java.util.stream.Collectors;

public class TicOtherMailDTO {
	private String sendCC;
	private String checkDate;
	private MonthCustMoreDTO monthCustMoreDTO;
	private int monthPayFlg;
	private List<FileReq> fileReqList;
	private int memberCc;//发客户发的时候添加的cc
	private int orderCc;//往客服发的时候添加的cc
	private int ehsFlg;
	private Integer state;
	private String subBuCode;
	private String fileUrl;
	private ReportAuthReq reportAuthReq;

	private String blacklistStr;//文件中包含的黑名单域名

	private List<PaymentSendDTO> paymentList;

	public TicOtherMailDTO(String fileUrl, ReportAuthReq reportAuthReq, List<String> blacklist) {
		this.fileUrl =fileUrl;
		this.reportAuthReq =reportAuthReq;
		this.blacklistStr = !ValidationUtil.isEmpty(blacklist)?blacklist.stream().collect(Collectors.joining(",")) : "";
	}

	public String getBlacklistStr() {
		return blacklistStr;
	}

	public void setBlacklistStr(String blacklistStr) {
		this.blacklistStr = blacklistStr;
	}

	public TicOtherMailDTO() {
	}

//	public TicOtherMailDTO(String fileUrl, ReportAuthReq reportAuthReq) {
//		this.fileUrl =fileUrl;
//		this.reportAuthReq =reportAuthReq;
//	}

	public String getFileUrl() {
		return fileUrl;
	}

	public void setFileUrl(String fileUrl) {
		this.fileUrl = fileUrl;
	}

	public ReportAuthReq getReportAuthReq() {
		return reportAuthReq;
	}

	public void setReportAuthReq(ReportAuthReq reportAuthReq) {
		this.reportAuthReq = reportAuthReq;
	}

	public String getSubBuCode() {
		return subBuCode;
	}

	public void setSubBuCode(String subBuCode) {
		this.subBuCode = subBuCode;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public int getEhsFlg() {
		return ehsFlg;
	}

	public void setEhsFlg(int ehsFlg) {
		this.ehsFlg = ehsFlg;
	}
	private String serviceCC;//只发订单操作人

	public int getMonthPayFlg() {
		return monthPayFlg;
	}

	public void setMonthPayFlg(int monthPayFlg) {
		this.monthPayFlg = monthPayFlg;
	}

	public MonthCustMoreDTO getMonthCustMoreDTO() {
		return monthCustMoreDTO;
	}

	public void setMonthCustMoreDTO(MonthCustMoreDTO monthCustMoreDTO) {
		this.monthCustMoreDTO = monthCustMoreDTO;
	}

	public String getSendCC() {
		return sendCC;
	}

	public void setSendCC(String sendCC) {
		this.sendCC = sendCC;
	}

	public String getCheckDate() {
		return checkDate;
	}

	public void setCheckDate(String checkDate) {
		this.checkDate = checkDate;
	}

	public List<FileReq> getFileReqList() {
		return fileReqList;
	}

	public void setFileReqList(List<FileReq> fileReqList) {
		this.fileReqList = fileReqList;
	}

	public int getMemberCc() {
		return memberCc;
	}

	public void setMemberCc(int memberCc) {
		this.memberCc = memberCc;
	}

	public int getOrderCc() {
		return orderCc;
	}

	public void setOrderCc(int orderCc) {
		this.orderCc = orderCc;
	}

	public String getServiceCC() {
		return serviceCC;
	}

	public void setServiceCC(String serviceCC) {
		this.serviceCC = serviceCC;
	}

	public List<PaymentSendDTO> getPaymentList() {
		return paymentList;
	}

	public void setPaymentList(List<PaymentSendDTO> paymentList) {
		this.paymentList = paymentList;
	}
}
