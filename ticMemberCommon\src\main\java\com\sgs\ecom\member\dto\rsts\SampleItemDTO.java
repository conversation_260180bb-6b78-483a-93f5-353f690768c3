package com.sgs.ecom.member.dto.rsts;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.vo.VOOrderSample;
import com.sgs.ecom.member.vo.VORSTSOrderDetail;

import java.util.List;

public class SampleItemDTO {
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String sampleNo;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String remark;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private VOOrderSample voOrderSample;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private List<VORSTSOrderDetail> voOrderDetailList;


    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getSampleNo() {
        return sampleNo;
    }

    public void setSampleNo(String sampleNo) {
        this.sampleNo = sampleNo;
    }

    public VOOrderSample getVoOrderSample() {
        return voOrderSample;
    }

    public void setVoOrderSample(VOOrderSample voOrderSample) {
        this.voOrderSample = voOrderSample;
    }

    public List<VORSTSOrderDetail> getVoOrderDetailList() {
        return voOrderDetailList;
    }

    public void setVoOrderDetailList(List<VORSTSOrderDetail> voOrderDetailList) {
        this.voOrderDetailList = voOrderDetailList;
    }


}
