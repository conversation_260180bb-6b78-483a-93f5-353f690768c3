<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.order.dao.OrderProductMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.vo.VOOrderProduct" >
    <id column="PRODUCT_ID" property="productId" jdbcType="BIGINT" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="PROD_ID" property="prodId" jdbcType="INTEGER" />
    <result column="PRODUCT_NAME" property="productName" jdbcType="VARCHAR" />
    <result column="SUB_TITLE" property="subTitle" jdbcType="VARCHAR" />
    <result column="STORE_ID" property="storeId" jdbcType="INTEGER" />
    <result column="STATE" property="state" jdbcType="INTEGER" />
    <result column="STORE_NAME" property="storeName" jdbcType="VARCHAR" />
    <result column="PRICE" property="price" jdbcType="DECIMAL" />
    <result column="QUANTITY" property="quantity" jdbcType="INTEGER" />
    <result column="TOTAL_PRICE" property="totalPrice" jdbcType="DECIMAL" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
    <result column="SUB_BU_CODE" property="subBuCode" jdbcType="VARCHAR" />
    <result column="REAL_TOTAL_PRICE" property="realTotalPrice" jdbcType="DECIMAL" />
    <result column="SHOP_DIS_AMOUNT" property="shopDisAmount" jdbcType="DECIMAL" />
    <result column="SUB_DISCOUNT_AMOUNT" property="subDiscountAmount" jdbcType="DECIMAL" />
    <result column="SUB_CS_DISCOUNT_AMOUNT" property="subCsDiscountAmount" jdbcType="DECIMAL" />
    <result column="SUB_SERVICE_AMOUNT" property="subServiceAmount" jdbcType="DECIMAL" />
    <result column="LOWEST_PRICE" property="lowestPrice" jdbcType="DECIMAL" />
    <result column="SKU_PRICE" property="skuPrice" jdbcType="DECIMAL" />
    <result column="LOWEST_PRICE_MARGIN" property="lowestPriceMargin" jdbcType="DECIMAL" />
  </resultMap>
  <sql id="Base_Column_List" >
    PRODUCT_ID, ORDER_NO, PROD_ID, PRODUCT_NAME, SUB_TITLE, STORE_ID, STORE_NAME, PRICE,
    QUANTITY, TOTAL_PRICE, CREATE_DATE, STATE_DATE,SUB_BU_CODE,REAL_TOTAL_PRICE,STATE,SHOP_DIS_AMOUNT,SUB_DISCOUNT_AMOUNT,SUB_CS_DISCOUNT_AMOUNT,SUB_SERVICE_AMOUNT
              ,LOWEST_PRICE, SKU_PRICE, LOWEST_PRICE_MARGIN
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from ORDER_PRODUCT
    where PRODUCT_ID = #{productId,jdbcType=BIGINT}  and state = 1
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from ORDER_PRODUCT
    where PRODUCT_ID = #{productId,jdbcType=BIGINT}
  </delete>

  <insert id="insertSelective" parameterType="com.sgs.ecom.member.vo.VOOrderProduct" >
    <selectKey resultType="java.lang.Long" keyProperty="productId" order="AFTER">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ORDER_PRODUCT
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="productId != null" >
        PRODUCT_ID,
      </if>
      <if test="orderNo != null" >
        ORDER_NO,
      </if>
      <if test="prodId != null" >
        PROD_ID,
      </if>
      <if test="productName != null" >
        PRODUCT_NAME,
      </if>
      <if test="subTitle != null" >
        SUB_TITLE,
      </if>
      <if test="storeId != null" >
        STORE_ID,
      </if>
      <if test="storeName != null" >
        STORE_NAME,
      </if>
      <if test="price != null" >
        PRICE,
      </if>
      <if test="realTotalPrice != null" >
        REAL_TOTAL_PRICE,
      </if>
      <if test="quantity != null" >
        QUANTITY,
      </if>
      <if test="totalPrice != null" >
        TOTAL_PRICE,
      </if>
      <if test="createDate != null" >
        CREATE_DATE,
      </if>
      <if test="stateDate != null" >
        STATE_DATE,
      </if>
      <if test="productMemo != null" >
        PRODUCT_MEMO,
      </if>
      <if test="skuAttr != null" >
        SKU_ATTR,
      </if>
      <if test="subBuCode != null" >
        SUB_BU_CODE,
      </if>
      <if test="productImg != null" >
        PRODUCT_IMG,
      </if>
      <if test="customType != null" >
        CUSTOM_TYPE,
      </if>
      <if test="state != null" >
        STATE,
      </if>
      <if test="shopDisAmount != null">
        SHOP_DIS_AMOUNT,
      </if>
      <if test="subDiscountAmount != null">
        SUB_DISCOUNT_AMOUNT,
      </if>
      <if test="subCsDiscountAmount != null">
        SUB_CS_DISCOUNT_AMOUNT,
      </if>
      <if test="subServiceAmount != null">
        SUB_SERVICE_AMOUNT,
      </if>
      <if test="lowestPrice != null">
        LOWEST_PRICE,
      </if>
      <if test="skuPrice != null">
        SKU_PRICE,
      </if>
      <if test="lowestPriceMargin != null">
        LOWEST_PRICE_MARGIN,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="productId != null" >
        #{productId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="prodId != null" >
        #{prodId,jdbcType=INTEGER},
      </if>
      <if test="productName != null" >
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="subTitle != null" >
        #{subTitle,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null" >
        #{storeId},
      </if>
      <if test="storeName != null" >
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="price != null" >
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="realTotalPrice != null" >
        #{realTotalPrice,jdbcType=DECIMAL},
      </if>
      <if test="quantity != null" >
        #{quantity,jdbcType=INTEGER},
      </if>
      <if test="totalPrice != null" >
        #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        #{stateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="productMemo != null" >
        #{productMemo},
      </if>
      <if test="skuAttr != null" >
        #{skuAttr},
      </if>
      <if test="subBuCode != null" >
        #{subBuCode},
      </if>
      <if test="productImg != null" >
        #{productImg},
      </if>
      <if test="customType != null" >
        #{customType},
      </if>
      <if test="state != null" >
        #{state},
      </if>
      <if test="shopDisAmount != null">
        SHOP_DIS_AMOUNT,
      </if>
      <if test="subDiscountAmount != null">
        SUB_DISCOUNT_AMOUNT,
      </if>
      <if test="subCsDiscountAmount != null">
        SUB_CS_DISCOUNT_AMOUNT,
      </if>
      <if test="subServiceAmount != null">
        SUB_SERVICE_AMOUNT,
      </if>
      <if test="lowestPrice != null">
        LOWEST_PRICE,
      </if>
      <if test="skuPrice != null">
        SKU_PRICE,
      </if>
      <if test="lowestPriceMargin != null">
        LOWEST_PRICE_MARGIN,
      </if>
    </trim>
  </insert>

  <insert id="insertReturnKey" keyProperty="productId" parameterType="com.sgs.ecom.member.entity.order.OrderProduct" useGeneratedKeys="true">
    insert into ORDER_PRODUCT (
    ORDER_NO, PROD_ID, PRODUCT_NAME,
    SUB_TITLE, STORE_ID, STORE_NAME,
    PRICE, REAL_TOTAL_PRICE, QUANTITY,
    TOTAL_PRICE, CREATE_DATE, PRODUCT_MEMO,
    SKU_ATTR, SUB_BU_CODE, PRODUCT_IMG,
    CUSTOM_TYPE,STATE ,SHOP_DIS_AMOUNT,SUB_DISCOUNT_AMOUNT,SUB_CS_DISCOUNT_AMOUNT,SUB_SERVICE_AMOUNT,
    LOWEST_PRICE, SKU_PRICE, LOWEST_PRICE_MARGIN)
    values (
    #{orderNo,jdbcType=VARCHAR}, #{prodId,jdbcType=INTEGER}, #{productName,jdbcType=VARCHAR},
    #{subTitle,jdbcType=VARCHAR}, #{storeId,jdbcType=VARCHAR}, #{storeName,jdbcType=VARCHAR},
    #{price,jdbcType=DECIMAL}, #{realTotalPrice,jdbcType=DECIMAL}, #{quantity,jdbcType=INTEGER},
    #{totalPrice,jdbcType=DECIMAL}, #{createDate,jdbcType=TIMESTAMP}, #{productMemo,jdbcType=VARCHAR},
    #{skuAttr,jdbcType=VARCHAR}, #{subBuCode,jdbcType=VARCHAR}, #{productImg,jdbcType=VARCHAR},
    #{customType,jdbcType=VARCHAR}, #{state,jdbcType=INTEGER}, #{shopDisAmount,jdbcType=DECIMAL}, #{subDiscountAmount,jdbcType=DECIMAL},
    #{subCsDiscountAmount,jdbcType=DECIMAL}, #{subServiceAmount,jdbcType=DECIMAL}
             , #{lowestPrice,jdbcType=DECIMAL}, #{skuPrice,jdbcType=DECIMAL}, #{lowestPriceMargin,jdbcType=DECIMAL}
    )
  </insert>

  <insert id="insertForeach"  >
    insert into ORDER_PRODUCT
    (ORDER_NO, PROD_ID, PRODUCT_NAME, SUB_TITLE, STORE_ID,
    STORE_NAME,PRICE, QUANTITY, TOTAL_PRICE, CREATE_DATE,
    STATE_DATE,PRODUCT_MEMO,SKU_ATTR,SUB_BU_CODE,REAL_TOTAL_PRICE,STATE,SHOP_DIS_AMOUNT,SUB_DISCOUNT_AMOUNT,SUB_CS_DISCOUNT_AMOUNT,SUB_SERVICE_AMOUNT,
    LOWEST_PRICE, SKU_PRICE, LOWEST_PRICE_MARGIN)
    values
    <foreach collection ="list" item="item" index= "index" separator =",">
      (
      #{item.orderNo}, #{item.prodId},#{item.productName},#{item.subTitle},#{item.storeId},
      #{item.storeName}, #{item.price},#{item.quantity},#{item.totalPrice},#{item.createDate},
      #{item.stateDate}, #{item.productMemo}, #{item.skuAttr}, #{item.subBuCode}, #{item.realTotalPrice}, #{item.state}, #{shopDisAmount,jdbcType=DECIMAL}, #{subDiscountAmount,jdbcType=DECIMAL},
      #{subCsDiscountAmount,jdbcType=DECIMAL}, #{subServiceAmount,jdbcType=DECIMAL}
      , #{lowestPrice,jdbcType=DECIMAL}, #{skuPrice,jdbcType=DECIMAL}, #{lowestPriceMargin,jdbcType=DECIMAL}
      )
    </foreach>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.ecom.member.vo.VOOrderProduct" >
    update ORDER_PRODUCT
    <set >
      <if test="orderNo != null" >
        ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="prodId != null" >
        PROD_ID = #{prodId,jdbcType=INTEGER},
      </if>
      <if test="productName != null" >
        PRODUCT_NAME = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="subTitle != null" >
        SUB_TITLE = #{subTitle,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null" >
        STORE_ID = #{storeId,jdbcType=INTEGER},
      </if>
      <if test="storeName != null" >
        STORE_NAME = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="price != null" >
        PRICE = #{price,jdbcType=DECIMAL},
      </if>
      <if test="quantity != null" >
        QUANTITY = #{quantity,jdbcType=INTEGER},
      </if>
      <if test="totalPrice != null" >
        TOTAL_PRICE = #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="createDate != null" >
        CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        STATE_DATE = #{stateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="skuAttr != null" >
        SKU_ATTR = #{skuAttr,jdbcType=TIMESTAMP},
      </if>
      <if test="subBuCode != null" >
        SUB_BU_CODE = #{subBuCode,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        STATE = #{state,jdbcType=INTEGER},
      </if>
      <if test="lowestPrice != null" >
        LOWEST_PRICE = #{lowestPrice,jdbcType=DECIMAL},
      </if>
      <if test="skuPrice != null" >
        SKU_PRICE = #{skuPrice,jdbcType=DECIMAL},
      </if>
      <if test="lowestPriceMargin != null" >
        LOWEST_PRICE_MARGIN = #{lowestPriceMargin,jdbcType=DECIMAL},
      </if>
    </set>
    where PRODUCT_ID = #{productId,jdbcType=BIGINT}
  </update>


  <!--自定义 -->
  <sql id="sqlListDTO" >
    PRODUCT_ID, ORDER_NO, PROD_ID, PRODUCT_NAME, SUB_TITLE, STORE_ID, STORE_NAME, PRICE,
    QUANTITY, TOTAL_PRICE, CREATE_DATE, STATE_DATE,SKU_ATTR,PRODUCT_MEMO,SUB_BU_CODE,PRODUCT_IMG,CUSTOM_TYPE,REAL_TOTAL_PRICE,STATE
              ,LOWEST_PRICE,SKU_PRICE, LOWEST_PRICE_MARGIN
  </sql>
  <resultMap id="resultDTO" type="com.sgs.ecom.member.dto.order.OrderProductDTO" >
    <id column="PRODUCT_ID" property="productId" jdbcType="BIGINT" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="PROD_ID" property="prodId" jdbcType="INTEGER" />
    <result column="PRODUCT_NAME" property="productName" jdbcType="VARCHAR" />
    <result column="SUB_TITLE" property="subTitle" jdbcType="VARCHAR" />
    <result column="STORE_ID" property="storeId" jdbcType="INTEGER" />
    <result column="STORE_NAME" property="storeName" jdbcType="VARCHAR" />
    <result column="PRICE" property="price" jdbcType="DECIMAL" />
    <result column="QUANTITY" property="quantity" jdbcType="INTEGER" />
    <result column="TOTAL_PRICE" property="totalPrice" jdbcType="DECIMAL" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
    <result column="SKU_ATTR" property="skuAttr" jdbcType="VARCHAR" />
    <result column="PRODUCT_IMG" property="picPath" jdbcType="VARCHAR" />
    <result column="PRODUCT_MEMO" property="productMemo" jdbcType="VARCHAR" />
    <result column="SUB_BU_CODE" property="subBuCode" jdbcType="VARCHAR" />
    <result column="CUSTOM_TYPE" property="customType" jdbcType="VARCHAR" />
    <result column="REAL_TOTAL_PRICE" property="realTotalPrice" jdbcType="DECIMAL" />
    <result column="STATE" property="state" jdbcType="INTEGER" />
    <result column="LOWEST_PRICE" property="lowestPrice" jdbcType="DECIMAL" />
    <result column="SKU_PRICE" property="skuPrice" jdbcType="DECIMAL" />
    <result column="LOWEST_PRICE_MARGIN" property="lowestPriceMargin" jdbcType="DECIMAL" />
  </resultMap>

  <select id="selectListByMap" resultMap="resultDTO" >
    select
    <include refid="sqlListDTO" />
    from ORDER_PRODUCT
    <include refid="baseQueryWhere"/>
    order by PRODUCT_ID
  </select>

  <sql id="baseQueryWhere">
    where 1=1
    AND STATE = 1
    <if test="orderNo != null">
      AND ORDER_NO=#{orderNo}
    </if>
    <if test="orderNoList != null ">
      AND ORDER_NO in
      <foreach collection="orderNoList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="subOrderNoList != null ">
      AND ORDER_NO in (select RELATE_ORDER_NO from ORDER_BASE_INFO where ORDER_NO in
         <foreach collection="subOrderNoList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
      )

    </if>
  </sql>


   <select id="selectNumByMap"  resultType="java.lang.Integer">
    SELECT  COUNT(PRODUCT_ID)  from ORDER_PRODUCT WHERE  PROD_ID =#{prodId} and state = 1
   </select>

    <select id="selectNumListByMap" resultType="com.sgs.ecom.member.dto.order.OrderProductDTO">
      SELECT
        PROD_ID as prodId,
        count( PROD_ID ) as count
      FROM
        order_product
      WHERE
            1=1 AND STATE = 1
      <if test="ProdIdList != null ">
        AND PROD_ID in
        <foreach collection="ProdIdList" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>

      GROUP BY
        PROD_ID


    </select>


</mapper>