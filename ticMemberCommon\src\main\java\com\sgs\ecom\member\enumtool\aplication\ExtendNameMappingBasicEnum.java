package com.sgs.ecom.member.enumtool.aplication;

import com.platform.util.ValidationUtil;
import org.apache.commons.lang.StringUtils;

import java.util.List;

/**
 * RSTS-MSDS/SDS
 * sds标准对应的动态字段的枚举值
 */
public enum ExtendNameMappingBasicEnum {
    EU_GHS("欧盟SDS/MSDS", "EU_GHS", "EU GHS-Format"),
    US_GHS("美国SDS/MSDS", "US_GHS", "US GHS-Format"),
    GB_GHS("中国SDS/MSDS", "GB_GHS", "GB GHS-Format"),


    ;

    ExtendNameMappingBasicEnum(String name, String index, String nameCh) {
        this.name = name;
        this.index = index;
        this.nameCh = nameCh;
    }

    public static String getName(String index) {
        for (ExtendNameMappingBasicEnum c : ExtendNameMappingBasicEnum.values()) {
            if (c.getIndex().equals(index)) {
                return c.getName();
            }
        }
        return "3";
    }


    public static String getIndex(String name) {
        for (ExtendNameMappingBasicEnum c : ExtendNameMappingBasicEnum.values()) {
            if (c.name().equals(name)) {
                return c.getIndex();
            }
        }
        return "";
    }

    public static String getNameChByName(String name) {
        if(StringUtils.isBlank(name)){
            return "";
        }

        for (ExtendNameMappingBasicEnum c : ExtendNameMappingBasicEnum.values()) {
            if (c.getName().equals(name.toLowerCase())) {
                return c.getNameCh();
            }
        }
        return "";
    }


    public static String getNameByNameCh(String nameCh) {
        for (ExtendNameMappingBasicEnum c : ExtendNameMappingBasicEnum.values()) {
            if (c.getNameCh().equals(nameCh)) {
                return c.getName().toUpperCase();
            }
        }
        return "";
    }


    public static String getIndexByExtendName(List<String> list) {
        if(ValidationUtil.isEmpty(list))
            return "";

        StringBuilder stringBuilder = new StringBuilder();
        list.stream().forEach(extendName ->{
            String index = ExtendNameMappingBasicEnum.getIndexByName(extendName);
            if(!ValidationUtil.isEmpty(index)){
                stringBuilder.append(index+",");
            }
        });
        String toString = stringBuilder.toString();
        return  ValidationUtil.isEmpty(toString)?"":toString.substring(0,toString.length()-1);
    }

    private static String getIndexByName(String extendName) {
        for (ExtendNameMappingBasicEnum c : ExtendNameMappingBasicEnum.values()) {
            if (extendName.contains(c.getName())) {
                return c.getIndex();
            }
        }
        return "";
    }


    private String name;
    private String index;
    private String nameCh;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }
}
