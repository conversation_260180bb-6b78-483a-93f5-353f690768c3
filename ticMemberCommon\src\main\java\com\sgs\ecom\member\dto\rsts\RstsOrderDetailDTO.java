package com.sgs.ecom.member.dto.rsts;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.dto.center.EnumDTO;
import com.sgs.ecom.member.dto.pay.LabBankDTO;
import com.sgs.ecom.member.request.rsts.RstsApplicationReq;
import com.sgs.ecom.member.vo.VORSTSOrderDetail;

import java.math.BigDecimal;
import java.util.List;

public class RstsOrderDetailDTO {
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int sampleFlg=0;

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private List<SampleItemDTO> sampleItemDTOList;

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private SampleInfoDTO sampleInfoDTO;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private RstsApplicationReq rstsApplicationReq;


    @ApiAnno(groups={BaseQryFilter.Default.class})
    private List<VORSTSOrderDetail> baseItemList;


    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int isBind=1;//
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BigDecimal reportPrice;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String bossNo;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private Long custId;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String belongLabCode;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String labCode;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String labCodeValue;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private List<EnumDTO> labCodeEnum;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String vbaAccount;
    //账号信息只有成功提交才有
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private LabBankDTO labBankDTO;


    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String fromSource;;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String fromUrl;;

    public RstsOrderDetailDTO() {
    }

    public RstsOrderDetailDTO(String labCode) {
        this.labCode = labCode;
    }

    public RstsOrderDetailDTO(List<SampleItemDTO> sampleItemDTOList, SampleInfoDTO sampleInfoDTO, RstsApplicationReq rstsApplicationReq) {
        this.sampleItemDTOList = sampleItemDTOList;
        this.sampleInfoDTO = sampleInfoDTO;
        this.rstsApplicationReq = rstsApplicationReq;
    }

    public List<SampleItemDTO> getSampleItemDTOList() {
        return sampleItemDTOList;
    }
    public void setSampleItemDTOList(List<SampleItemDTO> sampleItemDTOList) {
        this.sampleItemDTOList = sampleItemDTOList;
    }

    public int getSampleFlg() {
        return sampleFlg;
    }

    public void setSampleFlg(int sampleFlg) {
        this.sampleFlg = sampleFlg;
    }

    public SampleInfoDTO getSampleInfoDTO() {
        return sampleInfoDTO;
    }

    public void setSampleInfoDTO(SampleInfoDTO sampleInfoDTO) {
        this.sampleInfoDTO = sampleInfoDTO;
    }



    public RstsApplicationReq getRstsApplicationReq() {
        return rstsApplicationReq;
    }

    public void setRstsApplicationReq(RstsApplicationReq rstsApplicationReq) {
        this.rstsApplicationReq = rstsApplicationReq;
    }

    public List<VORSTSOrderDetail> getBaseItemList() {
        return baseItemList;
    }

    public void setBaseItemList(List<VORSTSOrderDetail> baseItemList) {
        this.baseItemList = baseItemList;
    }

    public int getIsBind() {
        return isBind;
    }

    public void setIsBind(int isBind) {
        this.isBind = isBind;
    }

    public BigDecimal getReportPrice() {
        return reportPrice;
    }

    public void setReportPrice(BigDecimal reportPrice) {
        this.reportPrice = reportPrice;
    }

    public String getBossNo() {
        return bossNo;
    }

    public void setBossNo(String bossNo) {
        this.bossNo = bossNo;
    }

    public Long getCustId() {
        return custId;
    }

    public void setCustId(Long custId) {
        this.custId = custId;
    }

    public String getBelongLabCode() {
        return belongLabCode;
    }

    public void setBelongLabCode(String belongLabCode) {
        this.belongLabCode = belongLabCode;
    }

    public String getVbaAccount() {
        return vbaAccount;
    }

    public void setVbaAccount(String vbaAccount) {
        this.vbaAccount = vbaAccount;
    }

    public List<EnumDTO> getLabCodeEnum() {
        return labCodeEnum;
    }

    public void setLabCodeEnum(List<EnumDTO> labCodeEnum) {
        this.labCodeEnum = labCodeEnum;
    }

    public String getLabCode() {
        return labCode;
    }

    public void setLabCode(String labCode) {
        this.labCode = labCode;
    }

    public String getLabCodeValue() {
        return labCodeValue;
    }

    public void setLabCodeValue(String labCodeValue) {
        this.labCodeValue = labCodeValue;
    }

    public LabBankDTO getLabBankDTO() {
        return labBankDTO;
    }

    public void setLabBankDTO(LabBankDTO labBankDTO) {
        this.labBankDTO = labBankDTO;
    }

    public String getFromSource() {
        return fromSource;
    }

    public void setFromSource(String fromSource) {
        this.fromSource = fromSource;
    }

    public String getFromUrl() {
        return fromUrl;
    }

    public void setFromUrl(String fromUrl) {
        this.fromUrl = fromUrl;
    }
}
