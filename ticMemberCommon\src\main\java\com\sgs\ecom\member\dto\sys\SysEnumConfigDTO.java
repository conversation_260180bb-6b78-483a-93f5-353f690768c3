package com.sgs.ecom.member.dto.sys;

import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.sgs.base.BaseQryFilter;

import java.util.List;

public class SysEnumConfigDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select COL_NAME,TABLE_NAME,CREATE_DATE,ENUM_CODE,STATE_DATE,SHOW_SORT,STATE,ENUM_NAME,IS_FILL,CONFIG_ID,REMARK from SYS_ENUM_CONFIG"; 
 
 
 	@ApiAnno(groups={Default.class,QueryList.class,QuerySummary.class})
 	@BeanAnno(value="COL_NAME", getName="getColName", setName="setColName")
 	private String colName;
 	@ApiAnno(groups={Default.class,QueryList.class,QuerySummary.class})
 	@BeanAnno(value="TABLE_NAME", getName="getTableName", setName="setTableName")
 	private String tableName;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="ENUM_CODE", getName="getEnumCode", setName="setEnumCode")
 	private String enumCode;

 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="SHOW_SORT", getName="getShowSort", setName="setShowSort")
 	private int showSort;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="ENUM_NAME", getName="getEnumName", setName="setEnumName")
 	private String enumName;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="IS_FILL", getName="getIsFill", setName="setIsFill")
 	private int isFill;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="CONFIG_ID", getName="getConfigId", setName="setConfigId")
 	private long configId;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="REMARK", getName="getRemark", setName="setRemark")
 	private String remark;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="ENUM_EXTEND", getName="getEnumExtend", setName="setEnumExtend")
 	private String enumExtend;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="ENUM_NAME_EN", getName="getEnumNameEn", setName="setEnumNameEn")
 	private String enumNameEn;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="REMARK_EN", getName="getRemarkEn", setName="setRemarkEn")
    private String remarkEn;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="PARENT_ENUM_CODE", getName="getParentEnumCode", setName="setParentEnumCode")
    private String parentEnumCode;

 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(dtocls={SysEnumConfigDTO.class})
 	private List<SysEnumConfigDTO> children;
 	
 	public void setColName(String colName){
 		 this.colName=colName;
 	}
 	public String getColName(){
 		 return this.colName;
 	}
 
 	 
 	public void setTableName(String tableName){
 		 this.tableName=tableName;
 	}
 	public String getTableName(){
 		 return this.tableName;
 	}
 
 	 

 
 	 
 	public void setEnumCode(String enumCode){
 		 this.enumCode=enumCode;
 	}
 	public String getEnumCode(){
 		 return this.enumCode;
 	}
 

 
 	 
 	public void setShowSort(int showSort){
 		 this.showSort=showSort;
 	}
 	public int getShowSort(){
 		 return this.showSort;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setEnumName(String enumName){
 		 this.enumName=enumName;
 	}
 	public String getEnumName(){
 		 return this.enumName;
 	}
 
 	 
 	public void setIsFill(int isFill){
 		 this.isFill=isFill;
 	}
 	public int getIsFill(){
 		 return this.isFill;
 	}
 
 	 
 	public void setConfigId(long configId){
 		 this.configId=configId;
 	}
 	public long getConfigId(){
 		 return this.configId;
 	}
 
 	 
 	public void setRemark(String remark){
 		 this.remark=remark;
 	}
 	public String getRemark(){
 		 return this.remark;
 	}
	public String getEnumExtend() {
		return enumExtend;
	}
	public void setEnumExtend(String enumExtend) {
		this.enumExtend = enumExtend;
	}
	public String getEnumNameEn() {
		return enumNameEn;
	}
	public void setEnumNameEn(String enumNameEn) {
		this.enumNameEn = enumNameEn;
	}
	public String getRemarkEn() {
		return remarkEn;
	}
	public void setRemarkEn(String remarkEn) {
		this.remarkEn = remarkEn;
	}
	public String getParentEnumCode() {
		return parentEnumCode;
	}
	public void setParentEnumCode(String parentEnumCode) {
		this.parentEnumCode = parentEnumCode;
	}
	public List<SysEnumConfigDTO> getChildren() {
		return children;
	}
	public void setChildren(List<SysEnumConfigDTO> children) {
		this.children = children;
	}
 
}