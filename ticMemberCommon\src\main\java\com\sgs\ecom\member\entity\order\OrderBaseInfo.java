package com.sgs.ecom.member.entity.order;

import javax.persistence.Id;
import java.math.BigDecimal;


public class OrderBaseInfo {


	public OrderBaseInfo() {
	}

	public OrderBaseInfo(Long orderId,int state,String dateStr) {
		this.orderId=orderId;
		this.state=state;
		this.stateDate=dateStr;
	}

	private Integer userSex;
	private String userEmail;
	private String payDate;
	private Long userId;
	private String recommendReason;
	private Integer hisState;
	private BigDecimal urgentAmount;
	private Integer isDelete;
	private String platform;
	private BigDecimal platformAmount;

	private Long lineId;

	private Integer payState;

	private String sampleRequirements;

	private String businessLine;

	private String offerDate;

	private BigDecimal realAmount;

	private String relateOrderNo;

	private Integer isPayReceived;

	private Integer isRead;

	private String stateDate;

	private String tmpGroupNo;

	private Integer isInvoice;

	private String productName;

	private Integer isTest;

	private String companyName;

	private String reportForm;

	private String createDate;

	private Integer subState;

	private String csName;

	private String csPhone;

	private String reportLuaCode;

	private String reportFormCode;

	private Integer state;

	private String orderNo;

	private String csBranch;

	private String csEmail;

	private String businessPersonEmail;

	private String productImg;

	private Long labId;

	private String labName;

	private Integer isUrgent;

	private String bu;

	private String reportLua;

	private BigDecimal testCycle;

	private Long questionId;

	private String csCode;

	private Integer orderType;

	private Integer isRemind;

	private String csNameEn;

	private String userName;

	private String platformOrder;

	private BigDecimal orderAmount;

	private String userPhone;
	@Id
	private Long orderId;

	private String city;

	private String groupNo;

	private Integer totalNums;

	private String categoryPath;

	private String auditCode;

	private Long catagoryId;

	private BigDecimal discountAmount;

	private String province;

	private String orderExpDate;

	private Integer proState;

	private BigDecimal serviceAmount;
	private String confirmOrderDate;


	private String accountNo;
	private String orderSource;

	private String closeCode;
	private String closeReason;
	private Integer testLabel;
	private String lastResponseDate;
	private String orderSourceFrom;
	private Integer refundState;
	private Integer payMethod;
	private String operatorSource;

	private String salesCode;
	private String salesPhone;
	private String promoInfo;
	private String fromSource;
	private String fromUrl;
	private String bossNo;
	private Long custId;
	private Integer monthPay;
	private BigDecimal shopDisAmount;//店铺优惠金额
	private String abstractCustcode;
	private String currency;

	private String companyAddressEn;
	private String companyAddressCn;
	private String companyNameEn;
	private String town;

	private String confirmUseState;
	private Integer noticeNum;
	private Integer isElectron; //是否是上海开票显示电子 0-否 1-是

	private BigDecimal csDiscountAmount;
	private String recommendReasonImage;
	private String testCycleMemo;
	private String leadsCode;
	private String deadlineTime;
	private BigDecimal exchangeRate;
	private String applySubmitDate;
	private String salesEmail;

	public BigDecimal getCsDiscountAmount() {
		return csDiscountAmount;
	}

	public void setCsDiscountAmount(BigDecimal csDiscountAmount) {
		this.csDiscountAmount = csDiscountAmount;
	}

	public String getRecommendReasonImage() {
		return recommendReasonImage;
	}

	public void setRecommendReasonImage(String recommendReasonImage) {
		this.recommendReasonImage = recommendReasonImage;
	}

	public String getTestCycleMemo() {
		return testCycleMemo;
	}

	public void setTestCycleMemo(String testCycleMemo) {
		this.testCycleMemo = testCycleMemo;
	}

	public String getLeadsCode() {
		return leadsCode;
	}

	public void setLeadsCode(String leadsCode) {
		this.leadsCode = leadsCode;
	}

	public String getDeadlineTime() {
		return deadlineTime;
	}

	public void setDeadlineTime(String deadlineTime) {
		this.deadlineTime = deadlineTime;
	}

	public BigDecimal getExchangeRate() {
		return exchangeRate;
	}

	public void setExchangeRate(BigDecimal exchangeRate) {
		this.exchangeRate = exchangeRate;
	}

	public String getApplySubmitDate() {
		return applySubmitDate;
	}

	public void setApplySubmitDate(String applySubmitDate) {
		this.applySubmitDate = applySubmitDate;
	}

	public Integer getIsElectron() {
		return isElectron;
	}

	public void setIsElectron(Integer isElectron) {
		this.isElectron = isElectron;
	}

	private String operatorCode;

    public String getCompanyAddressEn() {
		return companyAddressEn;
	}

	public void setCompanyAddressEn(String companyAddressEn) {
		this.companyAddressEn = companyAddressEn;
	}

	public String getCompanyAddressCn() {
		return companyAddressCn;
	}

	public void setCompanyAddressCn(String companyAddressCn) {
		this.companyAddressCn = companyAddressCn;
	}

	public String getCompanyNameEn() {
		return companyNameEn;
	}

	public void setCompanyNameEn(String companyNameEn) {
		this.companyNameEn = companyNameEn;
	}

	public String getTown() {
		return town;
	}

	public void setTown(String town) {
		this.town = town;
	}

	public BigDecimal getShopDisAmount() {
		return shopDisAmount;
	}

	public void setShopDisAmount(BigDecimal shopDisAmount) {
		this.shopDisAmount = shopDisAmount;
	}

	public Integer getProState() {
		return proState;
	}

	public void setProState(Integer proState) {
		this.proState = proState;
	}





	public void setUserSex(Integer userSex){
		this.userSex=userSex;
	}
	public Integer getUserSex(){
		return this.userSex;
	}


	public void setUserEmail(String userEmail){
		this.userEmail=userEmail;
	}
	public String getUserEmail(){
		return this.userEmail;
	}


	public void setPayDate(String payDate){
		this.payDate=payDate;
	}
	public String getPayDate(){
		return this.payDate;
	}


	public void setUserId(Long userId){
		this.userId=userId;
	}
	public Long getUserId(){
		return this.userId;
	}


	public void setRecommendReason(String recommendReason){
		this.recommendReason=recommendReason;
	}
	public String getRecommendReason(){
		return this.recommendReason;
	}


	public void setHisState(Integer hisState){
		this.hisState=hisState;
	}
	public Integer getHisState(){
		return this.hisState;
	}


	public void setUrgentAmount(BigDecimal urgentAmount){
		this.urgentAmount=urgentAmount;
	}
	public BigDecimal getUrgentAmount(){
		return this.urgentAmount;
	}


	public void setIsDelete(Integer isDelete){
		this.isDelete=isDelete;
	}
	public Integer getIsDelete(){
		return this.isDelete;
	}


	public void setPlatform(String platform){
		this.platform=platform;
	}
	public String getPlatform(){
		return this.platform;
	}


	public void setLineId(Long lineId){
		this.lineId=lineId;
	}
	public Long getLineId(){
		return this.lineId;
	}


	public void setPayState(Integer payState){
		this.payState=payState;
	}
	public Integer getPayState(){
		return this.payState;
	}


	public void setSampleRequirements(String sampleRequirements){
		this.sampleRequirements=sampleRequirements;
	}
	public String getSampleRequirements(){
		return this.sampleRequirements;
	}


	public void setBusinessLine(String businessLine){
		this.businessLine=businessLine;
	}
	public String getBusinessLine(){
		return this.businessLine;
	}


	public void setOfferDate(String offerDate){
		this.offerDate=offerDate;
	}
	public String getOfferDate(){
		return this.offerDate;
	}


	public void setRealAmount(BigDecimal realAmount){
		this.realAmount=realAmount;
	}
	public BigDecimal getRealAmount(){
		return this.realAmount;
	}


	public void setRelateOrderNo(String relateOrderNo){
		this.relateOrderNo=relateOrderNo;
	}
	public String getRelateOrderNo(){
		return this.relateOrderNo;
	}


	public void setIsPayReceived(Integer isPayReceived){
		this.isPayReceived=isPayReceived;
	}
	public Integer getIsPayReceived(){
		return this.isPayReceived;
	}


	public void setIsRead(Integer isRead){
		this.isRead=isRead;
	}
	public Integer getIsRead(){
		return this.isRead;
	}


	public void setStateDate(String stateDate){
		this.stateDate=stateDate;
	}
	public String getStateDate(){
		return this.stateDate;
	}


	public void setTmpGroupNo(String tmpGroupNo){
		this.tmpGroupNo=tmpGroupNo;
	}
	public String getTmpGroupNo(){
		return this.tmpGroupNo;
	}


	public void setIsInvoice(Integer isInvoice){
		this.isInvoice=isInvoice;
	}
	public Integer getIsInvoice(){
		return this.isInvoice;
	}


	public void setProductName(String productName){
		this.productName=productName;
	}
	public String getProductName(){
		return this.productName;
	}





	public void setCompanyName(String companyName){
		this.companyName=companyName;
	}
	public String getCompanyName(){
		return this.companyName;
	}


	public void setReportForm(String reportForm){
		this.reportForm=reportForm;
	}
	public String getReportForm(){
		return this.reportForm;
	}


	public void setCreateDate(String createDate){
		this.createDate=createDate;
	}
	public String getCreateDate(){
		return this.createDate;
	}


	public void setSubState(Integer subState){
		this.subState=subState;
	}
	public Integer getSubState(){
		return this.subState;
	}




	public void setCsName(String csName){
		this.csName=csName;
	}
	public String getCsName(){
		return this.csName;
	}


	public void setReportLuaCode(String reportLuaCode){
		this.reportLuaCode=reportLuaCode;
	}
	public String getReportLuaCode(){
		return this.reportLuaCode;
	}


	public void setReportFormCode(String reportFormCode){
		this.reportFormCode=reportFormCode;
	}
	public String getReportFormCode(){
		return this.reportFormCode;
	}


	public void setState(Integer state){
		this.state=state;
	}
	public Integer getState(){
		return this.state;
	}


	public void setOrderNo(String orderNo){
		this.orderNo=orderNo;
	}
	public String getOrderNo(){
		return this.orderNo;
	}


	public void setCsBranch(String csBranch){
		this.csBranch=csBranch;
	}
	public String getCsBranch(){
		return this.csBranch;
	}


	public void setCsEmail(String csEmail){
		this.csEmail=csEmail;
	}
	public String getCsEmail(){
		return this.csEmail;
	}


	public void setProductImg(String productImg){
		this.productImg=productImg;
	}
	public String getProductImg(){
		return this.productImg;
	}


	public void setLabId(Long labId){
		this.labId=labId;
	}
	public Long getLabId(){
		return this.labId;
	}


	public void setLabName(String labName){
		this.labName=labName;
	}
	public String getLabName(){
		return this.labName;
	}


	public void setIsUrgent(Integer isUrgent){
		this.isUrgent=isUrgent;
	}
	public Integer getIsUrgent(){
		return this.isUrgent;
	}


	public void setBu(String bu){
		this.bu=bu;
	}
	public String getBu(){
		return this.bu;
	}


	public void setReportLua(String reportLua){
		this.reportLua=reportLua;
	}
	public String getReportLua(){
		return this.reportLua;
	}


	public void setTestCycle(BigDecimal testCycle){
		this.testCycle=testCycle;
	}
	public BigDecimal getTestCycle(){
		return this.testCycle;
	}


	public void setQuestionId(Long questionId){
		this.questionId=questionId;
	}
	public Long getQuestionId(){
		return this.questionId;
	}


	public void setCsCode(String csCode){
		this.csCode=csCode;
	}
	public String getCsCode(){
		return this.csCode;
	}


	public void setOrderType(Integer orderType){
		this.orderType=orderType;
	}
	public Integer getOrderType(){
		return this.orderType;
	}


	public void setIsRemind(Integer isRemind){
		this.isRemind=isRemind;
	}
	public Integer getIsRemind(){
		return this.isRemind;
	}


	public void setCsNameEn(String csNameEn){
		this.csNameEn=csNameEn;
	}
	public String getCsNameEn(){
		return this.csNameEn;
	}


	public void setUserName(String userName){
		this.userName=userName;
	}
	public String getUserName(){
		return this.userName;
	}


	public void setPlatformOrder(String platformOrder){
		this.platformOrder=platformOrder;
	}
	public String getPlatformOrder(){
		return this.platformOrder;
	}


	public void setOrderAmount(BigDecimal orderAmount){
		this.orderAmount=orderAmount;
	}
	public BigDecimal getOrderAmount(){
		return this.orderAmount;
	}


	public void setUserPhone(String userPhone){
		this.userPhone=userPhone;
	}
	public String getUserPhone(){
		return this.userPhone;
	}


	public void setOrderId(Long orderId){
		this.orderId=orderId;
	}
	public Long getOrderId(){
		return this.orderId;
	}


	public void setCity(String city){
		this.city=city;
	}
	public String getCity(){
		return this.city;
	}


	public void setGroupNo(String groupNo){
		this.groupNo=groupNo;
	}
	public String getGroupNo(){
		return this.groupNo;
	}


	public void setTotalNums(Integer totalNums){
		this.totalNums=totalNums;
	}
	public Integer getTotalNums(){
		return this.totalNums;
	}


	public void setCategoryPath(String categoryPath){
		this.categoryPath=categoryPath;
	}
	public String getCategoryPath(){
		return this.categoryPath;
	}


	public void setAuditCode(String auditCode){
		this.auditCode=auditCode;
	}
	public String getAuditCode(){
		return this.auditCode;
	}


	public void setCatagoryId(Long catagoryId){
		this.catagoryId=catagoryId;
	}
	public Long getCatagoryId(){
		return this.catagoryId;
	}


	public void setDiscountAmount(BigDecimal discountAmount){
		this.discountAmount=discountAmount;
	}
	public BigDecimal getDiscountAmount(){
		return this.discountAmount;
	}


	public void setProvince(String province){
		this.province=province;
	}
	public String getProvince(){
		return this.province;
	}


	public void setOrderExpDate(String orderExpDate){
		this.orderExpDate=orderExpDate;
	}
	public String getOrderExpDate(){
		return this.orderExpDate;
	}


	public void setServiceAmount(BigDecimal serviceAmount){
		this.serviceAmount=serviceAmount;
	}
	public BigDecimal getServiceAmount(){
		return this.serviceAmount;
	}

	public String getAccountNo() {
		return accountNo;
	}

	public void setAccountNo(String accountNo) {
		this.accountNo = accountNo;
	}

	public Integer getIsTest() {
		return isTest;
	}

	public void setIsTest(Integer isTest) {
		this.isTest = isTest;
	}

	public String getCloseCode() {
		return closeCode;
	}

	public void setCloseCode(String closeCode) {
		this.closeCode = closeCode;
	}

	public String getCloseReason() {
		return closeReason;
	}

	public void setCloseReason(String closeReason) {
		this.closeReason = closeReason;
	}

	public Integer getTestLabel() {
		return testLabel;
	}

	public void setTestLabel(Integer testLabel) {
		this.testLabel = testLabel;
	}

	public String getLastResponseDate() {
		return lastResponseDate;
	}

	public void setLastResponseDate(String lastResponseDate) {
		this.lastResponseDate = lastResponseDate;
	}

	public String getOrderSourceFrom() {
		return orderSourceFrom;
	}

	public void setOrderSourceFrom(String orderSourceFrom) {
		this.orderSourceFrom = orderSourceFrom;
	}

	public Integer getRefundState() {
		return refundState;
	}

	public void setRefundState(Integer refundState) {
		this.refundState = refundState;
	}

	public Integer getPayMethod() {
		return payMethod;
	}

	public void setPayMethod(Integer payMethod) {
		this.payMethod = payMethod;
	}

	public String getOperatorSource() {
		return operatorSource;
	}

	public void setOperatorSource(String operatorSource) {
		this.operatorSource = operatorSource;
	}

	public String getConfirmOrderDate() {
		return confirmOrderDate;
	}

	public void setConfirmOrderDate(String confirmOrderDate) {
		this.confirmOrderDate = confirmOrderDate;
	}

	public String getSalesCode() {
		return salesCode;
	}

	public void setSalesCode(String salesCode) {
		this.salesCode = salesCode;
	}

	public String getSalesPhone() {
		return salesPhone;
	}

	public void setSalesPhone(String salesPhone) {
		this.salesPhone = salesPhone;
	}

	public String getPromoInfo() {
		return promoInfo;
	}

	public void setPromoInfo(String promoInfo) {
		this.promoInfo = promoInfo;
	}


	public String getBossNo() {
		return bossNo;
	}

	public void setBossNo(String bossNo) {
		this.bossNo = bossNo;
	}

	public Long getCustId() {
		return custId;
	}

	public void setCustId(Long custId) {
		this.custId = custId;
	}

	public String getOrderSource() {
		return orderSource;
	}

	public void setOrderSource(String orderSource) {
		this.orderSource = orderSource;
	}

	public Integer getMonthPay() {
		return monthPay;
	}

	public void setMonthPay(Integer monthPay) {
		this.monthPay = monthPay;
	}

	public String getAbstractCustcode() {
		return abstractCustcode;
	}

	public void setAbstractCustcode(String abstractCustcode) {
		this.abstractCustcode = abstractCustcode;
	}

	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public String getConfirmUseState() {
		return confirmUseState;
	}

	public void setConfirmUseState(String confirmUseState) {
		this.confirmUseState = confirmUseState;
	}

	public String getFromSource() {
		return fromSource;
	}

	public void setFromSource(String fromSource) {
		this.fromSource = fromSource;
	}

	public String getFromUrl() {
		return fromUrl;
	}

	public void setFromUrl(String fromUrl) {
		this.fromUrl = fromUrl;
	}

	public String getBusinessPersonEmail() {
		return businessPersonEmail;
	}

	public void setBusinessPersonEmail(String businessPersonEmail) {
		this.businessPersonEmail = businessPersonEmail;
	}

	public Integer getNoticeNum() {
		return noticeNum;
	}

	public void setNoticeNum(Integer noticeNum) {
		this.noticeNum = noticeNum;
	}

	public String getOperatorCode() {
		return operatorCode;
	}

	public void setOperatorCode(String operatorCode) {
		this.operatorCode = operatorCode;
	}

	public BigDecimal getPlatformAmount() {
		return platformAmount;
	}

	public void setPlatformAmount(BigDecimal platformAmount) {
		this.platformAmount = platformAmount;
	}

	public String getCsPhone() {
		return csPhone;
	}

	public void setCsPhone(String csPhone) {
		this.csPhone = csPhone;
	}

	public String getSalesEmail() {
		return salesEmail;
	}

	public void setSalesEmail(String salesEmail) {
		this.salesEmail = salesEmail;
	}
}