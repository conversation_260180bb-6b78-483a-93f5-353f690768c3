<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.order.dao.OrderCustomerMapper" >



    <resultMap id="resultDTO" type="com.sgs.ecom.member.dto.order.OrderCustomerDTO" >
        <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
        <result column="OBJECT_TYPE" property="objectType" jdbcType="VARCHAR" />
        <result column="OBJECT_ID" property="objectId" jdbcType="INTEGER" />
    </resultMap>

    <sql id="sqlListDTO" >
        ORDER_NO,OBJECT_TYPE,OBJECT_ID
    </sql>

    <select id="qryCustomerByOrderNo" resultMap="resultDTO"  >
        select
        <include refid="sqlListDTO"/>
        from ORDER_CUSTOMER
        where state=1 and ORDER_NO=#{orderNo} order by SHARE_ID asc
    </select>
</mapper>