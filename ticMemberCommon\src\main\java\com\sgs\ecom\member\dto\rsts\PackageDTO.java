package com.sgs.ecom.member.dto.rsts;

import java.math.BigDecimal;
import java.util.List;

public class PackageDTO {
	public Long packageId;
	public String packageName;
	public String salesPrice;
	public BigDecimal testDays;
	public String unit;
	private String categoryPath;
	private String categoryMemo;
	private Long categoryId;
	private String price;
	private List<ItemDTO> items;


	public Long getPackageId() {
		return packageId;
	}

	public void setPackageId(Long packageId) {
		this.packageId = packageId;
	}

	public String getPackageName() {
		return packageName;
	}

	public void setPackageName(String packageName) {
		this.packageName = packageName;
	}

	public String getSalesPrice() {
		return salesPrice;
	}

	public void setSalesPrice(String salesPrice) {
		this.salesPrice = salesPrice;
	}

	public String getUnit() {
		return unit;
	}

	public void setUnit(String unit) {
		this.unit = unit;
	}

	public String getCategoryPath() {
		return categoryPath;
	}

	public void setCategoryPath(String categoryPath) {
		this.categoryPath = categoryPath;
	}

	public String getPrice() {
		return price;
	}

	public void setPrice(String price) {
		this.price = price;
	}

	public Long getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Long categoryId) {
		this.categoryId = categoryId;
	}

	public BigDecimal getTestDays() {
		return testDays;
	}

	public void setTestDays(BigDecimal testDays) {
		this.testDays = testDays;
	}

	public List<ItemDTO> getItems() {
		return items;
	}

	public void setItems(List<ItemDTO> items) {
		this.items = items;
	}

	public String getCategoryMemo() {
		return categoryMemo;
	}

	public void setCategoryMemo(String categoryMemo) {
		this.categoryMemo = categoryMemo;
	}
}
