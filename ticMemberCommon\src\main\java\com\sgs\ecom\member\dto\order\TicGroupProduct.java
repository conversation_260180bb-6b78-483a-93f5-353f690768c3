package com.sgs.ecom.member.dto.order;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sgs.ecom.member.request.tic.TicProductReq;
import com.sgs.ecom.member.util.serializer.PriceNumFormatSerializer;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Description :
 * @date 2024/6/19
 */
public class TicGroupProduct {
    private int row;//用于知道几个商品

    private String subBuCode;

    private int quantity;//当前sku下的购买数量

    private int reportNums;
    private int urgentNums;
    @JsonSerialize(using = PriceNumFormatSerializer.class)
    private BigDecimal shopDisAmount;//单sku下的店铺满减
    private List<TicProductReq> productDTOList;

    private String storeId;

    private Long labId;

    private String shopDisName;// 优惠券名称
    private String subDiscountName;//满减文字

    private  String prodIdAndSku;

    private String SpiltOrderType; //CreateOrderSplitType 枚举的type类型

    private Integer reportNumsAdd ;
    private Integer urgentNumsAdd ;

    public String getSubDiscountName() {
        return subDiscountName;
    }

    public void setSubDiscountName(String subDiscountName) {
        this.subDiscountName = subDiscountName;
    }

    public String getProdIdAndSku() {
        return prodIdAndSku;
    }

    public void setProdIdAndSku(String prodIdAndSku) {
        this.prodIdAndSku = prodIdAndSku;
    }

    public String getShopDisName() {
        return shopDisName;
    }

    public void setShopDisName(String shopDisName) {
        this.shopDisName = shopDisName;
    }

    public Long getLabId() {
        return labId;
    }

    public void setLabId(Long labId) {
        this.labId = labId;
    }

    public List<TicProductReq> getProductDTOList() {
        return productDTOList;
    }

    public void setProductDTOList(List<TicProductReq> productDTOList) {
        this.productDTOList = productDTOList;
    }

    public int getRow() {
        return row;
    }

    public void setRow(int row) {
        this.row = row;
    }

    public String getSubBuCode() {
        return subBuCode;
    }

    public void setSubBuCode(String subBuCode) {
        this.subBuCode = subBuCode;
    }

    public int getQuantity() {
        return quantity;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    public int getReportNums() {
        return reportNums;
    }

    public void setReportNums(int reportNums) {
        this.reportNums = reportNums;
    }

    public int getUrgentNums() {
        return urgentNums;
    }

    public void setUrgentNums(int urgentNums) {
        this.urgentNums = urgentNums;
    }

    public BigDecimal getShopDisAmount() {
        return shopDisAmount;
    }

    public void setShopDisAmount(BigDecimal shopDisAmount) {
        this.shopDisAmount = shopDisAmount;
    }

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    public String getSpiltOrderType() {
        return SpiltOrderType;
    }

    public void setSpiltOrderType(String spiltOrderType) {
        SpiltOrderType = spiltOrderType;
    }

    public Integer getReportNumsAdd() {
        return reportNumsAdd;
    }

    public void setReportNumsAdd(Integer reportNumsAdd) {
        this.reportNumsAdd = reportNumsAdd;
    }

    public Integer getUrgentNumsAdd() {
        return urgentNumsAdd;
    }

    public void setUrgentNumsAdd(Integer urgentNumsAdd) {
        this.urgentNumsAdd = urgentNumsAdd;
    }
}
