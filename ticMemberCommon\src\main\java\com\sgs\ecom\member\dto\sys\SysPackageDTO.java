package com.sgs.ecom.member.dto.sys;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.json.ScienceFormatSerializer;
import com.platform.util.json.TimeFormatSerializer;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.entity.sys.SysPackagePrice;

import java.sql.Timestamp;
import java.util.List;

public class SysPackageDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select CATEGORY_ID,CUST_ID,UNIT,PRICE,PERSON_CODE,STATE_DATE,STATE,DISCOUNT_RATE,SALES_PRICE,PACKAGE_LABEL,CREATE_DATE,BU,SAMPLE_REQUIREMENTS,PP_ID,CATEGORY_PATH,BUSINESS_LINE,TEST_DAYS,PACKAGE_NAME,PACKAGE_ID,MEMO from SYS_PACKAGE sp,SYS_PACKAGE_CATEGORY spc where sp.PACKAGE_ID=spc.PACKAGE_ID"; 
 
 
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="CATEGORY_ID", getName="getCategoryId", setName="setCategoryId")
 	private long categoryId;
 	@ApiAnno(groups={Default.class,QuerySummary.class})
 	@BeanAnno(value="CUST_ID", getName="getCustId", setName="setCustId")
 	private long custId;
 	@ApiAnno(groups={Default.class,QueryList.class,QueryDtl.class,QuerySummary.class,QueryCustom.class})
 	@BeanAnno(value="UNIT", getName="getUnit", setName="setUnit")
 	private String unit;
 	@ApiAnno(groups={Default.class,QueryList.class,QueryDtl.class,QuerySummary.class,QueryCustom.class})
 	@BeanAnno(value="PRICE", getName="getPrice", setName="setPrice")
 	private double price;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="PERSON_CODE", getName="getPersonCode", setName="setPersonCode")
 	private String personCode;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private Timestamp stateDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(groups={Default.class,QueryDtl.class})
 	@BeanAnno(value="DISCOUNT_RATE", getName="getDiscountRate", setName="setDiscountRate")
 	private double discountRate;
 	@ApiAnno(groups={Default.class,QueryList.class,QueryDtl.class,QuerySummary.class,QueryCustom.class})
 	@BeanAnno(value="SALES_PRICE", getName="getSalesPrice", setName="setSalesPrice")
 	private double salesPrice;
 	@ApiAnno(groups={Default.class,QueryDtl.class})
 	@BeanAnno(value="PACKAGE_LABEL", getName="getPackageLabel", setName="setPackageLabel")
 	private String packageLabel;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(groups={Default.class,QueryDtl.class})
 	@BeanAnno(value="BU", getName="getBu", setName="setBu")
 	private String bu;
 	@ApiAnno(groups={Default.class,QueryDtl.class,QuerySummary.class})
 	@BeanAnno(value="SAMPLE_REQUIREMENTS", getName="getSampleRequirements", setName="setSampleRequirements")
 	private String sampleRequirements;
 	@ApiAnno(groups={Default.class,QueryDtl.class})
 	@BeanAnno(value="PP_ID", getName="getPpId", setName="setPpId")
 	private String ppId;
 	@ApiAnno(groups={Default.class,QueryDtl.class})
 	@BeanAnno(value="BUSINESS_LINE", getName="getBusinessLine", setName="setBusinessLine")
 	private Integer businessLine;
 	@ApiAnno(groups={Default.class,QueryList.class,QueryDtl.class,QuerySummary.class,QueryCustom.class})
 	@BeanAnno(value="TEST_DAYS", getName="getTestDays", setName="setTestDays")
 	private int testDays;
 	@ApiAnno(groups={Default.class,QueryList.class,QueryDtl.class,QuerySummary.class,QueryCustom.class})
 	@BeanAnno(value="PACKAGE_NAME", getName="getPackageName", setName="setPackageName")
 	private String packageName;
 	@ApiAnno(groups={Default.class,QueryList.class,QueryDtl.class,QuerySummary.class,QueryCustom.class})
 	@BeanAnno(value="PACKAGE_ID", getName="getPackageId", setName="setPackageId")
 	private long packageId;
 	@ApiAnno(groups={Default.class,QueryDtl.class})
 	@BeanAnno(value="MEMO", getName="getMemo", setName="setMemo")
 	private String memo;
 	@ApiAnno(groups={Default.class,QueryList.class,QuerySummary.class,QueryCustom.class})
 	@BeanAnno(value="CATEGORY_PATH", getName="getCategoryPath", setName="setCategoryPath")
 	private String categoryPath;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="ITEM_NAME", getName="getItemName", setName="setItemName")
 	private String itemName;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="IS_OFTEN", getName="getIsOften", setName="setIsOften")
 	private int isOften;
 	@ApiAnno(groups={Default.class,QueryList.class,QuerySummary.class})
 	@BeanAnno(value="PACKAGE_TYPE", getName="getPackageType", setName="setPackageType")
 	private int packageType;
 	@ApiAnno(groups={Default.class,QueryList.class,QuerySummary.class,QueryCustom.class})
 	@BeanAnno(value="CUST_CATEGORY_PATH", getName="getCustCategoryPath", setName="setCustCategoryPath")
 	private String custCategoryPath;
 	@ApiAnno(groups={Default.class,QueryList.class,QuerySummary.class,QueryCustom.class})
 	@BeanAnno(value="CURRENCY", getName="getCurrency", setName="setCurrency")
 	private String currency;
	@ApiAnno(groups={Default.class,QueryList.class,QueryDtl.class})
	@BeanAnno(value="IS_HOT", getName="getIsHot", setName="setIsHot")
	private Integer isHot;

 	private Integer type;
 	@ApiAnno(groups={Default.class,QueryCustom.class,QuerySummary.class})
	@BeanAnno(dtocls={SysItemDTO.class})
 	private List<SysItemDTO> items;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	private List<SysPackagePrice> prices;
	@ApiAnno(groups={Default.class,QueryList.class,QuerySummary.class})
	private String accountType;
	@ApiAnno(groups={Default.class,QueryList.class,QuerySummary.class})
	private String accountCode;
 	@ApiAnno(groups={Default.class,QueryCustom.class,QueryDtl.class,QueryList.class})
 	private String categoryMemo;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(dtocls={SysPackageCategoryDTO.class})
 	private List<SysPackageCategoryDTO> categories;
	@ApiAnno(groups={Default.class,QueryList.class,QuerySummary.class,QueryCustom.class})
 	private int saleNum;

	public int getSaleNum() {
		return saleNum;
	}

	public void setSaleNum(int saleNum) {
		this.saleNum = saleNum;
	}

	public void setCategoryId(long categoryId){
 		 this.categoryId=categoryId;
 	}
 	public long getCategoryId(){
 		 return this.categoryId;
 	}
 
 	 
 	public void setCustId(long custId){
 		 this.custId=custId;
 	}
 	public long getCustId(){
 		 return this.custId;
 	}
 
 	 
 	public void setUnit(String unit){
 		 this.unit=unit;
 	}
 	public String getUnit(){
 		 return this.unit;
 	}
 
 	 
 	public void setPrice(double price){
 		 this.price=price;
 	}
 	@JsonSerialize(using = ScienceFormatSerializer.class) 
 	public double getPrice(){
 		 return this.price;
 	}
 
 	 
 	public void setPersonCode(String personCode){
 		 this.personCode=personCode;
 	}
 	public String getPersonCode(){
 		 return this.personCode;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setDiscountRate(double discountRate){
 		 this.discountRate=discountRate;
 	}
 	@JsonSerialize(using = ScienceFormatSerializer.class) 
 	public double getDiscountRate(){
 		 return this.discountRate;
 	}
 
 	 
 	public void setSalesPrice(double salesPrice){
 		 this.salesPrice=salesPrice;
 	}
 	@JsonSerialize(using = ScienceFormatSerializer.class) 
 	public double getSalesPrice(){
 		 return this.salesPrice;
 	}
 
 	 
 	public void setPackageLabel(String packageLabel){
 		 this.packageLabel=packageLabel;
 	}
 	public String getPackageLabel(){
 		 return this.packageLabel;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setBu(String bu){
 		 this.bu=bu;
 	}
 	public String getBu(){
 		 return this.bu;
 	}
 
 	 
 	public void setSampleRequirements(String sampleRequirements){
 		 this.sampleRequirements=sampleRequirements;
 	}
 	public String getSampleRequirements(){
 		 return this.sampleRequirements;
 	}
 
 	 
 	public void setPpId(String ppId){
 		 this.ppId=ppId;
 	}
 	public String getPpId(){
 		 return this.ppId;
 	}
 
 	 
 	public void setCategoryPath(String categoryPath){
 		 this.categoryPath=categoryPath;
 	}
 	public String getCategoryPath(){
 		 return this.categoryPath;
 	}
 
 	 
 	public void setBusinessLine(Integer businessLine){
 		 this.businessLine=businessLine;
 	}
 	public Integer getBusinessLine(){
 		 return this.businessLine;
 	}
 
 	 
 	public void setTestDays(int testDays){
 		 this.testDays=testDays;
 	}
 	public int getTestDays(){
 		 return this.testDays;
 	}
 
 	 
 	public void setPackageName(String packageName){
 		 this.packageName=packageName;
 	}
 	public String getPackageName(){
 		 return this.packageName;
 	}
 
 	 
 	public void setPackageId(long packageId){
 		 this.packageId=packageId;
 	}
 	public long getPackageId(){
 		 return this.packageId;
 	}
 
 	 
 	public void setMemo(String memo){
 		 this.memo=memo;
 	}
 	public String getMemo(){
 		 return this.memo;
 	}
	public String getItemName() {
		return itemName;
	}
	public void setItemName(String itemName) {
		this.itemName = itemName;
	}
	public int getIsOften() {
		return isOften;
	}
	public void setIsOften(int isOften) {
		this.isOften = isOften;
	}
	public int getPackageType() {
		return packageType;
	}
	public void setPackageType(int packageType) {
		this.packageType = packageType;
	}

	public List<SysItemDTO> getItems() {
		return items;
	}

	public void setItems(List<SysItemDTO> items) {
		this.items = items;
	}

	public String getCustCategoryPath() {
		return custCategoryPath;
	}
	public void setCustCategoryPath(String custCategoryPath) {
		this.custCategoryPath = custCategoryPath;
	}
	public String getCurrency() {
		return currency;
	}
	public void setCurrency(String currency) {
		this.currency = currency;
	}
	public List<SysPackagePrice> getPrices() {
		return prices;
	}
	public void setPrices(List<SysPackagePrice> prices) {
		this.prices = prices;
	}
	public Integer getType() {
		return type;
	}
	public void setType(Integer type) {
		this.type = type;
	}
	public String getCategoryMemo() {
		return categoryMemo;
	}
	public void setCategoryMemo(String categoryMemo) {
		this.categoryMemo = categoryMemo;
	}
	public List<SysPackageCategoryDTO> getCategories() {
		return categories;
	}
	public void setCategories(List<SysPackageCategoryDTO> categories) {
		this.categories = categories;
	}

	public Integer getIsHot() {
		return isHot;
	}

	public void setIsHot(Integer isHot) {
		this.isHot = isHot;
	}

	public String getAccountType() {
		return accountType;
	}

	public void setAccountType(String accountType) {
		this.accountType = accountType;
	}

	public String getAccountCode() {
		return accountCode;
	}

	public void setAccountCode(String accountCode) {
		this.accountCode = accountCode;
	}
}