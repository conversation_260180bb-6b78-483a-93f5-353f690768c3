package com.sgs.ecom.member.dto.user;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import lombok.Data;

/**
 * @ClassName UserMessageNoticeConfigDTO
 * <AUTHOR>
 * @Date 2025/7/25 16:01
 */
@Data
public class UserMessageNoticeConfigDTO {

    private String configId;

    private Long userId;

    @ApiAnno(groups={BaseQryFilter.QueryList.class})
    private String sendChannel;

    @ApiAnno(groups={BaseQryFilter.QueryList.class})
    private String noticeType;

    @ApiAnno(groups={BaseQryFilter.QueryList.class})
    private String businessGroup;

    @ApiAnno(groups={BaseQryFilter.QueryList.class})
    private Integer businessType;

    @ApiAnno(groups={BaseQryFilter.QueryList.class})
    private Integer isClose;

    private Integer state;

    private String createDate;

    private String stateDate;
}
