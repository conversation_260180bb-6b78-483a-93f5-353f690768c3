package com.sgs.ecom.member.enumtool.cust;

public enum CustLabeCodeEnum {
    BUYER("Buyer", "买家","901"),
    FACTORY("Factory", "工厂","902"),
    SUPPLIER("Supplier", "供应商","903"),
    ;

    private String code;
    private String value;
    private String bu;

    CustLabeCodeEnum(String code, String value,String bu) {
        this.code = code;
        this.value = value;
        this.bu=bu;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getBu() {
        return bu;
    }

    public void setBu(String bu) {
        this.bu = bu;
    }
}
