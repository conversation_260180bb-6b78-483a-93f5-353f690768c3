<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.user.dao.UserPromotionMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.entity.UserPromotion" >
    <id column="ACTIVITY_ID" property="activityId" jdbcType="BIGINT" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="USER_PHONE" property="userPhone" jdbcType="VARCHAR" />
    <result column="ACTIVITY_CODE" property="activityCode" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="INTEGER" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
    <result column="FIRST_UPGRADE_DATE" property="firstUpgradeDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    ACTIVITY_ID, USER_ID, USER_PHONE, ACTIVITY_CODE, STATE, CREATE_DATE, STATE_DATE, FIRST_UPGRADE_DATE
  </sql>
  <select id="qryPromotionByUser" resultMap="BaseResultMap" parameterType="com.sgs.ecom.member.vo.VOUserPromotion" >
    select 
    <include refid="Base_Column_List" />
    from USER_PROMOTION
    <where>
    <if test="activityCode != null" >
      and ACTIVITY_CODE = #{activityCode,jdbcType=VARCHAR}
    </if>
    <if test="userId != null" >
      and USER_ID = #{userId,jdbcType=BIGINT}
    </if>
    <if test="userPhone != null and userPhone !='' " >
      and USER_PHONE = #{userPhone,jdbcType=VARCHAR}
    </if>
    <if test="userEmail != null and userEmail !=''" >
        and USER_EMAIL = #{userEmail,jdbcType=VARCHAR}
    </if>
    </where>
    limit 1
  </select>
  <select id="qryUserSubmitPromotion" resultMap="BaseResultMap" parameterType="com.sgs.ecom.member.vo.VOUserPromotion" >
    select 
    <include refid="Base_Column_List" />
    from USER_PROMOTION
    where USER_ID = #{userId,jdbcType=BIGINT}
    <if test="activityCode != null" >
    	and ACTIVITY_CODE = #{activityCode,jdbcType=VARCHAR}
    </if>
  </select>
  <insert id="insertSelective" parameterType="com.sgs.ecom.member.entity.UserPromotion" >
    insert into USER_PROMOTION
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="activityId != null" >
        ACTIVITY_ID,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
      <if test="userPhone != null" >
        USER_PHONE,
      </if>
      <if test="userEmail != null" >
        USER_EMAIL,
      </if>
      <if test="accountType != null" >
        ACCOUNT_TYPE,
      </if>
      <if test="activityCode != null" >
        ACTIVITY_CODE,
      </if>
      <if test="state != null" >
        STATE,
      </if>
      <if test="firstUpgradeDate != null" >
        FIRST_UPGRADE_DATE,
      </if>
      <if test="activitySource != null" >
        ACTIVITY_SOURCE,
      </if>
      <if test="promotionCode != null" >
        PROMOTION_CODE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="activityId != null" >
        #{activityId,jdbcType=BIGINT},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="userPhone != null" >
        #{userPhone,jdbcType=VARCHAR},
      </if>
      <if test="userEmail != null" >
        #{userEmail,jdbcType=VARCHAR},
      </if>
      <if test="accountType != null" >
        #{accountType,jdbcType=VARCHAR},
      </if>
      <if test="activityCode != null" >
        #{activityCode,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        #{state,jdbcType=INTEGER},
      </if>
      <if test="firstUpgradeDate != null" >
        #{firstUpgradeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="activitySource != null" >
        #{activitySource},
      </if>
      <if test="promotionCode != null" >
        #{promotionCode},
      </if>
    </trim>
  </insert>
  <update id="updateState" parameterType="com.sgs.ecom.member.entity.UserPromotion">
  	update USER_PROMOTION set STATE = #{state,jdbcType=INTEGER}

    <if test="firstUpgradeDate != null" >
        ,FIRST_UPGRADE_DATE = #{firstUpgradeDate,jdbcType=TIMESTAMP}
    </if>
  	where ACTIVITY_ID = #{activityId,jdbcType=BIGINT}
  </update>






</mapper>