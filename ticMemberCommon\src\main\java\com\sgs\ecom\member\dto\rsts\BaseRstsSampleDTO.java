package com.sgs.ecom.member.dto.rsts;

import com.sgs.ecom.member.entity.user.UserSample;
import com.sgs.ecom.member.entity.user.UserSampleFrom;

import java.util.List;

/**
 * <AUTHOR>
 * @Description :
 * @date 2023/10/12
 */
public class BaseRstsSampleDTO {
    private  List<UserSample> userSampleList;
    private  List<UserSampleFrom> userSampleFromList;

    public List<UserSample> getUserSampleList() {
        return userSampleList;
    }

    public void setUserSampleList(List<UserSample> userSampleList) {
        this.userSampleList = userSampleList;
    }

    public List<UserSampleFrom> getUserSampleFromList() {
        return userSampleFromList;
    }

    public void setUserSampleFromList(List<UserSampleFrom> userSampleFromList) {
        this.userSampleFromList = userSampleFromList;
    }
}
