package com.sgs.ecom.member.dto.min;

import com.platform.annotation.ApiAnno;
import com.sgs.ecom.member.base.BaseOrderFilter;

public class MinReportTitleDTO {
    @ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
    private String titleCn;
    @ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
    private String addressCn;
    @ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
    private String titleEn;
    @ApiAnno(groups={BaseOrderFilter.QueryDtl.class})
    private String addressEn;

    public String getTitleCn() {
        return titleCn;
    }

    public void setTitleCn(String titleCn) {
        this.titleCn = titleCn;
    }

    public String getAddressCn() {
        return addressCn;
    }

    public void setAddressCn(String addressCn) {
        this.addressCn = addressCn;
    }

    public String getTitleEn() {
        return titleEn;
    }

    public void setTitleEn(String titleEn) {
        this.titleEn = titleEn;
    }

    public String getAddressEn() {
        return addressEn;
    }

    public void setAddressEn(String addressEn) {
        this.addressEn = addressEn;
    }
}
