package com.sgs.ecom.member.enumtool.order;

public enum ObjectTypeEnum {
    CUST("CUST", "企业"),
    USER("USER", "用户"),
    ;

    private String code;
    private String value;

    ObjectTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }


}
