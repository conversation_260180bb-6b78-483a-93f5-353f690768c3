package com.sgs.ecom.member.dto.oiq;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.util.ValidationUtil;
import com.platform.util.date.DateFormat;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.dto.base.BaseOrderDTO;
import com.sgs.ecom.member.dto.custom.SysPersonDTO;
import com.sgs.ecom.member.dto.detail.OrderApplicationFormDTO;
import com.sgs.ecom.member.dto.detail.OrderInvoiceDTO;
import com.sgs.ecom.member.dto.pay.BankDTO;
import com.sgs.ecom.member.util.serializer.TimeStringFormatSerializer;
import zipkin2.v1.V1Annotation;

import java.util.Date;

/**
 * <AUTHOR>
 */
public class OiqHeadPdfDTO {
	@ApiAnno(groups={BaseOrderFilter.WordQuotation.class})
	private String orderNo;
	@ApiAnno(groups={BaseOrderFilter.WordQuotation.class})
	private String to;
	@ApiAnno(groups={BaseOrderFilter.WordQuotation.class})
	private String attn;
	@ApiAnno(groups={BaseOrderFilter.WordQuotation.class})
	private String add;
	@ApiAnno(groups={BaseOrderFilter.WordQuotation.class})
	private String tel;
	@ApiAnno(groups={BaseOrderFilter.WordQuotation.class})
	private String email;
	@JsonSerialize(using = TimeStringFormatSerializer.class)
	@ApiAnno(groups={BaseOrderFilter.WordQuotation.class,BaseOrderFilter.WordForm.class})
	private String date;
	@ApiAnno(groups={BaseOrderFilter.WordQuotation.class})
	private String from;
	@ApiAnno(groups={BaseOrderFilter.WordQuotation.class})
	private String contact;
	@ApiAnno(groups={BaseOrderFilter.WordQuotation.class})
	private String sgsEmail;
	@ApiAnno(groups={BaseOrderFilter.WordQuotation.class})
	private String sgsPhone;
	@ApiAnno(groups={BaseOrderFilter.WordQuotation.class})
	private String salesEmail;


	public OiqHeadPdfDTO() {
	}

	public OiqHeadPdfDTO(OrderInvoiceDTO orderInvoiceDTO, BaseOrderDTO baseOrderDTO, BankDTO bankDTO, SalesInfoDTO salesInfoDTO, SysPersonDTO sysPersonDTO ) {
		if(!ValidationUtil.isEmpty(baseOrderDTO)){
			this.orderNo = baseOrderDTO.getPlatformOrder();
			this.date = baseOrderDTO.getCreateDate();
			this.sgsPhone=baseOrderDTO.getCsPhone();
			this.sgsEmail = baseOrderDTO.getCsEmail();
			this.contact=sysPersonDTO.getPersonName();
			if("2700".equals(baseOrderDTO.getBu())){
				this.contact=sysPersonDTO.getPersonNameEn();
			}
			this.salesEmail = baseOrderDTO.getSalesEmail();
		}
		if(!ValidationUtil.isEmpty(orderInvoiceDTO)){
			this.to = orderInvoiceDTO.getInvoiceTitle();
			this.attn = orderInvoiceDTO.getPayerName();
			this.add = orderInvoiceDTO.getRegAddress();
			this.tel = orderInvoiceDTO.getPayerPhone();
			this.email = orderInvoiceDTO.getPayerEmail();
		}
		if(!ValidationUtil.isEmpty(salesInfoDTO)){
			this.sgsPhone=salesInfoDTO.getSalesPhone();
			this.sgsEmail = salesInfoDTO.getSalesEmail();
			this.contact=salesInfoDTO.getSalesName();
		}

		if(!ValidationUtil.isEmpty(bankDTO)){
			this.from = bankDTO.getAccountName();
		}
	}

	public OiqHeadPdfDTO(OrderApplicationFormDTO orderApplicationFormDTO) {
		String date = "";
		try {
			date = DateFormat.dateToString(new Date(orderApplicationFormDTO.getStateDate().getTime()), "yyyy-MM-dd");
		} catch (Exception var6) {

		}
		this.date =date;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getTo() {
		return to;
	}

	public void setTo(String to) {
		this.to = to;
	}

	public String getAttn() {
		return attn;
	}

	public void setAttn(String attn) {
		this.attn = attn;
	}

	public String getTel() {
		return tel;
	}

	public void setTel(String tel) {
		this.tel = tel;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getDate() {
		return date.split("\\.")[0];
	}

	public void setDate(String date) {
		this.date = date;
	}

	public String getFrom() {
		return from;
	}

	public void setFrom(String from) {
		this.from = from;
	}

	public String getContact() {
		return contact;
	}

	public void setContact(String contact) {
		this.contact = contact;
	}

	public String getSgsEmail() {
		return sgsEmail;
	}

	public void setSgsEmail(String sgsEmail) {
		this.sgsEmail = sgsEmail;
	}

	public String getAdd() {
		return add;
	}

	public void setAdd(String add) {
		this.add = add;
	}

	public String getSalesEmail() {
		return salesEmail;
	}

	public void setSalesEmail(String salesEmail) {
		this.salesEmail = salesEmail;
	}

	public String getSgsPhone() {
		return sgsPhone;
	}

	public void setSgsPhone(String sgsPhone) {
		this.sgsPhone = sgsPhone;
	}
}
