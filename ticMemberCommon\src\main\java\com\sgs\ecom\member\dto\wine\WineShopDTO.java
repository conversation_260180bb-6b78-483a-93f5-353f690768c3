package com.sgs.ecom.member.dto.wine;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.dto.order.OrderProductDTO;
import com.sgs.ecom.member.enumtool.bbc.BbcStateEnum;
import com.sgs.ecom.member.util.serializer.TimeStringFormatSerializer;
import com.sgs.ecom.member.util.time.TimeCalendarUtil;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class WineShopDTO {


    @ApiAnno(groups = {BaseQryFilter.Default.class, BaseQryFilter.QuerySummary.class})
    @JsonSerialize(using = TimeStringFormatSerializer.class)
    private String createDate;

    @ApiAnno(groups = {BaseQryFilter.Default.class, BaseQryFilter.QuerySummary.class})
    private String orderNo;
    @ApiAnno(groups = {BaseQryFilter.Default.class,BaseQryFilter.QuerySummary.class})
    private BigDecimal price;
    @ApiAnno(groups = {BaseQryFilter.QuerySummary.class})
    private int quantity;
    @ApiAnno(groups = {BaseQryFilter.QuerySummary.class})
    private String testItem;
    @ApiAnno(groups = {BaseQryFilter.Default.class})
    private List<OrderProductDTO> orderProductDTOList;
    @ApiAnno(groups = {BaseQryFilter.Default.class, BaseQryFilter.QuerySummary.class})
    private String userName;
    @ApiAnno(groups = {BaseQryFilter.Default.class, BaseQryFilter.QuerySummary.class})
    private String userPhone;
    @ApiAnno(groups = {BaseQryFilter.Default.class, BaseQryFilter.QuerySummary.class})
    private String userEmail;
    @ApiAnno(groups = {BaseQryFilter.Default.class, BaseQryFilter.QuerySummary.class})
    private int state;
    @ApiAnno(groups = {BaseQryFilter.Default.class, BaseQryFilter.QuerySummary.class})
    private String stateShow;
    @ApiAnno(groups = {BaseQryFilter.Default.class, BaseQryFilter.QuerySummary.class})
    private String timeShow;
    @ApiAnno(groups = {BaseQryFilter.Default.class, BaseQryFilter.QuerySummary.class})
    private String hotelOfficialName;//酒店正式名称
    @ApiAnno(groups = {BaseQryFilter.Default.class, BaseQryFilter.QuerySummary.class})
    private String hotelCode;//酒店码
    @ApiAnno(groups = {BaseQryFilter.Default.class, BaseQryFilter.QuerySummary.class})
    private String brand;//品牌
    @ApiAnno(groups = {BaseQryFilter.Default.class, BaseQryFilter.QuerySummary.class})
    private String province;
    @ApiAnno(groups = {BaseQryFilter.Default.class, BaseQryFilter.QuerySummary.class})
    private String city;
    @ApiAnno(groups = {BaseQryFilter.Default.class, BaseQryFilter.QuerySummary.class})
    private String hotelAddress;//酒店所在地址


    @ApiAnno(groups = {BaseQryFilter.Default.class, BaseQryFilter.QuerySummary.class})
    private String storeName;
    @ApiAnno(groups = {BaseQryFilter.Default.class, BaseQryFilter.QuerySummary.class})
    private String companyLegalName;

    @ApiAnno(groups = {BaseQryFilter.Default.class, BaseQryFilter.QuerySummary.class})
    private String companyLegalAddress;

    @ApiAnno(groups = {BaseQryFilter.Default.class, BaseQryFilter.QuerySummary.class})
    private String companyRegNo;

    @ApiAnno(groups = {BaseQryFilter.Default.class, BaseQryFilter.QuerySummary.class})
    private String country;

    @ApiAnno(groups = {BaseQryFilter.Default.class, BaseQryFilter.QuerySummary.class})
    private String numberOfRooms;

    @ApiAnno(groups = {BaseQryFilter.Default.class, BaseQryFilter.QuerySummary.class})
    private String managementType;
    @ApiAnno(groups = {BaseQryFilter.Default.class, BaseQryFilter.QuerySummary.class})
    private Integer isElectron; //是否是上海开票显示电子 0-否 1-是

    private Date startDate;// 第一次进入待提交申请表状态的时间
    @ApiAnno(groups = {BaseQryFilter.Default.class})
    private Date endDate;//订单完成状态 ? 取订单完成时间：当前系统时间

    @ApiAnno(groups = {BaseQryFilter.Default.class})
    private String endDateString;//订单完成状态 ? 取订单完成时间：当前系统时间

    @ApiAnno(groups = {BaseQryFilter.QuerySummary.class})
    private String endDateShow;//订单完成状态 ? 取订单完成时间：当前系统时间

    @ApiAnno(groups = {BaseQryFilter.Default.class, BaseQryFilter.QuerySummary.class})
    private String coreName;//中台
    @ApiAnno(groups = {BaseQryFilter.Default.class, BaseQryFilter.QuerySummary.class})
    private String areaName;//区域
    @ApiAnno(groups = {BaseQryFilter.Default.class, BaseQryFilter.QuerySummary.class})
    private String cityProper;//城区
    @ApiAnno(groups = {BaseQryFilter.Default.class, BaseQryFilter.QuerySummary.class})
    private String whid;//whid
    @ApiAnno(groups = {BaseQryFilter.Default.class, BaseQryFilter.QuerySummary.class})
    private String businessStatus;//经营状态
    @ApiAnno(groups = {BaseQryFilter.Default.class, BaseQryFilter.QuerySummary.class})
    private String hotelArea;//酒店区域
    @ApiAnno(groups = {BaseQryFilter.Default.class, BaseQryFilter.QuerySummary.class})
    private String hotelProvince;//酒店省区
    @ApiAnno(groups = {BaseQryFilter.Default.class, BaseQryFilter.QuerySummary.class})
    private String hotelSubArea;//酒店片区

    public String getBusinessStatus() {
        return businessStatus;
    }

    public void setBusinessStatus(String businessStatus) {
        this.businessStatus = businessStatus;
    }

    public String getHotelArea() {
        return hotelArea;
    }

    public void setHotelArea(String hotelArea) {
        this.hotelArea = hotelArea;
    }

    public String getHotelProvince() {
        return hotelProvince;
    }

    public void setHotelProvince(String hotelProvince) {
        this.hotelProvince = hotelProvince;
    }

    public String getHotelSubArea() {
        return hotelSubArea;
    }

    public void setHotelSubArea(String hotelSubArea) {
        this.hotelSubArea = hotelSubArea;
    }

    public String getWhid() {
        return whid;
    }

    public void setWhid(String whid) {
        this.whid = whid;
    }

    public String getCoreName() {
        return coreName;
    }

    public void setCoreName(String coreName) {
        this.coreName = coreName;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getCityProper() {
        return cityProper;
    }

    public void setCityProper(String cityProper) {
        this.cityProper = cityProper;
    }

    public Integer getIsElectron() {
        return isElectron;
    }

    public void setIsElectron(Integer isElectron) {
        this.isElectron = isElectron;
    }

    public String getEndDateShow() {
        if (ValidationUtil.isEmpty(endDate)) {
            return "";
        }
        return TimeCalendarUtil.getDateToString(endDate);
    }

    public String getEndDateString() {
        if(!ValidationUtil.isEmpty(endDate)){
            return TimeCalendarUtil.getDateToString(endDate);
        }
        return endDateString;
    }

    public String getTestItem() {
        return testItem;
    }

    public void setTestItem(String testItem) {
        this.testItem = testItem;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public int getQuantity() {
        return quantity;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getCompanyLegalName() {
        return companyLegalName;
    }

    public void setCompanyLegalName(String companyLegalName) {
        this.companyLegalName = companyLegalName;
    }

    public String getCompanyLegalAddress() {
        return companyLegalAddress;
    }

    public void setCompanyLegalAddress(String companyLegalAddress) {
        this.companyLegalAddress = companyLegalAddress;
    }

    public String getCompanyRegNo() {
        return companyRegNo;
    }

    public void setCompanyRegNo(String companyRegNo) {
        this.companyRegNo = companyRegNo;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getNumberOfRooms() {
        return numberOfRooms;
    }

    public void setNumberOfRooms(String numberOfRooms) {
        this.numberOfRooms = numberOfRooms;
    }

    public String getManagementType() {
        return managementType;
    }

    public void setManagementType(String managementType) {
        this.managementType = managementType;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public List<OrderProductDTO> getOrderProductDTOList() {
        return orderProductDTOList;
    }

    public void setOrderProductDTOList(List<OrderProductDTO> orderProductDTOList) {
        this.orderProductDTOList = orderProductDTOList;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserPhone() {
        return userPhone;
    }

    public void setUserPhone(String userPhone) {
        this.userPhone = userPhone;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public String getStateShow() {
        if (state == 12 || state == 13) {
            return "申请表确认中";
        }
        return BbcStateEnum.getNameCh(state);
    }


    public String getTimeShow() {
        if (ValidationUtil.isEmpty(startDate)) {//说名订单没有进入待提交申请表状态
            return "服务未开始";
        }
        Date endDateNew = endDate;
        if (ValidationUtil.isEmpty(endDateNew)) {//说名订单没有进入完成状态
            endDateNew = new Date();
        }
        return String.valueOf(TimeCalendarUtil.differentDaysByMillisecond(startDate, endDateNew)) + "天";
    }


    public String getHotelOfficialName() {
        return hotelOfficialName;
    }

    public void setHotelOfficialName(String hotelOfficialName) {
        this.hotelOfficialName = hotelOfficialName;
    }

    public String getHotelCode() {
        return hotelCode;
    }

    public void setHotelCode(String hotelCode) {
        this.hotelCode = hotelCode;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getHotelAddress() {
        return hotelAddress;
    }

    public void setHotelAddress(String hotelAddress) {
        this.hotelAddress = hotelAddress;
    }
}
