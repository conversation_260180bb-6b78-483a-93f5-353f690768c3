package com.sgs.ecom.member.enums;

import java.util.ArrayList;
import java.util.List;

public enum ImpUserSmapleFormEnum {

	reportLua("reportLua", "报告语言",0),
	sampleName("sampleName", "样品名称",1),
	sampleNameEn("sampleNameEn", "Sample Name",2),
	productInfo("productInfo", "型号",3),
	productInfoEn("productInfoEn", "Model",4),
	extendedModelNo("extendedModelNo", "扩展型号",5),
	extendedModelNoEn("extendedModelNoEn", "Extended Model No",6),

	materialGrade("materialGrade", "主要成分",7),
	materialGradeEn("materialGradeEn", "Material",8),

	lotNo("lotNo", "批号",9),
	lotNoEn("lotNoEn", "Lot No.",10),


	remark("remark", "客户参考信息",11),
	remarkEn("remarkEn", "Customer Referrence",12),

	producer("producer", "制造商",13),
	producerEn("producerEn", "Producer",14),

	supplierName("supplierName", "供应商",15),
	supplierNameEn("supplierNameEn", "Supplier",16),
	productBatch("productBatch", "料号",17),
	productBatchEn("productBatchEn", "Material No.",18),
	buyersName("buyersName", "买家",19),
	buyersNameEn("buyersNameEn", "Buyer",20),

	originalProduct("originalProduct", "原产地",21),
	originalProductEn("originalProductEn", "Origin of the Product",22),
	destinationProduct("destinationProduct", "目的地",23),
	destinationProductEn("destinationProductEn", "Destination of the Product",24),

	sampleMemo("sampleMemo", "样品备注",25),
	annex("annex", "附件",26),
	sampleFeaturesStr("sampleFeaturesStr", "样品特性",27),
	sampleState("sampleState", "样品状态",28),



	;

	ImpUserSmapleFormEnum(String name, String nameCh, int min) {
		this.name = name;
		this.nameCh = nameCh;
		this.min = min;
	}
	public static List<String> getNameList() {
		List<String> list = new ArrayList<>();
		for (ImpUserSmapleFormEnum c : ImpUserSmapleFormEnum.values()) {
			list.add(c.name);
		}
		return list;
	}
	public static int getSortShow(String name) {
		for (ImpUserSmapleFormEnum c : ImpUserSmapleFormEnum.values()) {
			if (c.name().equals(name)) {
				return c.getMin();
			}
		}
		return 0;
	}

	public static String getNameCh(String name) {
		for (ImpUserSmapleFormEnum c : ImpUserSmapleFormEnum.values()) {
			if (c.name().equals(name)) {
				return c.getNameCh();
			}
		}
		return "";
	}

	public static String getName(String name) {
		for (ImpUserSmapleFormEnum c : ImpUserSmapleFormEnum.values()) {
			if (c.name().equals(name)) {
				return c.getNameCh();
			}
		}
		return null;
	}
	public static String getNameCh(int index) {
		for (ImpUserSmapleFormEnum c : ImpUserSmapleFormEnum.values()) {
			if (c.getIndex()==index) {
				return c.getNameCh();
			}
		}
		return "";
	}

	public static ImpUserSmapleFormEnum getEnum(String name) {
		for (ImpUserSmapleFormEnum c : ImpUserSmapleFormEnum.values()) {
			if (c.getName().equals(name)) {
				return c;
			}
		}
		return null;
	}

//	public static String getMinByIndex(int index) {
//		for (UserSmapleFormEnum c : UserSmapleFormEnum.values()) {
//			if (c.getIndex()==index) {
//				if(index==10){
//					return "M";
//				}
//				return c.getMin();
//			}
//		}
//		return "";
//	}

//	public  static int getEnumStateByMin(String min){
//		for (UserSmapleFormEnum c : UserSmapleFormEnum.values()) {
//			if (c.getMin().contains(min)) {
//				return c.getIndex();
//			}
//		}
//		return -2;
//	}

	private String name;
	private int index;
	private String nameCh;
	private int min;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public int getIndex() {
		return index;
	}

	public void setIndex(int index) {
		this.index = index;
	}

	public String getNameCh() {
		return nameCh;
	}

	public void setNameCh(String nameCh) {
		this.nameCh = nameCh;
	}

	public int getMin() {
		return min;
	}

	public void setMin(int min) {
		this.min = min;
	}
}
