package com.sgs.ecom.member.dto.portal;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.base.BaseOrderFilter;

public class PortalInquiryBaseDTO {
    @ApiAnno(groups={BaseOrderFilter.OrderList.class})
    private String orderNo;
    @ApiAnno(groups={BaseOrderFilter.OrderList.class})
    private int state;
    @ApiAnno(groups={BaseOrderFilter.OrderList.class})
    private int flg;
    @ApiAnno(groups={BaseOrderFilter.OrderList.class})
    private String quotationConfirmTime;

    public PortalInquiryBaseDTO() {
    }

    public PortalInquiryBaseDTO(String orderNo, int state,int flg) {
        this.orderNo = orderNo;
        this.state = state;
        this.flg=flg;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public int getFlg() {
        return flg;
    }

    public void setFlg(int flg) {
        this.flg = flg;
    }

    public String getQuotationConfirmTime() {
        return quotationConfirmTime;
    }

    public void setQuotationConfirmTime(String quotationConfirmTime) {
        this.quotationConfirmTime = quotationConfirmTime;
    }
}
