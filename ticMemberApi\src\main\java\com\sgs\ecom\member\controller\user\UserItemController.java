package com.sgs.ecom.member.controller.user;

import com.sgs.ecom.member.request.cust.CustReq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.base.BaseBean;
import com.sgs.ecom.member.service.user.interfaces.IUserItemSV;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import com.sgs.ecom.member.vo.VOSysPackage;

@RestController
@RequestMapping("/business/api.v2.user/item")
@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class,SysException.class})
public class UserItemController extends ControllerUtil {

	@Autowired
	private IUserItemSV userItemSV;
	
	/**   
	* @Function: qryOftenItem
	* @Description: 查询常用套餐测试项目信息
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2018-11-23
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2018-11-23 shenyi    v1.0                 新增
	*/
	@HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "qryOftenItem", method = { RequestMethod.POST })
    public ResultBody qryOftenItem(@RequestHeader(value="accessToken") String token,
								   @RequestBody CustReq custReq) throws Exception {
		return ResultBody.newInstance(userItemSV.qryOftenItem(getUserDTO(token).getUserId(),custReq.getCustId()));
	}
	
	/**   
	* @Function: qryUnoftenItem
	* @Description: 查询非常用套餐测试项目信息
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2018-11-23
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2018-11-23 shenyi    v1.0                 新增
	*/
	@HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "qryUnoftenItem", method = { RequestMethod.POST })
    public ResultBody qryUnoftenItem(@RequestBody VOSysPackage sysPackage,
    	@RequestHeader(value="accessToken") String token,
    	@RequestHeader(value="appId") String appId) throws Exception {
		return ResultBody.newInstance(userItemSV.qryUnoftenItem(sysPackage, getUserId(appId, token)));
	}
	
	/**   
	* @Function: qryPackageItem
	* @Description: 查询套餐下测试项目信息
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2018-11-23
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2018-11-23 shenyi    v1.0                 新增
	*/
	@HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "qryPackageItem", method = { RequestMethod.POST })
    public ResultBody qryPackageItem(@Validated(BaseBean.Query.class) 
    	@RequestBody VOSysPackage sysPackage,
    	@RequestHeader(value="accessToken") String token,
    	@RequestHeader(value="appId") String appId) throws Exception {
		return ResultBody.newInstance(userItemSV.qryPackageItem(sysPackage ,getUserId(appId, token)));
	}
	
}
