package com.sgs.ecom.member.dto.order;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.util.serializer.TimeStringFormatSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class OrderSampleDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select CREATE_DATE,PRODUCT_BATCH,GROUP_NO,SAMPLE_NAME,STATE_DATE,SAMPLE_NAME_EN,STATE,ORDER_NO,PRODUCT_INFO,MATERIAL_GRADE,REMARK,SAMPLE_ID from ORDER_SAMPLE"; 
 
 
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
	@JsonSerialize(using = TimeStringFormatSerializer.class)
 	private String createDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="PRODUCT_BATCH", getName="getProductBatch", setName="setProductBatch")
 	private String productBatch;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="SAMPLE_NAME", getName="getSampleName", setName="setSampleName")
 	private String sampleName;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
	@JsonSerialize(using = TimeStringFormatSerializer.class)
 	private String stateDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="SAMPLE_NAME_EN", getName="getSampleNameEn", setName="setSampleNameEn")
 	private String sampleNameEn;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="ORDER_NO", getName="getOrderNo", setName="setOrderNo")
 	private String orderNo;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="PRODUCT_INFO", getName="getProductInfo", setName="setProductInfo")
 	private String productInfo;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="MATERIAL_GRADE", getName="getMaterialGrade", setName="setMaterialGrade")
 	private String materialGrade;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="REMARK", getName="getRemark", setName="setRemark")
 	private String remark;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="SAMPLE_ID", getName="getSampleId", setName="setSampleId")
 	private long sampleId;

	@ApiAnno(groups={Default.class})
	private String sampleNo;
	@ApiAnno(groups={Default.class})
	private String groupNo;
	@ApiAnno(groups={Default.class})
	private String row;
	@ApiAnno(groups={Default.class})
	private String sampleNameCn;
	@ApiAnno(groups={Default.class})
	private String supplierName;
	@ApiAnno(groups={Default.class})
	private String buyersName;

	@ApiAnno(groups={Default.class})
	private String buyersNameEn;
	@ApiAnno(groups={Default.class})
	private String supplierNameEn;
	@ApiAnno(groups={Default.class})
	private String remarkEn;
	@ApiAnno(groups={Default.class})
	private String sampleNameShow;

	@ApiAnno(groups={Default.class})
	private String productBatchEn;
	@ApiAnno(groups={Default.class})
	private String productInfoEn;
	@ApiAnno(groups={Default.class})
	private String materialGradeEn;
	@ApiAnno(groups={Default.class})
	private String lotNo;
	@ApiAnno(groups={Default.class})
	private String lotNoEn;
	@ApiAnno(groups={Default.class})
	private String producer;
	@ApiAnno(groups={Default.class})
	private String producerEn;
	@ApiAnno(groups={Default.class})
	private String userSampleNo;

	@ApiAnno(groups={Default.class})
	private String limitedDateOfUse;
	@ApiAnno(groups={Default.class})
	private String limitedDateOfUseEn;
	@ApiAnno(groups={Default.class})
	private String manufacturerAddress;
	@ApiAnno(groups={Default.class})
	private String manufacturerAddressEn;
	@ApiAnno(groups={Default.class})
	private String evaluateProject;
	@ApiAnno(groups={Default.class})
	private String evaluateProjectEn;
	@ApiAnno(groups={Default.class})
	private String otherRemark;
	@ApiAnno(groups={Default.class})
	private String otherRemarkEn;
	@ApiAnno(groups={Default.class})
	private String registrantName;
	@ApiAnno(groups={Default.class})
	private String registrantNameEn;
	@ApiAnno(groups={Default.class})
	private String registrantAddress;
	@ApiAnno(groups={Default.class})
	private String registrantAddressEn;
	@ApiAnno(groups={Default.class})
	private String chinaResponsiblePersonName;
	@ApiAnno(groups={Default.class})
	private String chinaResponsiblePersonNameEn;
	@ApiAnno(groups={Default.class})
	private String chinaResponsiblePersonAddress;
	@ApiAnno(groups={Default.class})
	private String chinaResponsiblePersonAddressEn;

	@ApiAnno(groups={Default.class})
	private String manufactureCompanyName;
	@ApiAnno(groups={Default.class})
	private String manufactureAddress;
	@ApiAnno(groups={Default.class})
	private String factoryCompanyName;
	@ApiAnno(groups={Default.class})
	private String factoryAddress;
	@ApiAnno(groups={Default.class})
	private String productDescription;
	@ApiAnno(groups={Default.class})
	private String tradeMark;
	@ApiAnno(groups={Default.class})
	private String modelName;
	@ApiAnno(groups={Default.class})
	private String ratedVoltage;
	@ApiAnno(groups={Default.class})
	private String electronCurrent;
	@ApiAnno(groups={Default.class})
	private String power;
	@ApiAnno(groups={Default.class})
	private String fequency;
	@ApiAnno(groups={Default.class})
	private String ip;
	@ApiAnno(groups={Default.class})
	private String isReportShow;


	/**
	 * 样品状态
	 */
	@ApiAnno(groups={Default.class})
	private String sampleState;

	/**
	 * 样品状态备注
	 */
	@ApiAnno(groups={Default.class})
	private String sampleStateRemark;

	/**
	 * 样品重量
	 */
	@ApiAnno(groups={Default.class})
	private String sampleWeight;

	/**
	 * 样品重量(英文)
	 */
	@ApiAnno(groups={Default.class})
	private String sampleWeightEn;

	//新增对象供打印申请表使用
	@ApiAnno(groups={Default.class})
	private List<OrderSampleGroup> orderSampleGroupList = new ArrayList<>();
















	@ApiAnno(groups={Default.class})
	private String modelNo;
	@ApiAnno(groups={Default.class})
	private String modelNoEn;

	@ApiAnno(groups={Default.class})
	private String materialNo;
	@ApiAnno(groups={Default.class})
	private String materialNoEn;

	@ApiAnno(groups={Default.class})
	private String clientReferenceInformation;
	@ApiAnno(groups={Default.class})
	private String clientReferenceInformationEn;

	@ApiAnno(groups={Default.class})
	private String buyer;
	@ApiAnno(groups={Default.class})
	private String buyerEn;

	@ApiAnno(groups={Default.class})
	private String supplier;
	@ApiAnno(groups={Default.class})
	private String supplierEn;

	@ApiAnno(groups={Default.class})
	private String manufacturer;
	@ApiAnno(groups={Default.class})
	private String manufacturerEn;

	@ApiAnno(groups={Default.class})
	private String originOfTheProduct;
	@ApiAnno(groups={Default.class})
	private String originOfTheProductEn;

	@ApiAnno(groups={Default.class})
	private String destinationOfTheProduct;
	@ApiAnno(groups={Default.class})
	private String destinationOfTheProductEn;

	@ApiAnno(groups={Default.class})
	private String sampleNumName;

	@ApiAnno(groups={Default.class})
	private String mainSubstance;
	@ApiAnno(groups={Default.class})
	private String mainSubstanceEn;


	@ApiAnno(groups={Default.class})
	private String detectionSample;//送测的样品具有
	@ApiAnno(groups={Default.class})
	private String detectionSampleRemark;//送测的样品具有 其他备注

	@ApiAnno(groups={Default.class})
	private String sampleCharacter;//样品性状


	@ApiAnno(groups={Default.class})
	private String sDSIngredientFileNames;//液体与粉末请在下方上传MSDS附件或在样品备注栏备注主要成分进行评估 文件

	@ApiAnno(groups={Default.class})
	private String sDSIngredient;//液体与粉末请在下方上传MSDS附件或在样品备注栏备注主要成分进行评估 备注

	@ApiAnno(groups={Default.class})
	private String liquidOrprintingInk;//若为液体、油墨类样品
	@ApiAnno(groups={Default.class})
	private String liquidOrprintingInkRemark;//若为液体、油墨类样品备注

	@ApiAnno(groups={Default.class})
	private String testMemo;//测试备注

	@ApiAnno(groups={Default.class})
	private String testMemoFileName;//测试备注 附件
	@ApiAnno(groups={Default.class})
	private String productPackageOutFileName;//EC店铺产品外包装附件名称

	@ApiAnno(groups={Default.class})
	private List<OrderProductDTO> orderdetailRelatItemList = new ArrayList<>();

	private Long detailId;
	@ApiAnno(groups={Default.class})
	private String sampleNameStr;//转供打印申请表使用

	@ApiAnno(groups={Default.class})
	private String sampleNum; // 样品数量(中文信息)
	@ApiAnno(groups={Default.class})
	private String sampleNumEn; // 样品数量(英文信息)
	@ApiAnno(groups={Default.class})
	private String material; // 成分(中文信息)
	@ApiAnno(groups={Default.class})
	private String materialEn; // 成分(英文信息)
	@ApiAnno(groups={Default.class})
	private String colour; // 颜色(中文信息)
	@ApiAnno(groups={Default.class})
	private String colourEn; // 颜色(英文信息)
	@ApiAnno(groups={Default.class})
	private String brandName; // 品牌名称(中文信息)
	@ApiAnno(groups={Default.class})
	private String brandNameEn; // 品牌名称(英文信息)
	@ApiAnno(groups={Default.class})
	private String styleNo; // 款号(中文信息)
	@ApiAnno(groups={Default.class})
	private String styleNoEn; // 款号(英文信息)
	@ApiAnno(groups={Default.class})
	private String orderNumber; // 订单号(中文信息)，样品信息中的订单号
	@ApiAnno(groups={Default.class})
	private String orderNumberEn; // 订单号(英文信息)，样品信息中的订单号
	@ApiAnno(groups={Default.class})
	private String manufacturerDate; // 生产日期(中文信息)
	@ApiAnno(groups={Default.class})
	private String manufacturerDateEn; // 生产日期(英文信息)
	@ApiAnno(groups={Default.class})
	private String specialInfo; // 特殊信息备注(中文信息)
	@ApiAnno(groups={Default.class})
	private String specialInfoEn; // 特殊信息备注(英文信息)
	@ApiAnno(groups={Default.class})
	private String itemNo; // 货号(中文信息)
	@ApiAnno(groups={Default.class})
	private String itemNoEn; // 货号(英文信息)
	@ApiAnno(groups={Default.class})
	private String agent; // 代理商(中文信息)
	@ApiAnno(groups={Default.class})
	private String agentEn; // 代理商(英文信息)
	@ApiAnno(groups={Default.class})
	private String countryOfOrigin; // 原产国(中文信息)
	@ApiAnno(groups={Default.class})
	private String countryOfOriginEn; // 原产国(英文信息)
	@ApiAnno(groups={Default.class})
	private String exportedTo; // 目的国(中文信息)
	@ApiAnno(groups={Default.class})
	private String exportedToEn; // 目的国(英文信息)
	@ApiAnno(groups={Default.class})
	private String ageOfProductUse; // 产品使用年龄段(中文信息)
	@ApiAnno(groups={Default.class})
	private String ageOfProductUseEn; // 产品使用年龄段(英文信息)
	@ApiAnno(groups={Default.class})
	private String isNeed = "0"; // 检查标签/说明书/外包装信息

	@ApiAnno(groups={Default.class})
	private String manufacturerName ; //生产商名称中文 sl 申请表

	@ApiAnno(groups={Default.class})
	private String manufacturerNameEn ; //生产商名称英文 sl申请表

	@ApiAnno(groups={Default.class})
	private String productGrade; //sl-default 申请表的 产品等级字段
	@ApiAnno(groups={Default.class})
	private String safetyTechnicalLevel;//sl-default 申请表的 安全技术级别

	/******************* AFL ************************/
	@ApiAnno(groups={Default.class})
	private String other;
	@ApiAnno(groups={Default.class})
	private String otherEn;
	@ApiAnno(groups={Default.class})
	private String sampleStorageRequirements;
	@ApiAnno(groups={Default.class})
	private String sampleStorageRequirementsRemark;
	@ApiAnno(groups={Default.class})
	private String sampleDueTime;
	@ApiAnno(groups={Default.class})
	private String sampleHazardDescription;
	@ApiAnno(groups={Default.class})
	private String sampleHazardDescriptionRemark;

	private String filtrationEfficiencyClass;//sl-sh-kz 过滤效率等级

	private String protectiveEffectLevel;//sl-sh-kz 防护效果级别

	private String isSterilization;//sl-sh-kz 是否经过环氧乙烷灭菌处理
	private String expirationDateInfo;//sl-sh-kz 有效期信息 需要将历史数据orderNo的key割接成expirationDateInfo
	private String expirationDateInfoEn;//sl-sh-kz 有效期信息 需要将历史数据orderNo的key割接成expirationDateInfo


	private String endUses;	//SL-hz-wb-regular  End Uses (样品最终用途)
	private String endUsesEn;//SL-hz-wb-regular  End Uses (样品最终用途)

	private String careInstruction;	//SL-hz-wb-regular  Care Instruction (洗涤文字或符号)
	private String careInstructionEn;//SL-hz-wb-regular  Care Instruction (洗涤文字或符号)

	private String methodsUsed;//SL-hz-wb-regular  采用标准  		中文展示
	private String methodsUsedStr;//SL-hz-wb-regular    采用标准  	选择其他时，备注的值

	private String sampleDescription;
	private String sampleDescriptionEn;

	private String sampleRawMaterial;

	private String companyLegalName; // 公司营业执照名称(中文信息)
	private String companyLegalNameEn; // 公司营业执照名称(英文信息)
	private String companyLegalAddress; // 公司营业执照地址(中文信息)
	private String companyLegalAddressEn; // 公司营业执照地址(英文信息)
	private String country; // 国家(中文信息)
	private String countryEn; // 国家(英文信息)
	private String companyRegNo; // 公司注册号(中文信息)
	private String companyRegNoEn; // 公司注册号(英文信息)
	private String contactName; // 联系人姓名(中文信息)
	private String contactNameEn; // 联系人姓名(英文信息)
	private String hotelCode;//项目编码
	private String hotelOfficialName; // 酒店正式名称(中文信息)
	private String hotelOfficialNameEn; // 酒店正式名称(英文信息)
	private String brand; // 品牌(中文信息)
	private String brandEn; // 品牌(英文信息)
	private String hotelAddress; // 酒店地址(中文信息)
	private String hotelAddressEn; // 酒店地址(英文信息)
	private String numberOfRooms; // 房间数量(中文信息)
	private String numberOfRoomsEn; // 房间数量(英文信息)
	private String telephone; // 电话(中文信息)
	private String telephoneEn; // 电话(英文信息)
	private String email; // 邮件(中文信息)
	private String emailEn; // 邮件(英文信息)
	private String whid; // WHID
	private String dateOfSampling; // 采样日期/时间
	private String sampleRisk; // 样品危险性
	private String examineProjectArea; // 检测项目领域
	private String examineProjectAreaRemark; // 检测项目领域其他备注
	private String purposeOfTesting; // 测试目的
	private String purposeOfTestingRemark; // 测试目的其他备注
	private String sampleContainerPlasticBottles; // 塑料瓶(P)数量+规格
	private String sampleContainerVial; // 玻璃瓶(G)数量+规格
	private String sampleContainerOther; // 其他
	private String transportationStorageMethods; // 运输保存方式
	private String transportationStorageMethodsRemark; // 运输保存方式其他备注

	public String getSampleNameShow() {
 		String sampleNameShow="";
 		if(StringUtils.isNotBlank(sampleNameCn)){
			return sampleNameCn;
		}
		if(StringUtils.isNotBlank(sampleNameEn)){
			return sampleNameEn;
		}
		if(StringUtils.isNotBlank(sampleName)){
			return sampleName;
		}
		return sampleNameShow;
	}
}