package com.sgs.ecom.member.dto.rpc;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter.Default;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 问题标签选项
 *
 * <AUTHOR>
 * @since 2025-05-13 16:02
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionLabelOption {

    /**
     * 选项ID
     */
    @ApiAnno(groups={Default.class})
    private Long optionId;

    /**
     * 标签编码
     */
    @ApiAnno(groups={Default.class})
    private String labelCode;

    /**
     * 选项编码
     */
    @ApiAnno(groups={Default.class})
    private String optionCode;

    /**
     * 选项名称
     */
    @ApiAnno(groups={Default.class})
    private String optionName;

    /**
     * 是否有填空题 1：是 0：否
     */
    @ApiAnno(groups={Default.class})
    private Integer isFill;

    /**
     * 排序
     */
    @ApiAnno(groups={Default.class})
    private Integer sortShow;

    /**
     * 状态 1：有效 0：无效
     */
    private Integer state;

    /**
     * 创建时间
     */
    @ApiAnno(groups={Default.class})
    private Date createDate;

    /**
     * 创建人
     */
    @ApiAnno(groups={Default.class})
    private String createBy;

    /**
     * 修改时间
     */
    @ApiAnno(groups={Default.class})
    private Date stateDate;

    /**
     * 修改人
     */
    @ApiAnno(groups={Default.class})
    private String modifiedBy;
}
