<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.user.dao.UserSampleAttributeMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.vo.VOUserSampleAttribute" >
    <id column="ID" property="id" jdbcType="BIGINT" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="ATTR_NAME" property="attrName" jdbcType="VARCHAR" />
    <result column="ATTR_VALUE" property="attrValue" jdbcType="VARCHAR" />
    <result column="IS_DEFAULT" property="isDefault" jdbcType="TINYINT" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="SAMPLE_NO" property="sampleNo" jdbcType="VARCHAR" />
    <result column="ATTR_EXTEND" property="attrExtend" jdbcType="VARCHAR" />
    <result column="ATTR_CODE" property="attrCode" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, USER_ID, ATTR_NAME, ATTR_VALUE, IS_DEFAULT, CREATE_DATE, SAMPLE_NO, ATTR_EXTEND,ATTR_CODE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from USER_SAMPLE_ATTRIBUTE
    where ID = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from USER_SAMPLE_ATTRIBUTE
    where ID = #{id,jdbcType=BIGINT}
  </delete>


  <insert id="insertForeach"  >
    insert into USER_SAMPLE_ATTRIBUTE
    ( USER_ID, ATTR_NAME,
    ATTR_VALUE, IS_DEFAULT, CREATE_DATE,
    SAMPLE_NO, ATTR_EXTEND,ATTR_CODE)
    values
    <foreach collection ="list" item="item" index= "index" separator =",">
      ( #{item.userId}, #{item.attrName,jdbcType=VARCHAR},
      #{item.attrValue,jdbcType=VARCHAR}, #{item.isDefault,jdbcType=TINYINT}, #{item.createDate,jdbcType=VARCHAR},
      #{item.sampleNo,jdbcType=VARCHAR}, #{item.attrExtend,jdbcType=VARCHAR},#{item.attrCode,jdbcType=VARCHAR})
    </foreach>
  </insert>




  <delete id="delSampleAttributeByMap">
    delete from USER_SAMPLE_ATTRIBUTE where USER_ID=#{userId} and SAMPLE_NO=#{sampleNo}
  </delete>


  <resultMap id="resultDTO" type="com.sgs.ecom.member.dto.user.UserSampleAttributeDTO" >
    <id column="ID" property="id" jdbcType="BIGINT" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="ATTR_NAME" property="attrName" jdbcType="VARCHAR" />
    <result column="ATTR_VALUE" property="attrValue" jdbcType="VARCHAR" />
    <result column="IS_DEFAULT" property="isDefault" jdbcType="TINYINT" />
    <result column="SAMPLE_NO" property="sampleNo" jdbcType="VARCHAR" />
    <result column="ATTR_EXTEND" property="attrExtend" jdbcType="VARCHAR" />
    <result column="ATTR_CODE" property="attrCode" jdbcType="VARCHAR" />
  </resultMap>

  <sql id="sqlListDTO" >
    ID, USER_ID, ATTR_NAME, ATTR_VALUE, IS_DEFAULT,SAMPLE_NO, ATTR_EXTEND, ATTR_CODE
  </sql>

  <!--自定义-->
  <select id="selectListByMap" resultMap="resultDTO" >
    select
    <include refid="sqlListDTO" />
    from USER_SAMPLE_ATTRIBUTE
    <include refid="baseQueryWhere"/>
  </select>

  <sql id="baseQueryWhere">
    where 1=1
    <if test="userId != null">
      AND USER_ID=#{userId}
    </if>
    <if test="isDefault != null">
      AND IS_DEFAULT=#{isDefault}
    </if>
    <if test="sampleNo != null">
      AND SAMPLE_NO=#{sampleNo}
    </if>
    <if test="sampleNoList != null ">
      AND SAMPLE_NO in
      <foreach collection="sampleNoList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </sql>




</mapper>