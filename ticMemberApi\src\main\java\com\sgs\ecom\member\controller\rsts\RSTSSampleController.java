package com.sgs.ecom.member.controller.rsts;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.ecom.member.request.base.ImpReq;
import com.sgs.ecom.member.request.detail.OrderSampleReq;
import com.sgs.ecom.member.service.rsts.interfaces.IRSTSSampleService;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.rsts/sample")
public class RSTSSampleController extends ControllerUtil {

    @Autowired
    private IRSTSSampleService irstsSampleService;
    /**
    *@Function: addSample
    *@Description 新增订单样品（）
    *@param: [token, orderSampleReq]
    *@author: Xiwei_Qiu @date: 2021/10/13 @version:
    **/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "addSample", method = { RequestMethod.POST })
    public ResultBody addSample(
            @RequestHeader(value="accessToken") String token,
            @RequestBody OrderSampleReq orderSampleReq) throws Exception {
        return ResultBody.newInstance(irstsSampleService.addSample(orderSampleReq,getUserDTO(token)));
    }
    /**
    *@Function: saveSample
    *@Description 修改订单样品
    *@param: [token, orderSampleReq]
    *@author: Xiwei_Qiu @date: 2021/10/22 @version:
    **/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "modSample", method = { RequestMethod.POST })
    public ResultBody saveSample(
            @RequestHeader(value="accessToken") String token,
            @RequestBody OrderSampleReq orderSampleReq
    ) throws Exception {
        return ResultBody.success(irstsSampleService.modSample(orderSampleReq,getUserDTO(token)));
    }
    /**
    *@Function: delSample
    *@Description 删除订单样品
    *@param: [token, orderSampleReq]
    *@author: Xiwei_Qiu @date: 2021/10/22 @version:
    **/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "delSample", method = { RequestMethod.POST })
    public ResultBody delSample(
            @RequestHeader(value="accessToken") String token,
            @RequestBody OrderSampleReq orderSampleReq
    ) throws Exception {
        irstsSampleService.delSample(orderSampleReq,getUserDTO(token));
        return ResultBody.success();
    }

    /**
    *@Function: impOrderSample
    *@Description 导入样品
    *@param: [impReq, token]
    *@author: Xiwei_Qiu @date: 2021/11/4 @version:
    **/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "impOrderSample", method = { RequestMethod.POST })
    public ResultBody impOrderSample(
            @RequestBody ImpReq impReq,
            @RequestHeader(value="accessToken") String token) throws Exception {
        return ResultBody.newInstance(irstsSampleService.impOrderSample(impReq,getUserDTO(token)));
    }


    /**
     * @Description :导入样品-rsts-new
     * <AUTHOR> Zhang
     * @Date  2023/10/13
     * @param impReq:
     * @param token:
     * @return: com.sgs.ecom.member.util.ResultBody
     **/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "impSampleNew", method = { RequestMethod.POST })
    public ResultBody impSampleNew(
            @RequestBody ImpReq impReq,
            @RequestHeader(value="accessToken") String token) throws Exception {
        return ResultBody.newInstance(irstsSampleService.impSampleNew(impReq,getUserDTO(token)));
    }

}
