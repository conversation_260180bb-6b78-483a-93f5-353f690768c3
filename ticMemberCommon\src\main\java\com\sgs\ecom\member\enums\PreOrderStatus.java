package com.sgs.ecom.member.enums;

public enum PreOrderStatus {

	NEW("受理中", "1"), CLOSE("已完成", "5"), CANCEL("已取消", "7"), 
	PENDING("暂缓处理", "6"), TESTING("测试中", "8"), REPORTING("测试中", "9"),
	CONFIRMED("受理中", "3"), COMPLETED("已完成", "10");
		
    // 成员变量  
    private String name;  
    private String index;  
    // 构造方法  
    private PreOrderStatus(String name, String index) {  
        this.name = name;  
        this.index = index;  
    }  

    // 普通方法  
    public static String getName(String index) {  
        for (PreOrderStatus c : PreOrderStatus.values()) {  
            if (c.getIndex().equals(index)) {  
                return c.name;  
            }  
        }  
        return null;  
    }  
    // get set 方法  
    public String getName() {  
        return name;  
    }  

    public String getIndex() {  
        return index;  
    }  
}
