<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.mapper.acct.AcctBalanceMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.entity.AcctBalance" >
    <id column="BALANCE_ID" property="balanceId" jdbcType="BIGINT" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="BALANCE_TYPE" property="balanceType" jdbcType="INTEGER" />
    <result column="STORE_ID" property="storeId" jdbcType="BIGINT" />
    <result column="SKU_ID" property="skuId" jdbcType="BIGINT" />
    <result column="AREA_ID" property="areaId" jdbcType="BIGINT" />
    <result column="BALANCE" property="balance" jdbcType="INTEGER" />
    <result column="EFF_DATE" property="effDate" jdbcType="DATE" />
    <result column="EXP_DATE" property="expDate" jdbcType="DATE" />
    <result column="FREEZE_AMOUNT" property="freezeAmount" jdbcType="INTEGER" />
    <result column="STATE" property="state" jdbcType="INTEGER" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
    <result column="USER_TYPE" property="userType" jdbcType="TINYINT" />
  </resultMap>
  <sql id="Base_Column_List" >
    BALANCE_ID, USER_ID, BALANCE_TYPE, STORE_ID, SKU_ID, AREA_ID, BALANCE, EFF_DATE, 
    EXP_DATE, FREEZE_AMOUNT, STATE, CREATE_DATE, STATE_DATE, USER_TYPE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from ACCT_BALANCE
    where BALANCE_ID = #{balanceId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from ACCT_BALANCE
    where BALANCE_ID = #{balanceId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.sgs.ecom.member.entity.AcctBalance" >
    insert into ACCT_BALANCE (BALANCE_ID, USER_ID, BALANCE_TYPE, 
      STORE_ID, SKU_ID, AREA_ID, 
      BALANCE, EFF_DATE, EXP_DATE, 
      FREEZE_AMOUNT, STATE, CREATE_DATE, 
      STATE_DATE, USER_TYPE)
    values (#{balanceId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{balanceType,jdbcType=INTEGER}, 
      #{storeId,jdbcType=BIGINT}, #{skuId,jdbcType=BIGINT}, #{areaId,jdbcType=BIGINT}, 
      #{balance,jdbcType=INTEGER}, #{effDate,jdbcType=DATE}, #{expDate,jdbcType=DATE}, 
      #{freezeAmount,jdbcType=INTEGER}, #{state,jdbcType=INTEGER}, #{createDate,jdbcType=TIMESTAMP}, 
      #{stateDate,jdbcType=TIMESTAMP}, #{userType,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" keyProperty="balanceId" parameterType="com.sgs.ecom.member.entity.AcctBalance" useGeneratedKeys="true">
    insert into ACCT_BALANCE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="balanceId != null" >
        BALANCE_ID,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
      <if test="balanceType != null" >
        BALANCE_TYPE,
      </if>
      <if test="storeId != null" >
        STORE_ID,
      </if>
      <if test="skuId != null" >
        SKU_ID,
      </if>
      <if test="areaId != null" >
        AREA_ID,
      </if>
      <if test="balance != null" >
        BALANCE,
      </if>
      <if test="effDate != null" >
        EFF_DATE,
      </if>
      <if test="expDate != null" >
        EXP_DATE,
      </if>
      <if test="freezeAmount != null" >
        FREEZE_AMOUNT,
      </if>
      <if test="state != null" >
        STATE,
      </if>
      <if test="createDate != null" >
        CREATE_DATE,
      </if>
      <if test="stateDate != null" >
        STATE_DATE,
      </if>
      <if test="userType != null" >
        USER_TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="balanceId != null" >
        #{balanceId,jdbcType=BIGINT},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="balanceType != null" >
        #{balanceType,jdbcType=INTEGER},
      </if>
      <if test="storeId != null" >
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="skuId != null" >
        #{skuId,jdbcType=BIGINT},
      </if>
      <if test="areaId != null" >
        #{areaId,jdbcType=BIGINT},
      </if>
      <if test="balance != null" >
        #{balance,jdbcType=INTEGER},
      </if>
      <if test="effDate != null" >
        #{effDate,jdbcType=DATE},
      </if>
      <if test="expDate != null" >
        #{expDate,jdbcType=DATE},
      </if>
      <if test="freezeAmount != null" >
        #{freezeAmount,jdbcType=INTEGER},
      </if>
      <if test="state != null" >
        #{state,jdbcType=INTEGER},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        #{stateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="userType != null" >
        #{userType,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.ecom.member.entity.AcctBalance" >
    update ACCT_BALANCE
    <set >
      <if test="userId != null" >
        USER_ID = #{userId,jdbcType=BIGINT},
      </if>
      <if test="balanceType != null" >
        BALANCE_TYPE = #{balanceType,jdbcType=INTEGER},
      </if>
      <if test="storeId != null" >
        STORE_ID = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="skuId != null" >
        SKU_ID = #{skuId,jdbcType=BIGINT},
      </if>
      <if test="areaId != null" >
        AREA_ID = #{areaId,jdbcType=BIGINT},
      </if>
      <if test="balance != null" >
        BALANCE = #{balance,jdbcType=INTEGER},
      </if>
      <if test="effDate != null" >
        EFF_DATE = #{effDate,jdbcType=DATE},
      </if>
      <if test="expDate != null" >
        EXP_DATE = #{expDate,jdbcType=DATE},
      </if>
      <if test="freezeAmount != null" >
        FREEZE_AMOUNT = #{freezeAmount,jdbcType=INTEGER},
      </if>
      <if test="state != null" >
        STATE = #{state,jdbcType=INTEGER},
      </if>
      <if test="createDate != null" >
        CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        STATE_DATE = #{stateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="userType != null" >
        USER_TYPE = #{userType,jdbcType=TINYINT},
      </if>
    </set>
    where BALANCE_ID = #{balanceId,jdbcType=BIGINT}
  </update>
  <update id="updateBalance" >
    update ACCT_BALANCE
    set BALANCE = BALANCE + #{balance,jdbcType=INTEGER}
    where BALANCE_ID = #{balanceId,jdbcType=BIGINT}
    and BALANCE = #{oldBalance,jdbcType=INTEGER}
  </update>


  <!--自定义 -->
  <resultMap id="resultDTO" type="com.sgs.ecom.member.dto.AcctBalanceDTO" >
    <id column="BALANCE_ID" property="balanceId" jdbcType="BIGINT" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="BALANCE_TYPE" property="balanceType" jdbcType="INTEGER" />
    <result column="STORE_ID" property="storeId" jdbcType="BIGINT" />
    <result column="SKU_ID" property="skuId" jdbcType="BIGINT" />
    <result column="AREA_ID" property="areaId" jdbcType="BIGINT" />
    <result column="BALANCE" property="balance" jdbcType="INTEGER" />
    <result column="EFF_DATE" property="effDate" jdbcType="DATE" />
    <result column="EXP_DATE" property="expDate" jdbcType="DATE" />
    <result column="FREEZE_AMOUNT" property="freezeAmount" jdbcType="INTEGER" />
    <result column="STATE" property="state" jdbcType="INTEGER" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
    <result column="USER_TYPE" property="userType" jdbcType="TINYINT" />
  </resultMap>

  <sql id="sqlListDTO" >
    BALANCE_ID, USER_ID, BALANCE_TYPE, STORE_ID, SKU_ID, AREA_ID, BALANCE, EFF_DATE,
    EXP_DATE, FREEZE_AMOUNT, STATE, CREATE_DATE, STATE_DATE, USER_TYPE
  </sql>
  
  <select id="qryEffBalance" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from ACCT_BALANCE
    <include refid="baseQueryWhere"/>
    and EXP_DATE > now()
    limit 1
  </select>

  <select id="selectListByMap" resultMap="resultDTO" >
    select
    <include refid="sqlListDTO" />
    from ACCT_BALANCE
    <include refid="baseQueryWhere"/>
  </select>
  <sql id="baseQueryWhere">
    <where>
      <if test="userId != null" >
        AND USER_ID=#{userId,jdbcType=BIGINT}
      </if>
      <if test="userType != null" >
        AND USER_TYPE= #{userType,jdbcType=INTEGER}
      </if>
      <if test="balanceType != null" >
        AND BALANCE_TYPE=#{balanceType,jdbcType=INTEGER}
      </if>
    </where>
  </sql>

</mapper>