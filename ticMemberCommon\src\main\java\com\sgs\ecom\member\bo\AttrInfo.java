package com.sgs.ecom.member.bo;

import lombok.Data;

/**
 * <AUTHOR>
 * @Description :认证信息
 * @date 2024/7/15
 */
@Data
public class AttrInfo {
    private String existingCertificateType;//已有证书类型   ※选中check用
    private String existingCertificateFileNames;//已有证书文件名
    private Integer numberEmployees;//工厂人数
    private String factoryNames;//生产工厂名
    private String documentsProvidedType;//填写及提供之文件类型    ※选中check用
    private String documentsProvidedFileNames;//填写及提供之文件名
    private String isShanghaiUploadProject;// 是否属于上海市环境监测社会化服务监管系统上传项目


    /**
     * 信用证号
     */
    private String creditNo;

    /**
     * 款号
     */
    private String number;

    /**
     * 目的地
     */
    private String maker;

    /**
     * 船期
     */
    private String sailSchedule;

    /**
     * 致命缺陷
     */
    private String criticalDefect;

    /**
     * 重缺陷
     */
    private String seriousDefect;

    /**
     * 轻度缺陷
     */
    private String mildDefect;

    /**
     * 申请表备注
     */
    private String formMemo;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 检验日期
     */
    private String checkDate;

    /**
     * 抽样数量
     */
    private String sampleNums;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 验货地址-工厂名称
     */
    private String factoryName;

    /**
     * 验货地址-联系人
     */
    private String contactPerson;

    /**
     * 验货地址-电话
     */
    private String phone;

    /**
     * 验货地址-省
     */
    private String province;

    /**
     * 验货地址-市
     */
    private String city;

    /**
     * 验货地址-详细地址
     */
    private String address;
}
