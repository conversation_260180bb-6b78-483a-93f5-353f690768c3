package com.sgs.ecom.member.dto.pay;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.dto.OrderBaseInfoCheckDTO;
import com.sgs.ecom.member.util.collection.StrUtil;

import java.math.BigDecimal;
import java.util.List;

public class PaymentDTO {

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BigDecimal realAmount;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String orderType;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private List<PaymentInfoDTO> paymentInfoDTOList;

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String payMethod;//支付方式

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int isSecond;//是否是线下第二次拉起支付

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String orderNo;

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String createDate;

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String currency;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String currencyMark;

    public PaymentDTO() {
    }

    public PaymentDTO(OrderBaseInfoCheckDTO orderBaseInfoCheckDTO) {
        this.realAmount = orderBaseInfoCheckDTO.getRealAmount();
        this.orderType = orderBaseInfoCheckDTO.getOrderType();
    }
    public PaymentDTO(OrderBasePayDTO orderBasePayDTO ,BigDecimal realAmount) {
        this.realAmount = realAmount;
        this.orderType = orderBasePayDTO.getOrderType();
        this.orderNo=orderBasePayDTO.getOrderNo();
        this.createDate= StrUtil.isTime(orderBasePayDTO.getCreateDate());
        this.currency=orderBasePayDTO.getCurrency();
        this.currencyMark=orderBasePayDTO.getCurrencyMark();
    }

    public List<PaymentInfoDTO> getPaymentInfoDTOList() {
        return paymentInfoDTOList;
    }

    public void setPaymentInfoDTOList(List<PaymentInfoDTO> paymentInfoDTOList) {
        this.paymentInfoDTOList = paymentInfoDTOList;
    }


    public String getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(String payMethod) {
        this.payMethod = payMethod;
    }

    public int getIsSecond() {
        return isSecond;
    }

    public void setIsSecond(int isSecond) {
        this.isSecond = isSecond;
    }

    public BigDecimal getRealAmount() {
        return realAmount;
    }

    public void setRealAmount(BigDecimal realAmount) {
        this.realAmount = realAmount;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getCurrencyMark() {
        return currencyMark;
    }

    public void setCurrencyMark(String currencyMark) {
        this.currencyMark = currencyMark;
    }
}
