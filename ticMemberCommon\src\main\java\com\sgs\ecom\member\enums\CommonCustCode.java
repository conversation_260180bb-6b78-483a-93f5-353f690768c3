package com.sgs.ecom.member.enums;

public enum CommonCustCode {

	RSTS_COMMON("RSTS_COMMON", "1302"),
    X1("X1", "1");
	
    // 成员变量  
    private String name;  
    private String index;  
    // 构造方法  
    private CommonCustCode(String name, String index) {  
        this.name = name;  
        this.index = index;  
    }  

    // 普通方法  
    public static String getName(String index) {  
        for (CommonCustCode c : CommonCustCode.values()) {  
            if (c.getIndex().equals(index)) {  
                return c.name;  
            }  
        }  
        return null;  
    }

    //是否需要校验发票 true 需要，false 表示是 虚拟或者X用户
    public static Boolean checkInvoice(String custCode){
       if(CommonCustCode.RSTS_COMMON.getName().equals(custCode)){
           return false;
       }
        if(CommonCustCode.X1.getName().equals(custCode)){
            return false;
        }
        return true;
    }

    // get set 方法  
    public String getName() {  
        return name;  
    }  

    public String getIndex() {  
        return index;  
    }   
}
