package com.sgs.ecom.member.dto.user;
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.json.TimeFormatSerializer;
import com.sgs.base.BaseQryFilter;

import java.sql.Timestamp;

public class SysMemberBenefitsDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select IS_BASIC,BENEFITS_TYPE,EFF_DAYS,CREATE_DATE,BU,USE_LIMIT,BENEFITS_CODE,STATE_DATE,STATE,DAY_LIMIT,ID,LEVEL_ID from SYS_MEMBER_BENEFITS"; 
 
 
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="IS_BASIC", getName="getIsBasic", setName="setIsBasic")
 	private int isBasic;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="BENEFITS_TYPE", getName="getBenefitsType", setName="setBenefitsType")
 	private int benefitsType;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="EFF_DAYS", getName="getEffDays", setName="setEffDays")
 	private int effDays;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="BU", getName="getBu", setName="setBu")
 	private String bu;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="USE_LIMIT", getName="getUseLimit", setName="setUseLimit")
 	private int useLimit;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="BENEFITS_CODE", getName="getBenefitsCode", setName="setBenefitsCode")
 	private String benefitsCode;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private Timestamp stateDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="DAY_LIMIT", getName="getDayLimit", setName="setDayLimit")
 	private int dayLimit;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="ID", getName="getId", setName="setId")
 	private long id;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="LEVEL_ID", getName="getLevelId", setName="setLevelId")
 	private int levelId;

 	public void setIsBasic(int isBasic){
 		 this.isBasic=isBasic;
 	}
 	public int getIsBasic(){
 		 return this.isBasic;
 	}
 
 	 
 	public void setBenefitsType(int benefitsType){
 		 this.benefitsType=benefitsType;
 	}
 	public int getBenefitsType(){
 		 return this.benefitsType;
 	}
 
 	 
 	public void setEffDays(int effDays){
 		 this.effDays=effDays;
 	}
 	public int getEffDays(){
 		 return this.effDays;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setBu(String bu){
 		 this.bu=bu;
 	}
 	public String getBu(){
 		 return this.bu;
 	}
 
 	 
 	public void setUseLimit(int useLimit){
 		 this.useLimit=useLimit;
 	}
 	public int getUseLimit(){
 		 return this.useLimit;
 	}
 
 	 
 	public void setBenefitsCode(String benefitsCode){
 		 this.benefitsCode=benefitsCode;
 	}
 	public String getBenefitsCode(){
 		 return this.benefitsCode;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setDayLimit(int dayLimit){
 		 this.dayLimit=dayLimit;
 	}
 	public int getDayLimit(){
 		 return this.dayLimit;
 	}
 
 	 
 	public void setId(long id){
 		 this.id=id;
 	}
 	public long getId(){
 		 return this.id;
 	}
 
 	 
 	public void setLevelId(int levelId){
 		 this.levelId=levelId;
 	}
 	public int getLevelId(){
 		 return this.levelId;
 	}
 
 	 
}