package com.sgs.ecom.member.dto.order;

import com.platform.util.ValidationUtil;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.enumtool.aplication.OrderComponentOperatorTypeEnum;

import java.util.Date;

/**
 * <AUTHOR> Zhang
 * @Description :
 * @date 2023/9/22
 */
public class OrderComponentDTO extends BaseOrderFilter {

    private Long componentId;

    private String orderNo;

    private Integer componentType;

    private String componentCode;

    private String componentName;

    private String componentNameEn;

    private Integer operatorType;
    private String operatorTypeShow;

    private Double minValue;

    private Double maxValue;
    private String value;

    private Date createDate;

    private Date stateDate;

    private int num;

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

    public String getValue() {
        if(ValidationUtil.isEmpty(operatorType))
            return "";

        if(OrderComponentOperatorTypeEnum.GREATER_AND_EQUAL.getIndex().equals(String.valueOf(operatorType))){
            return String.valueOf(minValue);
        }else if(OrderComponentOperatorTypeEnum.LESS_AND_EQUAL.getIndex().equals(String.valueOf(operatorType))){
            return String.valueOf(maxValue);
        }else if(OrderComponentOperatorTypeEnum.INTERVAL.getIndex().equals(String.valueOf(operatorType))){
            return String.valueOf(minValue)+"-"+String.valueOf(maxValue);
        }

        return String.valueOf(minValue);
    }

    public String getOperatorTypeShow() {
        if(ValidationUtil.isEmpty(operatorType))
            return "";


        return OrderComponentOperatorTypeEnum.getName(String.valueOf(operatorType));
    }

    public Long getComponentId() {
        return componentId;
    }

    public void setComponentId(Long componentId) {
        this.componentId = componentId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo == null ? null : orderNo.trim();
    }

    public Integer getComponentType() {
        return componentType;
    }

    public void setComponentType(Integer componentType) {
        this.componentType = componentType;
    }

    public String getComponentCode() {
        return componentCode;
    }

    public void setComponentCode(String componentCode) {
        this.componentCode = componentCode == null ? null : componentCode.trim();
    }

    public String getComponentName() {
        return componentName;
    }

    public void setComponentName(String componentName) {
        this.componentName = componentName == null ? null : componentName.trim();
    }

    public String getComponentNameEn() {
        return componentNameEn;
    }

    public void setComponentNameEn(String componentNameEn) {
        this.componentNameEn = componentNameEn == null ? null : componentNameEn.trim();
    }

    public Integer getOperatorType() {
        return operatorType;
    }

    public void setOperatorType(Integer operatorType) {
        this.operatorType = operatorType;
    }

    public Double getMinValue() {
        return minValue;
    }

    public void setMinValue(Double minValue) {
        this.minValue = minValue;
    }

    public Double getMaxValue() {
        return maxValue;
    }

    public void setMaxValue(Double maxValue) {
        this.maxValue = maxValue;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getStateDate() {
        return stateDate;
    }

    public void setStateDate(Date stateDate) {
        this.stateDate = stateDate;
    }
}
