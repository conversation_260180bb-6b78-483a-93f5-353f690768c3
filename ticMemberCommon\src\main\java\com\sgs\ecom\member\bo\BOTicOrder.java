package com.sgs.ecom.member.bo; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import com.platform.annotation.CharacterVaild; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import java.util.Date; 
import com.platform.util.json.DateFormatSerializer; 
import com.platform.util.json.ScienceFormatSerializer; 
import com.platform.annotation.BeanAnno; 

public class BOTicOrder{
 
 	public static final String SEQUENCE = "ID"; 
  
 	public static final String BO_SQL = "TIC_Order"; 
 
 	public static final String OWNER ="bbc";

 	public static final String PARENTID="parentid";
 	public static final String ABSTRACT_CODE="abstractCode";
 	public static final String ORIGINALPRICE="originalprice";
 	public static final String STATUSCHECKAPPCREATTIME="statuscheckappcreattime";
 	public static final String PRODUCTNAME="productname";
 	public static final String ENNAME="enname";
 	public static final String ORDERCOMMENTS="ordercomments";
 	public static final String CONFIRMDATE="confirmdate";
 	public static final String INSPECTIONINFO="inspectioninfo";
 	public static final String COMPANYNAMECN="companynamecn";
 	public static final String MODIFIEDBY="modifiedby";
 	public static final String CHANGEPRICECOMMENTS="changepricecomments";
 	public static final String STATUSSERVICE="statusservice";
 	public static final String STATUSFINISH="statusfinish";
 	public static final String STATUSCANCELAPPCREATTIME="statuscancelappcreattime";
 	public static final String CHANGEPRICEDATE="changepricedate";
 	public static final String PAYDATE="paydate";
 	public static final String ABSTRACT_PAYMENT_DOCUMENT="abstractPaymentDocument";
 	public static final String ID="id";
 	public static final String STATUSREFUNDCREATTIME="statusrefundcreattime";
 	public static final String HAVEREAD="haveread";
 	public static final String PAYMENTSTATUS="paymentstatus";
 	public static final String STATUS="status";
 	public static final String STATUSPAYMENTDOCUMENT="statuspaymentdocument";
 	public static final String CHANGESTATUS="changestatus";
 	public static final String CREATEDBY="createdby";
 	public static final String STORENAME="storename";
 	public static final String RECOVERSTATUS="recoverstatus";
 	public static final String PAYAMOUNT="payamount";
 	public static final String COMFIRMAPPDATE="comfirmappdate";
 	public static final String STATUSRETURNCREATTIME="statusreturncreattime";
 	public static final String ABSTRACT_WAIT_PAYMENT_CREATTIME="abstractWaitPaymentCreattime";
 	public static final String STATUSWAITPAYMENT="statuswaitpayment";
 	public static final String FAPIAOINFO="fapiaoinfo";
 	public static final String CITY="city";
 	public static final String PROVINCE="province";
 	public static final String SUBTITLE="subtitle";
 	public static final String READSTATUS="readstatus";
 	public static final String ABSTRACT_PAYMENT_CREATTIME="abstractPaymentCreattime";
 	public static final String CANCELREASON="cancelreason";
 	public static final String CANCELDATE="canceldate";
 	public static final String CREATEDDATE="createddate";
 	public static final String STATUSREFUND="statusrefund";
 	public static final String ABSTRACT_PAYMENT_DOCUMENT_CREATTIME="abstractPaymentDocumentCreattime";
 	public static final String OFFLINESTATUS="offlinestatus";
 	public static final String DISTRICT="district";
 	public static final String CONFIRMAPPBY="confirmappby";
 	public static final String CANCELFLG="cancelflg";
 	public static final String SMSSEND="smssend";
 	public static final String OPERATOR="operator";
 	public static final String MAILSEND="mailsend";
 	public static final String ORDERFINISHTIME="orderfinishtime";
 	public static final String ABSTRACT_WAIT_PAYMENT="abstractWaitPayment";
 	public static final String COMPANYNAMEEN="companynameen";
 	public static final String ORDERNO="orderno";
 	public static final String MODIFIEDDATE="modifieddate";
 	public static final String REPORT_SEND_CC="reportSendCc";
 	public static final String EXPRESSINFO="expressinfo";
 	public static final String COMPANYADDRESS="companyaddress";
 	public static final String STATUSCHECKAPP="statuscheckapp";
 	public static final String IS_TICKET="isTicket";
 	public static final String CONTACTTEL="contacttel";
 	public static final String STATUSFINISHAPPCREATTIME="statusfinishappcreattime";
 	public static final String TESTING_STANDARD="testingStandard";
 	public static final String USERID="userid";
 	public static final String CLOSESTATUS="closestatus";
 	public static final String PAYACCOUNT="payaccount";
 	public static final String UPLOADREPORTDATE="uploadreportdate";
 	public static final String CONFIRMPAYMENTDATE="confirmpaymentdate";
 	public static final String STATUSSUBMITAPPCREATTIME="statussubmitappcreattime";
 	public static final String USERNAME="username";
 	public static final String SERVICE_ITEMS="serviceItems";
 	public static final String ORDERSTARTTIME="orderstarttime";
 	public static final String STATUSPAYMENTDOCUMENTCREATTIME="statuspaymentdocumentcreattime";
 	public static final String STATUSSERVICEAPPCREATTIME="statusserviceappcreattime";
 	public static final String IS_BILL="isBill";
 	public static final String STATUSCANCEL="statuscancel";
 	public static final String ABSTRACT_STATUS="abstractStatus";
 	public static final String ABSTRACT_CUSTCODE="abstractCustcode";
 	public static final String ABSTRACT_PAYMENT="abstractPayment";
 	public static final String IS_MONTH_PAY="isMonthPay";
 	public static final String STATUSSUBMITAPP="statussubmitapp";
 	public static final String LEADS_CODE="leadsCode";
 	public static final String STOREID="storeid";
 	public static final String TOTALPRICE="totalprice";
 	public static final String CONTACTEMAIL="contactemail";
 	public static final String CATEGORYNAME="categoryname";
 	public static final String CLOSEPAYORDER="closepayorder";
 	public static final String FILEAUDITPICINFO="fileauditpicinfo";
 	public static final String STATUSRETURN="statusreturn";
 	public static final String CONTACTNAME="contactname";
 	public static final String STATUSWAITPAYMENTCREATTIME="statuswaitpaymentcreattime";

 	@BeanAnno("ParentID")
 	private String parentid;
 	@BeanAnno("Abstract_Code")
 	private String abstractCode;
 	@BeanAnno("OriginalPrice")
 	private double originalprice;
 	@BeanAnno("StatusCheckAppCreatTime")
 	private Timestamp statuscheckappcreattime;
 	@BeanAnno("ProductName")
 	private String productname;
 	@BeanAnno("EnName")
 	private String enname;
 	@BeanAnno("OrderComments")
 	private String ordercomments;
 	@BeanAnno("ConfirmDate")
 	private Timestamp confirmdate;
 	@BeanAnno("InspectionInfo")
 	private String inspectioninfo;
 	@BeanAnno("CompanyNameCN")
 	private String companynamecn;
 	@BeanAnno("ModifiedBy")
 	private String modifiedby;
 	@BeanAnno("ChangePriceComments")
 	private String changepricecomments;
 	@BeanAnno("StatusService")
 	private String statusservice;
 	@BeanAnno("StatusFinish")
 	private String statusfinish;
 	@BeanAnno("StatusCancelAppCreatTime")
 	private Timestamp statuscancelappcreattime;
 	@BeanAnno("ChangePriceDate")
 	private Timestamp changepricedate;
 	@BeanAnno("PayDate")
 	private Timestamp paydate;
 	@BeanAnno("Abstract_Payment_Document")
 	private String abstractPaymentDocument;
 	@BeanAnno("ID")
 	private String id;
 	@BeanAnno("StatusRefundCreatTime")
 	private Timestamp statusrefundcreattime;
 	@BeanAnno("HaveRead")
 	private long haveread;
 	@BeanAnno("PaymentStatus")
 	private String paymentstatus;
 	@BeanAnno("Status")
 	private String status;
 	@BeanAnno("StatusPaymentDocument")
 	private String statuspaymentdocument;
 	@BeanAnno("ChangeStatus")
 	private String changestatus;
 	@BeanAnno("CreatedBy")
 	private String createdby;
 	@BeanAnno("StoreName")
 	private String storename;
 	@BeanAnno("RecoverStatus")
 	private String recoverstatus;
 	@BeanAnno("PayAmount")
 	private double payamount;
 	@BeanAnno("ComfirmAppDate")
 	private Timestamp comfirmappdate;
 	@BeanAnno("StatusReturnCreatTime")
 	private Timestamp statusreturncreattime;
 	@BeanAnno("Abstract_Wait_Payment_Creattime")
 	private Timestamp abstractWaitPaymentCreattime;
 	@BeanAnno("StatusWaitPayment")
 	private String statuswaitpayment;
 	@BeanAnno("FapiaoInfo")
 	private String fapiaoinfo;
 	@BeanAnno("City")
 	private String city;
 	@BeanAnno("Province")
 	private String province;
 	@BeanAnno("Subtitle")
 	private String subtitle;
 	@BeanAnno("ReadStatus")
 	private String readstatus;
 	@BeanAnno("Abstract_Payment_Creattime")
 	private Timestamp abstractPaymentCreattime;
 	@BeanAnno("CancelReason")
 	private String cancelreason;
 	@BeanAnno("CancelDate")
 	private Timestamp canceldate;
 	@BeanAnno("CreatedDate")
 	private Timestamp createddate;
 	@BeanAnno("StatusRefund")
 	private String statusrefund;
 	@BeanAnno("Abstract_Payment_Document_Creattime")
 	private Timestamp abstractPaymentDocumentCreattime;
 	@BeanAnno("OfflineStatus")
 	private String offlinestatus;
 	@BeanAnno("District")
 	private String district;
 	@BeanAnno("ConfirmAppBy")
 	private String confirmappby;
 	@BeanAnno("CancelFlg")
 	private String cancelflg;
 	@BeanAnno("SmsSend")
 	private long smssend;
 	@BeanAnno("Operator")
 	private String operator;
 	@BeanAnno("MailSend")
 	private long mailsend;
 	@BeanAnno("OrderFinishTime")
 	private Date orderfinishtime;
 	@BeanAnno("Abstract_Wait_Payment")
 	private String abstractWaitPayment;
 	@BeanAnno("CompanyNameEN")
 	private String companynameen;
 	@BeanAnno("OrderNo")
 	private String orderno;
 	@BeanAnno("ModifiedDate")
 	private Timestamp modifieddate;
 	@BeanAnno("report_send_cc")
 	private String reportSendCc;
 	@BeanAnno("ExpressInfo")
 	private String expressinfo;
 	@BeanAnno("CompanyAddress")
 	private String companyaddress;
 	@BeanAnno("StatusCheckApp")
 	private String statuscheckapp;
 	@BeanAnno("is_ticket")
 	private int isTicket;
 	@BeanAnno("ContactTel")
 	private String contacttel;
 	@BeanAnno("StatusFinishAppCreatTime")
 	private Timestamp statusfinishappcreattime;
 	@BeanAnno("Testing_Standard")
 	private String testingStandard;
 	@BeanAnno("UserID")
 	private String userid;
 	@BeanAnno("CloseStatus")
 	private String closestatus;
 	@BeanAnno("PayAccount")
 	private String payaccount;
 	@BeanAnno("UploadReportDate")
 	private Timestamp uploadreportdate;
 	@BeanAnno("ConfirmPaymentDate")
 	private Timestamp confirmpaymentdate;
 	@BeanAnno("StatusSubmitAppCreatTime")
 	private Timestamp statussubmitappcreattime;
 	@BeanAnno("UserName")
 	private String username;
 	@BeanAnno("Service_Items")
 	private String serviceItems;
 	@BeanAnno("OrderStartTime")
 	private String orderstarttime;
 	@BeanAnno("StatusPaymentDocumentCreatTime")
 	private Timestamp statuspaymentdocumentcreattime;
 	@BeanAnno("StatusServiceAppCreatTime")
 	private Timestamp statusserviceappcreattime;
 	@BeanAnno("is_bill")
 	private int isBill;
 	@BeanAnno("StatusCancel")
 	private String statuscancel;
 	@BeanAnno("Abstract_Status")
 	private String abstractStatus;
 	@BeanAnno("Abstract_CustCode")
 	private String abstractCustcode;
 	@BeanAnno("Abstract_Payment")
 	private String abstractPayment;
 	@BeanAnno("is_month_pay")
 	private int isMonthPay;
 	@BeanAnno("StatusSubmitApp")
 	private String statussubmitapp;
 	@BeanAnno("Leads_Code")
 	private String leadsCode;
 	@BeanAnno("StoreID")
 	private String storeid;
 	@BeanAnno("TotalPrice")
 	private double totalprice;
 	@BeanAnno("ContactEmail")
 	private String contactemail;
 	@BeanAnno("CategoryName")
 	private String categoryname;
 	@BeanAnno("ClosePayOrder")
 	private String closepayorder;
 	@BeanAnno("FileAuditPicInfo")
 	private String fileauditpicinfo;
 	@BeanAnno("StatusReturn")
 	private String statusreturn;
 	@BeanAnno("ContactName")
 	private String contactname;
 	@BeanAnno("StatusWaitPaymentCreatTime")
 	private Timestamp statuswaitpaymentcreattime;

 	@CharacterVaild(len = 50) 
 	public void setParentid(String parentid){
 		 this.parentid=parentid;
 	}
 	public String getParentid(){
 		 return this.parentid;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setAbstractCode(String abstractCode){
 		 this.abstractCode=abstractCode;
 	}
 	public String getAbstractCode(){
 		 return this.abstractCode;
 	}
 
 	 
 	public void setOriginalprice(double originalprice){
 		 this.originalprice=originalprice;
 	}
 	@JsonSerialize(using = ScienceFormatSerializer.class) 
 	public double getOriginalprice(){
 		 return this.originalprice;
 	}
 
 	 
 	public void setStatuscheckappcreattime(Timestamp statuscheckappcreattime){
 		 this.statuscheckappcreattime=statuscheckappcreattime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStatuscheckappcreattime(){
 		 return this.statuscheckappcreattime;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setProductname(String productname){
 		 this.productname=productname;
 	}
 	public String getProductname(){
 		 return this.productname;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setEnname(String enname){
 		 this.enname=enname;
 	}
 	public String getEnname(){
 		 return this.enname;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setOrdercomments(String ordercomments){
 		 this.ordercomments=ordercomments;
 	}
 	public String getOrdercomments(){
 		 return this.ordercomments;
 	}
 
 	 
 	public void setConfirmdate(Timestamp confirmdate){
 		 this.confirmdate=confirmdate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getConfirmdate(){
 		 return this.confirmdate;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setInspectioninfo(String inspectioninfo){
 		 this.inspectioninfo=inspectioninfo;
 	}
 	public String getInspectioninfo(){
 		 return this.inspectioninfo;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setCompanynamecn(String companynamecn){
 		 this.companynamecn=companynamecn;
 	}
 	public String getCompanynamecn(){
 		 return this.companynamecn;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setModifiedby(String modifiedby){
 		 this.modifiedby=modifiedby;
 	}
 	public String getModifiedby(){
 		 return this.modifiedby;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setChangepricecomments(String changepricecomments){
 		 this.changepricecomments=changepricecomments;
 	}
 	public String getChangepricecomments(){
 		 return this.changepricecomments;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setStatusservice(String statusservice){
 		 this.statusservice=statusservice;
 	}
 	public String getStatusservice(){
 		 return this.statusservice;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setStatusfinish(String statusfinish){
 		 this.statusfinish=statusfinish;
 	}
 	public String getStatusfinish(){
 		 return this.statusfinish;
 	}
 
 	 
 	public void setStatuscancelappcreattime(Timestamp statuscancelappcreattime){
 		 this.statuscancelappcreattime=statuscancelappcreattime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStatuscancelappcreattime(){
 		 return this.statuscancelappcreattime;
 	}
 
 	 
 	public void setChangepricedate(Timestamp changepricedate){
 		 this.changepricedate=changepricedate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getChangepricedate(){
 		 return this.changepricedate;
 	}
 
 	 
 	public void setPaydate(Timestamp paydate){
 		 this.paydate=paydate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getPaydate(){
 		 return this.paydate;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setAbstractPaymentDocument(String abstractPaymentDocument){
 		 this.abstractPaymentDocument=abstractPaymentDocument;
 	}
 	public String getAbstractPaymentDocument(){
 		 return this.abstractPaymentDocument;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setId(String id){
 		 this.id=id;
 	}
 	public String getId(){
 		 return this.id;
 	}
 
 	 
 	public void setStatusrefundcreattime(Timestamp statusrefundcreattime){
 		 this.statusrefundcreattime=statusrefundcreattime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStatusrefundcreattime(){
 		 return this.statusrefundcreattime;
 	}
 
 	 
 	public void setHaveread(long haveread){
 		 this.haveread=haveread;
 	}
 	public long getHaveread(){
 		 return this.haveread;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setPaymentstatus(String paymentstatus){
 		 this.paymentstatus=paymentstatus;
 	}
 	public String getPaymentstatus(){
 		 return this.paymentstatus;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setStatus(String status){
 		 this.status=status;
 	}
 	public String getStatus(){
 		 return this.status;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setStatuspaymentdocument(String statuspaymentdocument){
 		 this.statuspaymentdocument=statuspaymentdocument;
 	}
 	public String getStatuspaymentdocument(){
 		 return this.statuspaymentdocument;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setChangestatus(String changestatus){
 		 this.changestatus=changestatus;
 	}
 	public String getChangestatus(){
 		 return this.changestatus;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setCreatedby(String createdby){
 		 this.createdby=createdby;
 	}
 	public String getCreatedby(){
 		 return this.createdby;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setStorename(String storename){
 		 this.storename=storename;
 	}
 	public String getStorename(){
 		 return this.storename;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setRecoverstatus(String recoverstatus){
 		 this.recoverstatus=recoverstatus;
 	}
 	public String getRecoverstatus(){
 		 return this.recoverstatus;
 	}
 
 	 
 	public void setPayamount(double payamount){
 		 this.payamount=payamount;
 	}
 	@JsonSerialize(using = ScienceFormatSerializer.class) 
 	public double getPayamount(){
 		 return this.payamount;
 	}
 
 	 
 	public void setComfirmappdate(Timestamp comfirmappdate){
 		 this.comfirmappdate=comfirmappdate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getComfirmappdate(){
 		 return this.comfirmappdate;
 	}
 
 	 
 	public void setStatusreturncreattime(Timestamp statusreturncreattime){
 		 this.statusreturncreattime=statusreturncreattime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStatusreturncreattime(){
 		 return this.statusreturncreattime;
 	}
 
 	 
 	public void setAbstractWaitPaymentCreattime(Timestamp abstractWaitPaymentCreattime){
 		 this.abstractWaitPaymentCreattime=abstractWaitPaymentCreattime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getAbstractWaitPaymentCreattime(){
 		 return this.abstractWaitPaymentCreattime;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setStatuswaitpayment(String statuswaitpayment){
 		 this.statuswaitpayment=statuswaitpayment;
 	}
 	public String getStatuswaitpayment(){
 		 return this.statuswaitpayment;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setFapiaoinfo(String fapiaoinfo){
 		 this.fapiaoinfo=fapiaoinfo;
 	}
 	public String getFapiaoinfo(){
 		 return this.fapiaoinfo;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setCity(String city){
 		 this.city=city;
 	}
 	public String getCity(){
 		 return this.city;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setProvince(String province){
 		 this.province=province;
 	}
 	public String getProvince(){
 		 return this.province;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setSubtitle(String subtitle){
 		 this.subtitle=subtitle;
 	}
 	public String getSubtitle(){
 		 return this.subtitle;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setReadstatus(String readstatus){
 		 this.readstatus=readstatus;
 	}
 	public String getReadstatus(){
 		 return this.readstatus;
 	}
 
 	 
 	public void setAbstractPaymentCreattime(Timestamp abstractPaymentCreattime){
 		 this.abstractPaymentCreattime=abstractPaymentCreattime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getAbstractPaymentCreattime(){
 		 return this.abstractPaymentCreattime;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setCancelreason(String cancelreason){
 		 this.cancelreason=cancelreason;
 	}
 	public String getCancelreason(){
 		 return this.cancelreason;
 	}
 
 	 
 	public void setCanceldate(Timestamp canceldate){
 		 this.canceldate=canceldate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCanceldate(){
 		 return this.canceldate;
 	}
 
 	 
 	public void setCreateddate(Timestamp createddate){
 		 this.createddate=createddate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateddate(){
 		 return this.createddate;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setStatusrefund(String statusrefund){
 		 this.statusrefund=statusrefund;
 	}
 	public String getStatusrefund(){
 		 return this.statusrefund;
 	}
 
 	 
 	public void setAbstractPaymentDocumentCreattime(Timestamp abstractPaymentDocumentCreattime){
 		 this.abstractPaymentDocumentCreattime=abstractPaymentDocumentCreattime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getAbstractPaymentDocumentCreattime(){
 		 return this.abstractPaymentDocumentCreattime;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setOfflinestatus(String offlinestatus){
 		 this.offlinestatus=offlinestatus;
 	}
 	public String getOfflinestatus(){
 		 return this.offlinestatus;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setDistrict(String district){
 		 this.district=district;
 	}
 	public String getDistrict(){
 		 return this.district;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setConfirmappby(String confirmappby){
 		 this.confirmappby=confirmappby;
 	}
 	public String getConfirmappby(){
 		 return this.confirmappby;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setCancelflg(String cancelflg){
 		 this.cancelflg=cancelflg;
 	}
 	public String getCancelflg(){
 		 return this.cancelflg;
 	}
 
 	 
 	public void setSmssend(long smssend){
 		 this.smssend=smssend;
 	}
 	public long getSmssend(){
 		 return this.smssend;
 	}
 
 	 
 	@CharacterVaild(len = 20) 
 	public void setOperator(String operator){
 		 this.operator=operator;
 	}
 	public String getOperator(){
 		 return this.operator;
 	}
 
 	 
 	public void setMailsend(long mailsend){
 		 this.mailsend=mailsend;
 	}
 	public long getMailsend(){
 		 return this.mailsend;
 	}
 
 	 
 	public void setOrderfinishtime(Date orderfinishtime){
 		 this.orderfinishtime=orderfinishtime;
 	}
 	@JsonSerialize(using = DateFormatSerializer.class) 
 	public Date getOrderfinishtime(){
 		 return this.orderfinishtime;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setAbstractWaitPayment(String abstractWaitPayment){
 		 this.abstractWaitPayment=abstractWaitPayment;
 	}
 	public String getAbstractWaitPayment(){
 		 return this.abstractWaitPayment;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setCompanynameen(String companynameen){
 		 this.companynameen=companynameen;
 	}
 	public String getCompanynameen(){
 		 return this.companynameen;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setOrderno(String orderno){
 		 this.orderno=orderno;
 	}
 	public String getOrderno(){
 		 return this.orderno;
 	}
 
 	 
 	public void setModifieddate(Timestamp modifieddate){
 		 this.modifieddate=modifieddate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getModifieddate(){
 		 return this.modifieddate;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setReportSendCc(String reportSendCc){
 		 this.reportSendCc=reportSendCc;
 	}
 	public String getReportSendCc(){
 		 return this.reportSendCc;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setExpressinfo(String expressinfo){
 		 this.expressinfo=expressinfo;
 	}
 	public String getExpressinfo(){
 		 return this.expressinfo;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setCompanyaddress(String companyaddress){
 		 this.companyaddress=companyaddress;
 	}
 	public String getCompanyaddress(){
 		 return this.companyaddress;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setStatuscheckapp(String statuscheckapp){
 		 this.statuscheckapp=statuscheckapp;
 	}
 	public String getStatuscheckapp(){
 		 return this.statuscheckapp;
 	}
 
 	 
 	public void setIsTicket(int isTicket){
 		 this.isTicket=isTicket;
 	}
 	public int getIsTicket(){
 		 return this.isTicket;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setContacttel(String contacttel){
 		 this.contacttel=contacttel;
 	}
 	public String getContacttel(){
 		 return this.contacttel;
 	}
 
 	 
 	public void setStatusfinishappcreattime(Timestamp statusfinishappcreattime){
 		 this.statusfinishappcreattime=statusfinishappcreattime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStatusfinishappcreattime(){
 		 return this.statusfinishappcreattime;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setTestingStandard(String testingStandard){
 		 this.testingStandard=testingStandard;
 	}
 	public String getTestingStandard(){
 		 return this.testingStandard;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setUserid(String userid){
 		 this.userid=userid;
 	}
 	public String getUserid(){
 		 return this.userid;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setClosestatus(String closestatus){
 		 this.closestatus=closestatus;
 	}
 	public String getClosestatus(){
 		 return this.closestatus;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setPayaccount(String payaccount){
 		 this.payaccount=payaccount;
 	}
 	public String getPayaccount(){
 		 return this.payaccount;
 	}
 
 	 
 	public void setUploadreportdate(Timestamp uploadreportdate){
 		 this.uploadreportdate=uploadreportdate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getUploadreportdate(){
 		 return this.uploadreportdate;
 	}
 
 	 
 	public void setConfirmpaymentdate(Timestamp confirmpaymentdate){
 		 this.confirmpaymentdate=confirmpaymentdate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getConfirmpaymentdate(){
 		 return this.confirmpaymentdate;
 	}
 
 	 
 	public void setStatussubmitappcreattime(Timestamp statussubmitappcreattime){
 		 this.statussubmitappcreattime=statussubmitappcreattime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStatussubmitappcreattime(){
 		 return this.statussubmitappcreattime;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setUsername(String username){
 		 this.username=username;
 	}
 	public String getUsername(){
 		 return this.username;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setServiceItems(String serviceItems){
 		 this.serviceItems=serviceItems;
 	}
 	public String getServiceItems(){
 		 return this.serviceItems;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setOrderstarttime(String orderstarttime){
 		 this.orderstarttime=orderstarttime;
 	}
 	public String getOrderstarttime(){
 		 return this.orderstarttime;
 	}
 
 	 
 	public void setStatuspaymentdocumentcreattime(Timestamp statuspaymentdocumentcreattime){
 		 this.statuspaymentdocumentcreattime=statuspaymentdocumentcreattime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStatuspaymentdocumentcreattime(){
 		 return this.statuspaymentdocumentcreattime;
 	}
 
 	 
 	public void setStatusserviceappcreattime(Timestamp statusserviceappcreattime){
 		 this.statusserviceappcreattime=statusserviceappcreattime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStatusserviceappcreattime(){
 		 return this.statusserviceappcreattime;
 	}
 
 	 
 	public void setIsBill(int isBill){
 		 this.isBill=isBill;
 	}
 	public int getIsBill(){
 		 return this.isBill;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setStatuscancel(String statuscancel){
 		 this.statuscancel=statuscancel;
 	}
 	public String getStatuscancel(){
 		 return this.statuscancel;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setAbstractStatus(String abstractStatus){
 		 this.abstractStatus=abstractStatus;
 	}
 	public String getAbstractStatus(){
 		 return this.abstractStatus;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setAbstractCustcode(String abstractCustcode){
 		 this.abstractCustcode=abstractCustcode;
 	}
 	public String getAbstractCustcode(){
 		 return this.abstractCustcode;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setAbstractPayment(String abstractPayment){
 		 this.abstractPayment=abstractPayment;
 	}
 	public String getAbstractPayment(){
 		 return this.abstractPayment;
 	}
 
 	 
 	public void setIsMonthPay(int isMonthPay){
 		 this.isMonthPay=isMonthPay;
 	}
 	public int getIsMonthPay(){
 		 return this.isMonthPay;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setStatussubmitapp(String statussubmitapp){
 		 this.statussubmitapp=statussubmitapp;
 	}
 	public String getStatussubmitapp(){
 		 return this.statussubmitapp;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setLeadsCode(String leadsCode){
 		 this.leadsCode=leadsCode;
 	}
 	public String getLeadsCode(){
 		 return this.leadsCode;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setStoreid(String storeid){
 		 this.storeid=storeid;
 	}
 	public String getStoreid(){
 		 return this.storeid;
 	}
 
 	 
 	public void setTotalprice(double totalprice){
 		 this.totalprice=totalprice;
 	}
 	@JsonSerialize(using = ScienceFormatSerializer.class) 
 	public double getTotalprice(){
 		 return this.totalprice;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setContactemail(String contactemail){
 		 this.contactemail=contactemail;
 	}
 	public String getContactemail(){
 		 return this.contactemail;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setCategoryname(String categoryname){
 		 this.categoryname=categoryname;
 	}
 	public String getCategoryname(){
 		 return this.categoryname;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setClosepayorder(String closepayorder){
 		 this.closepayorder=closepayorder;
 	}
 	public String getClosepayorder(){
 		 return this.closepayorder;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setFileauditpicinfo(String fileauditpicinfo){
 		 this.fileauditpicinfo=fileauditpicinfo;
 	}
 	public String getFileauditpicinfo(){
 		 return this.fileauditpicinfo;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setStatusreturn(String statusreturn){
 		 this.statusreturn=statusreturn;
 	}
 	public String getStatusreturn(){
 		 return this.statusreturn;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setContactname(String contactname){
 		 this.contactname=contactname;
 	}
 	public String getContactname(){
 		 return this.contactname;
 	}
 
 	 
 	public void setStatuswaitpaymentcreattime(Timestamp statuswaitpaymentcreattime){
 		 this.statuswaitpaymentcreattime=statuswaitpaymentcreattime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStatuswaitpaymentcreattime(){
 		 return this.statuswaitpaymentcreattime;
 	}
 
 	 
}