package com.sgs.ecom.member.entity.user;

import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.sgs.base.BaseBean;

import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @Description : 用户样品库基础表-rsts
 * @date 2023/10/12
 */
public class UserSample {
    private Long sampleId;
    private Long userId;
    private String sampleName;
    private String sampleNameEn;
    private String productInfo;
    private String productInfoEn;
    private String materialGrade;
    private String materialGradeEn;
    private String productBatch;
    private String productBatchEn;
    private String supplierName;
    private String supplierNameEn;
    private String lotNo;
    private String lotNoEn;
    private String producer;
    private String producerEn;
    private String buyersName;
    private String buyersNameEn;
    private String remark;
    private String remarkEn;
    private Integer state;
    private String createDate;
    private String stateDate;
    private String sampleNo;
    private String sampleSign;

    public Long getSampleId() {
        return sampleId;
    }

    public void setSampleId(Long sampleId) {
        this.sampleId = sampleId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getSampleName() {
        return sampleName;
    }

    public void setSampleName(String sampleName) {
        this.sampleName = sampleName;
    }

    public String getSampleNameEn() {
        return sampleNameEn;
    }

    public void setSampleNameEn(String sampleNameEn) {
        this.sampleNameEn = sampleNameEn;
    }

    public String getProductInfo() {
        return productInfo;
    }

    public void setProductInfo(String productInfo) {
        this.productInfo = productInfo;
    }

    public String getProductInfoEn() {
        return productInfoEn;
    }

    public void setProductInfoEn(String productInfoEn) {
        this.productInfoEn = productInfoEn;
    }

    public String getMaterialGrade() {
        return materialGrade;
    }

    public void setMaterialGrade(String materialGrade) {
        this.materialGrade = materialGrade;
    }

    public String getMaterialGradeEn() {
        return materialGradeEn;
    }

    public void setMaterialGradeEn(String materialGradeEn) {
        this.materialGradeEn = materialGradeEn;
    }

    public String getProductBatch() {
        return productBatch;
    }

    public void setProductBatch(String productBatch) {
        this.productBatch = productBatch;
    }

    public String getProductBatchEn() {
        return productBatchEn;
    }

    public void setProductBatchEn(String productBatchEn) {
        this.productBatchEn = productBatchEn;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getSupplierNameEn() {
        return supplierNameEn;
    }

    public void setSupplierNameEn(String supplierNameEn) {
        this.supplierNameEn = supplierNameEn;
    }

    public String getLotNo() {
        return lotNo;
    }

    public void setLotNo(String lotNo) {
        this.lotNo = lotNo;
    }

    public String getLotNoEn() {
        return lotNoEn;
    }

    public void setLotNoEn(String lotNoEn) {
        this.lotNoEn = lotNoEn;
    }

    public String getProducer() {
        return producer;
    }

    public void setProducer(String producer) {
        this.producer = producer;
    }

    public String getProducerEn() {
        return producerEn;
    }

    public void setProducerEn(String producerEn) {
        this.producerEn = producerEn;
    }

    public String getBuyersName() {
        return buyersName;
    }

    public void setBuyersName(String buyersName) {
        this.buyersName = buyersName;
    }

    public String getBuyersNameEn() {
        return buyersNameEn;
    }

    public void setBuyersNameEn(String buyersNameEn) {
        this.buyersNameEn = buyersNameEn;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getRemarkEn() {
        return remarkEn;
    }

    public void setRemarkEn(String remarkEn) {
        this.remarkEn = remarkEn;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getStateDate() {
        return stateDate;
    }

    public void setStateDate(String stateDate) {
        this.stateDate = stateDate;
    }

    public String getSampleNo() {
        return sampleNo;
    }

    public void setSampleNo(String sampleNo) {
        this.sampleNo = sampleNo;
    }

    public String getSampleSign() {
        return sampleSign;
    }

    public void setSampleSign(String sampleSign) {
        this.sampleSign = sampleSign;
    }
}
