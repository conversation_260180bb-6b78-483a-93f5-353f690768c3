package com.sgs.ecom.member.dto.center;

import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.dto.application.OrderApplicationAttrDTO;

import java.util.List;

public class SysApplicationBasicDTO extends BaseOrderFilter {
 

 	@ApiAnno(groups={Default.class,OrderApplicationOther.class})
	@BeanAnno(dtocls={SysApplicationAttrDTO.class})
 	private String areaCode;

// 	@ApiAnno(groups={Default.class,OrderApplicationOther.class})
// 	@BeanAnno(dtocls={SysApplicationAttrDTO.class})
 	private List<SysApplicationAttrDTO> attrs;


 	
 	public void setAreaCode(String areaCode){
 		 this.areaCode=areaCode;
 	}
 	public String getAreaCode(){
 		 return this.areaCode;
 	}
 
	public List<SysApplicationAttrDTO> getAttrs() {
		return attrs;
	}
	public void setAttrs(List<SysApplicationAttrDTO> attrs) {
		this.attrs = attrs;
	}


}