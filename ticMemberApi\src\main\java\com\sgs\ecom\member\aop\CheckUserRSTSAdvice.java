package com.sgs.ecom.member.aop;

import com.sgs.ecom.member.util.ControllerUtil;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Aspect
@Component
public class CheckUserRSTSAdvice extends ControllerUtil {

    @Pointcut("@annotation(com.sgs.ecom.member.annotation.CheckUserRSTS)")
    @Order(10)
    public void checkUserRSTS() {

    }
}
