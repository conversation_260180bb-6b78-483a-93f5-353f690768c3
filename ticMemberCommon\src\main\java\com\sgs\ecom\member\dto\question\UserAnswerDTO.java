package com.sgs.ecom.member.dto.question;

import java.util.List;

public class UserAnswerDTO {

    public static final String REPLY_ID="replyId";
    public static final String QUESTION_ID="questionId";
    public static final String PART_NO="partNo";
    public static final String PART_NO_LIST="partNoList";
    public static final String STATE="state";
    public static final String SUBJECT_NO="subjectNo";
    public static final String OPTION_NO="optionNo";
    public static final String RELATE_PART_NO="relatePartNo";
    private String questionId;
    private String partNo;
    private String position;
    private String subjectNo;
    private String relatePartNo;
    private String questionType;
    private String mdCode;

    private String questionTitle;
    private List<UserAnswerOptonDTO> userAnswerOptonDTOList;


    public String getQuestionId() {
        return questionId;
    }

    public void setQuestionId(String questionId) {
        this.questionId = questionId;
    }

    public String getPartNo() {
        return partNo;
    }

    public void setPartNo(String partNo) {
        this.partNo = partNo;
    }

    public String getSubjectNo() {
        return subjectNo;
    }

    public void setSubjectNo(String subjectNo) {
        this.subjectNo = subjectNo;
    }

    public List<UserAnswerOptonDTO> getUserAnswerOptonDTOList() {
        return userAnswerOptonDTOList;
    }

    public void setUserAnswerOptonDTOList(List<UserAnswerOptonDTO> userAnswerOptonDTOList) {
        this.userAnswerOptonDTOList = userAnswerOptonDTOList;
    }

    public String getRelatePartNo() {
        return relatePartNo;
    }

    public void setRelatePartNo(String relatePartNo) {
        this.relatePartNo = relatePartNo;
    }

    public String getQuestionTitle() {
        return questionTitle;
    }

    public void setQuestionTitle(String questionTitle) {
        this.questionTitle = questionTitle;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getMdCode() {
        return mdCode;
    }

    public void setMdCode(String mdCode) {
        this.mdCode = mdCode;
    }

    public String getQuestionType() {
        return questionType;
    }

    public void setQuestionType(String questionType) {
        this.questionType = questionType;
    }
}
