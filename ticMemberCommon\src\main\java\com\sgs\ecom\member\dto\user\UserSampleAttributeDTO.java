package com.sgs.ecom.member.dto.user;

import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.sgs.base.BaseQryFilter; 

public class UserSampleAttributeDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select ATTR_VALUE,CREATE_DATE,ATTR_NAME,ATTR_EXTEND,USER_ID,SAMPLE_NO,ID,IS_DEFAULT from USER_SAMPLE_ATTRIBUTE"; 
 
 
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="ATTR_VALUE", getName="getAttrValue", setName="setAttrValue")
 	private String attrValue;

 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="ATTR_NAME", getName="getAttrName", setName="setAttrName")
 	private String attrName;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="ATTR_EXTEND", getName="getAttrExtend", setName="setAttrExtend")
 	private String attrExtend;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="USER_ID", getName="getUserId", setName="setUserId")
 	private Long userId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="SAMPLE_NO", getName="getSampleNo", setName="setSampleNo")
 	private String sampleNo;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="ID", getName="getId", setName="setId")
 	private Long id;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="IS_DEFAULT", getName="getIsDefault", setName="setIsDefault")
 	private int isDefault;
	private String attrCode;


 	public void setAttrValue(String attrValue){
 		 this.attrValue=attrValue;
 	}
 	public String getAttrValue(){
 		 return this.attrValue;
 	}



	public void setAttrName(String attrName){
 		 this.attrName=attrName;
 	}
 	public String getAttrName(){
 		 return this.attrName;
 	}
 
 	 
 	public void setAttrExtend(String attrExtend){
 		 this.attrExtend=attrExtend;
 	}
 	public String getAttrExtend(){
 		 return this.attrExtend;
 	}

 	 
 	public void setSampleNo(String sampleNo){
 		 this.sampleNo=sampleNo;
 	}
 	public String getSampleNo(){
 		 return this.sampleNo;
 	}


	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public void setIsDefault(int isDefault){
 		 this.isDefault=isDefault;
 	}
 	public int getIsDefault(){
 		 return this.isDefault;
 	}

	public String getAttrCode() {
		return attrCode;
	}

	public void setAttrCode(String attrCode) {
		this.attrCode = attrCode;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}
}