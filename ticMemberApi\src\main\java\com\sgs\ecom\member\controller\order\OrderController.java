package com.sgs.ecom.member.controller.order;


import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.base.BaseBean;
import com.sgs.ecom.member.domain.order.service.interfaces.IOrderBaseInfoDomainService;
import com.sgs.ecom.member.dto.OrderBaseInfoMoreDTO;
import com.sgs.ecom.member.dto.user.UserDTO;
import com.sgs.ecom.member.enums.OrderPrintEnum;
import com.sgs.ecom.member.enumtool.OrderTypeEnum;
import com.sgs.ecom.member.enumtool.event.EventEnum;
import com.sgs.ecom.member.enumtool.order.OrderPDFEnum;
import com.sgs.ecom.member.event.EventApiUtil;
import com.sgs.ecom.member.request.*;
import com.sgs.ecom.member.request.order.OrderHeadReq;
import com.sgs.ecom.member.service.order.interfaces.IOrderBaseInfoCustomService;
import com.sgs.ecom.member.service.order.interfaces.IOrderBaseInfoService;
import com.sgs.ecom.member.infrastructure.fegin.sso.rpc.SSORpcService;
import com.sgs.ecom.member.service.util.interfaces.IEurekaService;
import com.sgs.ecom.member.service.util.interfaces.IOrderOperatorService;
import com.sgs.ecom.member.service.util.interfaces.IOrderRpcService;
import com.sgs.ecom.member.service.util.interfaces.IOrderService;
import com.sgs.ecom.member.service.util.interfaces.IPDFService;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.order/order")
public class OrderController extends ControllerUtil {
	@Autowired
	private IOrderService orderService;
	@Autowired
	private IOrderOperatorService orderOperatorService;
	@Autowired
	private IOrderBaseInfoService orderBaseInfoService;
	@Autowired
	private IOrderBaseInfoCustomService orderBaseInfoCustomService;
	@Autowired
	private IPDFService pdfService;
	@Autowired
	private SSORpcService ssoRpcService;
	@Autowired
	private EventApiUtil eventApiUtil;
	@Autowired
	private IEurekaService eurekaService;
	@Autowired
	private IOrderBaseInfoDomainService orderBaseInfoDomainService;
	@Autowired
	private IOrderRpcService orderRpcService;
	/**
	 * @Function: qryList
	 * @Description 查询订单列表 
	 * @param: [orderBaseInfoReq, token]
	 * @author: Xiwei_Qiu @date: 2021/11/15 @version:
	 **/
	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "qryList", method = {RequestMethod.POST})
	public ResultBody qryList(
		@RequestBody OrderBaseInfoReq orderBaseInfoReq,
		@RequestHeader(value = "accessToken") String token) throws Exception {
		return ResultBody.newInstance(orderService.getPageList(orderBaseInfoReq, getUserDTO(token)));
	}

	/**
	 * @Function: qryHeadList
	 * @Description 查询首页头部数据 非登陆
	 * @param: [basePage]
	 * @author: Xiwei_Qiu @date: 2021/11/15 @version:
	 **/
	@AuthRequired(login = "NULL", sign = true)
	@RequestMapping(value = "qryHeadList", method = {RequestMethod.POST})
	public ResultBody qryHeadList(
		@RequestHeader(value = "bu",required = false) String bu,
		@RequestBody OrderHeadReq orderHeadReq) throws Exception {
		return ResultBody.newInstance(orderService.qryHeadList(orderHeadReq,bu));
	}

	/**
	 * @Function: updateDetail
	 * @Description 查询详情
	 * @param: [token, orderNoReq]
	 * @author: Xiwei_Qiu @date: 2021/11/15 @version:
	 **/
	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "qryDetail", method = {RequestMethod.POST})
	public ResultBody updateDetail(
		@RequestHeader(value = "accessToken") String token,
		@RequestHeader(value = "frontUrl",required = false) String frontUrl,
		@Validated(BaseBean.Query.class)
		@RequestBody OrderNoReq orderNoReq) throws Exception {
		return ResultBody.newInstance(orderService.qryDetail(orderNoReq.getOrderNo(),getUserDTO(token),orderNoReq.getCustId(),frontUrl));
	}


	//根据询价单查询订单数据
	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "qryOrder", method = {RequestMethod.POST})
	public ResultBody qryOrder(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody OrderNoReq orderNoReq
	) throws Exception {
		orderBaseInfoService.checkOrderBase(orderNoReq.getOrderNo(), getUserDTO(token), OrderTypeEnum.OIQINQUIRY.getIndex());
		return ResultBody.success(orderBaseInfoCustomService.selectOrderNoByRelateOrderNo(orderNoReq.getOrderNo(),false));
	}

	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "qryDetailPartQuestion", method = {RequestMethod.POST})
	public ResultBody qryDetailPartQuestion(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody OrderBaseInfoReq orderBaseInfoReq
	) throws Exception {
		return ResultBody.newInstance(orderService.qryDetailPartQuestion(orderBaseInfoReq, getUserDTO(token)));
	}

	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "qryConfig", method = {RequestMethod.POST})
	public ResultBody qryConfig(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody OrderBaseInfoReq orderBaseInfoReq) throws Exception {
		return ResultBody.newInstance(orderService.qryConfig(orderBaseInfoReq.getOrderNo(), getUserDTO(token)));
	}


	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "saveOrder", method = {RequestMethod.POST})
	public ResultBody saveOrder(
		@RequestHeader(value = "accessToken") String token,
		@RequestHeader(value = "operatorSource", defaultValue = "") String operatorSource,
		@Validated(BaseBean.Insert.class)
		@RequestBody OrderBaseAddReq orderBaseAddReq) throws Exception {
		orderBaseAddReq.setOperatorSource(operatorSource);
		Map map=orderOperatorService.saveOrder(orderBaseAddReq, getUserDTO(token));
		ssoRpcService.refresh(new JSONObject().toString());
		if(orderBaseAddReq.getSaveOrderFlg()==1){
			eventApiUtil.saveEvent(orderBaseAddReq.getEventOrderNo(), EventEnum.CREATE_ORDER_TO_LEADS);
			eventApiUtil.saveEvent(orderBaseAddReq.getEventOrderNo(), EventEnum.CREATE_ORDER_ADD_LABEL);
		}
		return ResultBody.newInstance(map);
	}


	/**
	 * @Description: 取消订单
	 * @Author: bowen zhang
	 * @Date: 2022/9/9
	 * @param token:
	 * @param orderOperationReq:
	 * @return: com.sgs.ecom.member.util.ResultBody
	 **/
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "operation", method = {RequestMethod.POST})
	public ResultBody operation(
		@RequestHeader(value = "accessToken") String token,
		@Validated(BaseBean.Insert.class)
		@RequestBody OrderOperationReq orderOperationReq) throws Exception {
		orderOperatorService.updateOrderOperation(orderOperationReq, getUserDTO(token));
		if(orderOperationReq.getModLabelFlg()==1){
			eventApiUtil.saveEvent(orderOperationReq.getEventOrderNo(), EventEnum.MOD_USER_LABEL);
		}
		return ResultBody.success();
	}

	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "confirmOrder", method = {RequestMethod.POST})
	public ResultBody confirmOrder(
		@RequestHeader(value = "accessToken") String token,
		@RequestHeader(value = "operatorSource") String operatorSource,
		@RequestBody ConfirmOrderReq confirmOrderReq) throws Exception {
		UserDTO userDTO = getUserDTO(token);
		confirmOrderReq.setOperatorSource(operatorSource);
		orderOperatorService.updateConfirmOrder(confirmOrderReq, userDTO);
		String orderNo=orderOperatorService.copyOrder(confirmOrderReq.getOrderNo(), userDTO);

		eventApiUtil.saveEvent(orderNo, EventEnum.MOD_USER_LABEL);

		return ResultBody.newInstance(orderNo);
	}


	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "sendWaitMail", method = {RequestMethod.POST})
	public ResultBody sendWaitMail(
			@RequestHeader(value = "accessToken") String token,
			@RequestBody OrderNoReq orderNoReq) throws Exception {

		orderOperatorService.sendWaitMail(orderNoReq, getUserDTO(token));
		return ResultBody.success();
	}

	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "copyOrder", method = {RequestMethod.POST})
	public ResultBody copyOrder(
		@RequestHeader(value = "accessToken") String token,
		@Validated(BaseBean.Insert.class)
		@RequestBody OrderNoReq orderNoReq) throws Exception {
		return ResultBody.newInstance(orderOperatorService.copyOrder(orderNoReq.getOrderNo(), getUserDTO(token)));
	}


	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "qryOrderReportPDF", method = {RequestMethod.POST})
	public void qryOrderReportPDF(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody OrderBaseInfoReq orderBaseInfoReq,
		HttpServletResponse httpServletResponse) throws Exception {
		pdfService.qryOrderPDF(orderBaseInfoReq.getOrderNo(), getUserDTO(token), OrderPDFEnum.WEB, httpServletResponse,0);
	}

	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "qryOrderReportPDFDel", method = {RequestMethod.POST})
	public void qryOrderReportPDFDel(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody OrderBaseInfoReq orderBaseInfoReq,
		HttpServletResponse httpServletResponse) throws Exception {
		pdfService.qryOrderPDF(orderBaseInfoReq.getOrderNo(), getUserDTO(token), OrderPDFEnum.WEB, httpServletResponse,0);
	}


	@RequestMapping(value = "qryOrderReportPDFTest", method = {RequestMethod.GET})
	public void qryOrderReportPDFTest(
		@RequestParam(value = "orderNo") String orderNo,
		HttpServletResponse httpServletResponse) throws Exception {

		OrderBaseInfoMoreDTO orderBaseInfoMoreDTO=orderBaseInfoService.selectDTOByOrderNo(orderNo);
		eurekaService.getOrderPDF(orderBaseInfoMoreDTO.getOrderNo(),orderBaseInfoMoreDTO.getGroupNo(),0,httpServletResponse);
		//pdfService.qryOrderPDF(orderNo, new UserDTO(12459L), OrderPDFEnum.WEB, httpServletResponse,true);
	}





	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "index", method = {RequestMethod.POST})
	public ResultBody index(
		@RequestHeader(value = "accessToken") String token
	) throws Exception {
		return ResultBody.newInstance(orderService.selectIndex(getUserDTO(token)));
	}


	/**
	 * @Function: getStateNum
	 * @Description 获取订单各状态的数据
	 * @author: Xiwei_Qiu
	 * @date: 2020/12/3
	 **/
	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "getStateNum", method = {RequestMethod.POST})
	public ResultBody getStateNum(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody OrderBaseInfoReq orderBaseInfoReq) throws Exception {
		return ResultBody.newInstance(orderService.getStateNum(orderBaseInfoReq, getUserDTO(token)));
	}


	/**
	 * @Function: getTotalNum
	 * @Description 获取订单总数
	 * @param: [accessToken]
	 * @author: Xiwei_Qiu
	 * @date: 2020/12/1
	 **/
	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "getTotalNum", method = {RequestMethod.POST})
	public ResultBody getTotalNum(
		@RequestHeader(value = "accessToken") String accessToken) throws Exception {
		return ResultBody.newInstance(orderService.getTotalNum(getUserDTO(accessToken)));
	}


	/**
	 * @Function: getLab
	 * @Description 获取当前用户所有可能出现的实验室
	 * @param: [accessToken]
	 * @author: Xiwei_Qiu @date: 2021/9/13 @version:
	 **/
	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "getLab", method = {RequestMethod.POST})
	public ResultBody getLab(
		@RequestHeader(value = "accessToken") String accessToken) throws Exception {
		return ResultBody.newInstance(orderBaseInfoCustomService.selectLab(getUserDTO(accessToken), OrderTypeEnum.OIQORDER.getIndex()));
	}

	/**
	 * @Function: getLab
	 * @Description 获取当前用户所有可能出现的店铺
	 * @param: [accessToken]
	 * @author: Xiwei_Qiu @date: 2021/9/13 @version:
	 **/
	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "getStore", method = {RequestMethod.POST})
	public ResultBody getStore(
		@RequestHeader(value = "accessToken") String accessToken) throws Exception {
		return ResultBody.newInstance(orderService.getStore(getUserDTO(accessToken)));
	}

	/**
	 * @Description: 客户端用户导出商城订单数据
	 * @Author: bowen zhang
	 * @Date: 2022/7/14
	 * @param token:
	 * @param orderBaseInfoReq:
	 * @param res:
	 * @return: void
	 **/
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "expOrderList", method = {RequestMethod.POST})
	public void expOrderList(
			@RequestHeader(value = "accessToken") String token,
			@RequestBody OrderBaseInfoReq orderBaseInfoReq,
			HttpServletResponse res
	) throws Exception {
		orderService.expOrderList(orderBaseInfoReq, getUserDTO(token), res);
	}

	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "qryQuotationData", method = {RequestMethod.POST})
	public ResultBody qryQuotationData(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody OrderNoReq orderNoReq) throws Exception {
		return ResultBody.newInstance(orderService.qryQuotationData(orderNoReq.getOrderNo(),getUserDTO(token)));
	}

	/**
	 * @Description : rsts-sds 独立页面查询订单是否存在
	 * <AUTHOR> Zhang
	 * @Date  2023/9/22
	 * @param orderNoReq:
	 * @return: com.sgs.ecom.member.util.ResultBody
	 **/
	@HystrixCommand
	@AuthRequired(login = "NULL", sign = true)
	@RequestMapping(value = "qryOrderBaseInfo", method = {RequestMethod.POST})
	public ResultBody qryOrderBaseInfo(
			@RequestBody OrderNoReq orderNoReq) throws Exception {
		return ResultBody.newInstance(orderBaseInfoDomainService.checkMsdsOrderNo(orderNoReq.getOrderNo()));
	}

	/**
	 * @Description :通过订单号组装申请表/报价单数据
	 * <AUTHOR> Zhang
	 * @Date  2024/7/4
	 * @param orderNoReq:
	 * @return: com.sgs.ecom.member.util.ResultBody
	 **/
	@HystrixCommand
//	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "qryFormInfo", method = {RequestMethod.POST})
	public ResultBody qryFormInfo(
			@RequestHeader(value = "accessToken") String token,
			@Validated(BaseBean.Query.class)
			@RequestBody OrderNoReq orderNoReq) throws Exception {
		OrderPrintReq orderPrintReq = new OrderPrintReq();
		orderPrintReq.setType(OrderPrintEnum.FORM.getIndex());
		return ResultBody.newInstance(orderService.qryFormInfo(orderNoReq.getOrderNo(),orderPrintReq, null, false));
	}

	/**
	 * @Function: qrySubOrderByOrder
	 * @Description: 查询订单子单信息
	 *
	 * @param: orderNo
	 * @return: ResultBody
	 *
	 * @version: 1.0
	 * @author: shenyi
	 * @date: 2024-07-05
	 *
	 * Modification History:
	 * Date         Author          Version            Description
	 *---------------------------------------------------------*
	 * 修改时间      修改人            版本                 修改原因
	 * 2024-07-05  shenyi           v1.0                 新增
	 */
	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "qrySub/{orderNo}", method = { RequestMethod.POST })
	public ResultBody qrySubOrderByOrder(@PathVariable String orderNo,
										 @RequestHeader(value="accessToken") String token) throws Exception {
		return ResultBody.newInstance(orderService.qrySubOrderByOrder(orderNo, getUserDTO(token)));
	}

	/**
	 * @Description :获取申请表文件列表
	 * <AUTHOR>
	 * @Date  2024/11/18
	 * @param orderNoReq:
	 * @return: ResultBody
	 **/
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "generateOrderPdfBySamples", method = { RequestMethod.POST })
	public ResultBody generateOrderPdfBySamples (
			@Validated(value = BaseBean.Query.class)
			@RequestBody OrderPrintReq orderNoReq,
			HttpServletResponse res) throws Exception {
		return ResultBody.newInstance(orderRpcService.generateOrderPdfBySamples(orderNoReq, res));
	}

	/**
	 * @Description :下载申请表压缩包形式
	 * <AUTHOR>
	 * @Date  2024/11/18
	 * @param orderNoReq:
	 * @return: void
	 **/
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "downLoadZipBySamples", method = { RequestMethod.POST })
	public void downLoadZipBySamples (
			@Validated(value = BaseBean.Query.class)
			@RequestBody OrderPrintReq orderNoReq,
			HttpServletResponse res) throws Exception {
		orderRpcService.downloadFormZipFileStream(orderNoReq,res);
	}
}
