package com.sgs.ecom.member.enumtool.order;

public enum OrderOperationEnum {
    CLOSE("close", 0, "取消订单"),
    NOSEE("noSee", 1, "客户删除订单至订单回收站"),
    OPEN("open", 2, "用户从回收站还原订单"),
    ;

    OrderOperationEnum(String name, Integer index, String nameCh) {
        this.name = name;
        this.index = index;
        this.nameCh = nameCh;
    }



    public static OrderOperationEnum getEnum(int index) {

        for (OrderOperationEnum c : OrderOperationEnum.values()) {
            if (c.getIndex()==index) {
                return c;
            }
        }
        return null;
    }



    private String name;
    private int index;
    private String nameCh;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }
}
