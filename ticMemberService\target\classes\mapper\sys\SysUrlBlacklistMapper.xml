<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.mapper.sys.SysUrlBlacklistMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.entity.sys.SysUrlBlacklist" >
    <id column="ID" property="id" jdbcType="BIGINT" />
    <result column="TYPE" property="type" jdbcType="VARCHAR" />
    <result column="CONTENT" property="content" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="INTEGER" />
  </resultMap>

  <resultMap id="BaseResultMapDTO" type="com.sgs.ecom.member.dto.sys.SysUrlBlacklistDTO" >
    <id column="ID" property="id" jdbcType="BIGINT" />
    <result column="TYPE" property="type" jdbcType="VARCHAR" />
    <result column="CONTENT" property="content" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, TYPE, CONTENT, STATE
  </sql>

  <sql id="Base_Qry_List" >
    where  1=1
    AND   STATE  = 1
    <if test="type != null">
      AND  TYPE = #{type}
    </if>

  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from sys_url_blacklist
    where ID = #{id,jdbcType=BIGINT}
  </select>
  <select id="qryList" resultType="com.sgs.ecom.member.dto.sys.SysUrlBlacklistDTO">
    select
    <include refid="Base_Column_List" />
    from sys_url_blacklist
    <include refid="Base_Qry_List" />



  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from sys_url_blacklist
    where ID = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.sgs.ecom.member.entity.sys.SysUrlBlacklist" >
    insert into sys_url_blacklist (ID, TYPE, CONTENT, 
      STATE)
    values (#{id,jdbcType=BIGINT}, #{type,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR}, 
      #{state,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.ecom.member.entity.sys.SysUrlBlacklist" >
    insert into sys_url_blacklist
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="type != null" >
        TYPE,
      </if>
      <if test="content != null" >
        CONTENT,
      </if>
      <if test="state != null" >
        STATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="type != null" >
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="content != null" >
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        #{state,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.ecom.member.entity.sys.SysUrlBlacklist" >
    update sys_url_blacklist
    <set >
      <if test="type != null" >
        TYPE = #{type,jdbcType=VARCHAR},
      </if>
      <if test="content != null" >
        CONTENT = #{content,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        STATE = #{state,jdbcType=INTEGER},
      </if>
    </set>
    where ID = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.ecom.member.entity.sys.SysUrlBlacklist" >
    update sys_url_blacklist
    set TYPE = #{type,jdbcType=VARCHAR},
      CONTENT = #{content,jdbcType=VARCHAR},
      STATE = #{state,jdbcType=INTEGER}
    where ID = #{id,jdbcType=BIGINT}
  </update>
</mapper>