package com.sgs.ecom.member.enums;

public enum OrderPrintEnum {

	FORM("申请表", "form"),
    QUOTATION("报价单", "quotation");

    // 成员变量
    private String name;
    private String index;
    // 构造方法
    private OrderPrintEnum(String name, String index) {
        this.name = name;  
        this.index = index;  
    }  

    // 普通方法  
    public static String getName(String index) {  
        for (OrderPrintEnum c : OrderPrintEnum.values()) {
            if (c.getIndex().equals(index)) {  
                return c.name;  
            }  
        }  
        return null;  
    }  
    // get set 方法  
    public String getName() {  
        return name;  
    }  

    public String getIndex() {  
        return index;  
    }  
}
