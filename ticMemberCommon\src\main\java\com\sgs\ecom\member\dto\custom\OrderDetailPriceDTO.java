package com.sgs.ecom.member.dto.custom;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.util.NumUtil;
import com.sgs.ecom.member.util.serializer.PriceNumFormatSerializer;

import java.math.BigDecimal;

public class OrderDetailPriceDTO {

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BigDecimal discountAmount;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BigDecimal orderAmount;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BigDecimal serviceAmount;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BigDecimal urgentAmount;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BigDecimal realAmount;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BigDecimal amount;

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BigDecimal discountAmountNum;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BigDecimal newDiscountAmountNum;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BigDecimal newDiscountAmountNumSub;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BigDecimal urgentAmountNum;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int flg=1;

    @JsonSerialize(using = PriceNumFormatSerializer.class)
    public BigDecimal getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount;
    }
    @JsonSerialize(using = PriceNumFormatSerializer.class)
    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }
    @JsonSerialize(using = PriceNumFormatSerializer.class)
    public BigDecimal getServiceAmount() {
        return serviceAmount;
    }

    public void setServiceAmount(BigDecimal serviceAmount) {
        this.serviceAmount = serviceAmount;
    }
    @JsonSerialize(using = PriceNumFormatSerializer.class)
    public BigDecimal getUrgentAmount() {
        return urgentAmount;
    }

    public void setUrgentAmount(BigDecimal urgentAmount) {
        this.urgentAmount = urgentAmount;
    }
    @JsonSerialize(using = PriceNumFormatSerializer.class)
    public BigDecimal getRealAmount() {
        return realAmount;
    }

    public void setRealAmount(BigDecimal realAmount) {
        this.realAmount = realAmount;
    }



    public BigDecimal getDiscountAmountNum() {
        return discountAmountNum;
    }

    public void setDiscountAmountNum(BigDecimal discountAmountNum) {
        this.discountAmountNum = discountAmountNum;
    }

    public BigDecimal getUrgentAmountNum() {
        return urgentAmountNum;
    }

    public void setUrgentAmountNum(BigDecimal urgentAmountNum) {
        this.urgentAmountNum = urgentAmountNum;
    }

    public int getFlg() {
        return flg;
    }

    public void setFlg(int flg) {
        this.flg = flg;
    }
    @JsonSerialize(using = PriceNumFormatSerializer.class)
    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getNewDiscountAmountNum() {
        return newDiscountAmountNum;
    }

    public void setNewDiscountAmountNum(BigDecimal newDiscountAmountNum) {
        this.newDiscountAmountNum = newDiscountAmountNum;
    }

    public BigDecimal getNewDiscountAmountNumSub() {
        return new BigDecimal(100).subtract(NumUtil.toZero(newDiscountAmountNum));
    }

    public void setNewDiscountAmountNumSub(BigDecimal newDiscountAmountNumSub) {
        this.newDiscountAmountNumSub = newDiscountAmountNumSub;
    }
}
