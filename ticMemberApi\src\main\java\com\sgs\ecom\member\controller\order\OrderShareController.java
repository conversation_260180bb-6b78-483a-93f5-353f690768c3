package com.sgs.ecom.member.controller.order;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.ecom.member.request.ConfirmOrderReq;
import com.sgs.ecom.member.request.OrderNoShareReq;
import com.sgs.ecom.member.service.order.interfaces.IOrderShareService;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.order/order/share")
public class OrderShareController extends ControllerUtil {

	@Autowired
	private IOrderShareService orderShareService;


	/**
	 * @Function: getShareUrl
	 * @Description
	 * @param: [orderNo, httpServletResponse]
	 * @author: Xiwei_Qiu @date: 2021/6/3 @version:
	 **/
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "getShare", method = {RequestMethod.POST})
	public ResultBody createShare(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody ConfirmOrderReq confirmOrderReq) throws Exception {
		return ResultBody.newInstance(orderShareService.createShare(confirmOrderReq, getUserDTO(token)));
	}



	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "updateShare", method = {RequestMethod.POST})
	public ResultBody updateShare(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody OrderNoShareReq orderNoShareReq) throws Exception {
		return ResultBody.newInstance(orderShareService.updateShare(orderNoShareReq, getUserDTO(token)));
	}

	@RequestMapping(value = "getShareDetail", method = {RequestMethod.POST})
	public ResultBody getShareDetail(
		@RequestBody OrderNoShareReq orderNoShareReq) throws Exception {
		return ResultBody.newInstance(orderShareService.getShareDetail(orderNoShareReq));
	}


	@RequestMapping(value = "getOrderShareDetail", method = {RequestMethod.POST})
	public ResultBody getOrderShareDetail(
			@RequestBody OrderNoShareReq orderNoShareReq) throws Exception {
		return ResultBody.newInstance(orderShareService.getOrderShareDetail(orderNoShareReq));
	}


	@RequestMapping(value = "getOrderShareInfo", method = {RequestMethod.POST})
	public ResultBody getOrderShareInfo(
			@RequestBody OrderNoShareReq orderNoShareReq) throws Exception {
		return ResultBody.newInstance(orderShareService.getOrderShareInfo(orderNoShareReq));
	}


}
