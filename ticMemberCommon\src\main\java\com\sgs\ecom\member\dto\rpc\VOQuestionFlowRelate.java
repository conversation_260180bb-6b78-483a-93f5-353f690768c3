package com.sgs.ecom.member.dto.rpc;

import com.platform.annotation.ExplainAnno;
import com.sgs.base.BaseBean;
import java.util.List;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@ExplainAnno(value="bean",name="问卷流程关系")
@Data
public class VOQuestionFlowRelate extends BaseBean {

	/**
	 * 章节id
	 */
	@NotNull(message = "章节ID不可为空", groups = {Update.class})
	private Long partId;

	/**
	 * 章节编码
	 */
	@NotNull(message = "章节编码不可为空", groups = {Insert.class, Update.class, Delete.class})
	private String partNo;
	/**
	 * 章节名称
	 */
	@NotNull(message = "章节名称不可为空", groups = {Insert.class, Update.class})
	private String partName;
	/**
	 * 所处位置
	 */
	private String position;
	/**
	 * 所属业务
	 */
	@Size(max = 20, message = "所属业务参数长度超出范围", groups = {Insert.class,
		Update.class})
	private String businessType;
	/**
	 * 业务编码
	 */
	@NotNull(message = "业务编码不可为空", groups = {Query.class})
	@Size(max = 20, message = "业务编码参数长度超出范围", groups = {Insert.class,
		Update.class, Delete.class})
	private String businessCode;
	/**
	 * 是否移动端展示
	 */
	private Integer isSimplifiedMode = 1;

	/**
	 * 章节下问题列表
	 */
	private List<QuestionSubjectDTO> subjects;

	/**
	 * 章节下关系列表
	 */
	private List<QuestionRelateDTO> relates;

	/**
	 * 回复编号
	 */
	private String replyNo;
 	 
}