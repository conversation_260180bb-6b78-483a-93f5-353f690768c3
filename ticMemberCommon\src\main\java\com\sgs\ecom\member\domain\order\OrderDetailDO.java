package com.sgs.ecom.member.domain.order;

import com.platform.util.ValidationUtil;
import com.sgs.base.BaseCopyObj;
import com.sgs.ecom.member.dto.bbc.BbcSkuAttrDTO;
import com.sgs.ecom.member.dto.detail.ItemCategoryDTO;
import com.sgs.ecom.member.dto.oiq.OiqOrderReqDTO;
import com.sgs.ecom.member.dto.oiq.applicationInfo.OiqItemDTO;
import com.sgs.ecom.member.entity.order.OrderDetail;
import com.sgs.ecom.member.enumtool.ResultEnumCode;
import com.sgs.ecom.member.request.oiq.OiqItemReq;
import com.sgs.ecom.member.request.tic.TicItemReq;
import com.sgs.ecom.member.request.tic.TicProductReq;
import com.sgs.ecom.member.util.BusinessException;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class OrderDetailDO extends OrderDetail {


    private BaseCopyObj baseCopy = new BaseCopyObj();


    public OrderDetail itemToEntity(OiqItemReq item, OiqOrderReqDTO oiqOrderReqDTO){

        if(StringUtils.isBlank(item.getItemName()) && (oiqOrderReqDTO.getOiqOrderReq().getType()==1 || oiqOrderReqDTO.getOiqOrderReq().getType()==2)){
            throw new BusinessException(ResultEnumCode.ITEM_NAME_ERROR);
        }
        OrderDetail orderDetailAdd=new OrderDetail();
        baseCopy.copyWithNull(orderDetailAdd,item);
        orderDetailAdd.setOrderNo(oiqOrderReqDTO.getOrderNo());
        orderDetailAdd.setGroupNo(oiqOrderReqDTO.getGroupNo());
        orderDetailAdd.setState(1);
        orderDetailAdd.setIsDefault(1);
        orderDetailAdd.setBuyNums(1);
        if(!ValidationUtil.isEmpty(oiqOrderReqDTO.getOiqOrderReq().getItemCategory()) && StringUtils.isNotBlank(oiqOrderReqDTO.getOiqOrderReq().getItemCategory().getCategoryPath())){
            orderDetailAdd.setCategoryPath(oiqOrderReqDTO.getOiqOrderReq().getItemCategory().getCategoryPath());
        }
        return orderDetailAdd;
    }




    public OrderDetail initMainProduct(TicProductReq ticProductReq, Long productId, String orderNo, String dateStr){
        OrderDetail orderDetail = new OrderDetail();
        orderDetail.setProductId(productId);
        String itemName = ticProductReq.getCombo();
        if(ValidationUtil.isEmpty(ticProductReq.getCombo())){
            //需要掏出来sku属性里面的套餐名称
            List<BbcSkuAttrDTO> newRulerSkuAttrList = ticProductReq.getNewRulerSkuAttrList();
            itemName= newRulerSkuAttrList.stream().filter(bbcSkuAttrDTO -> bbcSkuAttrDTO.getAttrKey().equals("packages")).map(BbcSkuAttrDTO::getAttrValue).collect(Collectors.joining(","));
        }
        orderDetail.setItemName(itemName);
        orderDetail.setIsDefault(1);
        orderDetail.setTestName(ticProductReq.getServiceItems());
        orderDetail.setStandardCode(ticProductReq.getTestingStandard());
        orderDetail.setItemType(1);
        orderDetail.setLabelName(ticProductReq.getLabelName());
//        orderDetail.setBuyNums(ticProductReq.getQuantity());
        initOrderInfo(orderDetail, orderNo, dateStr);
        initCommon(orderDetail);
        return orderDetail;
    }

    public List<OrderDetail> initSubProduct(List<TicItemReq> lstItem, Long productId, Long detailId,
                                            String orderNo, String dateStr, TicProductReq ticProductReq){
        List<OrderDetail> lstDetail = new ArrayList<>();
        for(TicItemReq ticItemReq : lstItem){
            OrderDetail orderDetail = new OrderDetail();
            baseCopy.copy(orderDetail, ticItemReq);
            orderDetail.setProductId(productId);
            orderDetail.setItemType(2);
            orderDetail.setBuyNums(1);
            if(!ValidationUtil.isEmpty(detailId)){
                orderDetail.setParentDetailId(detailId);
            }
            orderDetail.setTotalPrice(ticItemReq.getPrice());
            if(ValidationUtil.isEmpty(ticProductReq.getCombo())){
                orderDetail.setStandardCode(ticProductReq.getTestingStandard());
            }
            orderDetail.setIsDefault(1);
            initOrderInfo(orderDetail, orderNo, dateStr);
            initCommon(orderDetail);
            lstDetail.add(orderDetail);
        }

        return lstDetail;
    }

    private void initOrderInfo(OrderDetail orderDetail,String orderNo, String dateStr){
        orderDetail.setCreateDate(dateStr);
        orderDetail.setOrderNo(orderNo);
    }

    private void initCommon(OrderDetail orderDetail){
        orderDetail.setState(1);
    }

    public static ItemCategoryDTO listToItemCategory(List<OiqItemDTO> list){
        ItemCategoryDTO itemCategoryDTO=new ItemCategoryDTO();
        for(OiqItemDTO oiqItemDTO:list){
            itemCategoryDTO.setCategoryPath(oiqItemDTO.getCategoryPath());
        }
        return itemCategoryDTO;
    }
}
