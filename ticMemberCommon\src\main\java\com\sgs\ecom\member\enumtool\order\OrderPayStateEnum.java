package com.sgs.ecom.member.enumtool.order;

public enum OrderPayStateEnum {
    NOPAY("noPay", 0, "未支付"),
    PAY("pay", 1, "已支付"),
    TOPAY("toPay", 2, "已上传支付凭证"),
    ;

    OrderPayStateEnum(String name, int index, String nameCh) {
        this.name = name;
        this.index = index;
        this.nameCh = nameCh;
    }

    public static String getName(int index) {
        for (OrderPayStateEnum c : OrderPayStateEnum.values()) {
            if (c.getIndex()==index) {
                return c.getName();
            }
        }
        return null;
    }

    public static OrderPayStateEnum getEnum(int index) {
        for (OrderPayStateEnum c : OrderPayStateEnum.values()) {
            if (c.getIndex()==index) {
                return c;
            }
        }
        return null;
    }

    public static String getNameCh(int index) {
        for (OrderPayStateEnum c : OrderPayStateEnum.values()) {
            if (c.getIndex()==index) {
                return c.getNameCh();
            }
        }
        return null;
    }





    private String name;
    private int index;
    private String nameCh;

    public static String getNameChByPayState(String payState) {
        for (OrderPayStateEnum c : OrderPayStateEnum.values()) {
            if (String.valueOf(c.getIndex()).equals(payState)) {
                return c.getNameCh();
            }
        }
        return "";
    }


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }


    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }
}
