<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.order.dao.OrderApplicationAttrMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.vo.VOOrderApplicationAttr" >
    <id column="ATTR_ID" property="attrId" jdbcType="BIGINT" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="ATTR_CODE" property="attrCode" jdbcType="VARCHAR" />
    <result column="ATTR_NAME" property="attrName" jdbcType="VARCHAR" />
    <result column="ATTR_VALUE" property="attrValue" jdbcType="VARCHAR" />
    <result column="AREA_CODE" property="areaCode" jdbcType="VARCHAR" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="CLASSIFY_TYPE" property="classifyType" jdbcType="VARCHAR" />
    <result column="ENUM_CONFIG" property="enumConfig" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    ATTR_ID, ORDER_NO, ATTR_CODE, ATTR_NAME, ATTR_VALUE, AREA_CODE, CREATE_DATE, STATE, CLASSIFY_TYPE,ENUM_CONFIG
  </sql>


  <update id="deleteByOrderNo" parameterType="java.lang.String" >
    update ORDER_APPLICATION_ATTR set state=0
    <include refid="baseQueryWhere"/>
  </update>


  <insert id="insertForeach"  >
    insert into ORDER_APPLICATION_ATTR
    ( ORDER_NO, ATTR_CODE,ATTR_NAME, ATTR_VALUE, AREA_CODE, CREATE_DATE,STATE,CLASSIFY_TYPE,ENUM_CONFIG)
    values
    <foreach collection ="list" item="item" index= "index" separator =",">
      (
      #{item.orderNo}, #{item.attrCode},#{item.attrName},#{item.attrValue},#{item.areaCode},#{item.createDate},1,
      <if test="item.classifyType != null and item.classifyType !=''" >
        #{item.classifyType},
      </if>
      <if test="item.classifyType == null or item.classifyType == ''" >
        'APPLICATION',
      </if>
       #{item.enumConfig}
      )
    </foreach>
  </insert>

  <insert id="insertEntityForeach"  >
    insert into ORDER_APPLICATION_ATTR
    ( ORDER_NO, ATTR_CODE,ATTR_NAME, ATTR_VALUE, AREA_CODE, CREATE_DATE,STATE,CLASSIFY_TYPE,ENUM_CONFIG,ATTR_TEXT)
    values
    <foreach collection ="list" item="item" index= "index" separator =",">
      (
      #{item.orderNo}, #{item.attrCode},#{item.attrName},#{item.attrValue},#{item.areaCode},#{item.createDate},1,
      <if test="item.classifyType != null and item.classifyType !=''" >
        #{item.classifyType},
      </if>
      <if test="item.classifyType == null or item.classifyType == ''" >
        'APPLICATION',
      </if>
       #{item.enumConfig} ,#{item.attrText}
      )
    </foreach>
  </insert>

  <select id="selectVOListByMap" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from ORDER_APPLICATION_ATTR
    <include refid="baseQueryWhere"/>
  </select>


  <!-- 自定义-->
  <resultMap id="resultDTO" type="com.sgs.ecom.member.dto.application.OrderApplicationAttrDTO" >
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="ATTR_CODE" property="attrCode" jdbcType="VARCHAR" />
    <result column="ATTR_NAME" property="attrName" jdbcType="VARCHAR" />
    <result column="ATTR_VALUE" property="attrValue" jdbcType="VARCHAR" />
    <result column="AREA_CODE" property="areaCode" jdbcType="VARCHAR" />
    <result column="CLASSIFY_TYPE" property="classifyType" jdbcType="VARCHAR" />
    <result column="ENUM_CONFIG" property="enumConfig" jdbcType="VARCHAR" />
    <result column="ATTR_TEXT" property="attrTextString" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="baseDTO" >
      ORDER_NO, ATTR_CODE, ATTR_NAME, ATTR_VALUE, AREA_CODE,CLASSIFY_TYPE,ENUM_CONFIG,ATTR_TEXT
  </sql>
  <select id="selectCustom" resultMap="resultDTO" >
    select
    <include refid="baseDTO" />
    from ORDER_APPLICATION_ATTR
    <include refid="baseQueryWhere"/>
    order by ATTR_ID desc
    limit 1
  </select>
  <select id="selectListByMap" resultMap="resultDTO" >
    select
    <include refid="baseDTO" />
    from ORDER_APPLICATION_ATTR
    <include refid="baseQueryWhere"/>
    order by ATTR_ID desc
  </select>




  <sql id="baseQueryWhere">
    where  STATE=1
    <if test="orderNo != null">
      AND ORDER_NO=#{orderNo}
    </if>
    <if test="attrCode != null">
      AND ATTR_CODE=#{attrCode}
    </if>
    <if test="areaCode != null">
      AND AREA_CODE=#{areaCode}
    </if>
    <if test="attrCodeNot != null">
      AND ATTR_CODE!=#{attrCodeNot}
    </if>
    <if test="notAreaCode != null">
      AND AREA_CODE !=#{notAreaCode}
    </if>
    <if test="classifyType != null">
      AND CLASSIFY_TYPE =#{classifyType}
    </if>
    <if test="attrCodeList != null">
      AND ATTR_CODE in
      <foreach collection="attrCodeList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="orderNoList != null ">
      AND ORDER_NO in
      <foreach collection="orderNoList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </sql>

  <insert id="copyByOrderNo" parameterType="com.sgs.ecom.member.dto.order.CopyByOrder">
   insert into  ORDER_APPLICATION_ATTR
       (ORDER_NO,ATTR_CODE,ATTR_NAME,ATTR_VALUE,AREA_CODE,CREATE_DATE,STATE,CLASSIFY_TYPE,ENUM_CONFIG)
    select #{toOrderNo},ATTR_CODE,ATTR_NAME,ATTR_VALUE,AREA_CODE,CREATE_DATE,1,CLASSIFY_TYPE,ENUM_CONFIG
        from ORDER_APPLICATION_ATTR where ORDER_NO= #{useOrderNo} and state=1
  </insert>
</mapper>