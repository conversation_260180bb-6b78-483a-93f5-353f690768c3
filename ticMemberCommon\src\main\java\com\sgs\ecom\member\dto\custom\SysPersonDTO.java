package com.sgs.ecom.member.dto.custom;

import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.sgs.base.BaseQryFilter.QuerySummary;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class SysPersonDTO {
    private String csCode;
    private String csEmail;

    private String personName;
    private String personNameEn;

 	@ApiAnno(groups={QuerySummary.class})
 	@BeanAnno(value="PERSON_CODE", getName="getPersonCode", setName="setPersonCode")
 	private String personCode;
 	@ApiAnno(groups={QuerySummary.class})
 	@BeanAnno(value="PERSON_PHONE", getName="getPersonPhone", setName="setPersonPhone")
 	private String personPhone;
 	@ApiAnno(groups={QuerySummary.class})
 	@BeanAnno(value="PERSON_MAIL", getName="getPersonMail", setName="setPersonMail")
 	private String personMail;
 	@ApiAnno(groups={QuerySummary.class})
 	@BeanAnno(value="CUST_ID", getName="getCustId", setName="setCustId")
 	private long custId;
 	@ApiAnno(groups={QuerySummary.class})
 	@BeanAnno(value="CUST_CODE", getName="getCustCode", setName="setCustId")
 	private String custCode;

 	private String otherQrCode;

 	private List<Map> lineItem=new ArrayList<>();

	private List<Long> labIds=new ArrayList<>();

	private List<Map> roleAttr=new ArrayList<>();

    public String getCsCode() {
        return csCode;
    }

    public void setCsCode(String csCode) {
        this.csCode = csCode;
    }

    public String getCsEmail() {
        return csEmail;
    }

    public void setCsEmail(String csEmail) {
        this.csEmail = csEmail;
    }

    public String getPersonName() {
        return personName;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    public String getPersonNameEn() {
        return personNameEn;
    }

    public void setPersonNameEn(String personNameEn) {
        this.personNameEn = personNameEn;
    }

	public String getPersonCode() {
		return personCode;
	}

	public void setPersonCode(String personCode) {
		this.personCode = personCode;
	}

	public String getPersonPhone() {
		return personPhone;
	}

	public void setPersonPhone(String personPhone) {
		this.personPhone = personPhone;
	}

	public String getPersonMail() {
		return personMail;
	}

	public void setPersonMail(String personMail) {
		this.personMail = personMail;
	}

	public long getCustId() {
		return custId;
	}

	public void setCustId(long custId) {
		this.custId = custId;
	}


	public List<Map> getLineItem() {
		return lineItem;
	}

	public void setLineItem(List<Map> lineItem) {
		this.lineItem = lineItem;
	}

	public String getCustCode() {
		return custCode;
	}

	public void setCustCode(String custCode) {
		this.custCode = custCode;
	}

	public String getOtherQrCode() {
		return otherQrCode;
	}

	public void setOtherQrCode(String otherQrCode) {
		this.otherQrCode = otherQrCode;
	}

	public List<Map> getRoleAttr() {
		return roleAttr;
	}

	public void setRoleAttr(List<Map> roleAttr) {
		this.roleAttr = roleAttr;
	}

	public List<Long> getLabIds() {
		return labIds;
	}

	public void setLabIds(List<Long> labIds) {
		this.labIds = labIds;
	}
}
