package com.sgs.ecom.member.dto.rpc;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.json.TimeFormatSerializer;
import com.sgs.base.BaseQryFilter;
import java.sql.Timestamp;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class QuestionOptionDTO extends BaseQryFilter {
 
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="IS_NA", getName="getIsNa", setName="setIsNa")
 	private int isNa;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="PART_NO", getName="getPartNo", setName="setPartNo")
 	private String partNo;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private Timestamp stateDate;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="OPTION_INFO_EN", getName="getOptionInfoEn", setName="setOptionInfoEn")
 	private String optionInfoEn;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="OPTION_INFO", getName="getOptionInfo", setName="setOptionInfo")
 	private String optionInfo;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="SUBJECT_NO", getName="getSubjectNo", setName="setSubjectNo")
 	private String subjectNo;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="REGEX_RULE", getName="getRegexRule", setName="setRegexRule")
 	private String regexRule;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="OPTION_ID", getName="getOptionId", setName="setOptionId")
 	private long optionId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="IS_MUST", getName="getIsMust", setName="setIsMust")
 	private int isMust;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="SORT_SHOW", getName="getSortShow", setName="setSortShow")
 	private int sortShow;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="IS_CHECK", getName="getIsCheck", setName="setIsCheck")
 	private int isCheck;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="OPTION_NO", getName="getOptionNo", setName="setOptionNo")
 	private String optionNo;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STANDARD_CODE", getName="getStandardCode", setName="setStandardCode")
 	private String standardCode;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="IS_FILL", getName="getIsFill", setName="setIsFill")
 	private int isFill;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="IMG_PATH", getName="getImgPath", setName="setImgPath")
 	private String imgPath;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="OPTION_TYPE", getName="getOptionType", setName="setOptionType")
 	private int optionType;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="IS_DEFUALT", getName="getIsDefualt", setName="setIsDefualt")
 	private int isDefualt;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="MEMO", getName="getMemo", setName="setMemo")
 	private String memo;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="OPTION_SCORE", getName="getOptionScore", setName="setOptionScore")
 	private int optionScore;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="PARENT_OPTION_NO", getName="getParentOptionNo", setName="setParentOptionNo")
 	private String parentOptionNo;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="RELATE_TYPE", getName="getRelateType", setName="setRelateType")
 	private long relateType;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="ICON_PATH", getName="getIconPath", setName="setIconPath")
 	private String iconPath;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="RELATE_PART_NO", getName="getRelatePartNo", setName="setRelatePartNo")
 	private String relatePartNo;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="FILL_NOTICE", getName="getFillNotice", setName="setFillNotice")
 	private String fillNotice;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="IS_GROUP", getName="getIsGroup", setName="setIsGroup")
 	private int isGroup;
	@ApiAnno(groups={Default.class,QueryList.class})
	@BeanAnno(value="GROUP_IMAGE", getName="getGroupImage", setName="setGroupImage")
	private String groupImage;
	@ApiAnno(groups={Default.class,QueryList.class})
	@BeanAnno(value="SUB_OPTION", getName="getSubOption", setName="setSubOption")
	private String subOption;
	
 	private List<QuestionOptionDTO> optionItem;

	/**
	 * 标签
	 */
	@ApiAnno(groups={Default.class,QueryList.class})
	private List<QuestionLabel> labels;

	/**
	 * 标签编码
	 */
	@ApiAnno(groups={Default.class,QueryList.class})
	private String labelCode;

 	public void setIsNa(int isNa){
 		 this.isNa=isNa;
 	}
 	public int getIsNa(){
 		 return this.isNa;
 	}
 
 	 
 	public void setPartNo(String partNo){
 		 this.partNo=partNo;
 	}
 	public String getPartNo(){
 		 return this.partNo;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setOptionInfoEn(String optionInfoEn){
 		 this.optionInfoEn=optionInfoEn;
 	}
 	public String getOptionInfoEn(){
 		 return this.optionInfoEn;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setOptionInfo(String optionInfo){
 		 this.optionInfo=optionInfo;
 	}
 	public String getOptionInfo(){
 		 return this.optionInfo;
 	}
 
 	 
 	public void setSubjectNo(String subjectNo){
 		 this.subjectNo=subjectNo;
 	}
 	public String getSubjectNo(){
 		 return this.subjectNo;
 	}
 
 	 
 	public void setRegexRule(String regexRule){
 		 this.regexRule=regexRule;
 	}
 	public String getRegexRule(){
 		 return this.regexRule;
 	}
 
 	 
 	public void setOptionId(long optionId){
 		 this.optionId=optionId;
 	}
 	public long getOptionId(){
 		 return this.optionId;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setIsMust(int isMust){
 		 this.isMust=isMust;
 	}
 	public int getIsMust(){
 		 return this.isMust;
 	}
 
 	 
 	public void setSortShow(int sortShow){
 		 this.sortShow=sortShow;
 	}
 	public int getSortShow(){
 		 return this.sortShow;
 	}
 
 	 
 	public void setIsCheck(int isCheck){
 		 this.isCheck=isCheck;
 	}
 	public int getIsCheck(){
 		 return this.isCheck;
 	}
 
 	 
 	public void setOptionNo(String optionNo){
 		 this.optionNo=optionNo;
 	}
 	public String getOptionNo(){
 		 return this.optionNo;
 	}
 
 	 
 	public void setStandardCode(String standardCode){
 		 this.standardCode=standardCode;
 	}
 	public String getStandardCode(){
 		 return this.standardCode;
 	}
 
 	 
 	public void setIsFill(int isFill){
 		 this.isFill=isFill;
 	}
 	public int getIsFill(){
 		 return this.isFill;
 	}
 
 	 
 	public void setImgPath(String imgPath){
 		 this.imgPath=imgPath;
 	}
 	public String getImgPath(){
 		 return this.imgPath;
 	}
 
 	 
 	public void setOptionType(int optionType){
 		 this.optionType=optionType;
 	}
 	public int getOptionType(){
 		 return this.optionType;
 	}
 
 	 
 	public void setIsDefualt(int isDefualt){
 		 this.isDefualt=isDefualt;
 	}
 	public int getIsDefualt(){
 		 return this.isDefualt;
 	}
 
 	 
 	public void setMemo(String memo){
 		 this.memo=memo;
 	}
 	public String getMemo(){
 		 return this.memo;
 	}
 
 	 
 	public void setOptionScore(int optionScore){
 		 this.optionScore=optionScore;
 	}
 	public int getOptionScore(){
 		 return this.optionScore;
 	}
	public String getParentOptionNo() {
		return parentOptionNo;
	}
	public void setParentOptionNo(String parentOptionNo) {
		this.parentOptionNo = parentOptionNo;
	}
	public List<QuestionOptionDTO> getOptionItem() {
		return optionItem;
	}
	public void setOptionItem(List<QuestionOptionDTO> optionItem) {
		this.optionItem = optionItem;
	}
	public long getRelateType() {
		return relateType;
	}
	public void setRelateType(long relateType) {
		this.relateType = relateType;
	}
	public String getIconPath() {
		return iconPath;
	}
	public void setIconPath(String iconPath) {
		this.iconPath = iconPath;
	}
	public String getRelatePartNo() {
		return relatePartNo;
	}
	public void setRelatePartNo(String relatePartNo) {
		this.relatePartNo = relatePartNo;
	}
	public String getFillNotice() {
		return fillNotice;
	}
	public void setFillNotice(String fillNotice) {
		this.fillNotice = fillNotice;
	}
	public int getIsGroup() {
		return isGroup;
	}
	public void setIsGroup(int isGroup) {
		this.isGroup = isGroup;
	}

	public String getGroupImage() {
		return groupImage;
	}

	public void setGroupImage(String groupImage) {
		this.groupImage = groupImage;
	}
	public String getSubOption() {
		return subOption;
	}
	public void setSubOption(String subOption) {
		this.subOption = subOption;
	}
}