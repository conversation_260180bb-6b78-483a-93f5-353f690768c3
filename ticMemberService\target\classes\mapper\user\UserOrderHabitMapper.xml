<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.user.dao.UserOrderHabitMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.vo.VOUserOrderHabit" >
    <id column="ID" property="id" jdbcType="BIGINT" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="ATTR_TYPE" property="attrType" jdbcType="VARCHAR" />
    <result column="ATTR_CODE" property="attrCode" jdbcType="VARCHAR" />
    <result column="ATTR_VALUE" property="attrValue" jdbcType="VARCHAR" />
    <result column="SYSTEM" property="system" jdbcType="VARCHAR" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, USER_ID, ATTR_TYPE,ATTR_CODE, ATTR_VALUE, `SYSTEM`, CREATE_DATE
  </sql>




  <insert id="insertForeach"  >
    insert into USER_ORDER_HABIT
    ( USER_ID, ATTR_TYPE,ATTR_CODE, ATTR_VALUE, `SYSTEM`,CUST_ID, CREATE_DATE)
    values
    <foreach collection ="list" item="item" index= "index" separator =",">
      (
      #{item.userId}, #{item.attrType},#{item.attrCode},#{item.attrValue},#{item.system},#{item.custId},#{item.createDate}
      )
    </foreach>
  </insert>

  <delete id="delHabitByMap">
    delete from USER_ORDER_HABIT where USER_ID=#{userId} and `SYSTEM`=#{system}   AND CUST_ID=#{custId}
  </delete>

  <select id="selectVOListByMap" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from USER_ORDER_HABIT
    <include refid="baseQueryWhere"/>
  </select>
  <sql id="baseQueryWhere">
    where 1=1
    <if test="userId != null">
      AND USER_ID=#{userId}
    </if>
    <if test="custId != null">
      AND CUST_ID=#{custId}
    </if>
  </sql>





</mapper>