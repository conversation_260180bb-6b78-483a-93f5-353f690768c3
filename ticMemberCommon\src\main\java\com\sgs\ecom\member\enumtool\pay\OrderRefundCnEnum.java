package com.sgs.ecom.member.enumtool.pay;

public enum OrderRefundCnEnum {
	REFUND_SHOW("refundShow", 1, "客户申请退款-客服未确认", "客户申请退款-客服已确认", "客户申请退款-客服已取消","客服退款操作"),
    OPERATOR_SHOW("operatorShow",2, "客户申请退款", "客户申请退款", "客户申请退款", "客服申请退款"),
    STATE_SHOW("stateShow", 3, "待客服确认更新", "客服已确认退款申请，请耐心等待返还", "客服已取消退款申请", "待客服确认更新"),
    TYPE_SHOW("typeShow", 4, "原支付返还", "原支付返还", "原支付返还", "原支付返还"),
    TYPE_PAY_SHOW("typePayShow", 5, "原支付返还 支付宝账号", "原支付返还 支付宝账号", "原支付返还 支付宝账号", "原支付返还"),
  
    
    ;
	OrderRefundCnEnum(String name, int index, String nameCh, String nameChB, String nameChC, String nameChD) {
        this.name = name;
        this.index = index;
        this.nameCh = nameCh;
        this.nameChB = nameChB;
        this.nameChC = nameChC;
        this.nameChD = nameChD;
    }

    public static String getName(int index) {
        for (OrderRefundCnEnum c : OrderRefundCnEnum.values()) {
            if (c.getIndex()==index) {
                return c.getName();
            }
        }
        return null;
    }

    public static OrderRefundCnEnum getEnum(int index) {
        for (OrderRefundCnEnum c : OrderRefundCnEnum.values()) {
            if (c.getIndex()==index) {
                return c;
            }
        }
        return null;
    }

    public static String getNameChB(int index) {
        for (OrderRefundCnEnum c : OrderRefundCnEnum.values()) {
            if (c.getIndex()==index) {
                return c.getNameChB();
            }
        }
        return null;
    }
    public static String getNameChC(int index) {
    	for (OrderRefundCnEnum c : OrderRefundCnEnum.values()) {
    		if (c.getIndex()==index) {
    			return c.getNameChC();
    		}
    	}
    	return null;
    }
    public static String getNameChD(int index) {
    	for (OrderRefundCnEnum c : OrderRefundCnEnum.values()) {
    		if (c.getIndex()==index) {
    			return c.getNameChD();
    		}
    	}
    	return null;
    }
    public static String getNameCh(int index) {
    	for (OrderRefundCnEnum c : OrderRefundCnEnum.values()) {
    		if (c.getIndex()==index) {
    			return c.getNameCh();
    		}
    	}
    	return null;
    }





    private String name;
    private int index;
    private String nameCh;
    private String nameChB;
    private String nameChC;
    private String nameChD;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }

	public String getNameChB() {
		return nameChB;
	}

	public void setNameChB(String nameChB) {
		this.nameChB = nameChB;
	}

	public String getNameChC() {
		return nameChC;
	}

	public void setNameChC(String nameChC) {
		this.nameChC = nameChC;
	}

	public String getNameChD() {
		return nameChD;
	}

	public void setNameChD(String nameChD) {
		this.nameChD = nameChD;
	}
    
}
