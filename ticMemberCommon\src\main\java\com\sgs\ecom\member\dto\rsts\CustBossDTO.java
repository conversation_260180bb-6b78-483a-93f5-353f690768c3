package com.sgs.ecom.member.dto.rsts;

import com.sgs.ecom.member.dto.OrderBaseInfoCheckDTO;
import com.sgs.ecom.member.dto.base.BaseOrderDTO;

public class CustBossDTO {
	private Long custId;
	private String bossNo;//企业的bossNo
	private String invoiceBossNo;
	private String saler;
	private String phone;
	private String taxNo;//是发票的税号
	private String belongLabCode;//送样实验室 客服的
	private String labCode;//用户自己选择的
	private String vbaAccount;
	private int isBind;
	private String custCode;
	private Integer monthPay;

	public CustBossDTO() {
	}

	public CustBossDTO(OrderBaseInfoCheckDTO orderBaseInfoCheckDTO) {
		this.bossNo = orderBaseInfoCheckDTO.getBossNo();
		this.saler = orderBaseInfoCheckDTO.getSalesCode();
		this.phone = orderBaseInfoCheckDTO.getSalesPhone();
		this.custId=orderBaseInfoCheckDTO.getCustId();
	}

	public CustBossDTO(BaseOrderDTO orderDTO) {
		this.bossNo = orderDTO.getBossNo();
		this.saler = orderDTO.getSalesCode();
		this.phone = orderDTO.getSalesPhone();
		this.custId=orderDTO.getCustId();
	}

	public CustBossDTO(String bossNo, String saler, String phone) {
		this.bossNo = bossNo;
		this.saler = saler;
		this.phone = phone;
	}

	public String getBossNo() {
		return bossNo;
	}

	public void setBossNo(String bossNo) {
		this.bossNo = bossNo;
	}

	public String getSaler() {
		return saler;
	}

	public void setSaler(String saler) {
		this.saler = saler;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public Long getCustId() {
		return custId;
	}

	public void setCustId(Long custId) {
		this.custId = custId;
	}

	public String getTaxNo() {
		return taxNo;
	}

	public void setTaxNo(String taxNo) {
		this.taxNo = taxNo;
	}

	public int getIsBind() {
		return isBind;
	}

	public void setIsBind(int isBind) {
		this.isBind = isBind;
	}

	public String getBelongLabCode() {
		return belongLabCode;
	}

	public void setBelongLabCode(String belongLabCode) {
		this.belongLabCode = belongLabCode;
	}

	public String getVbaAccount() {
		return vbaAccount;
	}

	public void setVbaAccount(String vbaAccount) {
		this.vbaAccount = vbaAccount;
	}

	public String getCustCode() {
		return custCode;
	}

	public void setCustCode(String custCode) {
		this.custCode = custCode;
	}

	public String getLabCode() {
		return labCode;
	}

	public void setLabCode(String labCode) {
		this.labCode = labCode;
	}

	public Integer getMonthPay() {
		return monthPay;
	}

	public void setMonthPay(Integer monthPay) {
		this.monthPay = monthPay;
	}

	public String getInvoiceBossNo() {
		return invoiceBossNo;
	}

	public void setInvoiceBossNo(String invoiceBossNo) {
		this.invoiceBossNo = invoiceBossNo;
	}
}
