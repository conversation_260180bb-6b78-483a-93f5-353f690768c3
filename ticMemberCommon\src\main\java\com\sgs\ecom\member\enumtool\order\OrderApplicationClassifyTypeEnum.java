package com.sgs.ecom.member.enumtool.order;

public enum OrderApplicationClassifyTypeEnum {
	APPLICATION("APPLICATION", "APPLICATION", "默认的其他数据"),
	MSDS("MSDS", "MSDS", "新的MSDS数据"),
	;





	OrderApplicationClassifyTypeEnum(String name, String index, String nameCh) {
		this.name = name;
		this.index = index;
		this.nameCh = nameCh;
	}

	private String name;
	private String index;
	private String nameCh;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getIndex() {
		return index;
	}

	public void setIndex(String index) {
		this.index = index;
	}

	public String getNameCh() {
		return nameCh;
	}

	public void setNameCh(String nameCh) {
		this.nameCh = nameCh;
	}
}
