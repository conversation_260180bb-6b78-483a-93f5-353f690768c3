package com.sgs.ecom.member.controller.question;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.base.BaseBean;
import com.sgs.ecom.member.request.OrderNoReq;
import com.sgs.ecom.member.request.question.AnswerReq;
import com.sgs.ecom.member.request.question.QuestionAddRedisReq;
import com.sgs.ecom.member.request.question.QuestionAddReq;
import com.sgs.ecom.member.service.question.interfaces.IUserQuestionService;
import com.sgs.ecom.member.service.util.interfaces.IOrderQuestionService;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import javax.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.order/oiq/question")
public class UserOiqQuestionController extends ControllerUtil {
 
    @Autowired
    private IOrderQuestionService orderQuestionService;
    @Resource
    private IUserQuestionService iUserQuestionService;



    @HystrixCommand
    @RequestMapping(value = "saveOption", method = { RequestMethod.POST })
    public ResultBody saveOptionRedis(
            @RequestHeader(value = "operatorSource", defaultValue = "") String operatorSource,
            @RequestBody List<QuestionAddReq> questionAddReqList
            ) throws Exception {

        QuestionAddRedisReq  questionAddRedisReq=new QuestionAddRedisReq();
        questionAddRedisReq.setQuestionId(questionAddReqList.get(0).getQuestionId());
        questionAddRedisReq.setQuestionName(questionAddReqList.get(0).getQuestionName());
        questionAddRedisReq.setReplyNo(questionAddReqList.get(0).getReplyNo());
        questionAddRedisReq.setProductName(questionAddReqList.get(0).getProductName());
        questionAddRedisReq.setCatagoryId(questionAddReqList.get(0).getCatagoryId());
        questionAddRedisReq.setQuestionAddReqList(questionAddReqList);
        questionAddRedisReq.setOperatorSource(operatorSource);
        if(StringUtils.isNotBlank(questionAddReqList.get(0).getMdCode())){
            questionAddRedisReq.setMdCode(questionAddReqList.get(0).getMdCode());
        }

        return ResultBody.newInstance(orderQuestionService.saveOptionRedis(questionAddRedisReq));
    }

    /**
     * 保存类目级别的数据缓存(选择大类对应step0问题缓存，step0提交)
     *
     * @param operatorSource
     * @param questionAddReqList
     * @return
     * @throws Exception
     */
    @HystrixCommand
    @AuthRequired(login = "NULL", sign = true)
    @RequestMapping(value = "saveCacheForStepZero", method = {RequestMethod.POST})
    public ResultBody saveCacheForStepZero(
        @RequestHeader(value = "operatorSource", defaultValue = "") String operatorSource,
        @RequestBody List<QuestionAddReq> questionAddReqList
    ) throws Exception {

        QuestionAddRedisReq  questionAddRedisReq=new QuestionAddRedisReq();
        questionAddRedisReq.setQuestionId(questionAddReqList.get(0).getQuestionId());
        questionAddRedisReq.setQuestionName(questionAddReqList.get(0).getQuestionName());
        questionAddRedisReq.setReplyNo(questionAddReqList.get(0).getReplyNo());
        questionAddRedisReq.setProductName(questionAddReqList.get(0).getProductName());
        questionAddRedisReq.setCatagoryId(questionAddReqList.get(0).getCatagoryId());
        questionAddRedisReq.setQuestionAddReqList(questionAddReqList);
        questionAddRedisReq.setOperatorSource(operatorSource);
        if(StringUtils.isNotBlank(questionAddReqList.get(0).getMdCode())){
            questionAddRedisReq.setMdCode(questionAddReqList.get(0).getMdCode());
        }

        return ResultBody.newInstance(orderQuestionService.saveInitialQuestionCache(questionAddRedisReq));
    }

    /**
     * 获取Step0答案
     *
     * @param answerReq
     * @return
     * @throws Exception
     */
    @HystrixCommand
    @AuthRequired(login = "NULL", sign = true)
    @RequestMapping(value = "getAiAnswerByStepZero", method = { RequestMethod.POST })
    public ResultBody getAiAnswerByStepZero(
        @RequestBody AnswerReq answerReq) throws Exception {
        return ResultBody.newInstance(orderQuestionService.getAiAnswerByStepZeroFromRedis(answerReq));
    }

    /**
    *@Function: getAnswer
    *@Description 获取答案
    *@param: [answerReq]
    *@author: Xiwei_Qiu @date: 2021/9/10 @version: 
    **/
    @HystrixCommand
    @RequestMapping(value = "getAnswer", method = { RequestMethod.POST })
    public ResultBody getAnswer(
            @Validated(BaseBean.Query.class)
            @RequestBody AnswerReq answerReq
    ) throws Exception {
        return ResultBody.newInstance(orderQuestionService.getAnswerMoreByRedis(answerReq,true));
    }




    /**
     *@Function: getAnswer
     *@Description 删除答案
     *@param: [answerReq]
     *@author: Xiwei_Qiu @date: 2021/9/10 @version: 
     **/
    @HystrixCommand
    @RequestMapping(value = "delAnswer", method = { RequestMethod.POST })
    public ResultBody delAnswer(
            @Validated(BaseBean.Query.class)
            @RequestBody AnswerReq answerReq
    ) throws Exception {
        orderQuestionService.delAnswerMore(answerReq);
        return ResultBody.success();
    }



    /**
    *@Function: getAnswerByPart
    *@Description 需求清单 （获取12宫格答案）
    *@param: [answerReq]
    *@author: Xiwei_Qiu @date: 2021/9/10 @version: 
    **/
    @RequestMapping(value = "getAnswerByPart", method = { RequestMethod.POST })
    public ResultBody getAnswerByPart(
            @Validated(BaseBean.Query.class)
            @RequestBody AnswerReq answerReq
    ) throws Exception {
        return ResultBody.newInstance(orderQuestionService.getAnswerMoreListNew(answerReq));
    }


    /**
    *@Function: copyQuestion
    *@Description 复制用户问卷
    *@param: [orderNoReq, token]
    *@author: Xiwei_Qiu @date: 2021/9/10 @version:
    **/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "copyQuestion", method = { RequestMethod.POST })
    public ResultBody copyQuestion(
            @RequestBody OrderNoReq orderNoReq,
            @RequestHeader(value="accessToken") String token) throws Exception {
        return ResultBody.newInstance(orderQuestionService.copyQuestion(orderNoReq.getOrderNo(),getUserDTO(token)));
    }




}
