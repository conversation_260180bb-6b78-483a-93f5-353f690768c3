package com.sgs.ecom.member.controller.user;

import com.sgs.base.BaseBean;
import com.sgs.ecom.member.request.cust.CustReq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.ecom.member.service.user.interfaces.IUserPackageSV;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import com.sgs.ecom.member.vo.VOSysCatagory;
import com.sgs.ecom.member.vo.VOSysPackage;

@RestController
@RequestMapping("/business/api.v2.user/package")
@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class,SysException.class})
public class UserPackageController extends ControllerUtil {

	@Autowired
	private IUserPackageSV userPackageSV;
	
    /**   
	* @Function: qryOftenPackage
	* @Description: 查询常用套餐信息
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2018-11-23
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2018-11-23 shenyi    v1.0                 新增
	*/
	@HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "qryOften", method = { RequestMethod.POST })
    public ResultBody qryOftenPackage(@RequestHeader(value="accessToken") String token,
	 @Validated(BaseBean.Query.class)
	 @RequestBody CustReq custReq) throws Exception {
		return ResultBody.newInstance(userPackageSV.qryOftenPackage(getUserDTO(token).getUserId(),custReq.getCustId()));
	}
	
    /**   
	* @Function: qryCommonPackage
	* @Description: 查询公共套餐信息
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2018-11-23
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2018-11-23 shenyi    v1.0                 新增
	*/
	@HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "qryCommon", method = { RequestMethod.POST })
    public ResultBody qryCommonPackage(@RequestBody VOSysPackage sysPackage,
		@RequestHeader(value="accessToken") String token,
    	@RequestHeader(value="appId") String appId) throws Exception {
		return ResultBody.newInstance(userPackageSV.qryCommonPackage(sysPackage, getUserId(appId, token)));
	}

	/**   
	* @Function: qryItemPrice
	* @Description: 查询套餐下测试项目信息
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2018-11-23
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2018-11-23 shenyi    v1.0                 新增
	*/
	@HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "qryPrice", method = { RequestMethod.POST })
    public ResultBody qryItemPrice(@RequestBody VOSysPackage sysPackage,
    	@RequestHeader(value="accessToken") String token,
    	@RequestHeader(value="appId") String appId) throws Exception {
		return ResultBody.newInstance(userPackageSV.qryItemPrice(sysPackage ,getUserId(appId, token)));
	}
	
	/**   
	* @Function: qryNextCategory
	* @Description: 查询下级类目
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2018-11-23
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2018-11-23 shenyi    v1.0                 新增
	*/
	@HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "qryNext", method = { RequestMethod.POST })
    public ResultBody qryNextCategory(@RequestBody VOSysCatagory category,
        @RequestHeader(value="accessToken") String token,
    	@RequestBody CustReq custReq) throws Exception {
		return ResultBody.newInstance(userPackageSV.qryNextCategory(category,getUserDTO(token).getUserId(),custReq.getCustId()));
	}
}
