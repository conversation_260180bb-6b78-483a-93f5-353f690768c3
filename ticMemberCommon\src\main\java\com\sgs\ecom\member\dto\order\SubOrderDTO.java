package com.sgs.ecom.member.dto.order;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.dto.OrderDetailDTO;
import com.sgs.ecom.member.dto.OrderOperatorLogDTO;
import com.sgs.ecom.member.dto.pay.OrderPayDTO;
import com.sgs.ecom.member.enumtool.bbc.BbcStateEnum;
import com.sgs.ecom.member.enumtool.order.OrderPayStateEnum;
import com.sgs.ecom.member.enumtool.order.OrderRefundStateEnum;
import com.sgs.ecom.member.util.serializer.PriceNumFormatSerializer;
import com.sgs.ecom.member.util.serializer.TimeStringFormatSerializer;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;

public class SubOrderDTO {

    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqSubList.class})
    private Long orderId;
    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqSubList.class})
    private String orderNo;
    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqSubList.class})
    private String groupNo;
    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqSubList.class})
    private Integer orderType;
    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqSubList.class})
    private String relateOrderNo;
    @JsonSerialize(using = TimeStringFormatSerializer.class)
    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqSubList.class})
    private String createDate;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String stateDate;
    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqSubList.class})
    private Integer isPayReceived;
    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqSubList.class})
    private String recommendReason;
    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqSubList.class})
    private String operatorCode;
    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqSubList.class})
    private Integer totalNums;
    @JsonSerialize(using = PriceNumFormatSerializer.class)
    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqSubList.class})
    private BigDecimal realAmount;
    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqSubList.class})
    private int payState;
    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqSubList.class})
    private int refundState;
    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqSubList.class})
    private int state;
    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqSubList.class})
    private String stateShow;
    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqSubList.class})
    private Integer payMethod;
    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqSubList.class})
    private Integer isRemind;

    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqSubList.class})
    private BigDecimal discountNum;
    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqSubList.class})
    private BigDecimal disAmount;
    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqSubList.class})
    private BigDecimal taxRates=BigDecimal.ZERO;
    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqSubList.class})
    private BigDecimal taxAmount=BigDecimal.ZERO;

    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqSubList.class})
    private List<OrderPayDTO> orderPayDTOList;
    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqSubList.class})
    private  List<OrderDetailDTO> orderDetailDTOList=new ArrayList<>();
    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqSubList.class})
    private BigDecimal urgentAmount;
    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqSubList.class})
    private int subUrgentFlg=0;
    @ApiAnno(groups={BaseOrderFilter.Default.class,BaseOrderFilter.OiqSubList.class})
    private String currency;
    @ApiAnno(groups={BaseOrderFilter.Default.class,BaseOrderFilter.OiqSubList.class})
    private String currencyMark;


    //ro使用
    @ApiAnno(groups={BaseOrderFilter.Default.class})
    private String toPayAmount;

    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqSubList.class})
    private int  isRefund;//客户端是否能显示发起申请退款的按钮 0-否 1-是


    private int monthPay;

    private OrderOperatorLogDTO lastConfirmLog;

    private String offPayAmount;
    private String onlinePayAmount;

    public int getIsRefund() {
        return isRefund;
    }

    public void setIsRefund(int isRefund) {
        this.isRefund = isRefund;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public String getRelateOrderNo() {
        return relateOrderNo;
    }

    public void setRelateOrderNo(String relateOrderNo) {
        this.relateOrderNo = relateOrderNo;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getStateDate() {
        return stateDate;
    }

    public void setStateDate(String stateDate) {
        this.stateDate = stateDate;
    }

    public Integer getIsPayReceived() {
        return isPayReceived;
    }

    public void setIsPayReceived(Integer isPayReceived) {
        this.isPayReceived = isPayReceived;
    }

    public String getRecommendReason() {
        return recommendReason;
    }

    public void setRecommendReason(String recommendReason) {
        this.recommendReason = recommendReason;
    }

    public String getOperatorCode() {
        return operatorCode;
    }

    public void setOperatorCode(String operatorCode) {
        this.operatorCode = operatorCode;
    }

    public Integer getTotalNums() {
        return totalNums;
    }

    public void setTotalNums(Integer totalNums) {
        this.totalNums = totalNums;
    }

    public BigDecimal getRealAmount() {
        return realAmount;
    }

    public void setRealAmount(BigDecimal realAmount) {
        this.realAmount = realAmount;
    }

    public int getPayState() {
        return payState;
    }

    public void setPayState(int payState) {
        this.payState = payState;
    }

    public List<OrderPayDTO> getOrderPayDTOList() {
        return orderPayDTOList;
    }

    public void setOrderPayDTOList(List<OrderPayDTO> orderPayDTOList) {
        this.orderPayDTOList = orderPayDTOList;
    }

    public int getRefundState() {
        return refundState;
    }

    public void setRefundState(int refundState) {
        this.refundState = refundState;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public Integer getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(Integer payMethod) {
        this.payMethod = payMethod;
    }

    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqSubList.class})
    private String payStateShow;
    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqSubList.class})
    private String refundStateShow;

    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OiqSubList.class})
    private String realAmountStr;


    public String getRealAmountStr() {

        String str="0.00";
        if(realAmount!=null ){
            DecimalFormat decimalFormat = new DecimalFormat("0.00");
            str = decimalFormat.format(realAmount);
        }
        return str;
    }

    public String getStateShow() {
        return BbcStateEnum.getNameCh(state);
    }

    public void setStateShow(String stateShow) {
        this.stateShow = stateShow;
    }

    public String getRefundStateShow() {
        return OrderRefundStateEnum.getNameCh(refundState);
    }

    public String getPayStateShow() {
        payStateShow= OrderPayStateEnum.getNameCh(payState);
        if(payStateShow==null){
            payStateShow="";
        }
        return payStateShow;
    }

    public Integer getIsRemind() {
        return isRemind;
    }

    public void setIsRemind(Integer isRemind) {
        this.isRemind = isRemind;
    }

    public List<OrderDetailDTO> getOrderDetailDTOList() {
        return orderDetailDTOList;
    }

    public void setOrderDetailDTOList(List<OrderDetailDTO> orderDetailDTOList) {
        this.orderDetailDTOList = orderDetailDTOList;
    }

    public BigDecimal getDiscountNum() {
        return discountNum;
    }

    public void setDiscountNum(BigDecimal discountNum) {
        this.discountNum = discountNum;
    }

    public BigDecimal getDisAmount() {
        return disAmount;
    }

    public void setDisAmount(BigDecimal disAmount) {
        this.disAmount = disAmount;
    }

    public String getGroupNo() {
        return groupNo;
    }

    public void setGroupNo(String groupNo) {
        this.groupNo = groupNo;
    }

    public BigDecimal getTaxRates() {
        return taxRates;
    }

    public void setTaxRates(BigDecimal taxRates) {
        this.taxRates = taxRates;
    }

    public BigDecimal getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
    }

    public int getSubUrgentFlg() {
        if(urgentAmount==null){
            return 0;
        }
        return urgentAmount.doubleValue()>0?1:0;
    }

    public void setSubUrgentFlg(int subUrgentFlg) {
        this.subUrgentFlg = subUrgentFlg;
    }

    public OrderOperatorLogDTO getLastConfirmLog() {
        return lastConfirmLog;
    }

    public void setLastConfirmLog(OrderOperatorLogDTO lastConfirmLog) {
        this.lastConfirmLog = lastConfirmLog;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }



    public String getToPayAmount() {
        return toPayAmount;
    }

    public void setToPayAmount(String toPayAmount) {
        this.toPayAmount = toPayAmount;
    }

    public int getMonthPay() {
        return monthPay;
    }

    public void setMonthPay(int monthPay) {
        this.monthPay = monthPay;
    }

    public String getOffPayAmount() {
        return offPayAmount;
    }

    public void setOffPayAmount(String offPayAmount) {
        this.offPayAmount = offPayAmount;
    }

    public String getOnlinePayAmount() {
        return onlinePayAmount;
    }

    public void setOnlinePayAmount(String onlinePayAmount) {
        this.onlinePayAmount = onlinePayAmount;
    }

    public String getCurrencyMark() {
        return currencyMark;
    }

    public void setCurrencyMark(String currencyMark) {
        this.currencyMark = currencyMark;
    }
}
