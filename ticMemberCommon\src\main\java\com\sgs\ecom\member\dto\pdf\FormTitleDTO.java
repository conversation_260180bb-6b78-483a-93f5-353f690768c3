package com.sgs.ecom.member.dto.pdf;

import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.ValidationUtil;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.dto.OrderDetailDTO;
import com.sgs.ecom.member.dto.min.StylesDTO;
import com.sgs.ecom.member.dto.oiq.applicationInfo.OiqDetailFlgDTO;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
public class FormTitleDTO {

    public static final String REPORT_SAMPLE_NAME_FLG="reportSampleNameFlg";
    public static final String SAMPLE_SHAPE_CODE_FLG="sampleShapeCodeFlg";
    public static final String QUALIFICATIONS_FLG="qualificationsFlg";




    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private String applicationName="申请公司名称(中文)";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private String applicationAddress="申请公司地址(中文)";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private  String applicationLinkPerson="联系人";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private  String applicationLinkPhone="电话";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private  String applicationLinkEmail="电子邮箱";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private  String reportLua="报告语言";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private  String reportForm="报告形式";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private  String reportMethod="报告出具要求";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private  String reportCompanyNameCn="公司名称(中文)";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private  String reportCompanyNameEn="Company Name(英文大写)";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private  String reportAddressCn="公司地址(中文)";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private  String reportAddressEn="Address(英文大写)";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private  String sampleCategoryName="样品分类";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private  String sampleShapeName="样品状态";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private  String sampleNum="样品数量";
    //发票数据 或发票抬头
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private  String invoiceTitle="公司名称";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private  String invoiceEmail="发票发送邮箱";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private  String bankName="开户行";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private  String regAddress="公司地址";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private  String regPhone="电话";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private  String taxNo="税号";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private  String bankNumber="账号";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private  String invoiceType="发票类型";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private  String country="国家及地区";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private  String foreignCity="城市";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private  String postCode="邮编";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private  String contact="联系人";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private  String payerName="付款方联系人";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private  String payerPhone="手机";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private  String payerEmail="电子邮箱";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private  String itemName="测试项目";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private  String standardCode="测试方法";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private  String itemTestMemo="测试备注";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private  String buyNums="数量";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private  String applicationTestMemo="测试备注";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private  String isUrgent="测试周期";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private  String qualifications="盖章要求";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private  String reportEmail="报告发送邮箱";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private  String reportStr="报告邮寄地址";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private  String reportSampleName="报告显示品名";
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    @BeanAnno(dtocls = {StylesDTO.class})
    private List<StylesDTO> styles;

    public FormTitleDTO(ConcurrentHashMap<String,String> map, List<OrderDetailDTO> orderDetailDTOList) {
        if(ValidationUtil.isEmpty(map)){
            return;
        }
        List<StylesDTO> list=new ArrayList<>();

        if(map.containsKey("-application-linkPerson")){
            list.add(new StylesDTO("applicationLinkPerson"));
        }
        if(map.containsKey("-application-linkPhone")){
            list.add(new StylesDTO("applicationLinkPhone"));
        }
        if(map.containsKey("-application-linkEmail")){
            list.add(new StylesDTO("applicationLinkEmail"));
        }

        if(map.containsKey("-report-reportLuaValue")){
            list.add(new StylesDTO("reportLua"));
            list.add(new StylesDTO("applicationName"));
            list.add(new StylesDTO("applicationAddress"));
        }
        if(map.containsKey("-report-reportFormValue")){
            list.add(new StylesDTO("reportForm"));
        }
        if(map.containsKey("-report-reportMethodShow")){
            list.add(new StylesDTO("reportMethod"));
        }
        if(map.containsKey("-application-companyNameCn")){
            list.add(new StylesDTO("applicationName"));
        }
        if(map.containsKey("-application-companyNameEn")){
            list.add(new StylesDTO("applicationName"));
        }
        if(map.containsKey("-application-companyAddressCn")){
            list.add(new StylesDTO("applicationAddress"));
        }
        if(map.containsKey("-application-companyAddressEn")){
            list.add(new StylesDTO("applicationAddress"));
        }
        if(map.containsKey("-report-reportCompanyNameCn")){
            list.add(new StylesDTO("reportCompanyNameCn"));
        }
        if(map.containsKey("-report-reportCompanyNameEn")){
            list.add(new StylesDTO("reportCompanyNameEn"));
        }
        if(map.containsKey("-report-reportAddressCn")){
            list.add(new StylesDTO("reportAddressCn"));
        }
        if(map.containsKey("-report-reportAddressEn")){
            list.add(new StylesDTO("reportAddressEn"));
        }
        if(map.containsKey("-orderInvoice-payerName")){
            list.add(new StylesDTO("payerName"));
        }
        if(map.containsKey("-orderInvoice-payerPhone")){
            list.add(new StylesDTO("payerPhone"));
        }
        if(map.containsKey("-orderInvoice-payerEmail")){
            list.add(new StylesDTO("payerEmail"));
        }
        if(map.containsKey("-orderInvoice-invoice-invoiceTypeShow")){
            list.add(new StylesDTO("invoiceType"));
        }
        if(map.containsKey("-application-testMemo")){
            list.add(new StylesDTO("applicationTestMemo"));
        }
        if(map.containsKey("-orderBase-isUrgentShow")){
            list.add(new StylesDTO("isUrgent"));
        }

        if(map.containsKey("-orderInvoice-invoice-isForeign")){
            list.add(new StylesDTO("invoiceTitle"));
            list.add(new StylesDTO("bankName"));
            list.add(new StylesDTO("regAddress"));
            list.add(new StylesDTO("regPhone"));
            list.add(new StylesDTO("taxNo"));
            list.add(new StylesDTO("bankNumber"));
            list.add(new StylesDTO("country"));
            list.add(new StylesDTO("foreignCity"));
            list.add(new StylesDTO("postCode"));
            list.add(new StylesDTO("contact"));
        }else{
            if(map.containsKey("-orderInvoice-invoice-invoiceTitle")){
                list.add(new StylesDTO("invoiceTitle"));
            }
            if(map.containsKey("-orderInvoice-invoice-bankName")){
                list.add(new StylesDTO("bankName"));
            }
            if(map.containsKey("-orderInvoice-invoice-regAddress")){
                list.add(new StylesDTO("regAddress"));
            }
            if(map.containsKey("-orderInvoice-invoice-regPhone")){
                list.add(new StylesDTO("regPhone"));
            }
            if(map.containsKey("-orderInvoice-invoice-taxNo")){
                list.add(new StylesDTO("taxNo"));
            }
            if(map.containsKey("-orderInvoice-invoice-bankNumber")){
                list.add(new StylesDTO("bankNumber"));
            }
            if(map.containsKey("-orderInvoice-invoice-country")){
                list.add(new StylesDTO("country"));
            }
            if(map.containsKey("-orderInvoice-invoice-foreignCity")){
                list.add(new StylesDTO("foreignCity"));
            }
            if(map.containsKey("-orderInvoice-invoice-postCode")){
                list.add(new StylesDTO("postCode"));
            }
            if(map.containsKey("-orderInvoice-invoice-contact")){
                list.add(new StylesDTO("contact"));
            }
        }

        if(map.containsKey("sampleNumFlg")){
            list.add(new StylesDTO("sampleNum"));
        }


        if(map.containsKey(QUALIFICATIONS_FLG)){
            list.add(new StylesDTO("sampleShapeName"));
        }
        if(map.containsKey(SAMPLE_SHAPE_CODE_FLG)){
            list.add(new StylesDTO("sampleShapeName"));
        }
        if(map.containsKey(REPORT_SAMPLE_NAME_FLG)){
            list.add(new StylesDTO("reportSampleName"));
        }


        if(map.containsKey("reportStrFlg")){
            list.add(new StylesDTO("reportStr"));
        }

        if(map.containsKey("reportEmailFlg")){
            list.add(new StylesDTO("reportEmail"));
        }
        if(map.containsKey(OiqDetailFlgDTO.ITEM_FLG)){
            list.add(new StylesDTO("itemName"));
            list.add(new StylesDTO("standardCode"));
            list.add(new StylesDTO("itemTestMemo"));
            list.add(new StylesDTO("buyNums"));
        }else{
            for(int n=0;n<orderDetailDTOList.size();n++){
                String key="-itemList-"+n+"-";
                if(map.containsKey(key+"itemName")){
                    list.add(new StylesDTO("itemName"));
                }
                if(map.containsKey(key+"standardCode")){
                    list.add(new StylesDTO("standardCode"));
                }
                if(map.containsKey(key+"testMemo")){
                    list.add(new StylesDTO("itemTestMemo"));
                }
                if(map.containsKey(key+"buyNums")){
                    list.add(new StylesDTO("buyNums"));
                }
            }
        }

        //新的
        if(map.containsKey("qualificationsFlg")){
            list.add(new StylesDTO("qualifications"));
        }


        this.styles=list;

    }

    public String getApplicationName() {
        return applicationName;
    }

    public void setApplicationName(String applicationName) {
        this.applicationName = applicationName;
    }

    public String getApplicationAddress() {
        return applicationAddress;
    }

    public void setApplicationAddress(String applicationAddress) {
        this.applicationAddress = applicationAddress;
    }

    public String getApplicationLinkPerson() {
        return applicationLinkPerson;
    }

    public void setApplicationLinkPerson(String applicationLinkPerson) {
        this.applicationLinkPerson = applicationLinkPerson;
    }

    public String getApplicationLinkPhone() {
        return applicationLinkPhone;
    }

    public void setApplicationLinkPhone(String applicationLinkPhone) {
        this.applicationLinkPhone = applicationLinkPhone;
    }

    public String getApplicationLinkEmail() {
        return applicationLinkEmail;
    }

    public void setApplicationLinkEmail(String applicationLinkEmail) {
        this.applicationLinkEmail = applicationLinkEmail;
    }

    public String getReportLua() {
        return reportLua;
    }

    public void setReportLua(String reportLua) {
        this.reportLua = reportLua;
    }

    public String getReportForm() {
        return reportForm;
    }

    public void setReportForm(String reportForm) {
        this.reportForm = reportForm;
    }

    public String getReportMethod() {
        return reportMethod;
    }

    public void setReportMethod(String reportMethod) {
        this.reportMethod = reportMethod;
    }

    public String getReportCompanyNameCn() {
        return reportCompanyNameCn;
    }

    public void setReportCompanyNameCn(String reportCompanyNameCn) {
        this.reportCompanyNameCn = reportCompanyNameCn;
    }

    public String getReportCompanyNameEn() {
        return reportCompanyNameEn;
    }

    public void setReportCompanyNameEn(String reportCompanyNameEn) {
        this.reportCompanyNameEn = reportCompanyNameEn;
    }

    public String getReportAddressCn() {
        return reportAddressCn;
    }

    public void setReportAddressCn(String reportAddressCn) {
        this.reportAddressCn = reportAddressCn;
    }

    public String getReportAddressEn() {
        return reportAddressEn;
    }

    public void setReportAddressEn(String reportAddressEn) {
        this.reportAddressEn = reportAddressEn;
    }

    public String getSampleCategoryName() {
        return sampleCategoryName;
    }

    public void setSampleCategoryName(String sampleCategoryName) {
        this.sampleCategoryName = sampleCategoryName;
    }

    public String getSampleShapeName() {
        return sampleShapeName;
    }

    public void setSampleShapeName(String sampleShapeName) {
        this.sampleShapeName = sampleShapeName;
    }

    public String getSampleNum() {
        return sampleNum;
    }

    public void setSampleNum(String sampleNum) {
        this.sampleNum = sampleNum;
    }

    public String getInvoiceTitle() {
        return invoiceTitle;
    }

    public void setInvoiceTitle(String invoiceTitle) {
        this.invoiceTitle = invoiceTitle;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getRegAddress() {
        return regAddress;
    }

    public void setRegAddress(String regAddress) {
        this.regAddress = regAddress;
    }

    public String getRegPhone() {
        return regPhone;
    }

    public void setRegPhone(String regPhone) {
        this.regPhone = regPhone;
    }

    public String getTaxNo() {
        return taxNo;
    }

    public void setTaxNo(String taxNo) {
        this.taxNo = taxNo;
    }

    public String getBankNumber() {
        return bankNumber;
    }

    public void setBankNumber(String bankNumber) {
        this.bankNumber = bankNumber;
    }

    public String getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(String invoiceType) {
        this.invoiceType = invoiceType;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getForeignCity() {
        return foreignCity;
    }

    public void setForeignCity(String foreignCity) {
        this.foreignCity = foreignCity;
    }

    public String getPostCode() {
        return postCode;
    }

    public void setPostCode(String postCode) {
        this.postCode = postCode;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public String getPayerName() {
        return payerName;
    }

    public void setPayerName(String payerName) {
        this.payerName = payerName;
    }

    public String getPayerPhone() {
        return payerPhone;
    }

    public void setPayerPhone(String payerPhone) {
        this.payerPhone = payerPhone;
    }

    public String getPayerEmail() {
        return payerEmail;
    }

    public void setPayerEmail(String payerEmail) {
        this.payerEmail = payerEmail;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getStandardCode() {
        return standardCode;
    }

    public void setStandardCode(String standardCode) {
        this.standardCode = standardCode;
    }

    public String getItemTestMemo() {
        return itemTestMemo;
    }

    public void setItemTestMemo(String itemTestMemo) {
        this.itemTestMemo = itemTestMemo;
    }

    public String getBuyNums() {
        return buyNums;
    }

    public void setBuyNums(String buyNums) {
        this.buyNums = buyNums;
    }

    public String getApplicationTestMemo() {
        return applicationTestMemo;
    }

    public void setApplicationTestMemo(String applicationTestMemo) {
        this.applicationTestMemo = applicationTestMemo;
    }

    public String getIsUrgent() {
        return isUrgent;
    }

    public void setIsUrgent(String isUrgent) {
        this.isUrgent = isUrgent;
    }

    public String getQualifications() {
        return qualifications;
    }

    public void setQualifications(String qualifications) {
        this.qualifications = qualifications;
    }

    public String getReportEmail() {
        return reportEmail;
    }

    public void setReportEmail(String reportEmail) {
        this.reportEmail = reportEmail;
    }

    public String getReportStr() {
        return reportStr;
    }

    public void setReportStr(String reportStr) {
        this.reportStr = reportStr;
    }

    public List<StylesDTO> getStyles() {
        return styles;
    }

    public void setStyles(List<StylesDTO> styles) {
        this.styles = styles;
    }

    public String getReportSampleName() {
        return reportSampleName;
    }

    public void setReportSampleName(String reportSampleName) {
        this.reportSampleName = reportSampleName;
    }

    public String getInvoiceEmail() {
        return invoiceEmail;
    }

    public void setInvoiceEmail(String invoiceEmail) {
        this.invoiceEmail = invoiceEmail;
    }
}
