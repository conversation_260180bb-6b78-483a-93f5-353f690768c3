package com.sgs.ecom.member.dto.user;
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import com.platform.annotation.ApiAnno; 
import com.platform.annotation.BeanAnno; 
import com.sgs.base.BaseQryFilter; 

public class UserInfoDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select USER_NICK,LEVEL_NAME,USER_SEX,USER_EMAIL,STATE_DATE,USER_ID,STATE,REG_IP,USER_NAME,U_ID,LEVEL_ID,USER_PHONE,CREATE_DATE,BBC_PWD,IS_BLACK,USER_PWD from USER_INFO"; 
 
 
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="USER_NICK", getName="getUserNick", setName="setUserNick")
 	private String userNick;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="LEVEL_NAME", getName="getLevelName", setName="setLevelName")
 	private String levelName;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="USER_SEX", getName="getUserSex", setName="setUserSex")
 	private int userSex;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="USER_EMAIL", getName="getUserEmail", setName="setUserEmail")
 	private String userEmail;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private Timestamp stateDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="USER_ID", getName="getUserId", setName="setUserId")
 	private long userId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="REG_IP", getName="getRegIp", setName="setRegIp")
 	private String regIp;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="USER_NAME", getName="getUserName", setName="setUserName")
 	private String userName;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="U_ID", getName="getUId", setName="setUId")
 	private long uId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="LEVEL_ID", getName="getLevelId", setName="setLevelId")
 	private int levelId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="USER_PHONE", getName="getUserPhone", setName="setUserPhone")
 	private String userPhone;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="BBC_PWD", getName="getBbcPwd", setName="setBbcPwd")
 	private String bbcPwd;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="IS_BLACK", getName="getIsBlack", setName="setIsBlack")
 	private int isBlack;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="USER_PWD", getName="getUserPwd", setName="setUserPwd")
 	private String userPwd;

 	public void setUserNick(String userNick){
 		 this.userNick=userNick;
 	}
 	public String getUserNick(){
 		 return this.userNick;
 	}
 
 	 
 	public void setLevelName(String levelName){
 		 this.levelName=levelName;
 	}
 	public String getLevelName(){
 		 return this.levelName;
 	}
 
 	 
 	public void setUserSex(int userSex){
 		 this.userSex=userSex;
 	}
 	public int getUserSex(){
 		 return this.userSex;
 	}
 
 	 
 	public void setUserEmail(String userEmail){
 		 this.userEmail=userEmail;
 	}
 	public String getUserEmail(){
 		 return this.userEmail;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setUserId(long userId){
 		 this.userId=userId;
 	}
 	public long getUserId(){
 		 return this.userId;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setRegIp(String regIp){
 		 this.regIp=regIp;
 	}
 	public String getRegIp(){
 		 return this.regIp;
 	}
 
 	 
 	public void setUserName(String userName){
 		 this.userName=userName;
 	}
 	public String getUserName(){
 		 return this.userName;
 	}
 
 	 
 	public void setUId(long uId){
 		 this.uId=uId;
 	}
 	public long getUId(){
 		 return this.uId;
 	}
 
 	 
 	public void setLevelId(int levelId){
 		 this.levelId=levelId;
 	}
 	public int getLevelId(){
 		 return this.levelId;
 	}
 
 	 
 	public void setUserPhone(String userPhone){
 		 this.userPhone=userPhone;
 	}
 	public String getUserPhone(){
 		 return this.userPhone;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setBbcPwd(String bbcPwd){
 		 this.bbcPwd=bbcPwd;
 	}
 	public String getBbcPwd(){
 		 return this.bbcPwd;
 	}
 
 	 
 	public void setIsBlack(int isBlack){
 		 this.isBlack=isBlack;
 	}
 	public int getIsBlack(){
 		 return this.isBlack;
 	}
 
 	 
 	public void setUserPwd(String userPwd){
 		 this.userPwd=userPwd;
 	}
 	public String getUserPwd(){
 		 return this.userPwd;
 	}
 
 	 
}