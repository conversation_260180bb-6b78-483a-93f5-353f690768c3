package com.sgs.ecom.member.fegin;

import com.platform.annotation.AuthRequired;
import com.platform.bo.<PERSON><PERSON>ys<PERSON>erson;
import com.sgs.base.BaseBean;
import com.sgs.ecom.member.enumtool.event.EventEnum;
import com.sgs.ecom.member.event.EventApiUtil;
import com.sgs.ecom.member.request.OrderBaseAddReq;
import com.sgs.ecom.member.request.PayMethodReq;
import com.sgs.ecom.member.request.*;
import com.sgs.ecom.member.request.file.ZipFiles;
import com.sgs.ecom.member.request.order.OrderEditReq;
import com.sgs.ecom.member.request.order.OrderHeadReq;
import com.sgs.ecom.member.request.pdf.OrderWordPrintReq;
import com.sgs.ecom.member.request.rpc.CenterCouponReq;
import com.sgs.ecom.member.service.pay.interfaces.IPayService;
import com.sgs.ecom.member.service.template.interfaces.ICenterRestTemplateService;
import com.sgs.ecom.member.service.util.interfaces.IOrderOperatorService;
import com.sgs.ecom.member.service.util.interfaces.IOrderRpcService;
import com.sgs.ecom.member.service.util.interfaces.IOrderService;
import com.sgs.ecom.member.service.util.interfaces.IPayUtilService;
import com.sgs.ecom.member.util.ResultBody;
import com.sgs.ecom.member.vo.sys.VOSysSampleBasic;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.concurrent.ConcurrentHashMap;

/**
*@Function:
*@Description
*@param: order调用member使用的rpc
*@author: Xiwei_Qiu @date: 2022/9/26 @version:
**/
@RestController
@RequestMapping("/business/rpc.v2.member/order")
public class OrderServiceRpc {

	@Autowired
	private IOrderRpcService orderRpcService;
	@Autowired
	private IPayService payService;
	@Resource
	private ICenterRestTemplateService centerRestTemplateService;
	@Resource
	private IPayUtilService payUtilService;
	@Autowired
	private EventApiUtil eventApiUtil;
	@Resource
	private IOrderOperatorService iOrderOperatorService;



	@RequestMapping(value = "saveCheckDate", method = { RequestMethod.POST })
	public ResultBody orderToInfo(
		@RequestParam(name = "orderNo", required = true) String orderNo,
		@RequestParam(name = "checkDate", required = true) String checkDate) throws Exception{
		orderRpcService.saveCheckDate(orderNo,checkDate);
		return ResultBody.success();
	}


	@RequestMapping(value = "copyQuestion", method = { RequestMethod.POST })
	public ResultBody copyQuestion(
		@RequestParam(name = "orderNo", required = true) String orderNo,
	    @RequestParam(name = "isOriginal", required = true) int isOriginal) throws Exception{
		return ResultBody.newInstance(orderRpcService.copyQuestion(orderNo,isOriginal));
	}



	@RequestMapping(value = "getFormPDFFileList", method = { RequestMethod.POST })
	public ResultBody getOrderPDF(
			@RequestParam(name = "orderNo", required = true) String orderNo) throws Exception {
		return ResultBody.newInstance(orderRpcService.getFormPDFFileList(orderNo,new ConcurrentHashMap<>()));
	}


	/**
	*@Function: saveQuestion
	*@Description 保存问卷数据
	*@param: [orderNo]
	*@author: Xiwei_Qiu @date: 2022/5/31 @version:
	**/
	@RequestMapping(value = "saveQuestion", method = { RequestMethod.POST })
	public ResultBody saveQuestion(
		@RequestBody OrderBaseAddReq orderBaseAddReq) throws Exception{
		orderRpcService.saveQuestion(orderBaseAddReq);
		return ResultBody.success();
	}

	/**
	 *@Function: saveQuestion
	 *@Description 获取问卷数据
	 *@param: [orderNo]
	 *@author: Xiwei_Qiu @date: 2022/5/31 @version:
	 **/
	@RequestMapping(value = "getQuestion", method = { RequestMethod.POST })
	public ResultBody getQuestion(
		@RequestParam(name = "orderNo", required = true) String orderNo,
		@RequestParam(name = "isOriginal", required = true) int isOriginal) throws Exception{
		return ResultBody.newInstance(orderRpcService.getQuestion(orderNo,isOriginal));
	}

	/**
	 *@Function: qryLabBankDTO
	 *@Description 获取实验室信息
	 *@param: [orderNo]
	 *@author: Xiwei_Qiu @date: 2022/5/31 @version:
	 **/
	@RequestMapping(value = "qryLabBankByLabId", method = { RequestMethod.POST })
	public ResultBody qryLabBankByLabId(
			@RequestParam(name = "labId", required = true) Long lab) throws Exception{
		return ResultBody.newInstance(payService.qryLabBankByLabId(lab,null));
	}



	@RequestMapping(value = "saveCouponList", method = { RequestMethod.POST })
	public ResultBody saveCouponList (
		@RequestBody CenterCouponReq centerCouponReq)throws Exception{
		orderRpcService.saveCouponList(centerCouponReq);
		return ResultBody.success();
	}

	/**
	*@Function: copyOrder
	*@Description 根据询价单生产订单
	*@param: [token, orderNoReq]
	*@author: Xiwei_Qiu @date: 2022/10/11 @version:
	**/
	@RequestMapping(value = "copyOrder", method = {RequestMethod.POST})
	public ResultBody copyOrder(
		@RequestBody OrderEditReq orderEditReq
	) throws Exception {
		return ResultBody.newInstance(orderRpcService.copyOrder(orderEditReq));
	}

	/**
	 * @Description :调取member查询动态样品的数据信息
	 * <AUTHOR> Zhang
	 * @Date  2023/12/14
	 * @param voSysSampleBasic: 
	 * @return: com.sgs.ecom.member.util.ResultBody
	 **/
	@RequestMapping(value = "qryDynamicSample", method = { RequestMethod.POST })
	public ResultBody qryDynamicSample(
			@RequestBody VOSysSampleBasic voSysSampleBasic) throws Exception{
		return ResultBody.newInstance(centerRestTemplateService.qrySampleBasic(voSysSampleBasic));
	}


	@RequestMapping(value = "modPayMethod", method = { RequestMethod.POST })
	public ResultBody modPayMethod (
			@Validated(BaseBean.Update.class)
			@RequestBody PayMethodReq payMethodReq)throws Exception{
		payUtilService.modPayMethod(payMethodReq);
		return ResultBody.success();
	}


	@RequestMapping(value = "payToQuotationConfirm", method = { RequestMethod.POST })
	public ResultBody payToQuotationConfirm(@RequestBody OrderEditReq orderEditReq) throws Exception {
		BOSysPerson boSysPerson=new BOSysPerson();
		if(StringUtils.isNotBlank(orderEditReq.getCsCode())){
			boSysPerson.setPersonCode(orderEditReq.getCsCode());
		}

		eventApiUtil.saveEvent(orderEditReq.getOrderNo(), EventEnum.PAY_TO_QUOTATION_CONFIRM,boSysPerson);
		return ResultBody.success();
	}

	/**
	 * @Description :接口通过订单号，生成对应的pdf文件
	 * <AUTHOR> Zhang
	 * @Date  2024/7/5
	 * @param orderNoReq:
	 * @return: com.sgs.ecom.member.util.ResultBody
	 **/
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "generateOrderPdf", method = { RequestMethod.POST })
	public ResultBody generateOrderPdf (
			@Validated(value = BaseBean.Query.class)
			@RequestBody OrderPrintReq orderNoReq) throws Exception {
		return ResultBody.newInstance(orderRpcService.generateOrderPdf(orderNoReq));
	}

	/**
	 * @Description :接口通过订单号，生成对应的pdf文件-- order使用
	 * <AUTHOR> Zhang
	 * @Date  2024/7/5
	 * @param orderNoReq:
	 * @return: com.sgs.ecom.member.util.ResultBody
	 **/
	@AuthRequired(login = "NULL", sign = true)
	@RequestMapping(value = "orderGenerateOrderPdf", method = { RequestMethod.POST })
	public ResultBody orderGenerateOrderPdf (
			@Validated(value = BaseBean.Query.class)
			@RequestBody OrderPrintReq orderNoReq) throws Exception {
		return ResultBody.newInstance(orderRpcService.generateOrderPdf(orderNoReq));
	}


	@RequestMapping(value = "getWordPdf", method = { RequestMethod.POST })
	public ResultBody getWordPdf (
			@Validated(value = BaseBean.Query.class)
			@RequestBody OrderWordPrintReq orderWordPrintReq) throws Exception {
		return ResultBody.newInstance(orderRpcService.getWordPdf(orderWordPrintReq));
	}

	@RequestMapping(value = "getWordStream", method = { RequestMethod.POST })
	public void getWordStream (
			@RequestBody OrderWordPrintReq orderWordPrintReq,
			HttpServletResponse httpServletResponse) throws Exception {
		orderRpcService.getWordStream(orderWordPrintReq,httpServletResponse);
	}

	/**
	 * @Description :https://cnjira.sgs.net/browse/TIC-27254 对接sl按样品打印申请表
	 * <AUTHOR> Zhang
	 * @Date  2024/10/9
	 * @param orderNoReq:
	 * @return: void
	 **/
	@AuthRequired(login = "NULL", sign = false)
	@RequestMapping(value = "downloadFormZip", method = { RequestMethod.POST })
	public ResultBody downloadFormZip (
			@Validated(value = BaseBean.Query.class)
			@RequestBody OrderPrintReq orderNoReq,
			HttpServletResponse res) throws Exception {
		ZipFiles zipFiles = orderRpcService.downloadFormZip(orderNoReq, res);
		// 清除fileId
		zipFiles.getFiles().forEach(x -> x.setFileId(null));
		return ResultBody.newInstance(zipFiles);
	}

	/**
	 * @Description :https://cnjira.sgs.net/browse/TIC-27254 对接sl按样品打印申请表
	 * <AUTHOR> Zhang
	 * @Date  2024/10/9
	 * @param orderNoReq:
	 * @return: void
	 **/
	@AuthRequired(login = "NULL", sign = false)
	@RequestMapping(value = "downloadFormZipTest", method = { RequestMethod.POST })
	public void downloadFormZipTest (
			@Validated(value = BaseBean.Query.class)
			@RequestBody OrderPrintReq orderNoReq,
			HttpServletResponse res) throws Exception {
		orderRpcService.downloadFormZipFileStream(orderNoReq,res);
	}

	/**
	 * @Description :https://cnjira.sgs.net/browse/TIC-28644 【功能优化】portal站OIQ最近确认报价订单列表
	 * <AUTHOR>
	 * @Date  2025/1/1
	 * @param bu:
	 * @return: void
	 **/
	@AuthRequired(login = "NULL", sign = false)
	@RequestMapping(value = "refreshCacheHeaderList", method = { RequestMethod.POST })
	public void refreshCacheHeaderList (
			@RequestParam(value = "orderNo") String orderNo,
			@RequestParam(value = "bu") String bu,
			@RequestParam(value = "delFlag") String delFlag) {
		iOrderOperatorService.refreshCacheHeaderList(orderNo, bu, delFlag);
	}
}
