<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.mapper.question.UserQuPartMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.vo.VOUserQuPart" >
    <id column="PART_ID" property="partId" jdbcType="BIGINT" />
    <result column="REPLY_ID" property="replyId" jdbcType="BIGINT" />
    <result column="QUESTION_ID" property="questionId" jdbcType="BIGINT" />
    <result column="PART_NO" property="partNo" jdbcType="VARCHAR" />
    <result column="PART_NAME" property="partName" jdbcType="VARCHAR" />
    <result column="PART_NAME_EN" property="partNameEn" jdbcType="VARCHAR" />
    <result column="SORT_SHOW" property="sortShow" jdbcType="INTEGER" />
    <result column="FINAL_SCORE" property="finalScore" jdbcType="INTEGER" />
    <result column="REAL_SCORE" property="realScore" jdbcType="INTEGER" />
    <result column="PART_SCORE" property="partScore" jdbcType="INTEGER" />
    <result column="STATE" property="state" jdbcType="TINYINT" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="POSITION" property="position" jdbcType="BIGINT" />
  </resultMap>
  <sql id="Base_Column_List" >
    PART_ID, REPLY_ID, QUESTION_ID, PART_NO, PART_NAME, PART_NAME_EN, SORT_SHOW, FINAL_SCORE, 
    REAL_SCORE, PART_SCORE, STATE, CREATE_DATE, STATE_DATE, ORDER_NO, USER_ID,POSITION
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from TB_USER_QU_PART
    where PART_ID = #{partId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from TB_USER_QU_PART
    where PART_ID = #{partId,jdbcType=BIGINT}
  </delete>

  <insert id="insertSelective" parameterType="com.sgs.ecom.member.vo.VOUserQuPart" >
    insert into TB_USER_QU_PART
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="partId != null" >
        PART_ID,
      </if>
      <if test="replyId != null" >
        REPLY_ID,
      </if>
      <if test="questionId != null" >
        QUESTION_ID,
      </if>
      <if test="partNo != null" >
        PART_NO,
      </if>
      <if test="partName != null" >
        PART_NAME,
      </if>
      <if test="partNameEn != null" >
        PART_NAME_EN,
      </if>
      <if test="sortShow != null" >
        SORT_SHOW,
      </if>
      <if test="finalScore != null" >
        FINAL_SCORE,
      </if>
      <if test="realScore != null" >
        REAL_SCORE,
      </if>
      <if test="partScore != null" >
        PART_SCORE,
      </if>
      <if test="state != null" >
        STATE,
      </if>
      <if test="createDate != null" >
        CREATE_DATE,
      </if>
      <if test="stateDate != null" >
        STATE_DATE,
      </if>
      <if test="orderNo != null" >
        ORDER_NO,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
      <if test="position != null" >
        POSITION,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="partId != null" >
        #{partId,jdbcType=BIGINT},
      </if>
      <if test="replyId != null" >
        #{replyId,jdbcType=BIGINT},
      </if>
      <if test="questionId != null" >
        #{questionId,jdbcType=BIGINT},
      </if>
      <if test="partNo != null" >
        #{partNo,jdbcType=VARCHAR},
      </if>
      <if test="partName != null" >
        #{partName,jdbcType=VARCHAR},
      </if>
      <if test="partNameEn != null" >
        #{partNameEn,jdbcType=VARCHAR},
      </if>
      <if test="sortShow != null" >
        #{sortShow,jdbcType=INTEGER},
      </if>
      <if test="finalScore != null" >
        #{finalScore,jdbcType=INTEGER},
      </if>
      <if test="realScore != null" >
        #{realScore,jdbcType=INTEGER},
      </if>
      <if test="partScore != null" >
        #{partScore,jdbcType=INTEGER},
      </if>
      <if test="state != null" >
        #{state,jdbcType=TINYINT},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        #{stateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="position != null" >
        #{position},
      </if>
    </trim>
  </insert>
   <update id="updateByPrimaryKeySelective" parameterType="com.sgs.ecom.member.vo.VOUserQuPart" >
    update TB_USER_QU_PART
    <set >
      <if test="replyId != null" >
        REPLY_ID = #{replyId,jdbcType=BIGINT},
      </if>
      <if test="questionId != null" >
        QUESTION_ID = #{questionId,jdbcType=BIGINT},
      </if>
      <if test="partNo != null" >
        PART_NO = #{partNo,jdbcType=VARCHAR},
      </if>
      <if test="partName != null" >
        PART_NAME = #{partName,jdbcType=VARCHAR},
      </if>
      <if test="partNameEn != null" >
        PART_NAME_EN = #{partNameEn,jdbcType=VARCHAR},
      </if>
      <if test="sortShow != null" >
        SORT_SHOW = #{sortShow,jdbcType=INTEGER},
      </if>
      <if test="finalScore != null" >
        FINAL_SCORE = #{finalScore,jdbcType=INTEGER},
      </if>
      <if test="realScore != null" >
        REAL_SCORE = #{realScore,jdbcType=INTEGER},
      </if>
      <if test="partScore != null" >
        PART_SCORE = #{partScore,jdbcType=INTEGER},
      </if>
      <if test="state != null" >
        STATE = #{state,jdbcType=TINYINT},
      </if>
      <if test="createDate != null" >
        CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        STATE_DATE = #{stateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="orderNo != null" >
        ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        USER_ID = #{userId,jdbcType=BIGINT},
      </if>
    </set>
    where PART_ID = #{partId,jdbcType=BIGINT}
  </update>



  <!--自定义-->
  <update id="updateBatchBindUserId" parameterType="com.sgs.ecom.member.vo.VOUserQuPart" >
    update TB_USER_QU_PART
    <set >
      <if test="replyId != null" >
        REPLY_ID = #{replyId,jdbcType=BIGINT},
      </if>
      <if test="userId != null" >
        USER_ID = #{userId,jdbcType=BIGINT},
      </if>
    </set>
    where REPLY_ID = #{replyId,jdbcType=BIGINT}
  </update>

  <select id="selectCountByMap" resultType="java.lang.Integer">
    select
    count(PART_ID)
    from TB_USER_QU_PART
    <include refid="baseQueryWhere"/>
  </select>

  <select id="selectListByMap" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from TB_USER_QU_PART
    <include refid="baseQueryWhere"/>
  </select>



  <sql id="baseQueryWhere">
    where 1=1
    <if test="replyId != null">
      AND REPLY_ID=#{replyId}
    </if>
    <if test="orderNo != null">
      AND ORDER_NO=#{orderNo}
    </if>
    <if test="partNo != null">
      AND PART_NO=#{partNo}
    </if>
    <if test="positionList != null ">
      AND POSITION in
      <foreach collection="positionList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </sql>


  <insert id="insertSelectiveForeach"  >
    insert into TB_USER_QU_PART (
    REPLY_ID, QUESTION_ID, PART_NO, PART_NAME, PART_NAME_EN, SORT_SHOW, FINAL_SCORE,
    REAL_SCORE, PART_SCORE, STATE, CREATE_DATE, STATE_DATE, ORDER_NO, USER_ID,POSITION
    )
    values
    <foreach collection ="list" item="part" index= "index" separator =",">
      (
        #{part.replyId,jdbcType=BIGINT},
        #{part.questionId,jdbcType=BIGINT},
        #{part.partNo,jdbcType=VARCHAR},
        #{part.partName,jdbcType=VARCHAR},
        #{part.partNameEn,jdbcType=VARCHAR},
        #{part.sortShow,jdbcType=INTEGER},
        #{part.finalScore,jdbcType=INTEGER},
        #{part.realScore,jdbcType=INTEGER},
        #{part.partScore,jdbcType=INTEGER},
        #{part.state,jdbcType=TINYINT},
        #{part.createDate,jdbcType=TIMESTAMP},
        #{part.stateDate,jdbcType=TIMESTAMP},
        #{part.orderNo,jdbcType=VARCHAR},
        #{part.userId,jdbcType=BIGINT},
        #{part.position}
      )
    </foreach>
  </insert>





</mapper>