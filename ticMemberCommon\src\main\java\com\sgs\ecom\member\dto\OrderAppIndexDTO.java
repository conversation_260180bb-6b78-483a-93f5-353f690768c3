package com.sgs.ecom.member.dto;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.dto.custom.StateAndNumDTO;

import java.util.List;

public class OrderAppIndexDTO {
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private List<StateAndNumDTO> orderIndexDTOList;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private OrderOperatorLogDTO orderOperatorLogDTO;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private OrderBaseInfoDTO orderBaseInfoDTO;

    public List<StateAndNumDTO> getOrderIndexDTOList() {
        return orderIndexDTOList;
    }

    public void setOrderIndexDTOList(List<StateAndNumDTO> orderIndexDTOList) {
        this.orderIndexDTOList = orderIndexDTOList;
    }

    public OrderOperatorLogDTO getOrderOperatorLogDTO() {
        return orderOperatorLogDTO;
    }

    public void setOrderOperatorLogDTO(OrderOperatorLogDTO orderOperatorLogDTO) {
        this.orderOperatorLogDTO = orderOperatorLogDTO;
    }

    public OrderBaseInfoDTO getOrderBaseInfoDTO() {
        return orderBaseInfoDTO;
    }

    public void setOrderBaseInfoDTO(OrderBaseInfoDTO orderBaseInfoDTO) {
        this.orderBaseInfoDTO = orderBaseInfoDTO;
    }
}
