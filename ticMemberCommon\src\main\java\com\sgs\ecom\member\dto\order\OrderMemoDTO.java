package com.sgs.ecom.member.dto.order;

import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.dto.detail.OrderAttachmentDTO;


import java.util.ArrayList;
import java.util.List;

public class OrderMemoDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select MEMO_INFO,CREATE_DATE,STATE_DATE,STATE,ORDER_NO,ID,MEMO_CODE from ORDER_MEMO"; 
 
 
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="MEMO_INFO", getName="getMemoInfo", setName="setMemoInfo")
 	private String memoInfo;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private String createDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private String stateDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="ORDER_NO", getName="getOrderNo", setName="setOrderNo")
 	private String orderNo;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="ID", getName="getId", setName="setId")
 	private long id;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="MEMO_CODE", getName="getMemoCode", setName="setMemoCode")
 	private String memoCode;
	private String personCode;

	@ApiAnno(groups={Default.class})
	private List<OrderAttachmentDTO> fileList=new ArrayList<>();

	public List<OrderAttachmentDTO> getFileList() {
		return fileList;
	}

	public void setFileList(List<OrderAttachmentDTO> fileList) {
		this.fileList = fileList;
	}

	public void setMemoInfo(String memoInfo){
 		 this.memoInfo=memoInfo;
 	}
 	public String getMemoInfo(){
 		 return this.memoInfo;
 	}


	public String getCreateDate() {
		String[] newstr = createDate.split("\\.");
		return newstr[0];
	}

	public void setCreateDate(String createDate) {
		this.createDate = createDate;
	}

	public String getStateDate() {
		String[] newstr = createDate.split("\\.");
		return newstr[0];
	}

	public void setStateDate(String stateDate) {
		this.stateDate = stateDate;
	}

	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setOrderNo(String orderNo){
 		 this.orderNo=orderNo;
 	}
 	public String getOrderNo(){
 		 return this.orderNo;
 	}
 
 	 
 	public void setId(long id){
 		 this.id=id;
 	}
 	public long getId(){
 		 return this.id;
 	}
 
 	 
 	public void setMemoCode(String memoCode){
 		 this.memoCode=memoCode;
 	}
 	public String getMemoCode(){
 		 return this.memoCode;
 	}


	public String getPersonCode() {
		return personCode;
	}

	public void setPersonCode(String personCode) {
		this.personCode = personCode;
	}
}