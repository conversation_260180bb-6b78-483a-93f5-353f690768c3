package com.sgs.ecom.member.dto.oiq;

import com.alibaba.fastjson.JSON;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.ValidationUtil;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.dto.application.ApplicationDefaultDTO;
import com.sgs.ecom.member.dto.application.OrderApplicationAttrDTO;
import com.sgs.ecom.member.dto.base.BaseOrderDTO;
import com.sgs.ecom.member.dto.center.EnumDTO;
import com.sgs.ecom.member.dto.detail.ItemCategoryDTO;
import com.sgs.ecom.member.dto.oiq.applicationInfo.OiqApplicationDTO;
import com.sgs.ecom.member.dto.oiq.applicationInfo.OiqItemDTO;
import com.sgs.ecom.member.dto.oiq.applicationInfo.OiqOrderBaseDTO;
import com.sgs.ecom.member.dto.oiq.applicationInfo.OiqOrderInvoiceDTO;
import com.sgs.ecom.member.dto.oiq.applicationInfo.OiqReportDTO;
import com.sgs.ecom.member.dto.oiq.applicationInfo.OiqSampleDTO;
import com.sgs.ecom.member.dto.oiq.applicationInfo.OrderShippingContactDTO;
import com.sgs.ecom.member.dto.oiq.applicationInfo.OrderShippingDTO;
import com.sgs.ecom.member.dto.order.CustomerIdDTO;
import com.sgs.ecom.member.dto.sample.SampleCategoryDTO;
import com.sgs.ecom.member.enumtool.OrderTypeEnum;
import com.sgs.ecom.member.request.oiq.OiqQryInfoReq;
import com.sgs.ecom.member.util.collection.StrUtil;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class OiqOrderDTO {

    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private OiqApplicationDTO application=new OiqApplicationDTO();
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private OiqReportDTO report=new OiqReportDTO();
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private OiqOrderInvoiceDTO orderInvoice=new OiqOrderInvoiceDTO();
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private List<OiqItemDTO> itemList=new ArrayList<>();
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    @BeanAnno(dtocls = {OiqSampleDTO.class})
    private List<OiqSampleDTO> sampleList=new ArrayList<>();
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private OiqOrderBaseDTO orderBase=new OiqOrderBaseDTO();
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String orderNo;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private List<CustomerIdDTO> customerList=new ArrayList<>();
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String tails;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private SampleCategoryDTO sampleCategory=new SampleCategoryDTO();
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private ItemCategoryDTO itemCategory=new ItemCategoryDTO();
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private OrderShippingDTO orderShipping=new OrderShippingDTO();

    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private OrderShippingContactDTO shipper=new OrderShippingContactDTO();
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private OrderShippingContactDTO consignee=new OrderShippingContactDTO();
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private OrderShippingContactDTO notify=new OrderShippingContactDTO();
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private OrderShippingContactDTO manufacture=new OrderShippingContactDTO();
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    @BeanAnno(dtocls = {OrderApplicationAttrDTO.class})
    private List<OrderApplicationAttrDTO> formAttrList=new ArrayList<>();
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    @BeanAnno(dtocls = {OrderApplicationAttrDTO.class})
    private List<OrderApplicationAttrDTO> destinationList=new ArrayList<>();
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    @BeanAnno(dtocls = {OrderApplicationAttrDTO.class})
    private List<OrderApplicationAttrDTO> costInformationList=new ArrayList<>();
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    @BeanAnno(dtocls = {OrderApplicationAttrDTO.class})
    private List<OrderApplicationAttrDTO> goods=new ArrayList<>();
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    @BeanAnno(dtocls = {OrderApplicationAttrDTO.class})
    private List<OrderApplicationAttrDTO> documentsInfo=new ArrayList<>();


    //是否使用用户订单，用户指偏好 0否 1是
    private int useUseOrder;//
    //当前订单基础对象
    private BaseOrderDTO currentOrder;
    private Boolean isPortal=true;
    private Boolean applicationLinkNullFlg=false;
    private String distinctLinkEmail;


    public OiqOrderDTO() {
    }

    public OiqOrderDTO(OiqQryInfoReq oiqQryInfoReq, EnumDTO enumDTO) {
        this.orderNo=oiqQryInfoReq.getOrderNo();
        this.currentOrder = oiqQryInfoReq.getOldOrder();
        //当有旧的单号 且 类型是询价单的才是询价单
        if(!ValidationUtil.isEmpty(oiqQryInfoReq.getOldOrder()) && OrderTypeEnum.OIQORDER.getIndex().equals(oiqQryInfoReq.getOldOrder().getOrderType())){
            this.isPortal=false;
        }
        //设置初始默认值
        if(!ValidationUtil.isEmpty(enumDTO)){
            String value=enumDTO.getEnumExtend();
            ApplicationDefaultDTO applicationDefaultDTO = JSON.parseObject(value, ApplicationDefaultDTO.class);
            // 报告要求
            initReportInfo(applicationDefaultDTO);
            // 样品信息
            initSampleInfo(applicationDefaultDTO);
            // 服务要求
            initServiceInfo(applicationDefaultDTO);
        }
    }

    private void initReportInfo(ApplicationDefaultDTO applicationDefaultDTO) {
        this.report.setReportLuaCode(StrUtil.toStr(applicationDefaultDTO.getReportLuaCode()));
        this.report.setReportFormCode(StrUtil.toStr(applicationDefaultDTO.getReportFormCode()));
        if(StringUtils.isNotBlank(applicationDefaultDTO.getReportMethod())){
            this.report.setReportMethod(Integer.parseInt(applicationDefaultDTO.getReportMethod()));
        }
        if(StringUtils.isNotBlank(applicationDefaultDTO.getReportTitleType())){
            this.report.setReportTitleType(Integer.parseInt(applicationDefaultDTO.getReportTitleType()));
        }
        if(StringUtils.isNotBlank(applicationDefaultDTO.getQualifications())){
            this.application.getApplicationAttr().setQualifications(applicationDefaultDTO.getQualifications());
        }
    }

    private void initSampleInfo(ApplicationDefaultDTO applicationDefaultDTO) {
        this.sampleCategory.setSampleCategoryCode(StrUtil.toStr(applicationDefaultDTO.getSampleCategory()));
        this.sampleCategory.setSampleName(StrUtil.toStr(applicationDefaultDTO.getReportShowName()));
    }

    private void initServiceInfo(ApplicationDefaultDTO applicationDefaultDTO) {
        if (StringUtils.isNotBlank(applicationDefaultDTO.getCycleRequirements())) {
            this.orderBase.setIsUrgent(Integer.parseInt(applicationDefaultDTO.getCycleRequirements()));
        }
        if (StringUtils.isNotBlank(applicationDefaultDTO.getSampleReceivingLocation())) {
            this.orderBase.setLabId(Long.parseLong(applicationDefaultDTO.getSampleReceivingLocation()));
        }
        this.orderBase.setSalesEmail(StrUtil.toStr(applicationDefaultDTO.getSalesEmail()));
        this.orderBase.setBusinessPersonEmail(StrUtil.toStr(applicationDefaultDTO.getBusinessPersonEmail()));
    }

    public OiqOrderDTO(BaseOrderDTO baseOrderDTO) {
        this.orderNo=baseOrderDTO.getOrderNo();
        this.currentOrder = baseOrderDTO;
        if(OrderTypeEnum.OIQORDER.getIndex().equals(baseOrderDTO.getOrderType())){
            this.isPortal=false;
        }
    }


    public OiqApplicationDTO getApplication() {
        return application;
    }

    public void setApplication(OiqApplicationDTO application) {
        this.application = application;
    }

    public OiqReportDTO getReport() {
        return report;
    }

    public void setReport(OiqReportDTO report) {
        this.report = report;
    }

    public int getUseUseOrder() {
        return useUseOrder;
    }

    public void setUseUseOrder(int useUseOrder) {
        this.useUseOrder = useUseOrder;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public BaseOrderDTO getCurrentOrder() {
        return currentOrder;
    }

    public void setCurrentOrder(BaseOrderDTO currentOrder) {
        this.currentOrder = currentOrder;
    }

    public Boolean getPortal() {
        return isPortal;
    }

    public void setPortal(Boolean portal) {
        isPortal = portal;
    }

    public Boolean getApplicationLinkNullFlg() {
        return applicationLinkNullFlg;
    }

    public void setApplicationLinkNullFlg(Boolean applicationLinkNullFlg) {
        this.applicationLinkNullFlg = applicationLinkNullFlg;
    }

    public OiqOrderInvoiceDTO getOrderInvoice() {
        return orderInvoice;
    }

    public void setOrderInvoice(OiqOrderInvoiceDTO orderInvoice) {
        this.orderInvoice = orderInvoice;
    }

    public List<OiqItemDTO> getItemList() {
        return itemList;
    }

    public void setItemList(List<OiqItemDTO> itemList) {
        this.itemList = itemList;
    }

    public List<OiqSampleDTO> getSampleList() {
        return sampleList;
    }

    public void setSampleList(List<OiqSampleDTO> sampleList) {
        this.sampleList = sampleList;
    }

    public OiqOrderBaseDTO getOrderBase() {
        return orderBase;
    }

    public void setOrderBase(OiqOrderBaseDTO orderBase) {
        this.orderBase = orderBase;
    }

    public String getDistinctLinkEmail() {
        return distinctLinkEmail;
    }

    public void setDistinctLinkEmail(String distinctLinkEmail) {
        this.distinctLinkEmail = distinctLinkEmail;
    }

    public String getTails() {
        return tails;
    }

    public void setTails(String tails) {
        this.tails = tails;
    }

    public List<CustomerIdDTO> getCustomerList() {
        return customerList;
    }

    public void setCustomerList(List<CustomerIdDTO> customerList) {
        this.customerList = customerList;
    }

    public SampleCategoryDTO getSampleCategory() {
        return sampleCategory;
    }

    public void setSampleCategory(SampleCategoryDTO sampleCategory) {
        this.sampleCategory = sampleCategory;
    }

    public ItemCategoryDTO getItemCategory() {
        return itemCategory;
    }

    public void setItemCategory(ItemCategoryDTO itemCategory) {
        this.itemCategory = itemCategory;
    }

    public OrderShippingDTO getOrderShipping() {
        return orderShipping;
    }

    public void setOrderShipping(OrderShippingDTO orderShipping) {
        this.orderShipping = orderShipping;
    }

    public OrderShippingContactDTO getShipper() {
        return shipper;
    }

    public void setShipper(OrderShippingContactDTO shipper) {
        this.shipper = shipper;
    }

    public OrderShippingContactDTO getConsignee() {
        return consignee;
    }

    public void setConsignee(OrderShippingContactDTO consignee) {
        this.consignee = consignee;
    }

    public OrderShippingContactDTO getNotify() {
        return notify;
    }

    public void setNotify(OrderShippingContactDTO notify) {
        this.notify = notify;
    }

    public List<OrderApplicationAttrDTO> getFormAttrList() {
        return formAttrList;
    }

    public void setFormAttrList(List<OrderApplicationAttrDTO> formAttrList) {
        this.formAttrList = formAttrList;
    }

    public List<OrderApplicationAttrDTO> getDestinationList() {
        return destinationList;
    }

    public void setDestinationList(List<OrderApplicationAttrDTO> destinationList) {
        this.destinationList = destinationList;
    }

    public List<OrderApplicationAttrDTO> getCostInformationList() {
        return costInformationList;
    }

    public void setCostInformationList(List<OrderApplicationAttrDTO> costInformationList) {
        this.costInformationList = costInformationList;
    }

    public List<OrderApplicationAttrDTO> getGoods() {
        return goods;
    }

    public void setGoods(List<OrderApplicationAttrDTO> goods) {
        this.goods = goods;
    }

    public OrderShippingContactDTO getManufacture() {
        return manufacture;
    }

    public void setManufacture(OrderShippingContactDTO manufacture) {
        this.manufacture = manufacture;
    }

    public List<OrderApplicationAttrDTO> getDocumentsInfo() {
        return documentsInfo;
    }

    public void setDocumentsInfo(List<OrderApplicationAttrDTO> documentsInfo) {
        this.documentsInfo = documentsInfo;
    }
}
