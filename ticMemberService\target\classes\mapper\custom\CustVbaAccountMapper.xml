<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.mapper.custom.CustVbaAccountMapper" >

  <resultMap id="dtoMap" type="com.sgs.ecom.member.dto.custom.CustVbaAccountDTO" >
    <result column="LAB_CODE" property="labCode" jdbcType="VARCHAR" />
    <result column="VBA_ACCOUNT_NO" property="vbaAccountNo" jdbcType="VARCHAR" />
  </resultMap>
  <select id="qryCustVbaAccountList" resultMap="dtoMap">
    select
    LAB_CODE,VBA_ACCOUNT_NO
    from TB_CUST_VBA_ACCOUNT
    <where>
      <include refid="Base_Qry_List" />
    </where>
  </select>


  <sql id="Base_Qry_List" >
    <if test="labCode != null" >
      AND LAB_CODE = #{labCode}
    </if>
    <if test="custId != null" >
      AND CUST_ID = #{custId}
    </if>
  </sql>


</mapper>