package com.sgs.ecom.member.dto.sample;

import com.platform.annotation.ApiAnno;
import com.sgs.ecom.member.base.BaseOrderFilter;

/**
 * <AUTHOR>
 */
public class SampleCategoryDTO {
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String sampleCategoryCode;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class,BaseOrderFilter.OrderToOther.class,BaseOrderFilter.WordForm.class})
    private String sampleCategoryName;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String sampleShapeCode;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class,BaseOrderFilter.OrderToOther.class,BaseOrderFilter.WordForm.class})
    private String sampleShapeName;
    @ApiAnno(groups={BaseOrderFilter.OrderToOther.class})
    private String sampleCategoryRemark;
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    private String sampleNum;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class,BaseOrderFilter.OrderToOther.class,BaseOrderFilter.WordForm.class})
    private String sampleName;

    public String getSampleCategoryCode() {
        return sampleCategoryCode;
    }

    public void setSampleCategoryCode(String sampleCategoryCode) {
        this.sampleCategoryCode = sampleCategoryCode;
    }

    public String getSampleCategoryName() {
        return sampleCategoryName;
    }

    public void setSampleCategoryName(String sampleCategoryName) {
        this.sampleCategoryName = sampleCategoryName;
    }

    public String getSampleShapeCode() {
        return sampleShapeCode;
    }

    public void setSampleShapeCode(String sampleShapeCode) {
        this.sampleShapeCode = sampleShapeCode;
    }

    public String getSampleShapeName() {
        return sampleShapeName;
    }

    public void setSampleShapeName(String sampleShapeName) {
        this.sampleShapeName = sampleShapeName;
    }

    public String getSampleCategoryRemark() {
        return sampleCategoryRemark;
    }

    public void setSampleCategoryRemark(String sampleCategoryRemark) {
        this.sampleCategoryRemark = sampleCategoryRemark;
    }

    public String getSampleNum() {
        return sampleNum;
    }

    public void setSampleNum(String sampleNum) {
        this.sampleNum = sampleNum;
    }

    public String getSampleName() {
        return sampleName;
    }

    public void setSampleName(String sampleName) {
        this.sampleName = sampleName;
    }
}
