package com.sgs.ecom.member.dto.rpc;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.enumtool.OrderTypeEnum;
import com.sgs.ecom.member.util.serializer.PriceNumFormatSerializer;
import com.sgs.ecom.member.util.serializer.TimeStringFormatSerializer;

import java.math.BigDecimal;

public class LeadsInfoDTO {

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private Long orderId;

    @JsonSerialize(using = TimeStringFormatSerializer.class)
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String createDate;
    @JsonSerialize(using = TimeStringFormatSerializer.class)
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String stateDate;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String stateShow;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String orderTypeShow;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String info;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String orderNo;
    @JsonSerialize(using = PriceNumFormatSerializer.class)
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BigDecimal amount;

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int state;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int orderType;

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getStateDate() {
        return stateDate;
    }

    public void setStateDate(String stateDate) {
        this.stateDate = stateDate;
    }

    public String getStateShow() {
        if(state==80){
            return "已成单";
        }
        if(state==91){
            return "已关闭";
        }

        return "跟进中";
    }

    public void setStateShow(String stateShow) {
        this.stateShow = stateShow;
    }

    public String getOrderTypeShow() {
        if(OrderTypeEnum.OIQORDER.getIndex().equals(String.valueOf(orderType))){
            return "OIQ";
        }
        if(OrderTypeEnum.TIC.getIndex().equals(String.valueOf(orderType))){
            return "在线商城";
        }
        return OrderTypeEnum.getName(String.valueOf(orderType));
    }

    public void setOrderTypeShow(String orderTypeShow) {
        this.orderTypeShow = orderTypeShow;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public BigDecimal getAmount() {
        if(state!=80){
            return null;
        }
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public int getOrderType() {
        return orderType;
    }

    public void setOrderType(int orderType) {
        this.orderType = orderType;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }
}
