<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.mapper.question.UserQuSubjectMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.vo.VOUserQuSubject" >
    <id column="SUBJECT_ID" property="subjectId" jdbcType="BIGINT" />
    <result column="REPLY_ID" property="replyId" jdbcType="BIGINT" />
    <result column="QUESTION_ID" property="questionId" jdbcType="BIGINT" />
    <result column="PART_NO" property="partNo" jdbcType="VARCHAR" />
    <result column="SUBJECT_NO" property="subjectNo" jdbcType="VARCHAR" />
    <result column="QUESTION_TITLE" property="questionTitle" jdbcType="VARCHAR" />
    <result column="QUESTION_TITLE_EN" property="questionTitleEn" jdbcType="VARCHAR" />
    <result column="QUESTION_TYPE" property="questionType" jdbcType="TINYINT" />
    <result column="SORT_SHOW" property="sortShow" jdbcType="INTEGER" />
    <result column="STATE" property="state" jdbcType="TINYINT" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    SUBJECT_ID, REPLY_ID, QUESTION_ID, PART_NO, SUBJECT_NO, QUESTION_TITLE, QUESTION_TITLE_EN, 
    QUESTION_TYPE, SORT_SHOW, STATE, CREATE_DATE, STATE_DATE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from TB_USER_QU_SUBJECT
    where SUBJECT_ID = #{subjectId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from TB_USER_QU_SUBJECT
    where SUBJECT_ID = #{subjectId,jdbcType=BIGINT}
  </delete>

  <insert id="insertSelective" parameterType="com.sgs.ecom.member.vo.VOUserQuSubject" >
    insert into TB_USER_QU_SUBJECT
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="subjectId != null" >
        SUBJECT_ID,
      </if>
      <if test="replyId != null" >
        REPLY_ID,
      </if>
      <if test="questionId != null" >
        QUESTION_ID,
      </if>
      <if test="partNo != null" >
        PART_NO,
      </if>
      <if test="subjectNo != null" >
        SUBJECT_NO,
      </if>
      <if test="questionTitle != null" >
        QUESTION_TITLE,
      </if>
      <if test="questionTitleEn != null" >
        QUESTION_TITLE_EN,
      </if>
      <if test="questionType != null" >
        QUESTION_TYPE,
      </if>
      <if test="sortShow != null" >
        SORT_SHOW,
      </if>
      <if test="state != null" >
        STATE,
      </if>
      <if test="createDate != null" >
        CREATE_DATE,
      </if>
      <if test="stateDate != null" >
        STATE_DATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="subjectId != null" >
        #{subjectId,jdbcType=BIGINT},
      </if>
      <if test="replyId != null" >
        #{replyId,jdbcType=BIGINT},
      </if>
      <if test="questionId != null" >
        #{questionId,jdbcType=BIGINT},
      </if>
      <if test="partNo != null" >
        #{partNo,jdbcType=VARCHAR},
      </if>
      <if test="subjectNo != null" >
        #{subjectNo,jdbcType=VARCHAR},
      </if>
      <if test="questionTitle != null" >
        #{questionTitle,jdbcType=VARCHAR},
      </if>
      <if test="questionTitleEn != null" >
        #{questionTitleEn,jdbcType=VARCHAR},
      </if>
      <if test="questionType != null" >
        #{questionType,jdbcType=TINYINT},
      </if>
      <if test="sortShow != null" >
        #{sortShow,jdbcType=INTEGER},
      </if>
      <if test="state != null" >
        #{state,jdbcType=TINYINT},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        #{stateDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.ecom.member.vo.VOUserQuSubject" >
    update TB_USER_QU_SUBJECT
    <set >
      <if test="replyId != null" >
        REPLY_ID = #{replyId,jdbcType=BIGINT},
      </if>
      <if test="questionId != null" >
        QUESTION_ID = #{questionId,jdbcType=BIGINT},
      </if>
      <if test="partNo != null" >
        PART_NO = #{partNo,jdbcType=VARCHAR},
      </if>
      <if test="subjectNo != null" >
        SUBJECT_NO = #{subjectNo,jdbcType=VARCHAR},
      </if>
      <if test="questionTitle != null" >
        QUESTION_TITLE = #{questionTitle,jdbcType=VARCHAR},
      </if>
      <if test="questionTitleEn != null" >
        QUESTION_TITLE_EN = #{questionTitleEn,jdbcType=VARCHAR},
      </if>
      <if test="questionType != null" >
        QUESTION_TYPE = #{questionType,jdbcType=TINYINT},
      </if>
      <if test="sortShow != null" >
        SORT_SHOW = #{sortShow,jdbcType=INTEGER},
      </if>
      <if test="state != null" >
        STATE = #{state,jdbcType=TINYINT},
      </if>
      <if test="createDate != null" >
        CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        STATE_DATE = #{stateDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where SUBJECT_ID = #{subjectId,jdbcType=BIGINT}
  </update>


  <!-- 自定义-->




  <insert id="insertForeach"  >
    insert into TB_USER_QU_SUBJECT (
    REPLY_ID, QUESTION_ID, PART_NO, SUBJECT_NO, QUESTION_TITLE, QUESTION_TITLE_EN,
    QUESTION_TYPE, SORT_SHOW, STATE, CREATE_DATE, STATE_DATE,IS_HIDE
    )
    values
    <foreach collection ="list" item="subject" index= "index" separator =",">
    (
        #{subject.replyId,jdbcType=BIGINT},
        #{subject.questionId,jdbcType=BIGINT},
        #{subject.partNo,jdbcType=VARCHAR},
        #{subject.subjectNo,jdbcType=VARCHAR},
        #{subject.questionTitle,jdbcType=VARCHAR},
        #{subject.questionTitleEn,jdbcType=VARCHAR},
        #{subject.questionType,jdbcType=TINYINT},
        #{subject.sortShow,jdbcType=INTEGER},
        #{subject.state,jdbcType=TINYINT},
        #{subject.createDate,jdbcType=TIMESTAMP},
        #{subject.stateDate,jdbcType=TIMESTAMP},
        #{subject.isHide}
      )
    </foreach>
  </insert>

  <select id="selectListByMap" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from TB_USER_QU_SUBJECT
    <include refid="baseQueryWhere"/>
  </select>

  <sql id="baseQueryWhere">
    where 1=1
    <if test="replyId != null">
      AND REPLY_ID=#{replyId}
    </if>
    <if test="partNo != null">
      AND PART_NO=#{partNo}
    </if>
    <if test="isHide != null">
      AND IS_HIDE=#{isHide}
    </if>
  </sql>

</mapper>