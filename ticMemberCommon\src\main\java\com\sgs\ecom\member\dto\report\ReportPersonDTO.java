package com.sgs.ecom.member.dto.report;

import com.platform.annotation.ApiAnno;
import com.sgs.ecom.member.base.BaseOrderFilter;

public class ReportPersonDTO {
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String name;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String phone;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String email;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
}
