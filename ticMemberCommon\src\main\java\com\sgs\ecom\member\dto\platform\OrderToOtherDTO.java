package com.sgs.ecom.member.dto.platform;

import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.dto.application.OrderApplicationAttrDTO;
import com.sgs.ecom.member.dto.base.BaseOrderDTO;
import com.sgs.ecom.member.dto.detail.OrderApplicationFormDTO;
import com.sgs.ecom.member.dto.detail.OrderAttachmentDTO;
import com.sgs.ecom.member.dto.detail.OrderExpressDTO;
import com.sgs.ecom.member.dto.detail.OrderInvoiceDTO;
import com.sgs.ecom.member.dto.oiq.applicationInfo.OiqItemDTO;
import com.sgs.ecom.member.dto.oiq.applicationInfo.OiqReportDTO;
import com.sgs.ecom.member.dto.oiq.applicationInfo.OiqSampleDTO;
import com.sgs.ecom.member.dto.order.OrderLinkDTO;
import com.sgs.ecom.member.dto.order.OrderMemoDTO;
import com.sgs.ecom.member.dto.sample.SampleCategoryDTO;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class OrderToOtherDTO {
    @ApiAnno(groups={BaseOrderFilter.OrderToOther.class})
    private String orderNo;
    @ApiAnno(groups={BaseOrderFilter.OrderToOther.class})
    private String relateOrderNo;
    @ApiAnno(groups={BaseOrderFilter.OrderToOther.class})
    private BaseOrderDTO baseInfo =new BaseOrderDTO();
    @ApiAnno(groups={BaseOrderFilter.OrderToOther.class})
    private OrderApplicationFormDTO applicationForm=new OrderApplicationFormDTO();
    @BeanAnno(dtocls = {OrderApplicationAttrDTO.class})
    @ApiAnno(groups={BaseOrderFilter.OrderToOther.class})
    private List<OrderApplicationAttrDTO> applicationAttr=new ArrayList<>();
    @ApiAnno(groups={BaseOrderFilter.OrderToOther.class})
    @BeanAnno(dtocls = {OrderExpressDTO.class})
    private List<OrderExpressDTO> delivers=new ArrayList<>();
    @ApiAnno(groups={BaseOrderFilter.OrderToOther.class})
    private OrderInvoiceDTO invoice=new OrderInvoiceDTO();
    @ApiAnno(groups={BaseOrderFilter.OrderToOther.class})
    @BeanAnno(dtocls = {OiqSampleDTO.class})
    private List<OiqSampleDTO> samples=new ArrayList<>();
    @ApiAnno(groups={BaseOrderFilter.OrderToOther.class})
    @BeanAnno(dtocls = {OiqItemDTO.class})
    private List<OiqItemDTO> items=new ArrayList<>();
    @ApiAnno(groups={BaseOrderFilter.OrderToOther.class})
    @BeanAnno(dtocls = {OrderLinkDTO.class})
    private List<OrderLinkDTO> orderLink =new ArrayList<>();
    @ApiAnno(groups={BaseOrderFilter.OrderToOther.class})
    private OiqReportDTO report =new OiqReportDTO();
    @ApiAnno(groups={BaseOrderFilter.OrderToOther.class})
    private List<OrderAttachmentDTO> attachments =new ArrayList<>();
    @ApiAnno(groups={BaseOrderFilter.OrderToOther.class})
    private List<OrderMemoDTO> memos =new ArrayList<>();
    @ApiAnno(groups={BaseOrderFilter.OrderToOther.class})
    private String belongSystem;
    @ApiAnno(groups={BaseOrderFilter.OrderToOther.class})
    private SampleCategoryDTO sampleCategory;



    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getRelateOrderNo() {
        return relateOrderNo;
    }

    public void setRelateOrderNo(String relateOrderNo) {
        this.relateOrderNo = relateOrderNo;
    }

    public BaseOrderDTO getBaseInfo() {
        return baseInfo;
    }

    public void setBaseInfo(BaseOrderDTO baseInfo) {
        this.baseInfo = baseInfo;
    }

    public OrderApplicationFormDTO getApplicationForm() {
        return applicationForm;
    }

    public void setApplicationForm(OrderApplicationFormDTO applicationForm) {
        this.applicationForm = applicationForm;
    }

    public List<OrderApplicationAttrDTO> getApplicationAttr() {
        return applicationAttr;
    }

    public void setApplicationAttr(List<OrderApplicationAttrDTO> applicationAttr) {
        this.applicationAttr = applicationAttr;
    }

    public List<OrderExpressDTO> getDelivers() {
        return delivers;
    }

    public void setDelivers(List<OrderExpressDTO> delivers) {
        this.delivers = delivers;
    }

    public OrderInvoiceDTO getInvoice() {
        return invoice;
    }

    public void setInvoice(OrderInvoiceDTO invoice) {
        this.invoice = invoice;
    }

    public List<OiqSampleDTO> getSamples() {
        return samples;
    }

    public void setSamples(List<OiqSampleDTO> samples) {
        this.samples = samples;
    }

    public List<OiqItemDTO> getItems() {
        return items;
    }

    public void setItems(List<OiqItemDTO> items) {
        this.items = items;
    }

    public List<OrderLinkDTO> getOrderLink() {
        return orderLink;
    }

    public void setOrderLink(List<OrderLinkDTO> orderLink) {
        this.orderLink = orderLink;
    }

    public OiqReportDTO getReport() {
        return report;
    }

    public void setReport(OiqReportDTO report) {
        this.report = report;
    }

    public List<OrderAttachmentDTO> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<OrderAttachmentDTO> attachments) {
        this.attachments = attachments;
    }

    public List<OrderMemoDTO> getMemos() {
        return memos;
    }

    public void setMemos(List<OrderMemoDTO> memos) {
        this.memos = memos;
    }

    public String getBelongSystem() {
        return belongSystem;
    }

    public void setBelongSystem(String belongSystem) {
        this.belongSystem = belongSystem;
    }

    public SampleCategoryDTO getSampleCategory() {
        return sampleCategory;
    }

    public void setSampleCategory(SampleCategoryDTO sampleCategory) {
        this.sampleCategory = sampleCategory;
    }
}
