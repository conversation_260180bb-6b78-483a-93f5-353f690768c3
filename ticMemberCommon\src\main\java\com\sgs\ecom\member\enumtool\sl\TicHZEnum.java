package com.sgs.ecom.member.enumtool.sl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum TicHZEnum {
    //type 0-输入框 1-前缀方形勾选框 2-圆形前缀勾选
    //isMust 0-否 1-是
    SAMPLE_NAME("sampleName","样品名称/面料最终用途*",0,1),
    SAMPLE_NUM("sampleNum","样品数量",0,0),
    INGREDIENT("ingredient","成分/材质",0,0),
    COLOUR("colour","颜色",0,0),
    MANUFACTURER_NAME("manufacturerName","生产商名称",0,0),
    STYLE("style","款号",0,0),
    BRAND_NAME("brandName","品牌名称(入住续签必填)",0,1),
    IS_REPORT_SHOW("isReportShow","显示在报告中样品信息栏",1,0),
    SPECIAL_INFORMATION_REMARKS("specialInformationRemarks","特殊信息备注",0,0),
    PRODUCT_GRADE("productGrade","产品等级",2,1),
    SAFETY_TECHNICAL_LEVEL("safetyTechnicalLevel","安全技术级别",2,1);
    private String nameEN;
    private String nameCN;
    private Integer type;
    private Integer isMust;

    TicHZEnum(String nameEN, String nameCN, Integer type, Integer isMust) {
        this.nameEN = nameEN;
        this.nameCN = nameCN;
        this.type = type;
        this.isMust = isMust;
    }

    /**
     * 转为数据
     * @return 枚举对象数组
     */
    public static List<Map<String, Object>> toList() {
        List<Map<String, Object>> list = new ArrayList<>();
        for (TicHZEnum item : TicHZEnum.values()) {
            Map<String, Object> map = new HashMap<>();
            map.put("nameEN", item.getNameEN());
            map.put("nameCN", item.getNameCN());
            map.put("type", item.getType());
            map.put("isMust", item.getIsMust());
            list.add(map);
        }
        return list;
    }

    public String getNameEN() {
        return nameEN;
    }

    public void setNameEN(String nameEN) {
        this.nameEN = nameEN;
    }

    public String getNameCN() {
        return nameCN;
    }

    public void setNameCN(String nameCN) {
        this.nameCN = nameCN;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getIsMust() {
        return isMust;
    }

    public void setIsMust(Integer isMust) {
        this.isMust = isMust;
    }
}
