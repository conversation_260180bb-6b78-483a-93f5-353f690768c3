package com.sgs.ecom.member.dto.pay;

import com.alibaba.fastjson.JSON;
import com.sgs.ecom.member.vo.VOOrderPay;
import org.apache.commons.lang.StringUtils;

import java.util.Map;

public class OrderOnlinePayDTO {


	private String orderNo;
	private String amount;
	private String tradeNo;
	private String payMethod;
	private String payAccount;
	private String accountName;
	private String payDate;
	private String paymentNo;
	private String buyInfo;
	private String originalOrderNo;
	private Map<String,Object> payMap;

	//返回使用
	private VOOrderPay voOrderPay;


	public OrderOnlinePayDTO(Map<String,String> params) {
		this.orderNo=params.get("orderNo");
		this.amount = params.get("amount");
		this.tradeNo = params.get("tradeNo");
		this.payMethod = params.get("payMethod");
		this.payAccount = params.get("payAccount");
		this.accountName = params.get("accountName");
		this.payDate = params.get("payDate");
		this.paymentNo = params.get("paymentNo");
		this.buyInfo=params.get("buyInfo");
		this.originalOrderNo=params.get("merOrderNo");
		String json=params.get("paymentInfo");
		if(StringUtils.isNotBlank(json)){
			Map map= JSON.parseObject(json,Map.class);
			if(map.containsKey("payMap")){
				this.payMap=JSON.parseObject(JSON.toJSONString(map.get("payMap")),Map.class);
			}
		}

	}


	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getAmount() {
		return amount;
	}

	public void setAmount(String amount) {
		this.amount = amount;
	}

	public String getTradeNo() {
		return tradeNo;
	}

	public void setTradeNo(String tradeNo) {
		this.tradeNo = tradeNo;
	}

	public String getPayMethod() {
		return payMethod;
	}

	public void setPayMethod(String payMethod) {
		this.payMethod = payMethod;
	}

	public String getPayAccount() {
		return payAccount;
	}

	public void setPayAccount(String payAccount) {
		this.payAccount = payAccount;
	}

	public String getAccountName() {
		return accountName;
	}

	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}

	public String getPayDate() {
		return payDate;
	}

	public void setPayDate(String payDate) {
		this.payDate = payDate;
	}

	public String getPaymentNo() {
		return paymentNo;
	}

	public void setPaymentNo(String paymentNo) {
		this.paymentNo = paymentNo;
	}

	public String getBuyInfo() {
		return buyInfo;
	}

	public void setBuyInfo(String buyInfo) {
		this.buyInfo = buyInfo;
	}


	public Map<String, Object> getPayMap() {
		return payMap;
	}

	public void setPayMap(Map<String, Object> payMap) {
		this.payMap = payMap;
	}

	public String getOriginalOrderNo() {
		return originalOrderNo;
	}

	public void setOriginalOrderNo(String originalOrderNo) {
		this.originalOrderNo = originalOrderNo;
	}

	@Override
	public String toString() {
		return "OrderOnlinePayDTO{" +
				"orderNo='" + orderNo + '\'' +
				", amount='" + amount + '\'' +
				", tradeNo='" + tradeNo + '\'' +
				", payMethod='" + payMethod + '\'' +
				", payAccount='" + payAccount + '\'' +
				", accountName='" + accountName + '\'' +
				", payDate='" + payDate + '\'' +
				", paymentNo='" + paymentNo + '\'' +
				", buyInfo='" + buyInfo + '\'' +
				'}';
	}

	public VOOrderPay getVoOrderPay() {
		return voOrderPay;
	}

	public void setVoOrderPay(VOOrderPay voOrderPay) {
		this.voOrderPay = voOrderPay;
	}
}
