package com.sgs.ecom.member.dto.oiq;

import com.platform.annotation.ApiAnno;
import com.sgs.ecom.member.base.BaseOrderFilter;

public class TfsOrderDTO {

    @ApiAnno(groups={BaseOrderFilter.ShareDetail.class, BaseOrderFilter.OiqOrderDetail.class, BaseOrderFilter.PortalOrderDetail.class, BaseOrderFilter.OiqOrderDetail.class})
    private String destinationCountry;
    @ApiAnno(groups={BaseOrderFilter.ShareDetail.class, BaseOrderFilter.OiqOrderDetail.class, BaseOrderFilter.PortalOrderDetail.class, BaseOrderFilter.OiqOrderDetail.class})
    private String ladingNo;
    @ApiAnno(groups={BaseOrderFilter.ShareDetail.class, BaseOrderFilter.OiqOrderDetail.class, BaseOrderFilter.PortalOrderDetail.class, BaseOrderFilter.OiqOrderDetail.class})
    private String outOrderNo;

    private String destinationCountryCode;



    public String getDestinationCountry() {
        return destinationCountry;
    }

    public void setDestinationCountry(String destinationCountry) {
        this.destinationCountry = destinationCountry;
    }

    public String getDestinationCountryCode() {
        return destinationCountryCode;
    }

    public void setDestinationCountryCode(String destinationCountryCode) {
        this.destinationCountryCode = destinationCountryCode;
    }

    public String getLadingNo() {
        return ladingNo;
    }

    public void setLadingNo(String ladingNo) {
        this.ladingNo = ladingNo;
    }

    public String getOutOrderNo() {
        return outOrderNo;
    }

    public void setOutOrderNo(String outOrderNo) {
        this.outOrderNo = outOrderNo;
    }
}
