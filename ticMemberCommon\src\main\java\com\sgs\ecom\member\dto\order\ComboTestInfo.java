package com.sgs.ecom.member.dto.order;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.json.TimeFormatSerializer;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.util.order.UseDateUtil;

import java.sql.Timestamp;
import java.util.Date;

public class ComboTestInfo {


    //type = 2
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String testItem;//检测项目
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String testingStandard;//检测标准

    public String getTestItem() {
        return testItem;
    }

    public void setTestItem(String testItem) {
        this.testItem = testItem;
    }

    public String getTestingStandard() {
        return testingStandard;
    }

    public void setTestingStandard(String testingStandard) {
        this.testingStandard = testingStandard;
    }
}
