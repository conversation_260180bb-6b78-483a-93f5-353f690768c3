package com.sgs.ecom.member.controller.rsts;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.base.BaseBean;
import com.sgs.ecom.member.dto.OrderBaseInfoCheckDTO;
import com.sgs.ecom.member.dto.user.UserDTO;
import com.sgs.ecom.member.enumtool.BaseOrderStateEnum;
import com.sgs.ecom.member.enumtool.OrderStateEnum;
import com.sgs.ecom.member.enumtool.OrderTypeEnum;
import com.sgs.ecom.member.enumtool.ResultEnumCode;
import com.sgs.ecom.member.request.OrderNoReq;
import com.sgs.ecom.member.request.base.BasePage;
import com.sgs.ecom.member.request.cust.CustReq;
import com.sgs.ecom.member.request.detail.ExpressReq;
import com.sgs.ecom.member.request.rsts.RstsApplicationReq;
import com.sgs.ecom.member.request.rsts.RstsConfirmFormReq;
import com.sgs.ecom.member.request.rsts.RstsUserReq;
import com.sgs.ecom.member.service.order.interfaces.IOrderBaseInfoService;
import com.sgs.ecom.member.service.rsts.interfaces.IRSTSApplicationService;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import com.sgs.ecom.member.vo.rsts.VORstsQryOrder;
import com.sgs.redis.RedisClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.rsts/application")
public class RSTSApplicationController extends ControllerUtil {

	@Autowired
	private IRSTSApplicationService irstsApplicationService;
	@Autowired
	private IOrderBaseInfoService orderBaseInfoService;
	@Autowired
	private RedisClient redisClient;

	/**
	 * @Description :step3 保存申请表数据 缓存数据-rsts-new
	 * <AUTHOR> Zhang
	 * @Date  2023/10/23
	 * @param token:
	 * @param rstsApplicationReq:
	 * @return: com.sgs.ecom.member.util.ResultBody
	 **/
	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "saveFormNew", method = {RequestMethod.POST})
	public ResultBody saveFormNew(
			@RequestHeader(value = "accessToken") String token,
			@Validated(BaseBean.Insert.class)
			@RequestBody RstsApplicationReq rstsApplicationReq) throws Exception {
		irstsApplicationService.saveFormNew(rstsApplicationReq,getUserDTO(token));
		return ResultBody.success();
	}



	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "userConfirmForm", method = {RequestMethod.POST})
	public ResultBody userConfirmForm(
			@RequestHeader(value = "accessToken") String token,
			@RequestBody RstsUserReq rstsUserReq) throws Exception {
		UserDTO userDTO=getUserDTO(token);
		String lockKey = "lock-userConfirmForm" + rstsUserReq.getOrderNo();
		String value = String.valueOf(System.currentTimeMillis()+10000);
		Boolean flag = redisClient.setLock(lockKey, value, 10);
		if (!flag) {
			throw new BusinessException(ResultEnumCode.LOCK_ERROR);
		}
		try {
			OrderBaseInfoCheckDTO orderBaseInfoCheckDTO = orderBaseInfoService.checkOrderBase(rstsUserReq.getOrderNo(), userDTO, OrderTypeEnum.RSTS.getIndex());
			if (orderBaseInfoCheckDTO.getState() != BaseOrderStateEnum.WAITAPPLY.getIndex()) {
				throw new BusinessException("3203","当前订单状态已更新为："+OrderStateEnum.getNameCh(String.valueOf(orderBaseInfoCheckDTO.getState())));
			}
			irstsApplicationService.confirmForm(rstsUserReq.getOrderNo(), userDTO,2,rstsUserReq.getExpressCode(), rstsUserReq.getExpressNo(),rstsUserReq.getCustId());
		}catch (BusinessException businessException){
			redisClient.releaseLock(lockKey, value);
			throw new BusinessException(businessException.getResultCode(),businessException.getDesc());
		}finally {
			redisClient.releaseLock(lockKey, value);
		}
		return ResultBody.success();
	}


	/**
	*@Function: confirmForm
	*@Description 提交申请表
	*@param: [token, orderNoReq]
	*@author: Xiwei_Qiu @date: 2021/12/6 @version:
	**/
	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "confirmForm", method = {RequestMethod.POST})
	public ResultBody confirmForm(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody RstsConfirmFormReq rstsConfirmFormReq) throws Exception {
		String lockKey = "lock-confirmForm" + rstsConfirmFormReq.getOrderNo();
		String value = String.valueOf(System.currentTimeMillis()+10000);
		Boolean flag = redisClient.setLock(lockKey, value, 10);
		if (!flag) {
			throw new BusinessException(ResultEnumCode.LOCK_ERROR);
		}
		try {
			irstsApplicationService.confirmForm(rstsConfirmFormReq.getOrderNo(), getUserDTO(token),rstsConfirmFormReq.getCrmFlg(),
					rstsConfirmFormReq.getExpressCode(), rstsConfirmFormReq.getExpressNo(),rstsConfirmFormReq.getCustId());
		}catch (BusinessException businessException){
			redisClient.releaseLock(lockKey, value);
			throw new BusinessException(businessException.getResultCode(),businessException.getDesc());
		}finally {
			redisClient.releaseLock(lockKey, value);
		}
		return ResultBody.success();
	}

	/**
	 * @Function: form
	 * @Description 查看申请表详情
	 * @param: [token, orderNoReq]
	 * @author: Xiwei_Qiu
	 * @date: 2021/3/9
	 **/
	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "form", method = {RequestMethod.POST})
	public ResultBody form(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody OrderNoReq orderNoReq) throws Exception {
		return ResultBody.newInstance(irstsApplicationService.selectRSTSDetail(orderNoReq, getUserDTO(token)));
	}

	/**
	 * @Description :保存用户企业偏好-rsts-new
	 * <AUTHOR> Zhang
	 * @Date  2023/11/1
	 * @param token:
	 * @param rstsApplicationReq:
	 * @return: com.sgs.ecom.member.util.ResultBody
	 **/
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "saveFormByUserNew", method = {RequestMethod.POST})
	public ResultBody saveFormByUserNew(
			@RequestHeader(value = "accessToken") String token,
			@RequestBody RstsApplicationReq rstsApplicationReq) throws Exception {
		rstsApplicationReq.setUserId( getUserDTO(token).getUserId());
		irstsApplicationService.saveFormByUserNew(rstsApplicationReq);
		return ResultBody.success();
	}

	/**
	 * @Description :查看申请表偏好 -rsts-new
	 * <AUTHOR> Zhang
	 * @Date  2023/11/1
	 * @param token:
	 * @param custReq:
	 * @return: com.sgs.ecom.member.util.ResultBody
	 **/
	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "formByUserNew", method = {RequestMethod.POST})
	public ResultBody formByUserNew(@RequestHeader(value = "accessToken") String token,
								 @Validated(BaseBean.Query.class)
								 @RequestBody CustReq custReq) throws Exception {
		return ResultBody.newInstance(irstsApplicationService.formByUserNew(getUserDTO(token),custReq.getCustId()));
	}

	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "reportByUser", method = {RequestMethod.POST})
	public ResultBody reportByUser(
			@RequestHeader(value = "accessToken") String token,
			@RequestBody BasePage basePage) throws Exception {
		return ResultBody.newInstance(irstsApplicationService.reportByUser(getUserDTO(token),basePage));
	}

	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "qryFormData", method = {RequestMethod.POST})
	public ResultBody qryFormData(
			@RequestHeader(value = "accessToken") String token,
			@Validated(BaseBean.Query.class)
			@RequestBody VORstsQryOrder voRstsQryOrder) throws Exception {
		return ResultBody.newInstance(irstsApplicationService.qryFormData(getUserDTO(token),voRstsQryOrder,true));
	}

	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "saveExpress", method = { RequestMethod.POST })
	public ResultBody saveExpress (
			@RequestHeader(value="accessToken") String token,
			@RequestBody ExpressReq expressReq)throws Exception{
		irstsApplicationService.saveExpress(expressReq,getUserDTO(token));
		return ResultBody.success();
	}
}
