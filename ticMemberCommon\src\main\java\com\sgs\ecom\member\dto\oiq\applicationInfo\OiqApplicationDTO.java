package com.sgs.ecom.member.dto.oiq.applicationInfo;

import com.alibaba.fastjson.JSONObject;
import com.platform.annotation.ApiAnno;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.dto.oiq.OiqOrderLinkDTO;
import com.sgs.ecom.member.enumtool.aplication.IsRefundSampleEnum;

import java.util.ArrayList;
import java.util.List;

public class OiqApplicationDTO {
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String companyNameCn;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String companyNameEn;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String companyAddressCn;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String companyAddressEn;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private List<OiqOrderLinkDTO> orderLinkList= new ArrayList<>();

    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String linkPhone;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String linkPerson;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String linkEmail;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String testMemo;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private List<JSONObject> testMemoImg=new ArrayList<>();//纵表
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private int isTransfer=1;//样品是否分包 0否 1是

    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private Integer isRefundSample=0;//是否需要退回样品1是0否
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String isRefundSampleShow;

    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private List<OiqAddressDTO> addressList=new ArrayList<>();
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private OiqApplicationAttrDTO applicationAttr=new OiqApplicationAttrDTO();






    //返回使用
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String saveTime;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private int saveFlg;//0无数据 1存过
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String stateDate;







    public String getCompanyNameCn() {
        return companyNameCn;
    }

    public void setCompanyNameCn(String companyNameCn) {
        this.companyNameCn = companyNameCn;
    }

    public String getCompanyNameEn() {
        return companyNameEn;
    }

    public void setCompanyNameEn(String companyNameEn) {
        this.companyNameEn = companyNameEn;
    }

    public String getCompanyAddressCn() {
        return companyAddressCn;
    }

    public void setCompanyAddressCn(String companyAddressCn) {
        this.companyAddressCn = companyAddressCn;
    }

    public String getCompanyAddressEn() {
        return companyAddressEn;
    }

    public void setCompanyAddressEn(String companyAddressEn) {
        this.companyAddressEn = companyAddressEn;
    }

    public String getTestMemo() {
        return testMemo;
    }

    public void setTestMemo(String testMemo) {
        this.testMemo = testMemo;
    }

    public List<JSONObject> getTestMemoImg() {
        return testMemoImg;
    }

    public void setTestMemoImg(List<JSONObject> testMemoImg) {
        this.testMemoImg = testMemoImg;
    }

    public Integer getIsRefundSample() {
        return isRefundSample;
    }

    public void setIsRefundSample(Integer isRefundSample) {
        this.isRefundSample = isRefundSample;
    }

    public int getIsTransfer() {
        return isTransfer;
    }

    public void setIsTransfer(int isTransfer) {
        this.isTransfer = isTransfer;
    }


    public String getSaveTime() {
        return saveTime;
    }

    public void setSaveTime(String saveTime) {
        this.saveTime = saveTime;
    }

    public int getSaveFlg() {
        return saveFlg;
    }

    public void setSaveFlg(int saveFlg) {
        this.saveFlg = saveFlg;
    }

    public List<OiqOrderLinkDTO> getOrderLinkList() {
        return orderLinkList;
    }

    public void setOrderLinkList(List<OiqOrderLinkDTO> orderLinkList) {
        this.orderLinkList = orderLinkList;
    }

    public String getLinkPhone() {
        return linkPhone;
    }

    public void setLinkPhone(String linkPhone) {
        this.linkPhone = linkPhone;
    }

    public String getLinkPerson() {
        return linkPerson;
    }

    public void setLinkPerson(String linkPerson) {
        this.linkPerson = linkPerson;
    }

    public String getLinkEmail() {
        return linkEmail;
    }

    public void setLinkEmail(String linkEmail) {
        this.linkEmail = linkEmail;
    }

    public List<OiqAddressDTO> getAddressList() {
        return addressList;
    }

    public void setAddressList(List<OiqAddressDTO> addressList) {
        this.addressList = addressList;
    }

    public OiqApplicationAttrDTO getApplicationAttr() {
        return applicationAttr;
    }

    public void setApplicationAttr(OiqApplicationAttrDTO applicationAttr) {
        this.applicationAttr = applicationAttr;
    }

    public String getStateDate() {
        return stateDate;
    }

    public void setStateDate(String stateDate) {
        this.stateDate = stateDate;
    }

    public String getIsRefundSampleShow() {
        if(isRefundSample==null){
            return "";
        }
        return IsRefundSampleEnum.getNameCh(isRefundSample);
    }

    public void setIsRefundSampleShow(String isRefundSampleShow) {
        this.isRefundSampleShow = isRefundSampleShow;
    }



}
