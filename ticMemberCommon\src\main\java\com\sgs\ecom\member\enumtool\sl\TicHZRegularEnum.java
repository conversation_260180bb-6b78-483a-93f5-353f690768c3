package com.sgs.ecom.member.enumtool.sl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum TicHZRegularEnum {
    //type 0-输入框 1-前缀方形勾选框 2-圆形前缀勾选
    //isMust 0-否 1-是
    SAMPLE_DESCRIPTION("sampleDescription","Sample Description*(样品描述，请按实际样品描述)",0,1),
    SAMPLE_NUM("sampleNum","No.of Samples(样品数)",0,0),
    COMPOSITION("composition","Composition(成分)",0,0),
    COLOUR("colour","Colour*(颜色)",0,1),
    STYLE("style","Style No.(款号)",0,0),
    ORDER_NO("orderNo","Order No.(订单号)",0,0),
    MANUFACTURER_NAME("manufacturerName","Manufacturer(生产厂商)",0,0),
    BUYER("buyer","Buyer (买主)",0,0),
    COUNTRY_OF_ORIGINAL("countryOfOriginal","Country of Original (原产国)",0,0),
    EXPORTED_TO("exportedTo","Exported to (出口国)",0,0),
    END_USES("endUses","End Uses (最终用途)",0,0),
    CARE_INSTRUCTION("careInstruction","Care Instruction (洗涤文字或符号)",0,0),
    OTHER_INFORMATION("otherInformation","其他需要显示的信息",0,0),
    STANDARDS("standards","Standards/Methods used(采用标准/方法)",2,1)
    ;
    private String nameEN;
    private String nameCN;
    private Integer type;
    private Integer isMust;

    TicHZRegularEnum(String nameEN, String nameCN, Integer type, Integer isMust) {
        this.nameEN = nameEN;
        this.nameCN = nameCN;
        this.type = type;
        this.isMust = isMust;
    }
    /**
     * 转为数据
     * @return 枚举对象数组
     */
    public static List<Map<String, Object>> toList() {
        List<Map<String, Object>> list = new ArrayList<>();
        for (TicHZRegularEnum item : TicHZRegularEnum.values()) {
            Map<String, Object> map = new HashMap<>();
            map.put("nameEN", item.getNameEN());
            map.put("nameCN", item.getNameCN());
            map.put("type", item.getType());
            map.put("isMust", item.getIsMust());
            list.add(map);
        }
        return list;
    }

    public String getNameEN() {
        return nameEN;
    }

    public void setNameEN(String nameEN) {
        this.nameEN = nameEN;
    }

    public String getNameCN() {
        return nameCN;
    }

    public void setNameCN(String nameCN) {
        this.nameCN = nameCN;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getIsMust() {
        return isMust;
    }

    public void setIsMust(Integer isMust) {
        this.isMust = isMust;
    }
}
