package com.sgs.ecom.member.enumtool.order;

/**
 * <AUTHOR>
 */

public enum PriceTypeEnum {
    DISCOUNT("DISCOUNT", 0, "优惠"),
    ONE_BITE_PRICE("BITE-PRICE", 1, "一口价"),
    MIN("MIN",2,"min的模板")

    ;
    PriceTypeEnum(String name, int index, String nameCh) {
        this.name = name;
        this.index = index;
        this.nameCh = nameCh;
    }


    public static String getName(Integer index) {
        if(index==null){
            return "DISCOUNT";
        }
        for (PriceTypeEnum c : PriceTypeEnum.values()) {
            if (c.getIndex()==index) {
                return c.getName();
            }
        }
        return "DISCOUNT";
    }





    private String name;
    private int index;
    private String nameCh;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }
}
