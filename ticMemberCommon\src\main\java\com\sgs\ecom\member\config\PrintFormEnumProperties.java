package com.sgs.ecom.member.config;
import com.google.common.collect.Maps;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description :打印申请表配置取到对应的申请表模板编号
 * @date 2024/7/11
 */
@RefreshScope
@Component
@ConfigurationProperties(prefix = "applicationform.order")
public class PrintFormEnumProperties {
    /**
     * WEB SITE配置
     */


    private Map<String, Integer> form = Maps.newHashMap();
    private Map<String, Integer> quotation = Maps.newHashMap();

    public Map<String, Integer> getForm() {
        return form;
    }

    public void setForm(Map<String, Integer> form) {
        this.form = form;
    }

    public Map<String, Integer> getQuotation() {
        return quotation;
    }

    public void setQuotation(Map<String, Integer> quotation) {
        this.quotation = quotation;
    }
}
