package com.sgs.ecom.member.entity;

import com.sgs.ecom.member.request.tic.TicProductReq;

import java.math.BigDecimal;

public class BbcOrderDetail {

	private String orderNo;

	private String createDate;

	private String stateDate;

	private int state;

	private String itemName;//extend_china_name

	private BigDecimal price;//money

	private String standardCode;//standard

	private Integer buyNums;

	private BigDecimal totalPrice;

	private String testName;//套餐的serviceItems

	private int itemType;//1套餐 2 单项
	private Long productId;
	private Long parentDetailId;

	private String extendName;//检测项目的分组名称

	public String getExtendName() {
		return extendName;
	}

	public void setExtendName(String extendName) {
		this.extendName = extendName;
	}

	public Long getParentDetailId() {
		return parentDetailId;
	}

	public void setParentDetailId(Long parentDetailId) {
		this.parentDetailId = parentDetailId;
	}

	public BbcOrderDetail() {

	}

	public BbcOrderDetail(String orderNo, String dateStr) {
		this.orderNo = orderNo;
		this.createDate = dateStr;
		this.stateDate=dateStr;
		this.state = 1;
	}

	public BbcOrderDetail(TicProductReq ticProductReq, String orderNo, String dateStr) {
		this.orderNo = orderNo;
		this.createDate = dateStr;
		this.stateDate=dateStr;
		this.state = 1;
		this.itemName=ticProductReq.getCombo();
		this.testName=ticProductReq.getServiceItems();
		this.standardCode=ticProductReq.getTestingStandard();
		this.itemType=1;

	}

	public Long getProductId() {
		return productId;
	}

	public void setProductId(Long productId) {
		this.productId = productId;
	}

	public String getItemName() {
		return itemName;
	}

	public void setItemName(String itemName) {
		this.itemName = itemName;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public String getStandardCode() {
		return standardCode;
	}

	public void setStandardCode(String standardCode) {
		this.standardCode = standardCode;
	}

	public Integer getBuyNums() {
		return buyNums;
	}

	public void setBuyNums(Integer buyNums) {
		this.buyNums = buyNums;
	}

	public BigDecimal getTotalPrice() {
		return totalPrice;
	}

	public void setTotalPrice(BigDecimal totalPrice) {
		this.totalPrice = totalPrice;
	}

	public String getCreateDate() {
		return createDate;
	}

	public void setCreateDate(String createDate) {
		this.createDate = createDate;
	}

	public String getStateDate() {
		return stateDate;
	}

	public void setStateDate(String stateDate) {
		this.stateDate = stateDate;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public int getState() {
		return state;
	}

	public void setState(int state) {
		this.state = state;
	}

	public String getTestName() {
		return testName;
	}

	public void setTestName(String testName) {
		this.testName = testName;
	}

	public int getItemType() {
		return itemType;
	}

	public void setItemType(int itemType) {
		this.itemType = itemType;
	}
}
