package com.sgs.ecom.member.enumtool.bbc;

public enum BbcSkuAttrEnum {

	CHECK_DATE("checkDate", "检验日期",3),
	PROVINCE("province", "检验省份",4),
	CITY("city", "检验城市",5),
	SAMPLING_NAME("samplingName", "抽样水平",2),
	TEST_NUM("testNum", "出货数量",1),
	SAMPLING("sampling", "抽样选项",6),


	REPORT_LUA("reportLua", "报告语言",10),
	REPORT_FORM("reportForm", "报告形式",11),
	LAB_INFO("labInfo", "实验室",12),
	PACKAGES("packages", "套餐名称",13),
	SERVICE_TYPE("serviceType", "服务类型",14),

	POINTS_NUM("pointsNum", "点位数量",15),
	COATING_TREATMENT_FEE("coatingTreatmentFee", "镀层处理费",16),


	CERTIFICATE_LUA("certificateLua", "证书语言",10),
	CERTIFICATE_FORM("certificateForm", "证书形式",11),

	//city("city", "开课城市",12),






	;

	BbcSkuAttrEnum(String name, String nameCh,int sortShow) {
		this.name = name;
		this.nameCh = nameCh;
		this.sortShow=sortShow;
	}
	public static String getNameCh(String name) {
		for (BbcSkuAttrEnum c : BbcSkuAttrEnum.values()) {
			if (c.getName().equals(name)) {
				return c.getNameCh();
			}
		}
		return "";
	}
	public static BbcSkuAttrEnum getEnum(String name) {
		for (BbcSkuAttrEnum c : BbcSkuAttrEnum.values()) {
			if (c.getName().equals(name)) {
				return c;
			}
		}
		return null;
	}

	public static BbcSkuAttrEnum getEnumByNameCh(String nameCh) {
		for (BbcSkuAttrEnum c : BbcSkuAttrEnum.values()) {
			if (c.getNameCh().equals(nameCh)) {
				return c;
			}
		}
		return null;
	}

	public static String getNameByNameCh(String nameCh) {
		for (BbcSkuAttrEnum c : BbcSkuAttrEnum.values()) {
			if (c.getNameCh().equals(nameCh)) {
				return c.getName();
			}
		}
		return nameCh;
	}


	private String name;
	private String nameCh;
	private int sortShow;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}


	public String getNameCh() {
		return nameCh;
	}

	public void setNameCh(String nameCh) {
		this.nameCh = nameCh;
	}

	public int getSortShow() {
		return sortShow;
	}

	public void setSortShow(int sortShow) {
		this.sortShow = sortShow;
	}
}
