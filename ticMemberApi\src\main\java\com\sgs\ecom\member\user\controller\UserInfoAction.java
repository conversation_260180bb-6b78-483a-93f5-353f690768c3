package com.sgs.ecom.member.user.controller;

import com.sgs.ecom.member.util.ControllerUtil;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.platform.annotation.AuthRequired;
import com.platform.util.CollectionService;
import com.platform.web.BaseAction;
import com.sgs.ecom.member.user.service.interfaces.IUserInfoSV;
import com.sgs.ecom.member.util.ResultBody;

@RestController
@RequestMapping("/business/api.v1.user/user")
public class UserInfoAction extends ControllerUtil {

	private static IUserInfoSV userInfoSV = CollectionService.getService(IUserInfoSV.class);
	
    /**   
	* @Function: qryUserInfoById
	* @Description: 查询会员信息
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
     * @throws Exception 
	* @date: 2018-07-16
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2018-07-16  shenyi    v1.0                 新增
	*/

    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "qry", method = { RequestMethod.POST })
    public ResultBody qryUserInfoById(@RequestBody String data,@RequestHeader(value="appId") String appId,
        @RequestHeader(value="accessToken") String token) throws Exception {
		return ResultBody.newInstance(userInfoSV.qryUserInfoById(data, getUserId(appId, token)));
	}
    
    /**   
	* @Function: modUserInfoByAttr
	* @Description: 修改用户属性
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
     * @throws Exception 
	* @date: 2018-08-30
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2018-08-30  shenyi    v1.0                 新增
	*/

    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "mod", method = { RequestMethod.POST })
    public ResultBody modUserInfo(@RequestBody String data,
								  @RequestHeader(value="appId") String appId,
        @RequestHeader(value="accessToken") String token) throws Exception {
		userInfoSV.modUserInfo(data, getUserId(appId, token));
		return ResultBody.success();
	}
    
    /**   
	* @Function: qryMonthCreditById
	* @Description: 查询会员月结额度
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
     * @throws Exception 
	* @date: 2020-01-14
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2020-01-14  shenyi    v1.0                 新增
	*/

    @AuthRequired(login = "NULL", sign = true)
    @RequestMapping(value = "qryMonthCredit", method = { RequestMethod.POST })
    public ResultBody qryMonthCreditById(@RequestBody String data,
										 @RequestHeader(value="appId") String appId,
										 @RequestHeader(value="accessToken")  String token) throws Exception {
		userInfoSV.qryMonthCreditById(data,getUserId(appId, token));
		return ResultBody.success();
	}
}
