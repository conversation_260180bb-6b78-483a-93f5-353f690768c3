package com.sgs.ecom.member.dto.bbc;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class BbcOrderInfoDTO {
	private String userName;
	private BigDecimal totalPrice;
	private String storeID;
	private String status;
	private String orderNo;
	private List<BbcProductDTO> orderLines;
	private String paymentStatus;//offPay线下支付 onPay 线上  abstractPay 月结
	private String isAbOrder;// N 不是月结 1是月结 对应monthPay 0 不是月结 2 月结已支付
	private String abOrderCode;
	private String fileAuditPicInfo;//报告已下载,报告未下载

	private Date createdDate;

	private String id;


	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public BigDecimal getTotalPrice() {
		return totalPrice;
	}

	public void setTotalPrice(BigDecimal totalPrice) {
		this.totalPrice = totalPrice;
	}

	public String getStoreID() {
		return storeID;
	}

	public void setStoreID(String storeID) {
		this.storeID = storeID;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}


	public String getPaymentStatus() {
		return paymentStatus;
	}

	public void setPaymentStatus(String paymentStatus) {
		this.paymentStatus = paymentStatus;
	}

	public String getIsAbOrder() {
		return isAbOrder;
	}

	public void setIsAbOrder(String isAbOrder) {
		this.isAbOrder = isAbOrder;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public List<BbcProductDTO> getOrderLines() {
		return orderLines;
	}

	public void setOrderLines(List<BbcProductDTO> orderLines) {
		this.orderLines = orderLines;
	}

	public String getAbOrderCode() {
		return abOrderCode;
	}

	public void setAbOrderCode(String abOrderCode) {
		this.abOrderCode = abOrderCode;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getFileAuditPicInfo() {
		return fileAuditPicInfo;
	}

	public void setFileAuditPicInfo(String fileAuditPicInfo) {
		this.fileAuditPicInfo = fileAuditPicInfo;
	}
}
