package com.sgs.ecom.member.domain.order;

import com.sgs.ecom.member.enumtool.order.GroupTypeEnum;
import com.sgs.ecom.member.util.order.UseDateUtil;
import com.sgs.ecom.member.vo.VOOrderGroup;

import java.util.Date;

public class OrderGroupDO {


    public static VOOrderGroup getVOOrderGroup(String orderNo, String groupNo, int isTemp, GroupTypeEnum groupTypeEnum){
        VOOrderGroup voOrderGroup=new VOOrderGroup();
        voOrderGroup.setOrderNo(orderNo);
        voOrderGroup.setGroupNo(groupNo);
        voOrderGroup.setIsTemp(isTemp);
        voOrderGroup.setOperatorType(groupTypeEnum.getIndex());
        voOrderGroup.setCreateDate(UseDateUtil.getDateString(new Date()));
        return voOrderGroup;
    }
}
