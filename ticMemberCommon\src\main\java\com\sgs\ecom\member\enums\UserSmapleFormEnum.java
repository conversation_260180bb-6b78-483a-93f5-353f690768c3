package com.sgs.ecom.member.enums;

import java.util.ArrayList;
import java.util.List;

public enum UserSmapleFormEnum {


	sampleName("sampleName", "样品名称",0),
	sampleNameEn("sampleNameEn", "Sample Name",1),
	productInfo("productInfo", "型号",2),
	productInfoEn("productInfoEn", "Model",3),
	extendedModelNo("extendedModelNo", "扩展型号",4),
	extendedModelNoEn("extendedModelNoEn", "Extended Model No",5),

	materialGrade("materialGrade", "主要成分",6),
	materialGradeEn("materialGradeEn", "Material",7),

	lotNo("lotNo", "批号",8),
	lotNoEn("lotNoEn", "Lot No.",9),


	remark("remark", "客户参考信息",10),
	remarkEn("remarkEn", "Customer Referrence",11),

	producer("producer", "制造商",12),
	producerEn("producerEn", "Producer",13),

	supplierName("supplierName", "供应商",14),
	supplierNameEn("supplierNameEn", "Supplier",15),
	productBatch("productBatch", "料号",16),
	productBatchEn("productBatchEn", "Material No.",17),
	buyersName("buyersName", "买家",18),
	buyersNameEn("buyersNameEn", "Buyer",19),

	originalProduct("originalProduct", "原产地",20),
	originalProductEn("originalProductEn", "Origin of the Product",21),
	destinationProduct("destinationProduct", "目的地",22),
	destinationProductEn("destinationProductEn", "Destination of the Product",23),

	sampleMemo("sampleMemo", "样品备注",24),
	annex("annex", "附件",25),



	;

	UserSmapleFormEnum(String name, String nameCh, int min) {
		this.name = name;
		this.nameCh = nameCh;
		this.min = min;
	}
	public static List<String> getNameList() {
		List<String> list = new ArrayList<>();
		for (UserSmapleFormEnum c : UserSmapleFormEnum.values()) {
			list.add(c.name);
		}
		return list;
	}
	public static int getSortShow(String name) {
		for (UserSmapleFormEnum c : UserSmapleFormEnum.values()) {
			if (c.name().equals(name)) {
				return c.getMin();
			}
		}
		return 0;
	}

	public static String getNameCh(String name) {
		for (UserSmapleFormEnum c : UserSmapleFormEnum.values()) {
			if (c.name().equals(name)) {
				return c.getNameCh();
			}
		}
		return "";
	}

	public static String getName(String name) {
		for (UserSmapleFormEnum c : UserSmapleFormEnum.values()) {
			if (c.name().equals(name)) {
				return c.getNameCh();
			}
		}
		return null;
	}
	public static String getNameCh(int index) {
		for (UserSmapleFormEnum c : UserSmapleFormEnum.values()) {
			if (c.getIndex()==index) {
				return c.getNameCh();
			}
		}
		return "";
	}

	public static UserSmapleFormEnum getEnum(String name) {
		for (UserSmapleFormEnum c : UserSmapleFormEnum.values()) {
			if (c.getName().equals(name)) {
				return c;
			}
		}
		return null;
	}

//	public static String getMinByIndex(int index) {
//		for (UserSmapleFormEnum c : UserSmapleFormEnum.values()) {
//			if (c.getIndex()==index) {
//				if(index==10){
//					return "M";
//				}
//				return c.getMin();
//			}
//		}
//		return "";
//	}

//	public  static int getEnumStateByMin(String min){
//		for (UserSmapleFormEnum c : UserSmapleFormEnum.values()) {
//			if (c.getMin().contains(min)) {
//				return c.getIndex();
//			}
//		}
//		return -2;
//	}

	private String name;
	private int index;
	private String nameCh;
	private int min;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public int getIndex() {
		return index;
	}

	public void setIndex(int index) {
		this.index = index;
	}

	public String getNameCh() {
		return nameCh;
	}

	public void setNameCh(String nameCh) {
		this.nameCh = nameCh;
	}

	public int getMin() {
		return min;
	}

	public void setMin(int min) {
		this.min = min;
	}
}
