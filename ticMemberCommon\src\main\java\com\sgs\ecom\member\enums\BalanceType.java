package com.sgs.ecom.member.enums;

public enum BalanceType {

	CASH("余额（可退）", 100000), INTEGRAL("积分", 200000), CREDIT("额度", 300000);

    // 成员变量  
    private String name;  
    private int index;  
    // 构造方法  
    private BalanceType(String name, int index) {  
        this.name = name;  
        this.index = index;  
    }  
   
    // 普通方法  
    public static String getName(int index) {  
        for (BalanceType c : BalanceType.values()) {  
            if (c.getIndex() == index) {  
                return c.name;  
            }  
        }  
        return null;  
    }  
    // get set 方法  
    public String getName() {  
        return name;  
    }  

    public int getIndex() {  
        return index;  
    }  
}
