<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.mapper.question.UserQuestionMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.vo.VOUserQuestion" >
    <id column="REPLY_ID" property="replyId" jdbcType="BIGINT" />
    <result column="QUESTION_ID" property="questionId" jdbcType="BIGINT" />
    <result column="QUESTION_NAME" property="questionName" jdbcType="VARCHAR" />
    <result column="QUESTION_NAME_EN" property="questionNameEn" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="TINYINT" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="FINAL_SCORE" property="finalScore" jdbcType="INTEGER" />
    <result column="REAL_SCORE" property="realScore" jdbcType="INTEGER" />
    <result column="LANGUAGE_FLAG" property="languageFlag" jdbcType="VARCHAR" />
    <result column="CURRENT_PART" property="currentPart" jdbcType="VARCHAR" />
    <result column="CLOUD_ID" property="cloudId" jdbcType="VARCHAR" />
    <result column="FILE_KEY" property="fileKey" jdbcType="VARCHAR" />
    <result column="ATTACHMENT_NAME" property="attachmentName" jdbcType="VARCHAR" />
    <result column="QUESTION_TYPE" property="questionType" jdbcType="TINYINT" />
    <result column="REPLY_NO" property="replyNo" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    REPLY_ID, QUESTION_ID, QUESTION_NAME, QUESTION_NAME_EN, STATE, CREATE_DATE, STATE_DATE, 
    ORDER_NO, USER_ID, FINAL_SCORE, REAL_SCORE, LANGUAGE_FLAG, CURRENT_PART, CLOUD_ID, 
    FILE_KEY, ATTACHMENT_NAME, QUESTION_TYPE, REPLY_NO
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from TB_USER_QUESTION
    where REPLY_ID = #{replyId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from TB_USER_QUESTION
    where REPLY_ID = #{replyId,jdbcType=BIGINT}
  </delete>
  
  <insert id="insertSelective" parameterType="com.sgs.ecom.member.vo.VOUserQuestion" >
    <selectKey resultType="java.lang.Long" keyProperty="replyId" order="AFTER">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into TB_USER_QUESTION
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="replyId != null" >
        REPLY_ID,
      </if>
      <if test="questionId != null" >
        QUESTION_ID,
      </if>
      <if test="questionName != null" >
        QUESTION_NAME,
      </if>
      <if test="questionNameEn != null" >
        QUESTION_NAME_EN,
      </if>
      <if test="state != null" >
        STATE,
      </if>
      <if test="createDate != null" >
        CREATE_DATE,
      </if>
      <if test="stateDate != null" >
        STATE_DATE,
      </if>
      <if test="orderNo != null" >
        ORDER_NO,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
      <if test="finalScore != null" >
        FINAL_SCORE,
      </if>
      <if test="realScore != null" >
        REAL_SCORE,
      </if>
      <if test="languageFlag != null" >
        LANGUAGE_FLAG,
      </if>
      <if test="currentPart != null" >
        CURRENT_PART,
      </if>
      <if test="cloudId != null" >
        CLOUD_ID,
      </if>
      <if test="fileKey != null" >
        FILE_KEY,
      </if>
      <if test="attachmentName != null" >
        ATTACHMENT_NAME,
      </if>
      <if test="questionType != null" >
        QUESTION_TYPE,
      </if>
      <if test="replyNo != null" >
        REPLY_NO,
      </if>
      <if test="isOriginal != null" >
        IS_ORIGINAL,
      </if>
      <if test="categoryPath != null" >
        CATEGORY_PATH,
      </if>
      <if test="categoryId != null" >
        CATEGORY_ID,
      </if>
      <if test="lineId != null" >
        LINE_ID,
      </if>
      <if test="businessLine != null" >
        BUSINESS_LINE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="replyId != null" >
        #{replyId,jdbcType=BIGINT},
      </if>
      <if test="questionId != null" >
        #{questionId,jdbcType=BIGINT},
      </if>
      <if test="questionName != null" >
        #{questionName,jdbcType=VARCHAR},
      </if>
      <if test="questionNameEn != null" >
        #{questionNameEn,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        #{state,jdbcType=TINYINT},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        #{stateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="finalScore != null" >
        #{finalScore,jdbcType=INTEGER},
      </if>
      <if test="realScore != null" >
        #{realScore,jdbcType=INTEGER},
      </if>
      <if test="languageFlag != null" >
        #{languageFlag,jdbcType=VARCHAR},
      </if>
      <if test="currentPart != null" >
        #{currentPart,jdbcType=VARCHAR},
      </if>
      <if test="cloudId != null" >
        #{cloudId,jdbcType=VARCHAR},
      </if>
      <if test="fileKey != null" >
        #{fileKey,jdbcType=VARCHAR},
      </if>
      <if test="attachmentName != null" >
        #{attachmentName,jdbcType=VARCHAR},
      </if>
      <if test="questionType != null" >
        #{questionType,jdbcType=TINYINT},
      </if>
      <if test="replyNo != null" >
        #{replyNo,jdbcType=VARCHAR},
      </if>
      <if test="isOriginal != null" >
        #{isOriginal},
      </if>
      <if test="categoryPath != null" >
        #{categoryPath},
      </if>
      <if test="categoryId != null" >
        #{categoryId},
      </if>
      <if test="lineId != null" >
        #{lineId},
      </if>
      <if test="businessLine != null" >
        #{businessLine},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.ecom.member.vo.VOUserQuestion" >
    update TB_USER_QUESTION
    <set >
      <if test="questionId != null" >
        QUESTION_ID = #{questionId,jdbcType=BIGINT},
      </if>
      <if test="questionName != null" >
        QUESTION_NAME = #{questionName,jdbcType=VARCHAR},
      </if>
      <if test="questionNameEn != null" >
        QUESTION_NAME_EN = #{questionNameEn,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        STATE = #{state,jdbcType=TINYINT},
      </if>
      <if test="createDate != null" >
        CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        STATE_DATE = #{stateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="orderNo != null" >
        ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        USER_ID = #{userId,jdbcType=BIGINT},
      </if>
      <if test="finalScore != null" >
        FINAL_SCORE = #{finalScore,jdbcType=INTEGER},
      </if>
      <if test="realScore != null" >
        REAL_SCORE = #{realScore,jdbcType=INTEGER},
      </if>
      <if test="languageFlag != null" >
        LANGUAGE_FLAG = #{languageFlag,jdbcType=VARCHAR},
      </if>
      <if test="currentPart != null" >
        CURRENT_PART = #{currentPart,jdbcType=VARCHAR},
      </if>
      <if test="cloudId != null" >
        CLOUD_ID = #{cloudId,jdbcType=VARCHAR},
      </if>
      <if test="fileKey != null" >
        FILE_KEY = #{fileKey,jdbcType=VARCHAR},
      </if>
      <if test="attachmentName != null" >
        ATTACHMENT_NAME = #{attachmentName,jdbcType=VARCHAR},
      </if>
      <if test="questionType != null" >
        QUESTION_TYPE = #{questionType,jdbcType=TINYINT},
      </if>
      <if test="replyNo != null" >
        REPLY_NO = #{replyNo,jdbcType=VARCHAR},
      </if>
    </set>
    where REPLY_ID = #{replyId,jdbcType=BIGINT}
  </update>



  <!--自定义 -->


  <resultMap id="resultDTO" type="com.sgs.ecom.member.dto.question.order.OrderQuestionDTO" >
    <id column="REPLY_ID" property="replyId" jdbcType="BIGINT" />
    <result column="CREATE_DATE" property="createDate" jdbcType="VARCHAR" />
    <result column="CATEGORY_ID" property="catagoryId" jdbcType="BIGINT" />
    <result column="QUESTION_ID" property="questionId" jdbcType="BIGINT" />
    <result column="LINE_ID" property="lineId" jdbcType="BIGINT" />
    <result column="BUSINESS_LINE" property="businessLine" jdbcType="VARCHAR" />
    <result column="QUESTION_TYPE" property="questionType" jdbcType="TINYINT" />
    <result column="QUESTION_NAME" property="questionName" jdbcType="VARCHAR" />
    <result column="QUESTION_NAME_EN" property="questionNameEn" jdbcType="VARCHAR" />
    <result column="CATEGORY_PATH" property="categoryPath" jdbcType="VARCHAR" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="BU" property="bu" jdbcType="VARCHAR" />
    <result column="CONFIRM_ORDER_DATE" property="confirmOrderDate" jdbcType="VARCHAR" />
  </resultMap>


  <select id="selectOrderQuestion" resultMap="resultDTO" >
    select obi.CREATE_DATE,tb.CATEGORY_ID,tb.CATEGORY_PATH,tb.LINE_ID,tb.BUSINESS_LINE,
    tb.REPLY_ID,tb.QUESTION_ID,tb.QUESTION_TYPE,tb.QUESTION_NAME,tb.QUESTION_NAME,
    obi.ORDER_NO,obi.BU,obi.CONFIRM_ORDER_DATE
    from ORDER_BASE_INFO obi ,TB_USER_QUESTION tb
    where obi.ORDER_NO=tb.ORDER_NO and obi.ORDER_NO=#{orderNo} and tb.IS_ORIGINAL=#{isOriginal} and tb.STATE=1
    limit 1
  </select>

  <update id="updateQuestionState">
    update TB_USER_QUESTION set state=0 where STATE=1 and IS_ORIGINAL=0 and ORDER_NO=#{orderNo}
  </update>


</mapper>