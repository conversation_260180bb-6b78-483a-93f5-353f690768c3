package com.sgs.ecom.member.enumtool.order;

public enum OrderApplicationBasicEnum {
	OTHER("<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "其他数据"),
	TRANSPORT("TRANSPORT", "TRANSPORT", "Transport Information运输资料"),
	PHY_CHEM("PHY_CHEM", "PHY_CHEM", "Physical and Chemical properties物理和化学性质"),
	STABLILTY_REACTIVITY("STABLILTY_REACTIVITY", "STABLILTY_REACTIVITY", "Stablilty and Reactivity稳定性和反应性"),
	HEALTH_EFFECTS("HEALTH_EFFECTS", "HEALTH_EFFECTS", "Health effects and Toxicological data健康影响及毒理学资料"),
	MSDS_APPLICATION("MSDS_APPLICATION", "MSDS_APPLICATION", "报告语种基础信息"),
	ADDITIONAL("ADDITIONAL", "ADDITIONAL", "Additional Information 补充资料"),
	BASIC("BASIC", "BASIC", "Product’s basic information 产品基本资料"),
	;





	OrderApplicationBasicEnum(String name, String index, String nameCh) {
		this.name = name;
		this.index = index;
		this.nameCh = nameCh;
	}

	private String name;
	private String index;
	private String nameCh;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getIndex() {
		return index;
	}

	public void setIndex(String index) {
		this.index = index;
	}

	public String getNameCh() {
		return nameCh;
	}

	public void setNameCh(String nameCh) {
		this.nameCh = nameCh;
	}
}
