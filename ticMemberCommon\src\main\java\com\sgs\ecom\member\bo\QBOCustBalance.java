package com.sgs.ecom.member.bo; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import com.platform.annotation.CharacterVaild; 
import com.platform.annotation.CheckAnno; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import com.platform.util.json.ScienceFormatSerializer; 
import com.platform.annotation.ExplainAnno; 
import com.platform.annotation.BeanAnno; 

@ExplainAnno(value="bean",name="月结余额")
public class QBOCustBalance{
 
 	public static final String SEQUENCE = ""; 
  
 	public static final String BO_SQL = "select a.USER_ID,b.CUST_ID,b.CUST_CODE,b.COMPANY_NAME,b.COMPANY_NAME_EN,b.COUNTRY,b.PROVICE,b.CITY,b.TOWN,b.ADDRESS,b.CREDIT_AMOUNT,b.STATE,b.CREATE_DATE,b.STATE_DATE,b.IS_FREEZE,b.<PERSON><PERSON>,c.<PERSON>LANCE_TYPE,c.<PERSON>ER_<PERSON>,c.<PERSON>,c.STORE_ID,c.SKU_ID,c.AREA_ID from TB_CUST_APPLY_RELATE a JOIN TB_CUST_INFO b ON a.CUST_ID=b.CUST_ID and b.STATE=1 and b.IS_FREEZE=0 LEFT JOIN ACCT_BALANCE c ON a.CUST_ID=c.USER_ID and c.BALANCE_TYPE= 300000"; 
 
 	public static final String OWNER ="member";

 	public static final String IS_FREEZE="isFreeze";
 	public static final String AREA_ID="areaId";
 	public static final String CUST_ID="custId";
 	public static final String PROVICE="provice";
 	public static final String CREDIT_AMOUNT="creditAmount";
 	public static final String STATE_DATE="stateDate";
 	public static final String USER_ID="userId";
 	public static final String STATE="state";
 	public static final String BALANCE="balance";
 	public static final String COMPANY_NAME="companyName";
 	public static final String CREATE_DATE="createDate";
 	public static final String COUNTRY="country";
 	public static final String CITY="city";
 	public static final String BALANCE_TYPE="balanceType";
 	public static final String TOWN="town";
 	public static final String USER_TYPE="userType";
 	public static final String SKU_ID="skuId";
 	public static final String ADDRESS="address";
 	public static final String STORE_ID="storeId";
 	public static final String CUST_CODE="custCode";
 	public static final String COMPANY_NAME_EN="companyNameEn";
 	public static final String MEMO="memo";

 	@BeanAnno(value="IS_FREEZE",table="b")
 	private int isFreeze;
 	@BeanAnno(value="AREA_ID",table="c")
 	private long areaId;
 	@BeanAnno(value="CUST_ID",table="b")
 	private long custId;
 	@BeanAnno(value="PROVICE",table="b")
 	private String provice;
 	@BeanAnno(value="CREDIT_AMOUNT",table="b")
 	private double creditAmount;
 	@BeanAnno(value="STATE_DATE",table="b")
 	private Timestamp stateDate;
 	@BeanAnno(value="USER_ID",table="a")
 	private long userId;
 	@BeanAnno(value="STATE",table="b")
 	private int state;
 	@BeanAnno(value="BALANCE",table="c")
 	private long balance;
 	@BeanAnno(value="COMPANY_NAME",table="b")
 	private String companyName;
 	@BeanAnno(value="CREATE_DATE",table="b")
 	private Timestamp createDate;
 	@BeanAnno(value="COUNTRY",table="b")
 	private String country;
 	@BeanAnno(value="CITY",table="b")
 	private String city;
 	@BeanAnno(value="BALANCE_TYPE",table="c")
 	private long balanceType;
 	@BeanAnno(value="TOWN",table="b")
 	private String town;
 	@BeanAnno(value="USER_TYPE",table="c")
 	private int userType;
 	@BeanAnno(value="SKU_ID",table="c")
 	private long skuId;
 	@BeanAnno(value="ADDRESS",table="b")
 	private String address;
 	@BeanAnno(value="STORE_ID",table="c")
 	private long storeId;
 	@BeanAnno(value="CUST_CODE",table="b")
 	private String custCode;
 	@BeanAnno(value="COMPANY_NAME_EN",table="b")
 	private String companyNameEn;
 	@BeanAnno(value="MEMO",table="b")
 	private String memo;

 	public void setIsFreeze(int isFreeze){
 		 this.isFreeze=isFreeze;
 	}
 	public int getIsFreeze(){
 		 return this.isFreeze;
 	}
 
 	 
 	public void setAreaId(long areaId){
 		 this.areaId=areaId;
 	}
 	public long getAreaId(){
 		 return this.areaId;
 	}
 
 	 
 	public void setCustId(long custId){
 		 this.custId=custId;
 	}
 	public long getCustId(){
 		 return this.custId;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setProvice(String provice){
 		 this.provice=provice;
 	}
 	@CheckAnno(len = 50) 
 	public String getProvice(){
 		 return this.provice;
 	}
 
 	 
 	public void setCreditAmount(double creditAmount){
 		 this.creditAmount=creditAmount;
 	}
 	@JsonSerialize(using = ScienceFormatSerializer.class) 
 	public double getCreditAmount(){
 		 return this.creditAmount;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setUserId(long userId){
 		 this.userId=userId;
 	}
 	public long getUserId(){
 		 return this.userId;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setBalance(long balance){
 		 this.balance=balance;
 	}
 	public long getBalance(){
 		 return this.balance;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setCompanyName(String companyName){
 		 this.companyName=companyName;
 	}
 	@CheckAnno(len = 200) 
 	public String getCompanyName(){
 		 return this.companyName;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	@CharacterVaild(len = 30) 
 	public void setCountry(String country){
 		 this.country=country;
 	}
 	@CheckAnno(len = 30) 
 	public String getCountry(){
 		 return this.country;
 	}
 
 	 
 	@CharacterVaild(len = 30) 
 	public void setCity(String city){
 		 this.city=city;
 	}
 	@CheckAnno(len = 30) 
 	public String getCity(){
 		 return this.city;
 	}
 
 	 
 	public void setBalanceType(long balanceType){
 		 this.balanceType=balanceType;
 	}
 	public long getBalanceType(){
 		 return this.balanceType;
 	}
 
 	 
 	@CharacterVaild(len = 30) 
 	public void setTown(String town){
 		 this.town=town;
 	}
 	@CheckAnno(len = 30) 
 	public String getTown(){
 		 return this.town;
 	}
 
 	 
 	public void setUserType(int userType){
 		 this.userType=userType;
 	}
 	public int getUserType(){
 		 return this.userType;
 	}
 
 	 
 	public void setSkuId(long skuId){
 		 this.skuId=skuId;
 	}
 	public long getSkuId(){
 		 return this.skuId;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setAddress(String address){
 		 this.address=address;
 	}
 	@CheckAnno(len = 500) 
 	public String getAddress(){
 		 return this.address;
 	}
 
 	 
 	public void setStoreId(long storeId){
 		 this.storeId=storeId;
 	}
 	public long getStoreId(){
 		 return this.storeId;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setCustCode(String custCode){
 		 this.custCode=custCode;
 	}
 	@CheckAnno(len = 50) 
 	public String getCustCode(){
 		 return this.custCode;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setCompanyNameEn(String companyNameEn){
 		 this.companyNameEn=companyNameEn;
 	}
 	@CheckAnno(len = 200) 
 	public String getCompanyNameEn(){
 		 return this.companyNameEn;
 	}
 
 	 
 	@CharacterVaild(len = 2000) 
 	public void setMemo(String memo){
 		 this.memo=memo;
 	}
 	@CheckAnno(len = 2000) 
 	public String getMemo(){
 		 return this.memo;
 	}
 
 	 
}