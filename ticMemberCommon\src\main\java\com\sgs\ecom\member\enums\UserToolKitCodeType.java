package com.sgs.ecom.member.enums;

/**
 * 用户小工具类型枚举
 */
public enum UserToolKitCodeType {


    //小工具工具包对应生成编码前缀
    CERTIFICATE("合格证", "HGZ"),
    REPORT_AUTHENTICITY("报告验真", "RA"),
    CARE_LABEL("水洗标", "SXB");
    // 成员变量
    private String name;
    private String index;
    // 构造方法
    private UserToolKitCodeType(String name, String index) {
        this.name = name;  
        this.index = index;  
    }  

    // 普通方法  
    public static String getName(String index) {  
        for (UserToolKitCodeType c : UserToolKitCodeType.values()) {
            if (c.getIndex().equals(index)) {  
                return c.name;  
            }  
        }  
        return null;  
    }  
    // get set 方法  
    public String getName() {  
        return name;  
    }  

    public String getIndex() {  
        return index;  
    }  
}
