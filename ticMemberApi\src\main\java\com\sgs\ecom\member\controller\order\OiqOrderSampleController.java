package com.sgs.ecom.member.controller.order;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.ecom.member.request.OrderNoReq;
import com.sgs.ecom.member.service.oiq.interfaces.IOiqOrderSampleService;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.oiq/sample")
public class OiqOrderSampleController extends ControllerUtil {

	@Autowired
	private IOiqOrderSampleService oiqOrderSampleService;

	/**
	 *@Function: orderList（样品属性修改）
	 *@Description 查询订单的样品（）
	 *@param: [orderSampleReq, token]
	 *@author: Xiwei_Qiu @date: 2022/5/7 @version:
	 **/
	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "qrySampleList", method = { RequestMethod.POST })
	public ResultBody qrySampleList(
		@RequestBody OrderNoReq orderNoReq,
		@RequestHeader(value="accessToken") String token) throws Exception {
		return ResultBody.newInstance(oiqOrderSampleService.qrySampleList(orderNoReq,getUserDTO(token)));
	}





}
