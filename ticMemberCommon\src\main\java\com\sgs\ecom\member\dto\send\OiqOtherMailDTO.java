package com.sgs.ecom.member.dto.send;

import com.sgs.ecom.member.dto.acct.MonthCustMoreDTO;
import com.sgs.ecom.member.dto.base.BaseOrderDTO;
import com.sgs.ecom.member.dto.detail.OrderExpressDTO;
import com.sgs.ecom.member.request.FileReq;

import java.util.List;

public class OiqOtherMailDTO {
	private String sendCC;
	private String checkDate;
	private MonthCustMoreDTO monthCustMoreDTO;
	private int monthPayFlg;
	private List<FileReq> fileReqList;
	private int memberCc;//发客户发的时候添加的cc
	private int orderCc;//往客服发的时候添加的cc
	private int ehsFlg;
	private Integer state;
	private String subBuCode;


	private BaseOrderDTO baseOrderDTO;


	public BaseOrderDTO getBaseOrderDTO() {
		return baseOrderDTO;
	}

	public void setBaseOrderDTO(BaseOrderDTO baseOrderDTO) {
		this.baseOrderDTO = baseOrderDTO;
	}


	public String getSubBuCode() {
		return subBuCode;
	}

	public void setSubBuCode(String subBuCode) {
		this.subBuCode = subBuCode;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public int getEhsFlg() {
		return ehsFlg;
	}

	public void setEhsFlg(int ehsFlg) {
		this.ehsFlg = ehsFlg;
	}
	private String serviceCC;//只发订单操作人

	public int getMonthPayFlg() {
		return monthPayFlg;
	}

	public void setMonthPayFlg(int monthPayFlg) {
		this.monthPayFlg = monthPayFlg;
	}

	public MonthCustMoreDTO getMonthCustMoreDTO() {
		return monthCustMoreDTO;
	}

	public void setMonthCustMoreDTO(MonthCustMoreDTO monthCustMoreDTO) {
		this.monthCustMoreDTO = monthCustMoreDTO;
	}

	public String getSendCC() {
		return sendCC;
	}

	public void setSendCC(String sendCC) {
		this.sendCC = sendCC;
	}

	public String getCheckDate() {
		return checkDate;
	}

	public void setCheckDate(String checkDate) {
		this.checkDate = checkDate;
	}

	public List<FileReq> getFileReqList() {
		return fileReqList;
	}

	public void setFileReqList(List<FileReq> fileReqList) {
		this.fileReqList = fileReqList;
	}

	public int getMemberCc() {
		return memberCc;
	}

	public void setMemberCc(int memberCc) {
		this.memberCc = memberCc;
	}

	public int getOrderCc() {
		return orderCc;
	}

	public void setOrderCc(int orderCc) {
		this.orderCc = orderCc;
	}

	public String getServiceCC() {
		return serviceCC;
	}

	public void setServiceCC(String serviceCC) {
		this.serviceCC = serviceCC;
	}
}
