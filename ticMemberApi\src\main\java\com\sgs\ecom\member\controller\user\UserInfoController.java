package com.sgs.ecom.member.controller.user;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.ecom.member.request.cust.CustReq;
import com.sgs.ecom.member.service.custom.interfaces.ICustInfoSV;
import com.sgs.ecom.member.service.user.interfaces.IUserInfoService;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/business/api.v1.user/info")
@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
public class UserInfoController extends ControllerUtil {

	@Resource
	private IUserInfoService userInfoService;
	@Autowired
	private ICustInfoSV custInfoSV;
	
    /**   
	* @Function: qryBelongCustByUser
	* @Description: 根据登录信息查询企业销售信息
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2018-11-23 shenyi    v1.0                 新增
	*/
	@HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "qryCustCs", method = { RequestMethod.POST })
    public ResultBody qryBelongCustCsByUser(@RequestHeader(value="accessToken") String token,
		@RequestBody CustReq custReq) throws Exception {
		return ResultBody.newInstance(custInfoSV.qryBelongCustCsByUser(getUserDTO(token),custReq.getCustId()));
	}
	
	/**   
	* @Function: qryBelongCustByUser
	* @Description: 根据登录信息查询企业信息
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2018-11-23
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2018-11-23 shenyi    v1.0                 新增
	*/
	@HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "qryCust", method = { RequestMethod.POST })
    public ResultBody qryBelongCustByUser(@RequestHeader(value="accessToken") String token,
		 @RequestBody CustReq custReq) throws Exception {
		return ResultBody.newInstance(custInfoSV.qryBelongCustByUser(getUserDTO(token),custReq.getCustId()));
	}
	
	/**   
	* @Function: qryUserManager
	* @Description: 查询用户业务经理
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2018-11-23
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2018-11-23 shenyi    v1.0                 新增
	*/
	@HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "qryUserCs", method = { RequestMethod.POST })
    public ResultBody qryUserManager(@RequestHeader(value="accessToken") String token,
    	@RequestHeader(value="appId") String appId) throws Exception {
		return ResultBody.newInstance(userInfoService.qryUserManager(getUserId(appId, token)));
	}
}
