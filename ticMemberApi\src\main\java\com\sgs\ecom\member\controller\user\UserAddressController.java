package com.sgs.ecom.member.controller.user;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.base.BaseBean;
import com.sgs.ecom.member.request.user.UserAddressIdReq;
import com.sgs.ecom.member.request.user.UserAddressListReq;
import com.sgs.ecom.member.request.user.UserAddressReq;
import com.sgs.ecom.member.service.user.interfaces.IUserAddressService;
import com.sgs.ecom.member.service.util.interfaces.IUserService;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/business/api.v2.user/address")
@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
public class UserAddressController extends ControllerUtil {

	@Autowired
	private IUserService userService;
	@Autowired
	private IUserAddressService userAddressService;


	/**
	 * @Function: save
	 * @Description 新增修改用户地址
	 * @param: [token, userAddressReq]
	 * @author: Xiwei_Qiu
	 * @date: 2020/12/8
	 **/
	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "save", method = {RequestMethod.POST})
	public ResultBody save(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody UserAddressReq userAddressReq) throws Exception {
		return ResultBody.success(userService.saveAddress(userAddressReq, getUserDTO(token)));
	}


	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "del", method = {RequestMethod.POST})
	public ResultBody del(
		@RequestHeader(value = "accessToken") String token,
		@Validated(BaseBean.Query.class)
		@RequestBody UserAddressIdReq userAddressIdReq) throws Exception {
		userService.delAddress(getUserDTO(token), userAddressIdReq);
		return ResultBody.success();
	}

	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "list", method = {RequestMethod.POST})
	public ResultBody list(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody UserAddressListReq userAddressListReq) throws Exception {
		return ResultBody.newInstance(userAddressService.getPageList(userAddressListReq,getUserDTO(token)));
	}
}
