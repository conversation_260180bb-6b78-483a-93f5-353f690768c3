package com.sgs.ecom.member.dto.min;

import java.util.List;

/**
 * @Description : ro新ui-补充信息跟着样品走，新增补充信息对象
 * <AUTHOR>
 * @Date  2023/10/31
 **/
public class RSTSSampleSupField {
	private List<AppFormField> appFormFields;
	private String appFormName;//特殊申请表


	public List<AppFormField> getAppFormFields() {
		return appFormFields;
	}

	public void setAppFormFields(List<AppFormField> appFormFields) {
		this.appFormFields = appFormFields;
	}

	public String getAppFormName() {
		return appFormName;
	}

	public void setAppFormName(String appFormName) {
		this.appFormName = appFormName;
	}
}
