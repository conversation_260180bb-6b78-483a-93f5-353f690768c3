<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.user.dao.UserToolkitRecordMapper">
    <resultMap id="BaseResultMap" type="com.sgs.ecom.member.entity.user.UserToolkitRecord">
        <id column="RECORD_ID" property="recordId" jdbcType="BIGINT"/>
        <result column="USER_ID" property="userId" jdbcType="BIGINT"/>
        <result column="TOOL_CODE" property="toolCode" jdbcType="VARCHAR"/>
        <result column="MAKE_NO" property="makeNo" jdbcType="VARCHAR"/>
        <result column="MAKE_TYPE" property="makeType" jdbcType="VARCHAR"/>
        <result column="MAKE_TITLE" property="makeTitle" jdbcType="VARCHAR"/>
        <result column="STATE" property="state" jdbcType="INTEGER"/>
        <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP"/>
        <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP"/>
        <result column="SHOW_MODE" property="showMode" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        RECORD_ID
        , USER_ID, TOOL_CODE, MAKE_NO, MAKE_TYPE, MAKE_TITLE, STATE, CREATE_DATE,
    STATE_DATE,SHOW_MODE
    </sql>
    <sql id="baseQueryWhere">
        <if test="recordId != null">
            and RECORD_ID = #{recordId}
        </if>
        <if test="userId != null">
            and USER_ID = #{userId}
        </if>
        <if test="toolCode != null">
            and TOOL_CODE = #{toolCode}
        </if>
        <if test="makeNo != null">
            and MAKE_NO = #{makeNo}
        </if>
        <if test="makeType != null">
            and MAKE_TYPE = #{makeType}
        </if>
        <if test="state != null">
            and STATE = #{state}
        </if>
        <if test="showMode != null">
            and SHOW_MODE = #{showMode}
        </if>
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from USER_TOOLKIT_RECORD
        where RECORD_ID = #{recordId,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from USER_TOOLKIT_RECORD
        where RECORD_ID = #{recordId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.sgs.ecom.member.dto.user.UserToolkitRecordDTO">
        insert into USER_TOOLKIT_RECORD (RECORD_ID, USER_ID, TOOL_CODE,
                                         MAKE_NO, MAKE_TYPE, MAKE_TITLE,
                                         STATE, CREATE_DATE, STATE_DATE,SHOW_MODE)
        values (#{recordId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{toolCode,jdbcType=VARCHAR},
                #{makeNo,jdbcType=VARCHAR}, #{makeType,jdbcType=VARCHAR}, #{makeTitle,jdbcType=VARCHAR},
                #{state,jdbcType=INTEGER}, #{createDate,jdbcType=TIMESTAMP}, #{stateDate,jdbcType=TIMESTAMP},#{showMode,jdbcType=VARCHAR})
    </insert>
    <insert id="insertBackKey" keyProperty="recordId" parameterType="com.sgs.ecom.member.entity.user.UserToolkitRecord"
            useGeneratedKeys="true">
        insert into USER_TOOLKIT_RECORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="recordId != null">
                RECORD_ID,
            </if>
            <if test="userId != null">
                USER_ID,
            </if>
            <if test="toolCode != null">
                TOOL_CODE,
            </if>
            <if test="makeNo != null">
                MAKE_NO,
            </if>
            <if test="makeType != null">
                MAKE_TYPE,
            </if>
            <if test="makeTitle != null">
                MAKE_TITLE,
            </if>
            <if test="state != null">
                STATE,
            </if>
            <if test="createDate != null">
                CREATE_DATE,
            </if>
            <if test="stateDate != null">
                STATE_DATE,
            </if>
            <if test="showMode != null">
                SHOW_MODE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="recordId != null">
                #{recordId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="toolCode != null">
                #{toolCode,jdbcType=VARCHAR},
            </if>
            <if test="makeNo != null">
                #{makeNo,jdbcType=VARCHAR},
            </if>
            <if test="makeType != null">
                #{makeType,jdbcType=VARCHAR},
            </if>
            <if test="makeTitle != null">
                #{makeTitle,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                #{state,jdbcType=INTEGER},
            </if>
            <if test="createDate != null">
                #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="stateDate != null">
                #{stateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="showMode != null">
                #{showMode,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sgs.ecom.member.dto.user.UserToolkitRecordDTO">
        update USER_TOOLKIT_RECORD
        <set>
            <if test="userId != null">
                USER_ID = #{userId,jdbcType=BIGINT},
            </if>
            <if test="toolCode != null">
                TOOL_CODE = #{toolCode,jdbcType=VARCHAR},
            </if>
            <if test="makeNo != null">
                MAKE_NO = #{makeNo,jdbcType=VARCHAR},
            </if>
            <if test="makeType != null">
                MAKE_TYPE = #{makeType,jdbcType=VARCHAR},
            </if>
            <if test="makeTitle != null">
                MAKE_TITLE = #{makeTitle,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                STATE = #{state,jdbcType=INTEGER},
            </if>
            <if test="createDate != null">
                CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="stateDate != null">
                STATE_DATE = #{stateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="showMode != null">
                SHOW_MODE =  #{showMode,jdbcType=VARCHAR},
            </if>
        </set>
        where RECORD_ID = #{recordId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sgs.ecom.member.dto.user.UserToolkitRecordDTO">
        update USER_TOOLKIT_RECORD
        set USER_ID     = #{userId,jdbcType=BIGINT},
            TOOL_CODE   = #{toolCode,jdbcType=VARCHAR},
            MAKE_NO     = #{makeNo,jdbcType=VARCHAR},
            MAKE_TYPE   = #{makeType,jdbcType=VARCHAR},
            MAKE_TITLE  = #{makeTitle,jdbcType=VARCHAR},
            STATE       = #{state,jdbcType=INTEGER},
            CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
            STATE_DATE  = #{stateDate,jdbcType=TIMESTAMP},
            SHOW_MODE  = #{showMode,jdbcType=VARCHAR}
        where RECORD_ID = #{recordId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch">
            update USER_TOOLKIT_RECORD

            <trim prefix="set" suffixOverrides=",">
                <trim prefix="USER_ID =case" suffix="end,">
                    <foreach collection="list" item="item" index="index">
                        <if test="item.userId !=null">
                            when RECORD_ID=#{item.recordId} then #{item.userId}
                        </if>
                    </foreach>
                </trim>
                <trim prefix="TOOL_CODE =case" suffix="end,">
                    <foreach collection="list" item="item" index="index">
                        <if test="item.toolCode !=null">
                            when RECORD_ID=#{item.recordId} then #{item.toolCode}
                        </if>
                    </foreach>
                </trim>
                <trim prefix="MAKE_NO =case" suffix="end,">
                    <foreach collection="list" item="item" index="index">
                        <if test="item.makeNo !=null">
                            when RECORD_ID=#{item.recordId} then #{item.makeNo}
                        </if>
                    </foreach>
                </trim>
                <trim prefix="MAKE_TYPE =case" suffix="end,">
                    <foreach collection="list" item="item" index="index">
                        <if test="item.makeType !=null">
                            when RECORD_ID=#{item.recordId} then #{item.makeType}
                        </if>
                    </foreach>
                </trim>
                <trim prefix="STATE =case" suffix="end,">
                    <foreach collection="list" item="item" index="index">
                        <if test="item.state !=null">
                            when RECORD_ID=#{item.recordId} then #{item.state}
                        </if>
                    </foreach>
                </trim>
                <trim prefix="STATE =case" suffix="end,">
                    <foreach collection="list" item="item" index="index">
                        <if test="item.state !=null">
                            when RECORD_ID=#{item.recordId} then #{item.state}
                        </if>
                    </foreach>
                </trim>
                <trim prefix="SHOW_MODE =case" suffix="end,">
                    <foreach collection="list" item="item" index="index">
                        <if test="item.showMode !=null">
                            when RECORD_ID=#{item.recordId} then #{item.showMode}
                        </if>
                    </foreach>
                </trim>
            </trim>
            where
            <foreach collection="list" separator="or" item="item" index="index">
                RECORD_ID=#{item.recordId}
            </foreach>
    </update>

    <!-- 根据条件查询返回集合 -->
    <select id="selectListByMap" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from USER_TOOLKIT_RECORD
        <where>
            <if test="userId != null">
                and USER_ID = #{userId}
            </if>
            <if test="toolCode != null">
                and TOOL_CODE = #{toolCode}
            </if>
            <if test="makeType != null">
                and MAKE_TYPE = #{makeType}
            </if>
            <if test="state != null">
                and STATE = #{state}
            </if>
            <if test="showMode != null">
               AND SHOW_MODE =  #{showMode}
            </if>

        </where>

        ORDER BY CREATE_DATE DESC



    </select>
    <select id="selectCountByMap" resultType="java.lang.Integer">
        select count(RECORD_ID)
        from USER_TOOLKIT_RECORD
        where state = 1
        <include refid="baseQueryWhere"></include>>
    </select>
    <select id="selectOneByMap" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from USER_TOOLKIT_RECORD
        <where>
            <if test="userId != null">
                and USER_ID = #{userId}
            </if>
            <if test="toolCode != null">
                and TOOL_CODE = #{toolCode}
            </if>
            <if test="makeType != null">
                and MAKE_TYPE = #{makeType}
            </if>
            <if test="showMode != null">
                AND SHOW_MODE =  #{showMode}
            </if>
            <if test="state != null">
                AND STATE =  #{state}
            </if>
        </where>

        ORDER BY CREATE_DATE DESC
        limit 1


    </select>
</mapper>