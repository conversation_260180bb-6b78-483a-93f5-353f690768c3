<!DOCTYPE html
        PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd padding: 10px 0 0 0; margin: 0;">
<html xmlns="http://www.w3.org/1999/xhtml padding: 10px 0 0 0; margin: 0;">

<head>
    <meta charset="UTF-8">
    </meta>
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    </meta>
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    </meta>

    <style type="text/css">
        .template-htmml {
            background: #fff;
            z-index: 2222;
            width: 100%;
        }
        .template-htmml-logo {
            padding-bottom: 20px;
        }
        .template-htmml-logo img {
            width: 150px;
        }
        .template-htmml-title {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }
        .template-htmml-title p:first-child {
            font-size: 16px;
        }
        .template-htmml-item {
            margin: 35px 0 10px 0;
            text-align: left;
        }
        .template-htmml-item li {
            list-style: none;
            width: 100%;
            height: 25px;
            line-height: 25px;
            text-align: left;
        }
        .template-htmml-table-box {
            position: relative;
        }
        .template-htmml-table-box table {
            border-top: 1px solid #ddd;
            border-right: 1px solid #ddd;
        }
        .template-htmml-table-box th,
        .template-htmml-table-box td {
            border-bottom: 1px solid #ddd;
            border-left: 1px solid #ddd;
            padding: 5px;
        }
        .template-htmml-table-box img {
            position: absolute;
            bottom: -50px;
            right: 0;
            width: 120px;
        }
        .template-htmml-box-wrap {
            padding: 3px0 0 0 0;
        }
        .template-htmml-box-wrap .bank-info,
        .template-htmml-box-wrap .tips {
            padding-top: 20px;
        }
        .template-htmml-box-wrap p,
        .template-htmml-box-wrap .tips {
            line-height: 10px;
            height: 10px;
        }
    </style>
</head>

<body>
<div id='template-htmml'
     class="template-htmml">
    <div style="font-family: SimSun; color: black; margin: 0;font-size:12px;word-wrap : break-word ;word-break:break-all;">
        <div class="template-htmml-logo">
            <img src="https://cnapp.sgs.net/sgsLogin/images/<EMAIL>"></img>
            <h4>SGS-CSTC Standards Technical services Co,Ltd</h4>
        </div>
        <div class="template-htmml-title" style='text-align: center;'>
            <h3>Monthly Report for Client</h3>
            <p>(Date From:${(voBillInfo.billStartDate)!''} To:${(voBillInfo.billEndDate)!''})</p>
        </div>
        <table class="template-htmml-item" border="0"
               width='100%'
               cellspacing='0'
               cellpadding='0'>
            <tr>
                <td width='30%'>Client：<b>${(voBillInfo.invoiceTitle)!''}</b></td>
                <td width='75%'></td>
            </tr>
            <tr>
                <td width='30%'>Customer Number：${(voBillInfo.customerNumber)!''}</td>
                <td width='75%'></td>
            </tr>
            <tr>
                <td width='30%'>Contact：${userName}</td>
                <td width='75%'>Tel：${userPhone}</td>
            </tr>
        </table>
        <div class="template-htmml-table-box">
            <table border="0"
                   width='100%'
                   cellspacing='0'
                   cellpadding='0'>
                <thead>
                <tr>
                    <th align='center' width='26%'>TIC No.</th>
                    <th align='center' width='27%'>报告号</th>
                    <th align='center' width='27%'>Reference No.</th>
                    <th align='center' width='10%'>RMB</th>
                    <th align='center' width='10%'>Remark</th>
                </tr>
                </thead>
                <tbody>
                <#list lstDtl as item>
                    <tr >
                        <td align='center'>${(item.orderNo)!''}</td>
                        <td align='center'>
                            <#list item.reportNameList as report>
                                <p>${(report) !''}</p>
                            </#list>
                        </td>
                        <td align='center'>${(item.referenceInfo)!''}</td>
                        <td align='center'>${(item.orderAmountShow)!''}</td>
                        <td align='center'>${(item.userName)!''}</td>
                    </tr>
                </#list>
                </tbody>
                <tfoot>
                <tr>
                    <th></th>
                    <th></th>
                    <th align="center">TOTAL:</th>
                    <th align="right">${total}</th>
                    <th></th>
                </tr>
                </tfoot>
            </table>
            <img src="https://apublicfilesuat.sgsonline.com.cn/TIC/2021/12/%E5%95%86%E5%9F%8E%E4%B8%93%E7%94%A8%E7%AB%A0%281%29_1640759621045.png"
                 alt=""></img>
        </div>
        <div class="template-htmml-box-wrap">
            <div class="bank-info">
                <p>Please use the following Bank Account for payment /请将汇款汇入以下银行账号：</p>
                <p>账户名称：通标标准技术服务有限公司杭州分公司</p>
                <p>收款账号：**********</p>
                <p>开户银行：花旗银行（中国）有限公司杭州分行</p>
                <p>开户行号：************</p>
                <p>开户银行地址：中国杭州市庆春路118号嘉德广场十三楼A,B,G单元</p>
            </div>
            <div class="tips">
                <p>备注:请贵司收到报表核对无误后按合同指定日期内付款。</p>
                <p>
                    <b>并将付款凭证发至我司邮箱sgsonline<EMAIL> 请在邮件中注明我司的税务发票号。</b>
                </p>
                <p>如有疑问请与我司Vera Ding 联系，TEL:0571-******** EMAIL:<EMAIL> 谢谢配合!</p>
            </div>
        </div>
    </div>
</div>

</body>

</html>