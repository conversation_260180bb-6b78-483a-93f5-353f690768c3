<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.order.dao.OrderOperatorLogMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.vo.VOOrderOperatorLog" >
    <id column="LOG_ID" property="logId" jdbcType="BIGINT" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="ORDER_TYPE" property="orderType" jdbcType="INTEGER" />
    <result column="DETAIL_NO" property="detailNo" jdbcType="VARCHAR" />
    <result column="CS_CODE" property="csCode" jdbcType="VARCHAR" />
    <result column="OPERATOR_TYPE" property="operatorType" jdbcType="TINYINT" />
    <result column="OPERATOR_DATE" property="operatorDate" jdbcType="TIMESTAMP" />
    <result column="MEMO" property="memo" jdbcType="VARCHAR" />
    <result column="ORIGINAL_PRICE" property="originalPrice" jdbcType="INTEGER" />
    <result column="CURRENT_PRICE" property="currentPrice" jdbcType="INTEGER" />
    <result column="FILE_URL" property="fileUrl" jdbcType="VARCHAR" />
    <result column="IS_COMFIRM" property="isComfirm" jdbcType="INTEGER" />
    <result column="CURRENCY" property="currency" jdbcType="VARCHAR" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.sgs.ecom.member.vo.VOOrderOperatorLog" extends="BaseResultMap" >
    <result column="OPERATOR_TEXT" property="operatorText" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    LOG_ID, ORDER_NO, ORDER_TYPE, DETAIL_NO, CS_CODE, OPERATOR_TYPE, OPERATOR_DATE, MEMO, 
    ORIGINAL_PRICE, CURRENT_PRICE, FILE_URL,IS_COMFIRM,CURRENCY
  </sql>
  <sql id="Blob_Column_List" >
    OPERATOR_TEXT
  </sql>
  <select id="selectByPrimaryOneKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from ORDER_OPERATOR_LOG
    where LOG_ID = #{logId,jdbcType=BIGINT}
  </select>


  <insert id="insertSelectiveOne" parameterType="com.sgs.ecom.member.vo.VOOrderOperatorLog" >
    insert into ORDER_OPERATOR_LOG
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="logId != null" >
        LOG_ID,
      </if>
      <if test="orderNo != null" >
        ORDER_NO,
      </if>
      <if test="orderType != null" >
        ORDER_TYPE,
      </if>
      <if test="detailNo != null" >
        DETAIL_NO,
      </if>
      <if test="csCode != null" >
        CS_CODE,
      </if>
      <if test="operatorType != null" >
        OPERATOR_TYPE,
      </if>
      <if test="operatorDate != null" >
        OPERATOR_DATE,
      </if>
      <if test="memo != null" >
        MEMO,
      </if>
      <if test="originalPrice != null" >
        ORIGINAL_PRICE,
      </if>
      <if test="currentPrice != null" >
        CURRENT_PRICE,
      </if>
      <if test="fileUrl != null" >
        FILE_URL,
      </if>
      <if test="operatorText != null" >
        OPERATOR_TEXT,
      </if>
      <if test="isShow != null" >
        IS_SHOW,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="logId != null" >
        #{logId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null" >
        #{orderType,jdbcType=INTEGER},
      </if>
      <if test="detailNo != null" >
        #{detailNo,jdbcType=VARCHAR},
      </if>
      <if test="csCode != null" >
        #{csCode,jdbcType=VARCHAR},
      </if>
      <if test="operatorType != null" >
        #{operatorType,jdbcType=TINYINT},
      </if>
      <if test="operatorDate != null" >
        #{operatorDate,jdbcType=TIMESTAMP},
      </if>
      <if test="memo != null" >
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="originalPrice != null" >
        #{originalPrice},
      </if>
      <if test="currentPrice != null" >
        #{currentPrice},
      </if>
      <if test="fileUrl != null" >
        #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="operatorText != null" >
        #{operatorText,jdbcType=LONGVARCHAR},
      </if>
      <if test="isShow != null" >
        #{isShow},
      </if>
    </trim>
  </insert>





  <resultMap id="resultDTO" type="com.sgs.ecom.member.dto.OrderOperatorLogDTO" >
    <id column="LOG_ID" property="logId" jdbcType="BIGINT" />
    <result column="ORDER_TYPE" property="orderType" jdbcType="VARCHAR" />
    <result column="OPERATOR_DATE" property="operatorDate" jdbcType="TIMESTAMP" />
    <result column="CS_CODE" property="csCode" jdbcType="VARCHAR" />
    <result column="MEMO" property="memo" jdbcType="VARCHAR" />
    <result column="OPERATOR_TYPE" property="operatorType" jdbcType="VARCHAR" />
    <result column="OPERATOR_TEXT" property="operatorText" jdbcType="VARCHAR" />
    <result column="ORIGINAL_PRICE" property="originalPrice" jdbcType="DECIMAL" />
    <result column="CURRENT_PRICE" property="currentPrice" jdbcType="DECIMAL" />
    <result column="FLAG" property="flag" jdbcType="INTEGER" />
    <result column="IS_COMFIRM" property="isComfirm" jdbcType="INTEGER" />
    <result column="IS_SHOW" property="isShow" jdbcType="INTEGER" />
    <result column="CURRENCY" property="currency" jdbcType="VARCHAR" />
    <result column="FILE_URL" property="fileUrl" jdbcType="VARCHAR" />
    <result column="IS_COMFIRM" property="isComfirm" jdbcType="INTEGER" />
    <result column="ORDER_NO" property="orderNo" jdbcType="INTEGER" />
    <result column="DETAIL_NO" property="detailNo" jdbcType="INTEGER" />
  </resultMap>

  <select id="selectListByMap" resultMap="resultDTO" >
    select
    LOG_ID,ORDER_TYPE,OPERATOR_DATE,MEMO,OPERATOR_TYPE,ORIGINAL_PRICE,CURRENT_PRICE,
    FLAG,IS_COMFIRM,IS_SHOW,FILE_URL,ORDER_NO,DETAIL_NO,CURRENCY,
    IF(OPERATOR_TYPE=2,'系统',CS_CODE) as CS_CODE,
    IF(OPERATOR_TYPE=901 or OPERATOR_TYPE=902,
    '',OPERATOR_TEXT)  as OPERATOR_TEXT
    from ORDER_OPERATOR_LOG
    <include refid="baseQueryWhere"/>
    <include refid="orderBy"/>
  </select>
  <sql id="baseQueryWhere">
    where  OPERATOR_TYPE!=101
    <if test="orderType != null">
      AND ORDER_TYPE=#{orderType}
    </if>
    <if test="orderNo != null">
      AND ORDER_NO=#{orderNo}
    </if>
    <if test="detailNo != null">
      AND DETAIL_NO=#{detailNo}
    </if>
    <if test="operatorText != null" >
      AND OPERATOR_TEXT= #{operatorText}
    </if>
    <if test="operatorType != null">
      AND OPERATOR_TYPE=#{operatorType}
    </if>
    <if test="notConfirmFlg != null">
      AND IS_COMFIRM !=#{notConfirmFlg}
    </if>
    <if test="operatorTypeList != null">
      and OPERATOR_TYPE in
      <foreach collection="operatorTypeList" separator="," open="(" close=")" item="operatorType">
        #{operatorType}
      </foreach>
    </if>
    <if test="orderNoList != null">
      and ORDER_NO in
      <foreach collection="orderNoList" separator="," open="(" close=")" item="orderNo">
        #{orderNo}
      </foreach>
    </if>
  </sql>


  <sql id="orderBy">
    <if test="orderBy != null ">
      order by ${orderBy}
    </if>
  </sql>

  <select id="getLastOrderLog" resultMap="resultDTO" >
    select
    LOG_ID,OPERATOR_DATE,MEMO,CS_CODE,OPERATOR_TYPE,OPERATOR_TEXT,IS_COMFIRM,ORIGINAL_PRICE,CURRENT_PRICE
    from ORDER_OPERATOR_LOG
    <include refid="baseQueryWhere"/>
    <include refid="orderBy"/>
    limit 1
  </select>
</mapper>