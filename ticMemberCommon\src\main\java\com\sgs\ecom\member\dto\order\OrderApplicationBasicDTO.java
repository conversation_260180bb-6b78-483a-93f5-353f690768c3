package com.sgs.ecom.member.dto.order;

import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.dto.application.OrderApplicationAttrDTO;
import com.sgs.ecom.member.dto.center.SysApplicationAttrDTO;

import java.util.List;

public class OrderApplicationBasicDTO extends BaseOrderFilter {
 

 	@ApiAnno(groups={Default.class,OrderApplicationOther.class})
	@BeanAnno(dtocls={SysApplicationAttrDTO.class})
 	private String areaCode;


	@ApiAnno(groups={Default.class,OrderApplicationOther.class})
	@BeanAnno(dtocls={OrderApplicationAttrDTO.class})
	private List<OrderApplicationAttrDTO> attrDTOList;
 	
 	public void setAreaCode(String areaCode){
 		 this.areaCode=areaCode;
 	}
 	public String getAreaCode(){
 		 return this.areaCode;
 	}
 



	public List<OrderApplicationAttrDTO> getAttrDTOList() {
		return attrDTOList;
	}

	public void setAttrDTOList(List<OrderApplicationAttrDTO> attrDTOList) {
		this.attrDTOList = attrDTOList;
	}
}