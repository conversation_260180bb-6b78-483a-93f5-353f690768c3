package com.sgs.ecom.member.fegin;

import com.platform.annotation.AuthRequired;
import com.platform.util.CollectionService;
import com.sgs.ecom.member.acct.service.interfaces.IAcctBalanceSV;
import com.sgs.ecom.member.dto.acct.MonthCustMoreDTO;
import com.sgs.ecom.member.service.acct.interfaces.IAcctBalanceService;
import com.sgs.ecom.member.service.custom.interfaces.ICustInfoSV;
import com.sgs.ecom.member.service.custom.interfaces.ICustServiceSV;
import com.sgs.ecom.member.util.ResultBody;
import com.sgs.ecom.member.vo.VOAcctBalance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/business/rpc.v2.member/balance")
public class AcctBalanceRpc {

	@Autowired
	private IAcctBalanceService acctBalanceService;
	@Autowired
	private ICustInfoSV custInfoSV;

	@AuthRequired(login = "NULL", sign = true)
	@RequestMapping(value = "addByCust", method = { RequestMethod.POST })
	public ResultBody addBalanceByCust(@RequestBody VOAcctBalance voAcctBalance) throws Exception {
		acctBalanceService.addBalanceByCust(voAcctBalance);
		return ResultBody.success();
	}
}
