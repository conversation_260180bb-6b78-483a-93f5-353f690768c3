package com.sgs.ecom.member.controller.order;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.base.BaseBean;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.dto.order.OrderSampleMoreDTO;
import com.sgs.ecom.member.request.OrderNoReq;
import com.sgs.ecom.member.service.oiq.interfaces.IOiqOrderDetailService;
import com.sgs.ecom.member.service.order.interfaces.IOrderBaseInfoService;
import com.sgs.ecom.member.service.order.interfaces.IOrderSampleService;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import com.sgs.util.json.JsonTransUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.oiq/detail")
public class OiqOrderDetailController extends ControllerUtil {


    @Resource
    private IOiqOrderDetailService oiqOrderDetailService;

    /**
    * @params [accessToken, orderNoReq]
    * @return com.sgs.ecom.member.util.ResultBody
    * @description 查询项目数据
    * <AUTHOR> || created at 2023/8/28 17:03
    */
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "qryList", method = { RequestMethod.POST })
    public ResultBody qryList(
            @RequestHeader(value="accessToken") String token,
            @Validated(BaseBean.Query.class)
            @RequestBody OrderNoReq orderNoReq) throws Exception {
        return ResultBody.newInstance(oiqOrderDetailService.qryList(orderNoReq.getOrderNo(),getUserDTO(token)));
    }





}
