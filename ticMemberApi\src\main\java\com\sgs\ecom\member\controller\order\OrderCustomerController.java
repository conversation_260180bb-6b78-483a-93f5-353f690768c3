package com.sgs.ecom.member.controller.order;

import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;

import com.sgs.base.BaseBean;
import com.sgs.ecom.member.annotation.CheckOrderShare;
import com.sgs.ecom.member.dto.user.UserDTO;
import com.sgs.ecom.member.request.OrderNoReq;
import com.sgs.ecom.member.request.oiq.OiqQryInfoReq;
import com.sgs.ecom.member.request.order.OrderCustomerListReq;
import com.sgs.ecom.member.request.order.OrderCustomerReq;
import com.sgs.ecom.member.service.order.interfaces.IOrderCustomerService;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import org.apache.commons.lang.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;


@RestController
@RequestMapping("/business/api.v2.order/customer")
public class OrderCustomerController extends ControllerUtil {

	@Resource
	private IOrderCustomerService orderCustomerService;

	@HystrixCommand
	@AuthRequired(login = "NULL", sign = true)
	@RequestMapping(value = "getCustomerIdList", method = {RequestMethod.POST})
	public ResultBody getCustomerIdList(
			@RequestBody OrderCustomerReq orderCustomerReq) throws Exception {
		return ResultBody.newInstance(orderCustomerService.getCustomerIdList(orderCustomerReq));
	}

	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "saveCustomer", method = {RequestMethod.POST})
	public ResultBody saveCustomer(
			@RequestHeader(value="system") String system,
			@RequestBody OrderCustomerReq orderCustomerReq,
			@RequestHeader(value = "accessToken") String token) throws Exception {
		orderCustomerService.saveCustomer(orderCustomerReq, getUserDTO(token));
		return ResultBody.success();
	}


	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "qryCustomerOrderList", method = {RequestMethod.POST})
	public ResultBody qryCustomerOrderList(
			@RequestHeader(value="system") String system,
			@RequestBody OrderCustomerListReq orderCustomerListReq ,
			@RequestHeader(value = "accessToken") String token,
			@RequestHeader(value = "bu",required = false) String bu) throws Exception {
		if(StringUtils.isNotBlank(bu)){
			orderCustomerListReq.setBu(bu);
		}
		return ResultBody.newInstance(orderCustomerService.qryCustomerOrderList(orderCustomerListReq, getUserDTO(token)));
	}

	@CheckOrderShare
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "expOrderList", method = {RequestMethod.POST})
	public void expOrderList(
			@RequestHeader(value = "accessToken") String token,
			@RequestBody OrderCustomerListReq orderCustomerListReq ,
			@RequestHeader(value = "bu",required = false) String bu,HttpServletResponse res) throws Exception {
		if(StringUtils.isNotBlank(bu)){
			orderCustomerListReq.setBu(bu);
		}
		orderCustomerService.expOrderList(orderCustomerListReq, getUserDTO(token), res);
	}

	@HystrixCommand
	@CheckOrderShare
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "qryInfo", method = { RequestMethod.POST })
	public ResultBody qryInfo(
			@RequestHeader(value="accessToken") String token,
			@RequestBody OiqQryInfoReq qryInfoReq,
			@RequestHeader(value = "bu",required = false) String bu) throws Exception {
		UserDTO userDTO= StringUtils.isBlank(token)?null:getUserDTO(token);
		if(StringUtils.isNotBlank(bu)){
			qryInfoReq.setBu(bu);
		}
		return ResultBody.newInstance(orderCustomerService.qryInfo(qryInfoReq,userDTO));
	}

	@CheckOrderShare
	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "qryDetail", method = {RequestMethod.POST})
	public ResultBody updateDetail(
			@RequestHeader(value = "accessToken") String token,
			@Validated(BaseBean.Query.class)
			@RequestBody OrderNoReq orderNoReq) throws Exception {
		return ResultBody.success(orderCustomerService.qryDetail(orderNoReq.getOrderNo()));
	}
}
