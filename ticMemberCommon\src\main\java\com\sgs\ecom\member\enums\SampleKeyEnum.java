package com.sgs.ecom.member.enums;

import com.sgs.ecom.member.dto.order.OrderSampleFromDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName SampleKeyEnum
 * <AUTHOR>
 * @Date 2024-09-14 12:19
 */

@Getter
@AllArgsConstructor
public enum SampleKeyEnum {

    // 枚举值执行对应方法

    SampleStorageRequirements("sampleStorageRequirements"),
    SampleHazardDescription("sampleHazardDescription"),
    ExamineProjectArea("examineProjectArea"),
    PurposeOfTesting("purposeOfTesting"),
    TransportationStorageMethods("transportationStorageMethods"),
    Brand("brand"),
    SampleState("sampleState"),
    ;

    private String sampleKey;

}
