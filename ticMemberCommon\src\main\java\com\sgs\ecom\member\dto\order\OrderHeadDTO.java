package com.sgs.ecom.member.dto.order;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.json.TimeFormatSerializer;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.util.order.UseDateUtil;
import com.sgs.ecom.member.util.serializer.TimeStringFormatSerializer;
import org.springframework.format.annotation.DateTimeFormat;

import java.sql.Timestamp;
import java.util.Date;

public class OrderHeadDTO {
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String orderNo;
    private Long orderId;
    @JsonSerialize(using = TimeStringFormatSerializer.class)
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String stateDate;


    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String dateStr;

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int userSex;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    //注解BeanAnno增加 shield = "phone",工具包版本2.3.92.RELEASW.AZURE。
    // 配置枚举姓名name,手机phone,身份证idcard.
    @BeanAnno(shield = "name")
    private String userName;

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String categoryPath;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String itemNameList;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String city;

    private String createDate;
    private String endDate;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String quotationTimeStr;

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public int getUserSex() {
        return userSex;
    }

    public void setUserSex(int userSex) {
        this.userSex = userSex;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getCategoryPath() {
        return categoryPath;
    }

    public void setCategoryPath(String categoryPath) {
        this.categoryPath = categoryPath;
    }

    public String getItemNameList() {
        return itemNameList;
    }

    public void setItemNameList(String itemNameList) {
        this.itemNameList = itemNameList;
    }

    public String getStateDate() {
        return stateDate;
    }

    public void setStateDate(String stateDate) {
        this.stateDate = stateDate;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getDateStr() {
        return UseDateUtil.getHeadDateShow(stateDate);
    }

    public void setDateStr(String dateStr) {
        this.dateStr = dateStr;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getQuotationTimeStr() {
        return  UseDateUtil.getQuotationTimeShow(createDate,endDate);
    }

    public void setQuotationTimeStr(String quotationTimeStr) {
        this.quotationTimeStr = quotationTimeStr;
    }
}
