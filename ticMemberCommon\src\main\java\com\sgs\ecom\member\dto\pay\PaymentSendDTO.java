package com.sgs.ecom.member.dto.pay;

import lombok.Data;

/**
 * <AUTHOR>
 * @Description :
 * @date 2024/11/5
 */
@Data
public class PaymentSendDTO {
    private String paymentType;//支付方式
    private String paymentTypeName;//支付方式中文
    private String originalPrice;//应付金额
    private String paymentAmount;//实付金额
    private String paymentDate;//付款时间
    private String paymentNo;//
    private String parentId;//商户订单号
    //支付凭证上传时间
    private String createDate;
    //支付凭证
    private String fileName;
    private String accountName;
}
