package com.sgs.ecom.member.dto.min;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class MinSampleDTO {
  /* "roId": 22,
	   "name": "样品2",
	   "ename": "sample2",
	   "model": "model2",
	   "sn": "sn2",
	   "material": "material2",
	   "manufacturer0": "生产商",
	   "manufacturer1": "manufacturer",
	   "buyer0": "购买商",
	   "buyer1": "buyer",
	   "remark": "我是备注2",
	    */
	private String roId;
	private String code;
	private String name;
	private String ename;
	private String model;//产品规格  productInfo
	private String sn;//货号 productBatch
	private String material;//材质 materialGrade
	private String emodel;//产品规格  productInfo
	private String esn;//货号 productBatch
	private String ematerial;//材质 materialGrade
	private String manufacturer0;// 供应商supplierName
	private String manufacturer1;
	private String buyer0;
	private String buyer1;
	private String remark;
	private String eremark;
	private String extendedModelNo;
	private String extendedModelNoEn;
	private String originalProduct;
	private String originalProductEn;
	private String destinationProduct;
	private String destinationProductEn;

	private String lotNo;
	private String elotNo;
	private String producer;
	private String eproducer;

	private String hazardousArticleOther;//其他的附加信息
	private Long [] hazardousArticleInd;

	private Map map;
	private String  note;
	private List<RSTSFileDTO> sampleFiles=new ArrayList<>();


	private List<RSTSSampleSupField> appForms;

	public List<RSTSSampleSupField> getAppForms() {
		return appForms;
	}

	public void setAppForms(List<RSTSSampleSupField> appForms) {
		this.appForms = appForms;
	}

	public String getRoId() {
		return roId;
	}

	public void setRoId(String roId) {
		this.roId = roId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getEname() {
		return ename;
	}

	public void setEname(String ename) {
		this.ename = ename;
	}

	public String getModel() {
		return model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	public String getSn() {
		return sn;
	}

	public void setSn(String sn) {
		this.sn = sn;
	}

	public String getMaterial() {
		return material;
	}

	public void setMaterial(String material) {
		this.material = material;
	}

	public String getManufacturer0() {
		return manufacturer0;
	}

	public void setManufacturer0(String manufacturer0) {
		this.manufacturer0 = manufacturer0;
	}

	public String getManufacturer1() {
		return manufacturer1;
	}

	public void setManufacturer1(String manufacturer1) {
		this.manufacturer1 = manufacturer1;
	}

	public String getBuyer0() {
		return buyer0;
	}

	public void setBuyer0(String buyer0) {
		this.buyer0 = buyer0;
	}

	public String getBuyer1() {
		return buyer1;
	}

	public void setBuyer1(String buyer1) {
		this.buyer1 = buyer1;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getHazardousArticleOther() {
		return hazardousArticleOther;
	}

	public void setHazardousArticleOther(String hazardousArticleOther) {
		this.hazardousArticleOther = hazardousArticleOther;
	}

	public Long[] getHazardousArticleInd() {
		return hazardousArticleInd;
	}

	public void setHazardousArticleInd(Long[] hazardousArticleInd) {
		this.hazardousArticleInd = hazardousArticleInd;
	}

	public String getEmodel() {
		return emodel;
	}

	public void setEmodel(String emodel) {
		this.emodel = emodel;
	}

	public String getEsn() {
		return esn;
	}

	public void setEsn(String esn) {
		this.esn = esn;
	}

	public String getEmaterial() {
		return ematerial;
	}

	public void setEmaterial(String ematerial) {
		this.ematerial = ematerial;
	}

	public String getLotNo() {
		return lotNo;
	}

	public void setLotNo(String lotNo) {
		this.lotNo = lotNo;
	}

	public String getElotNo() {
		return elotNo;
	}

	public void setElotNo(String elotNo) {
		this.elotNo = elotNo;
	}

	public String getProducer() {
		return producer;
	}

	public void setProducer(String producer) {
		this.producer = producer;
	}

	public String getEproducer() {
		return eproducer;
	}

	public void setEproducer(String eproducer) {
		this.eproducer = eproducer;
	}

	public Map getMap() {
		return map;
	}

	public void setMap(Map map) {
		this.map = map;
	}

	public String getEremark() {
		return eremark;
	}

	public void setEremark(String eremark) {
		this.eremark = eremark;
	}

	public List<RSTSFileDTO> getSampleFiles() {
		return sampleFiles;
	}

	public void setSampleFiles(List<RSTSFileDTO> sampleFiles) {
		this.sampleFiles = sampleFiles;
	}

	public String getNote() {
		return note;
	}

	public void setNote(String note) {
		this.note = note;
	}

	public String getExtendedModelNo() {
		return extendedModelNo;
	}

	public void setExtendedModelNo(String extendedModelNo) {
		this.extendedModelNo = extendedModelNo;
	}

	public String getExtendedModelNoEn() {
		return extendedModelNoEn;
	}

	public void setExtendedModelNoEn(String extendedModelNoEn) {
		this.extendedModelNoEn = extendedModelNoEn;
	}

	public String getOriginalProduct() {
		return originalProduct;
	}

	public void setOriginalProduct(String originalProduct) {
		this.originalProduct = originalProduct;
	}

	public String getOriginalProductEn() {
		return originalProductEn;
	}

	public void setOriginalProductEn(String originalProductEn) {
		this.originalProductEn = originalProductEn;
	}

	public String getDestinationProduct() {
		return destinationProduct;
	}

	public void setDestinationProduct(String destinationProduct) {
		this.destinationProduct = destinationProduct;
	}

	public String getDestinationProductEn() {
		return destinationProductEn;
	}

	public void setDestinationProductEn(String destinationProductEn) {
		this.destinationProductEn = destinationProductEn;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}
}
