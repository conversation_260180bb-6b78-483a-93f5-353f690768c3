package com.sgs.ecom.member.controller.order;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.ecom.member.domain.order.service.interfaces.IOrderBaseInfoDomainService;
import com.sgs.ecom.member.dto.base.BaseOrderDTO;
import com.sgs.ecom.member.dto.user.UserDTO;
import com.sgs.ecom.member.enumtool.BaseOrderStateEnum;
import com.sgs.ecom.member.enumtool.OiqMailEnum;
import com.sgs.ecom.member.enumtool.OiqSmsEnum;
import com.sgs.ecom.member.enumtool.OrderTypeEnum;
import com.sgs.ecom.member.enumtool.event.EventEnum;
import com.sgs.ecom.member.enumtool.send.WechatEnum;
import com.sgs.ecom.member.event.EventApiUtil;
import com.sgs.ecom.member.infrastructure.event.MailEventUtil;
import com.sgs.ecom.member.infrastructure.event.SmsEventUtil;
import com.sgs.ecom.member.request.OrderNoReq;
import com.sgs.ecom.member.request.detail.ExpressReq;
import com.sgs.ecom.member.request.oiq.*;
import com.sgs.ecom.member.service.oiq.interfaces.IOiqOrderApplicationService;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import com.sgs.ecom.member.util.send.ApiOtherDTO;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.oiq/application")
public class OiqOrderApplicationController extends ControllerUtil {

    @Resource
    private IOiqOrderApplicationService oiqOrderApplicationService;
    @Resource
    private EventApiUtil eventApiUtil;
    @Resource
    private MailEventUtil mailEventUtil;
    @Resource
    private SmsEventUtil smsEventUtil;
    @Resource
    private IOrderBaseInfoDomainService orderBaseInfoDomainService;

    /**
     *@Function: save
     *@Description 保存申请表
     *@param: [token, applicationReq]
     *@version:
     *@author: Xiwei_Qiu
     *@date: 2021/3/9
     **/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "saveForm", method = { RequestMethod.POST })
    public ResultBody saveForm (
            @RequestHeader(value = "system",required = false) String system,
            @RequestHeader(value="accessToken") String token,
            @RequestBody OiqOrderReq oiqOrderReq)throws Exception{

        oiqOrderReq.setSystem(system);
        String jsonStr=oiqOrderApplicationService.checkFormToSendFlg(oiqOrderReq,getUserDTO(token));
        JSONObject jsonObject=oiqOrderApplicationService.saveForm(oiqOrderReq,getUserDTO(token));
        //发给客户的短信已经合并 发客服就小门户会发
        if(StringUtils.isNotBlank(oiqOrderReq.getEventOrderNo())){
            eventApiUtil.saveEvent(oiqOrderReq.getEventOrderNo(), EventEnum.SAVE_INVOICE_BOSS);
        }
        Boolean sendWeChat=false;
        if(oiqOrderReq.getEventFlg()==1 && OrderTypeEnum.OIQ_PORTAL_ORDER.getIndex().equals(oiqOrderReq.getOrderType()) && StringUtils.isBlank(jsonStr)){
            eventApiUtil.saveEvent(oiqOrderReq.getEventOrderNo(), EventEnum.MOD_USER_LABEL);
            sendWeChat=true;
            mailEventUtil.sendMail(oiqOrderReq.getEventOrderNo(),oiqOrderReq.getOrderType(),oiqOrderReq.getType()==1? OiqMailEnum.OIQ_PORTAL_SAVE_FORM:OiqMailEnum.OIQ_PORTAL_UPDATE_FORM,2L);
        }
        if(oiqOrderReq.getEventFlg()==1 && OrderTypeEnum.OIQORDER.getIndex().equals(oiqOrderReq.getOrderType())){
            mailEventUtil.sendMail(oiqOrderReq.getEventOrderNo(),oiqOrderReq.getOrderType(), OiqMailEnum.USER_SAVE_FORM,0L);
            smsEventUtil.sendSms(oiqOrderReq.getEventOrderNo(),oiqOrderReq.getOrderType(), OiqSmsEnum.USER_SAVE_FORM);
            sendWeChat=true;
        }
        if(StringUtils.isNotBlank(jsonStr)){
            eventApiUtil.saveEvent(oiqOrderReq.getEventOrderNo(), EventEnum.SAVE_TO_SEND_DML_MAIL, new ApiOtherDTO(jsonStr));
        }
        if(oiqOrderReq.getSaveOrderFlg()==1 && StringUtils.isNotBlank(oiqOrderReq.getApiOtherDTO().getInquiryOrderNo())){
            eventApiUtil.saveEvent(oiqOrderReq.getApiOtherDTO().getInquiryOrderNo(), EventEnum.CREATE_ORDER_ADD_LABEL);
        }
        if(sendWeChat){
            BaseOrderDTO baseOrderDTO=orderBaseInfoDomainService.selectBaseByOrderNo(oiqOrderReq.getEventOrderNo());
            if(baseOrderDTO.getState()!= BaseOrderStateEnum.WAITEXAMINE.getIndex()){
                eventApiUtil.sendWechatMsg(oiqOrderReq.getEventOrderNo(),EventEnum.SEND_WECHAT_MSG, WechatEnum.USER_SAVE_FORM);
            }
        }

        return ResultBody.newInstance(jsonObject);
    }



    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "qryApplication", method = { RequestMethod.POST })
    public ResultBody qryApplication(
            @RequestHeader(value="accessToken") String token,
            @RequestBody OrderNoReq orderNoReq) throws Exception {
        return ResultBody.newInstance(oiqOrderApplicationService.qryApplication(orderNoReq, getUserDTO(token)));
    }

    /**
    * @params [token, orderNoReq]
    * @return com.sgs.ecom.member.util.ResultBody
    * @description 用于查上一单的数据
    * <AUTHOR> || created at 2023/8/28 14:25
    */
    @HystrixCommand
    @AuthRequired(login = "NULL", sign = true)
    @RequestMapping(value = "qryInfo", method = { RequestMethod.POST })
    public ResultBody qryInfo(
            @RequestHeader(value="accessToken") String token,
            @RequestBody OiqQryInfoReq qryInfoReq,
            @RequestHeader(value = "bu",required = false) String bu) throws Exception {
        UserDTO userDTO= StringUtils.isBlank(token)?null:getUserDTO(token);
        if(StringUtils.isNotBlank(bu)){
            qryInfoReq.setBu(bu);
        }
        return ResultBody.newInstance(oiqOrderApplicationService.qryInfo(qryInfoReq,userDTO));
    }

    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "qryExpress", method = { RequestMethod.POST })
    public ResultBody qryExpress (
            @RequestHeader(value="accessToken") String token,
            @RequestBody OrderNoReq orderNoReq)throws Exception{
        return ResultBody.newInstance(oiqOrderApplicationService.selectExpressMore(orderNoReq,getUserDTO(token),token));
    }

    /**
    * @params [token, expressReq]
    * @return com.sgs.ecom.member.util.ResultBody
    * @description 保存样品寄送
    * <AUTHOR> || created at 2023/10/27 10:29
    */
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "saveExpress", method = { RequestMethod.POST })
    public ResultBody saveExpress (
            @RequestHeader(value="accessToken") String token,
            @RequestBody ExpressReq expressReq)throws Exception{
        oiqOrderApplicationService.saveExpress(expressReq,getUserDTO(token));
        return ResultBody.success();
    }

    @HystrixCommand
    @AuthRequired(login = "NULL", sign = true)
    @RequestMapping(value = "qryLabByLabCode", method = { RequestMethod.POST })
    public ResultBody qryLabByLabCode (
            @RequestBody OiqLabReq oiqLabReq)throws Exception{
        return ResultBody.newInstance(oiqOrderApplicationService.qryLabByLabCode(oiqLabReq));
    }

    @HystrixCommand
    @AuthRequired(login = "NULL", sign = true)
    @RequestMapping(value = "qryLineByLineCode", method = { RequestMethod.POST })
    public ResultBody qryLineByLineCode (
            @RequestBody OiqLineReq oiqLineReq)throws Exception{
        return ResultBody.newInstance(oiqOrderApplicationService.qryLineByLineCode(oiqLineReq));
    }

    @HystrixCommand
    @AuthRequired(login = "NULL", sign = true)
    @RequestMapping(value = "qryProgramUrlByOrder", method = { RequestMethod.POST })
    public ResultBody qryProgramUrlByOrder (
            @RequestBody OiqWechatUrlReq oiqWechatUrlReq)throws Exception{
        return ResultBody.newInstance(oiqOrderApplicationService.qryProgramUrlByOrder(oiqWechatUrlReq));
    }


}
