package com.sgs.ecom.member.bo; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import com.platform.annotation.CharacterVaild; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import com.platform.util.json.PriceFormatSerializer; 
import com.platform.annotation.BeanAnno; 

public class BOOrderBaseInfo{
 
 	public static final String SEQUENCE = "ORDER_ID"; 
  
 	public static final String BO_SQL = "{ORDER_BASE_INFO}"; 
 
 	public static final String OWNER ="member";

 	public static final String PAY_DATE="payDate";
 	public static final String STATE_DATE="stateDate";
 	public static final String ORDER_TYPE="orderType";
 	public static final String STATE="state";
 	public static final String USER_ID="userId";
 	public static final String ORDER_NO="orderNo";
 	public static final String USER_NAME="userName";
 	public static final String PLATFORM_ORDER="platformOrder";
 	public static final String ORDER_AMOUNT="orderAmount";
 	public static final String PLATFORM="platform";
 	public static final String USER_PHONE="userPhone";
 	public static final String CREATE_DATE="createDate";
 	public static final String ORDER_ID="orderId";
 	public static final String PAY_STATE="payState";
 	public static final String DISCOUNT_AMOUNT="discountAmount";
 	public static final String REAL_AMOUNT="realAmount";
 	public static final String RELATE_ORDER_NO="relateOrderNo";

 	@BeanAnno("PAY_DATE")
 	private Timestamp payDate;
 	@BeanAnno("STATE_DATE")
 	private Timestamp stateDate;
 	@BeanAnno("ORDER_TYPE")
 	private long orderType;
 	@BeanAnno("STATE")
 	private long state;
 	@BeanAnno("USER_ID")
 	private long userId;
 	@BeanAnno("ORDER_NO")
 	private String orderNo;
 	@BeanAnno("USER_NAME")
 	private String userName;
 	@BeanAnno("PLATFORM_ORDER")
 	private String platformOrder;
 	@BeanAnno("ORDER_AMOUNT")
 	private long orderAmount;
 	@BeanAnno("PLATFORM")
 	private String platform;
 	@BeanAnno("USER_PHONE")
 	private long userPhone;
 	@BeanAnno("CREATE_DATE")
 	private Timestamp createDate;
 	@BeanAnno("ORDER_ID")
 	private long orderId;
 	@BeanAnno("PAY_STATE")
 	private long payState;
 	@BeanAnno("DISCOUNT_AMOUNT")
 	private long discountAmount;
 	@BeanAnno("REAL_AMOUNT")
 	private long realAmount;
 	@BeanAnno("RELATE_ORDER_NO")
 	private String relateOrderNo;

 	public void setPayDate(Timestamp payDate){
 		 this.payDate=payDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getPayDate(){
 		 return this.payDate;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setOrderType(long orderType){
 		 this.orderType=orderType;
 	}
 	public long getOrderType(){
 		 return this.orderType;
 	}
 
 	 
 	public void setState(long state){
 		 this.state=state;
 	}
 	public long getState(){
 		 return this.state;
 	}
 
 	 
 	public void setUserId(long userId){
 		 this.userId=userId;
 	}
 	public long getUserId(){
 		 return this.userId;
 	}
 
 	 
 	@CharacterVaild(len = 30) 
 	public void setOrderNo(String orderNo){
 		 this.orderNo=orderNo;
 	}
 	public String getOrderNo(){
 		 return this.orderNo;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setUserName(String userName){
 		 this.userName=userName;
 	}
 	public String getUserName(){
 		 return this.userName;
 	}
 
 	 
 	@CharacterVaild(len = 30) 
 	public void setPlatformOrder(String platformOrder){
 		 this.platformOrder=platformOrder;
 	}
 	public String getPlatformOrder(){
 		 return this.platformOrder;
 	}
 
 	 
 	public void setOrderAmount(long orderAmount){
 		 this.orderAmount=orderAmount;
 	}
 	@JsonSerialize(using = PriceFormatSerializer.class) 
 	public long getOrderAmount(){
 		 return this.orderAmount;
 	}
 
 	 
 	@CharacterVaild(len = 20) 
 	public void setPlatform(String platform){
 		 this.platform=platform;
 	}
 	public String getPlatform(){
 		 return this.platform;
 	}
 
 	 
 	public void setUserPhone(long userPhone){
 		 this.userPhone=userPhone;
 	}
 	public long getUserPhone(){
 		 return this.userPhone;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setOrderId(long orderId){
 		 this.orderId=orderId;
 	}
 	public long getOrderId(){
 		 return this.orderId;
 	}
 
 	 
 	public void setPayState(long payState){
 		 this.payState=payState;
 	}
 	public long getPayState(){
 		 return this.payState;
 	}
 
 	 
 	public void setDiscountAmount(long discountAmount){
 		 this.discountAmount=discountAmount;
 	}
 	@JsonSerialize(using = PriceFormatSerializer.class) 
 	public long getDiscountAmount(){
 		 return this.discountAmount;
 	}
 
 	 
 	public void setRealAmount(long realAmount){
 		 this.realAmount=realAmount;
 	}
 	@JsonSerialize(using = PriceFormatSerializer.class) 
 	public long getRealAmount(){
 		 return this.realAmount;
 	}
 
 	 
 	@CharacterVaild(len = 30) 
 	public void setRelateOrderNo(String relateOrderNo){
 		 this.relateOrderNo=relateOrderNo;
 	}
 	public String getRelateOrderNo(){
 		 return this.relateOrderNo;
 	}
 
 	 
}