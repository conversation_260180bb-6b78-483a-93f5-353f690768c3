package com.sgs.ecom.member.dto.user;
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.json.TimeFormatSerializer;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.dto.rsts.SampleAttributeDTO;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class UserSampleDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select MATERIAL_GRADE_EN,PRODUCT_BATCH_EN,SUPPLIER_NAME_EN,STATE_DATE,STATE,USER_ID,CUSTOMER_REFERRENCE_EN,PRODUCT_INFO,PRODUCT_INFO_EN,CREATE_DATE,PRODUCT_BATCH,BUYERS_NAME_EN,SAMPLE_NAME,SAMPLE_NAME_EN,SAMPLE_NO,MATERIAL_GRADE,CUSTOMER_REFERRENCE,SUPPLIER_NAME,BUYERS_NAME,SAMPLE_ID from USER_SAMPLE";

 
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="MATERIAL_GRADE_EN", getName="getMaterialGradeEn", setName="setMaterialGradeEn")
 	private String materialGradeEn;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="PRODUCT_BATCH_EN", getName="getProductBatchEn", setName="setProductBatchEn")
 	private String productBatchEn;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="SUPPLIER_NAME_EN", getName="getSupplierNameEn", setName="setSupplierNameEn")
 	private String supplierNameEn;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private Timestamp stateDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="USER_ID", getName="getUserId", setName="setUserId")
 	private Long userId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="PRODUCT_INFO", getName="getProductInfo", setName="setProductInfo")
 	private String productInfo;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="PRODUCT_INFO_EN", getName="getProductInfoEn", setName="setProductInfoEn")
 	private String productInfoEn;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="PRODUCT_BATCH", getName="getProductBatch", setName="setProductBatch")
 	private String productBatch;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="BUYERS_NAME_EN", getName="getBuyersNameEn", setName="setBuyersNameEn")
 	private String buyersNameEn;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="SAMPLE_NAME", getName="getSampleName", setName="setSampleName")
 	private String sampleName;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="SAMPLE_NAME_EN", getName="getSampleNameEn", setName="setSampleNameEn")
 	private String sampleNameEn;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="SAMPLE_NO", getName="getSampleNo", setName="setSampleNo")
 	private String sampleNo;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="MATERIAL_GRADE", getName="getMaterialGrade", setName="setMaterialGrade")
 	private String materialGrade;

 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="SUPPLIER_NAME", getName="getSupplierName", setName="setSupplierName")
 	private String supplierName;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="BUYERS_NAME", getName="getBuyersName", setName="setBuyersName")
 	private String buyersName;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="SAMPLE_ID", getName="getSampleId", setName="setSampleId")
 	private Long sampleId;
	@ApiAnno(groups={Default.class})
 	private String remark;
	@ApiAnno(groups={Default.class})
 	private String remarkEn;
	@ApiAnno(groups={Default.class})
	private List<UserSampleAttributeDTO> userSampleAttributeDTOList;
	@ApiAnno(groups={Default.class})
	private List<SampleAttributeDTO> sampleFeatures=new ArrayList<>();
	@ApiAnno(groups={Default.class})
	private UserSampleAttributeDTO sampleStateDTO=new UserSampleAttributeDTO();
	@ApiAnno(groups={Default.class})
	private String lotNo;
	@ApiAnno(groups={Default.class})
	private String lotNoEn;
	@ApiAnno(groups={Default.class})
	private String producer;
	@ApiAnno(groups={Default.class})
	private String producerEn;
	@ApiAnno(groups={Default.class})
	private String sampleSign;
	@ApiAnno(groups={Default.class})
	private List<UserSampleFromDTO> sampleFromDTOList=new ArrayList<>();

	public String getSampleSign() {
		return sampleSign;
	}

	public void setSampleSign(String sampleSign) {
		this.sampleSign = sampleSign;
	}

	public String getLotNo() {
		return lotNo;
	}

	public void setLotNo(String lotNo) {
		this.lotNo = lotNo;
	}

	public String getLotNoEn() {
		return lotNoEn;
	}

	public void setLotNoEn(String lotNoEn) {
		this.lotNoEn = lotNoEn;
	}

	public String getProducer() {
		return producer;
	}

	public void setProducer(String producer) {
		this.producer = producer;
	}

	public String getProducerEn() {
		return producerEn;
	}

	public void setProducerEn(String producerEn) {
		this.producerEn = producerEn;
	}

	public void setMaterialGradeEn(String materialGradeEn){
 		 this.materialGradeEn=materialGradeEn;
 	}
 	public String getMaterialGradeEn(){
 		 return this.materialGradeEn;
 	}
 
 	 
 	public void setProductBatchEn(String productBatchEn){
 		 this.productBatchEn=productBatchEn;
 	}
 	public String getProductBatchEn(){
 		 return this.productBatchEn;
 	}
 
 	 
 	public void setSupplierNameEn(String supplierNameEn){
 		 this.supplierNameEn=supplierNameEn;
 	}
 	public String getSupplierNameEn(){
 		 return this.supplierNameEn;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}


	public Long getUserId() {
		return userId;
	}


	public void setProductInfo(String productInfo){
 		 this.productInfo=productInfo;
 	}
 	public String getProductInfo(){
 		 return this.productInfo;
 	}
 
 	 
 	public void setProductInfoEn(String productInfoEn){
 		 this.productInfoEn=productInfoEn;
 	}
 	public String getProductInfoEn(){
 		 return this.productInfoEn;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setProductBatch(String productBatch){
 		 this.productBatch=productBatch;
 	}
 	public String getProductBatch(){
 		 return this.productBatch;
 	}
 
 	 
 	public void setBuyersNameEn(String buyersNameEn){
 		 this.buyersNameEn=buyersNameEn;
 	}
 	public String getBuyersNameEn(){
 		 return this.buyersNameEn;
 	}
 
 	 
 	public void setSampleName(String sampleName){
 		 this.sampleName=sampleName;
 	}
 	public String getSampleName(){
 		 return this.sampleName;
 	}
 
 	 
 	public void setSampleNameEn(String sampleNameEn){
 		 this.sampleNameEn=sampleNameEn;
 	}
 	public String getSampleNameEn(){
 		 return this.sampleNameEn;
 	}
 
 	 
 	public void setSampleNo(String sampleNo){
 		 this.sampleNo=sampleNo;
 	}
 	public String getSampleNo(){
 		 return this.sampleNo;
 	}
 
 	 
 	public void setMaterialGrade(String materialGrade){
 		 this.materialGrade=materialGrade;
 	}
 	public String getMaterialGrade(){
 		 return this.materialGrade;
 	}
 
 	 

 	 
 	public void setSupplierName(String supplierName){
 		 this.supplierName=supplierName;
 	}
 	public String getSupplierName(){
 		 return this.supplierName;
 	}
 
 	 
 	public void setBuyersName(String buyersName){
 		 this.buyersName=buyersName;
 	}
 	public String getBuyersName(){
 		 return this.buyersName;
 	}


	public Long getSampleId() {
		return sampleId;
	}

	public void setSampleId(Long sampleId) {
		this.sampleId = sampleId;
	}



	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getRemarkEn() {
		return remarkEn;
	}

	public void setRemarkEn(String remarkEn) {
		this.remarkEn = remarkEn;
	}

	public List<UserSampleAttributeDTO> getUserSampleAttributeDTOList() {
		return userSampleAttributeDTOList;
	}

	public void setUserSampleAttributeDTOList(List<UserSampleAttributeDTO> userSampleAttributeDTOList) {
		this.userSampleAttributeDTOList = userSampleAttributeDTOList;
	}

	public UserSampleAttributeDTO getSampleStateDTO() {
		return sampleStateDTO;
	}

	public void setSampleStateDTO(UserSampleAttributeDTO sampleStateDTO) {
		this.sampleStateDTO = sampleStateDTO;
	}

	public List<SampleAttributeDTO> getSampleFeatures() {
		return sampleFeatures;
	}

	public void setSampleFeatures(List<SampleAttributeDTO> sampleFeatures) {
		this.sampleFeatures = sampleFeatures;
	}

	public List<UserSampleFromDTO> getSampleFromDTOList() {
		return sampleFromDTOList;
	}

	public void setSampleFromDTOList(List<UserSampleFromDTO> sampleFromDTOList) {
		this.sampleFromDTOList = sampleFromDTOList;
	}
}