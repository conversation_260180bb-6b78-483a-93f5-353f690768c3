package com.sgs.ecom.member.dto.question.order;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;

import java.util.HashMap;
import java.util.Map;

public class CopyQuestionDTO {

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private Map mapDTOSet;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private Long catagoryId;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private Long questionId;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String replyNo;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String categoryPath;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int [] idPath;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String questionName;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String bu;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int isExp;



    public Map getMapDTOSet() {
        return mapDTOSet;
    }

    public void setMapDTOSet(Map mapDTOSet) {
        this.mapDTOSet = mapDTOSet;
    }

    public Long getCatagoryId() {
        return catagoryId;
    }

    public void setCatagoryId(Long catagoryId) {
        this.catagoryId = catagoryId;
    }

    public Long getQuestionId() {
        return questionId;
    }

    public void setQuestionId(Long questionId) {
        this.questionId = questionId;
    }

    public String getReplyNo() {
        return replyNo;
    }

    public void setReplyNo(String replyNo) {
        this.replyNo = replyNo;
    }

    public String getCategoryPath() {
        return categoryPath;
    }

    public void setCategoryPath(String categoryPath) {
        this.categoryPath = categoryPath;
    }


    public String getQuestionName() {
        return questionName;
    }

    public void setQuestionName(String questionName) {
        this.questionName = questionName;
    }

    public String getBu() {
        return bu;
    }

    public void setBu(String bu) {
        this.bu = bu;
    }

    public int[] getIdPath() {
        return idPath;
    }

    public void setIdPath(int[] idPath) {
        this.idPath = idPath;
    }

    public int getIsExp() {
        return isExp;
    }

    public void setIsExp(int isExp) {
        this.isExp = isExp;
    }
}
