<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.order.dao.OrderLabelMapper" >

    <resultMap id="resultMap" type="com.sgs.ecom.member.dto.order.OrderLabelDTO" >
        <id column="LABEL_ID" jdbcType="BIGINT" property="labelId"/>
        <result column="ORDER_NO" jdbcType="VARCHAR" property="orderNo"/>
        <result column="LABEL_GROUP" jdbcType="VARCHAR" property="labelGroup"/>
        <result column="LABEL_CODE" jdbcType="VARCHAR" property="labelCode"/>
        <result column="LABEL_VALUE" jdbcType="VARCHAR" property="labelValue"/>
        <result column="STATE" jdbcType="TINYINT" property="state"/>
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="STATE_DATE" jdbcType="TIMESTAMP" property="stateDate"/>
    </resultMap>
    <sql id="Base_Column_List">
        LABEL_ID, ORDER_NO, LABEL_GROUP, LABEL_CODE, LABEL_VALUE, STATE, CREATE_DATE, STATE_DATE
    </sql>

    <insert id="insert" parameterType="com.sgs.ecom.member.entity.order.OrderLabel">
        <selectKey keyProperty="labelId" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into order_label(ORDER_NO, LABEL_GROUP, LABEL_CODE,
        LABEL_VALUE, STATE, CREATE_DATE, STATE_DATE)
        values (#{orderNo,jdbcType=VARCHAR}, #{labelGroup,jdbcType=VARCHAR}, #{labelCode,jdbcType=VARCHAR},
        #{labelValue,jdbcType=VARCHAR}, #{state,jdbcType=TINYINT}, #{createDate,jdbcType=TIMESTAMP}, #{stateDate,jdbcType=TIMESTAMP})
    </insert>

    <insert id="insertSelective" parameterType="com.sgs.ecom.member.entity.order.OrderLabel">
        <selectKey keyProperty="labelId" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into order_label
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderNo != null">
                ORDER_NO,
            </if>
            <if test="labelGroup != null">
                LABEL_GROUP,
            </if>
            <if test="labelCode != null">
                LABEL_CODE,
            </if>
            <if test="labelValue != null">
                LABEL_VALUE,
            </if>
            <if test="state != null">
                STATE,
            </if>
            <if test="createDate != null">
                CREATE_DATE,
            </if>
            <if test="stateDate != null">
                STATE_DATE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderNo != null">
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="labelGroup != null">
                #{labelGroup,jdbcType=VARCHAR},
            </if>
            <if test="labelCode != null">
                #{labelCode,jdbcType=VARCHAR},
            </if>
            <if test="labelValue != null">
                #{labelValue,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                #{state,jdbcType=TINYINT},
            </if>
            <if test="createDate != null">
                #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="stateDate != null">
                #{stateDate,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <select id="selectListByVOOrderLabel" resultMap="resultMap"  >
        select ORDER_NO,LABEL_VALUE
        from ORDER_LABEL
        where STATE =1
        and LABEL_CODE = #{labelCode,jdbcType=VARCHAR}
        and LABEL_GROUP = #{labelGroup,jdbcType=VARCHAR}
        <if test="orderNoList != null ">
            AND ORDER_NO in
            <foreach collection="orderNoList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <update id="delLabelByCode" >
        update ORDER_LABEL set STATE=0 where state=1 and LABEL_CODE = #{labelCode} and ORDER_NO= #{orderNo}
    </update>

</mapper>