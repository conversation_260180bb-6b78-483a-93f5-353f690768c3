package com.sgs.ecom.member.dto.order;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.util.collection.StrUtil;
import com.sgs.ecom.member.util.time.TimeCalendarUtil;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.Date;

public class OrderToDoDTO {
    private String sortNum;
    @ApiAnno(groups={BaseOrderFilter.OrderList.class})
    private String createDateShow;
    @ApiAnno(groups={BaseOrderFilter.OrderList.class})
    private String memo;
    @ApiAnno(groups={BaseOrderFilter.OrderList.class})
    private String remark;


    @ApiAnno(groups={BaseOrderFilter.OrderList.class})
    private String orderNo;
    @ApiAnno(groups={BaseOrderFilter.OrderList.class})
    private Integer OrderType;
    private BigDecimal realAmount;
    private String createDate;
    private String currency;



    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public BigDecimal getRealAmount() {
        return realAmount;
    }

    public void setRealAmount(BigDecimal realAmount) {
        this.realAmount = realAmount;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getCreateDateShow() {
        if(StringUtils.isBlank(createDate)){
            return "";
        }
        Date date= TimeCalendarUtil.getStringToDate(createDate);
        String time=TimeCalendarUtil.getDateToDateString(date);
        return time;
    }

    public void setCreateDateShow(String createDateShow) {
        this.createDateShow = createDateShow;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getOrderType() {
        return OrderType;
    }

    public void setOrderType(Integer orderType) {
        OrderType = orderType;
    }

    public String getSortNum() {
        return sortNum;
    }

    public void setSortNum(String sortNum) {
        this.sortNum = sortNum;
    }
}
