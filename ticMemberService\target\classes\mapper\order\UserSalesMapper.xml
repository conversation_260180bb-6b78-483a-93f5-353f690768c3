<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.user.dao.UserSalesMapper" >


  <select id="qryLastSalesByUser" resultType="java.lang.String"  >
    select STAFF_CODE  from USER_SALES
    where  USER_ID=#{userId}  AND BU=#{bu} AND line_id=#{lineId} and state=1
    order by ID desc
      limit 1
  </select>

  <sql id="baseQueryWhere">
    where 1=1
    <if test="userId != null">
      AND USER_ID=#{userId}
    </if>
    <if test="bu != null">
      AND BU=#{bu}
    </if>
  </sql>

  <insert id="insertSelective" parameterType="com.sgs.ecom.member.vo.VOUserSales" >
    insert into USER_SALES
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="userId != null" >
        USER_ID,
      </if>
      <if test="personCode != null" >
        PERSON_CODE,
      </if>
      <if test="staffCode != null" >
        STAFF_CODE,
      </if>
      <if test="bu != null" >
        BU,
      </if>
      <if test="state != null" >
        STATE,
      </if>
      <if test="createDate != null" >
        CREATE_DATE,
      </if>
      <if test="stateDate != null" >
        STATE_DATE,
      </if>
      <if test="lineId != null" >
        LINE_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="userId != null" >
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="personCode != null" >
        #{personCode,jdbcType=VARCHAR},
      </if>
      <if test="staffCode != null" >
        #{staffCode,jdbcType=VARCHAR},
      </if>
      <if test="bu != null" >
        #{bu,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        #{state,jdbcType=INTEGER},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        #{stateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lineId != null" >
        #{lineId}
      </if>
    </trim>
  </insert>

</mapper>