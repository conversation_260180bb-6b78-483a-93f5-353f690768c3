package com.sgs.ecom.member.domain.user;

import com.platform.util.ValidationUtil;
import com.sgs.base.BaseCopyObj;
import com.sgs.ecom.member.entity.user.UserToolkitRecordDtl;
import com.sgs.ecom.member.enums.ReportTransType;
import com.sgs.ecom.member.enums.UserToolKitType;
import com.sgs.ecom.member.request.report.ReportAuthReq;
import com.sgs.ecom.member.request.report.ReportAuthRsp;
import com.sgs.ecom.member.util.attr.AttrUtil;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

public class UserToolkitRecordDtlDO extends UserToolkitRecordDtl {
    private BaseCopyObj baseCopy = new BaseCopyObj();


    public List<UserToolkitRecordDtl> addUserToolkitRecordDtl(Long key, ReportAuthReq reportAuthReq, String qrcode, UserToolKitType reportAuthenticity, String url, ReportAuthRsp reportAuthRsp) {
        return add(reportAuthReq,qrcode,key,reportAuthenticity,url,reportAuthRsp);
    }

    private List<UserToolkitRecordDtl> add(ReportAuthReq reportAuthReq, String qrcode, Long key, UserToolKitType reportAuthenticity, String url, ReportAuthRsp reportAuthRsp) {
        List<Map<String, Object>> fieldInfo = AttrUtil.getFieldInfo(reportAuthRsp);
        return  getUserToolkitRecordDtl(fieldInfo,qrcode,key,reportAuthenticity,url,reportAuthRsp);
    }

    private List<UserToolkitRecordDtl> getUserToolkitRecordDtl(List<Map<String, Object>> fieldInfo, String qrcode, Long key, UserToolKitType reportAuthenticity, String url, ReportAuthRsp reportAuthRsp) {
        List<UserToolkitRecordDtl> list = new ArrayList<>();
        fieldInfo.stream().forEach(stringObjectMap -> {
            UserToolkitRecordDtl userToolkitRecordDtl = new UserToolkitRecordDtl();
            userToolkitRecordDtl = init(key,reportAuthenticity);
            other(userToolkitRecordDtl,stringObjectMap,qrcode,reportAuthRsp);
            list.add(userToolkitRecordDtl);
        });
        return list;
    }

    private void addQrCode(List<UserToolkitRecordDtl> list, String qrcode, Long key, UserToolKitType reportAuthenticity, String url) {
        UserToolkitRecordDtl userToolkitRecordDtl= new UserToolkitRecordDtl();
        userToolkitRecordDtl = init(key,reportAuthenticity);
        userToolkitRecordDtl.setAttrCode("qrcode");
        userToolkitRecordDtl.setAttrName("qrcode");
        userToolkitRecordDtl.setAttrValue(qrcode);

//        UserToolkitRecordDtl userToolkitRecordDtl1= new UserToolkitRecordDtl();
//        userToolkitRecordDtl1 = init(key,reportAuthenticity);
//        userToolkitRecordDtl1.setAttrCode("url");
//        userToolkitRecordDtl1.setAttrName("url");
//        userToolkitRecordDtl1.setAttrValue(url);
        list.add(userToolkitRecordDtl);
//        list.add(userToolkitRecordDtl1);
    }

    private void other(UserToolkitRecordDtl userToolkitRecordDtl, Map<String, Object> stringObjectMap, String qrcode, ReportAuthRsp reportAuthRsp) {
        userToolkitRecordDtl.setAttrCode((String) stringObjectMap.get("name"));
        userToolkitRecordDtl.setAttrName(ReportTransType.getName((String) stringObjectMap.get("name")));
        String type = (String) stringObjectMap.get("type");
        String value = "";
        if(type.equals("long")){
            value =ValidationUtil.isEmpty (stringObjectMap.get("value"))?null: String.valueOf((long) stringObjectMap.get("value"));
        }else if(type.equals("class java.lang.Integer")){
            value =ValidationUtil.isEmpty (stringObjectMap.get("value"))?null: String.valueOf((Integer) stringObjectMap.get("value"));
        }else if(type.equals("boolean")){
            String name = (String) stringObjectMap.get("name");
            if(name.equals("isSuccess")){
                value =reportAuthRsp.isSuccess()?"2":"1";
            }else {
                value =ValidationUtil.isEmpty (stringObjectMap.get("value"))?null: (boolean) stringObjectMap.get("value")?"true":"false";
            }
        }else if(type.equals("interface java.util.List")){
            value ="";
        }else {
            value =ValidationUtil.isEmpty (stringObjectMap.get("value"))?null: ValidationUtil.isEmpty(stringObjectMap.get("value"))?"":(String) stringObjectMap.get("value");
        }
        userToolkitRecordDtl.setAttrValue(value);
    }

    private UserToolkitRecordDtl init(Long key, UserToolKitType reportAuthenticity) {
        UserToolkitRecordDtl userToolkitRecordDtl = new UserToolkitRecordDtl();
        userToolkitRecordDtl.setRecordId(key);
        userToolkitRecordDtl.setCreateDate(new Timestamp(new Date().getTime()));
        userToolkitRecordDtl.setToolCode(reportAuthenticity.getIndex());
        return userToolkitRecordDtl;
    }
}