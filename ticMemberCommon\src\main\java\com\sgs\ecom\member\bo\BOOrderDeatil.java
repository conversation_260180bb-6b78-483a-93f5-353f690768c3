package com.sgs.ecom.member.bo; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import com.platform.annotation.CharacterVaild; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import com.platform.util.json.PriceFormatSerializer; 
import com.platform.annotation.BeanAnno; 

public class BOOrderDeatil{
 
 	public static final String SEQUENCE = "DETAIL_ID"; 
  
 	public static final String BO_SQL = "{ORDER_DETAIL}"; 
 
 	public static final String OWNER ="member";

 	public static final String ORIGINAL_PRICE="originalPrice";
 	public static final String PRICE="price";
 	public static final String STATE_DATE="stateDate";
 	public static final String SKU_ATTR="skuAttr";
 	public static final String ORDER_NO="orderNo";
 	public static final String DIS_PRICE="disPrice";
 	public static final String MAIL_SEND="mailSend";
 	public static final String SMS_SEND="smsSend";
 	public static final String PRODUCT_ID="productId";
 	public static final String PRODUCT_NAME="productName";
 	public static final String PROD_IMG="prodImg";
 	public static final String PRODUCT_CODE="productCode";
 	public static final String CREATE_DATE="createDate";
 	public static final String DETAIL_NO="detailNo";
 	public static final String PRODUCT_ATTR="productAttr";
 	public static final String DETAIL_ID="detailId";
 	public static final String STORE_NAME="storeName";
 	public static final String REFUND_APPLY="refundApply";
 	public static final String SKU_ID="skuId";
 	public static final String TOTAL_PRICE="totalPrice";
 	public static final String BUY_NUMS="buyNums";
 	public static final String STORE_ID="storeId";
 	public static final String CATEGORY_NAME="categoryName";
 	public static final String MEMO="memo";

 	@BeanAnno("ORIGINAL_PRICE")
 	private long originalPrice;
 	@BeanAnno("PRICE")
 	private long price;
 	@BeanAnno("STATE_DATE")
 	private Timestamp stateDate;
 	@BeanAnno("SKU_ATTR")
 	private String skuAttr;
 	@BeanAnno("ORDER_NO")
 	private String orderNo;
 	@BeanAnno("DIS_PRICE")
 	private long disPrice;
 	@BeanAnno("MAIL_SEND")
 	private int mailSend;
 	@BeanAnno("SMS_SEND")
 	private int smsSend;
 	@BeanAnno("PRODUCT_ID")
 	private long productId;
 	@BeanAnno("PRODUCT_NAME")
 	private String productName;
 	@BeanAnno("PROD_IMG")
 	private String prodImg;
 	@BeanAnno("PRODUCT_CODE")
 	private String productCode;
 	@BeanAnno("CREATE_DATE")
 	private Timestamp createDate;
 	@BeanAnno("DETAIL_NO")
 	private String detailNo;
 	@BeanAnno("PRODUCT_ATTR")
 	private String productAttr;
 	@BeanAnno("DETAIL_ID")
 	private long detailId;
 	@BeanAnno("STORE_NAME")
 	private String storeName;
 	@BeanAnno("REFUND_APPLY")
 	private int refundApply;
 	@BeanAnno("SKU_ID")
 	private long skuId;
 	@BeanAnno("TOTAL_PRICE")
 	private long totalPrice;
 	@BeanAnno("BUY_NUMS")
 	private long buyNums;
 	@BeanAnno("STORE_ID")
 	private String storeId;
 	@BeanAnno("CATEGORY_NAME")
 	private String categoryName;
 	@BeanAnno("MEMO")
 	private String memo;

 	public void setOriginalPrice(long originalPrice){
 		 this.originalPrice=originalPrice;
 	}
 	@JsonSerialize(using = PriceFormatSerializer.class) 
 	public long getOriginalPrice(){
 		 return this.originalPrice;
 	}
 
 	 
 	public void setPrice(long price){
 		 this.price=price;
 	}
 	@JsonSerialize(using = PriceFormatSerializer.class) 
 	public long getPrice(){
 		 return this.price;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	@CharacterVaild(len = 1000) 
 	public void setSkuAttr(String skuAttr){
 		 this.skuAttr=skuAttr;
 	}
 	public String getSkuAttr(){
 		 return this.skuAttr;
 	}
 
 	 
 	@CharacterVaild(len = 30) 
 	public void setOrderNo(String orderNo){
 		 this.orderNo=orderNo;
 	}
 	public String getOrderNo(){
 		 return this.orderNo;
 	}
 
 	 
 	public void setDisPrice(long disPrice){
 		 this.disPrice=disPrice;
 	}
 	@JsonSerialize(using = PriceFormatSerializer.class) 
 	public long getDisPrice(){
 		 return this.disPrice;
 	}
 
 	 
 	public void setMailSend(int mailSend){
 		 this.mailSend=mailSend;
 	}
 	public int getMailSend(){
 		 return this.mailSend;
 	}
 
 	 
 	public void setSmsSend(int smsSend){
 		 this.smsSend=smsSend;
 	}
 	public int getSmsSend(){
 		 return this.smsSend;
 	}
 
 	 
 	public void setProductId(long productId){
 		 this.productId=productId;
 	}
 	public long getProductId(){
 		 return this.productId;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setProductName(String productName){
 		 this.productName=productName;
 	}
 	public String getProductName(){
 		 return this.productName;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setProdImg(String prodImg){
 		 this.prodImg=prodImg;
 	}
 	public String getProdImg(){
 		 return this.prodImg;
 	}
 
 	 
 	@CharacterVaild(len = 32) 
 	public void setProductCode(String productCode){
 		 this.productCode=productCode;
 	}
 	public String getProductCode(){
 		 return this.productCode;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	@CharacterVaild(len = 30) 
 	public void setDetailNo(String detailNo){
 		 this.detailNo=detailNo;
 	}
 	public String getDetailNo(){
 		 return this.detailNo;
 	}
 
 	 
 	@CharacterVaild(len = 1000) 
 	public void setProductAttr(String productAttr){
 		 this.productAttr=productAttr;
 	}
 	public String getProductAttr(){
 		 return this.productAttr;
 	}
 
 	 
 	public void setDetailId(long detailId){
 		 this.detailId=detailId;
 	}
 	public long getDetailId(){
 		 return this.detailId;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setStoreName(String storeName){
 		 this.storeName=storeName;
 	}
 	public String getStoreName(){
 		 return this.storeName;
 	}
 
 	 
 	public void setRefundApply(int refundApply){
 		 this.refundApply=refundApply;
 	}
 	public int getRefundApply(){
 		 return this.refundApply;
 	}
 
 	 
 	public void setSkuId(long skuId){
 		 this.skuId=skuId;
 	}
 	public long getSkuId(){
 		 return this.skuId;
 	}
 
 	 
 	public void setTotalPrice(long totalPrice){
 		 this.totalPrice=totalPrice;
 	}
 	@JsonSerialize(using = PriceFormatSerializer.class) 
 	public long getTotalPrice(){
 		 return this.totalPrice;
 	}
 
 	 
 	public void setBuyNums(long buyNums){
 		 this.buyNums=buyNums;
 	}
 	public long getBuyNums(){
 		 return this.buyNums;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setStoreId(String storeId){
 		 this.storeId=storeId;
 	}
 	public String getStoreId(){
 		 return this.storeId;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setCategoryName(String categoryName){
 		 this.categoryName=categoryName;
 	}
 	public String getCategoryName(){
 		 return this.categoryName;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setMemo(String memo){
 		 this.memo=memo;
 	}
 	public String getMemo(){
 		 return this.memo;
 	}
 
 	 
}