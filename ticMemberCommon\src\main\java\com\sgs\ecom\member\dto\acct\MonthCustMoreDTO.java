package com.sgs.ecom.member.dto.acct;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.annotation.IsNullAnno;
import com.platform.util.json.TimeFormatSerializer;
import com.sgs.base.BaseQryFilter.QueryDtl;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
*@Function:
*@Description
*@param: 月结使用
*@author: <PERSON><PERSON>_Qi<PERSON> @date: 2022/9/6 @version:
**/
public class MonthCustMoreDTO {

	@ApiAnno(groups={QueryDtl.class})
	private Long custId;
	@ApiAnno(groups={QueryDtl.class})
	private String address;
	@ApiAnno(groups={QueryDtl.class})
	private String addressEn;
	@ApiAnno(groups={QueryDtl.class})
	private String custCode;
	@ApiAnno(groups={QueryDtl.class})
	private String companyName;
	@ApiAnno(groups={QueryDtl.class})
	private String companyNameEn;
	@ApiAnno(groups={QueryDtl.class})
	private String taxNo;
	@ApiAnno(groups={QueryDtl.class})
	private String regPhone;
	@ApiAnno(groups={QueryDtl.class})
	private String bankName;
	@ApiAnno(groups={QueryDtl.class})
	private String bankNumber;
	@ApiAnno(groups={QueryDtl.class})
	private String invoiceTitle;
	@ApiAnno(groups={QueryDtl.class})
	private String regAddress;
	@ApiAnno(groups={QueryDtl.class})
	private Long userId;
	@ApiAnno(groups={QueryDtl.class})
	private String userPhone;
	@ApiAnno(groups={QueryDtl.class})
	private String userEmail;
	@ApiAnno(groups={QueryDtl.class})
	private String province;
	@ApiAnno(groups={QueryDtl.class})
	private String city;
	@ApiAnno(groups={QueryDtl.class})
	private String town;
	@ApiAnno(groups={QueryDtl.class})
	private int acctState;
	@ApiAnno(groups={QueryDtl.class})
	private BigDecimal creditAmount;
	@ApiAnno(groups={QueryDtl.class})
	private int isFreeze;




	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getAddressEn() {
		return addressEn;
	}

	public void setAddressEn(String addressEn) {
		this.addressEn = addressEn;
	}

	public String getCompanyNameEn() {
		return companyNameEn;
	}

	public void setCompanyNameEn(String companyNameEn) {
		this.companyNameEn = companyNameEn;
	}

	public String getTaxNo() {
		return taxNo;
	}

	public void setTaxNo(String taxNo) {
		this.taxNo = taxNo;
	}

	public String getRegPhone() {
		return regPhone;
	}

	public void setRegPhone(String regPhone) {
		this.regPhone = regPhone;
	}

	public String getBankName() {
		return bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}

	public String getBankNumber() {
		return bankNumber;
	}

	public void setBankNumber(String bankNumber) {
		this.bankNumber = bankNumber;
	}

	public String getInvoiceTitle() {
		return invoiceTitle;
	}

	public void setInvoiceTitle(String invoiceTitle) {
		this.invoiceTitle = invoiceTitle;
	}

	public String getRegAddress() {
		return regAddress;
	}

	public void setRegAddress(String regAddress) {
		this.regAddress = regAddress;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}


	public String getUserEmail() {
		return userEmail;
	}

	public void setUserEmail(String userEmail) {
		this.userEmail = userEmail;
	}

	public String getCustCode() {
		return custCode;
	}

	public void setCustCode(String custCode) {
		this.custCode = custCode;
	}

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public String getUserPhone() {
		return userPhone;
	}

	public void setUserPhone(String userPhone) {
		this.userPhone = userPhone;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getTown() {
		return town;
	}

	public void setTown(String town) {
		this.town = town;
	}

	public Long getCustId() {
		return custId;
	}

	public void setCustId(Long custId) {
		this.custId = custId;
	}

	public int getAcctState() {
		return acctState;
	}

	public void setAcctState(int acctState) {
		this.acctState = acctState;
	}

	public BigDecimal getCreditAmount() {
		return creditAmount;
	}

	public void setCreditAmount(BigDecimal creditAmount) {
		this.creditAmount = creditAmount;
	}

	public int getIsFreeze() {
		return isFreeze;
	}

	public void setIsFreeze(int isFreeze) {
		this.isFreeze = isFreeze;
	}
}