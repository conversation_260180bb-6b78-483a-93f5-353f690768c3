<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.order.dao.OrderComponentMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.entity.order.OrderComponent" >
    <id column="COMPONENT_ID" property="componentId" jdbcType="BIGINT" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="COMPONENT_TYPE" property="componentType" jdbcType="INTEGER" />
    <result column="COMPONENT_CODE" property="componentCode" jdbcType="VARCHAR" />
    <result column="COMPONENT_NAME" property="componentName" jdbcType="VARCHAR" />
    <result column="COMPONENT_NAME_EN" property="componentNameEn" jdbcType="VARCHAR" />
    <result column="OPERATOR_TYPE" property="operatorType" jdbcType="INTEGER" />
    <result column="MIN_VALUE" property="minValue" jdbcType="DECIMAL" />
    <result column="MAX_VALUE" property="maxValue" jdbcType="DECIMAL" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
  </resultMap>

  <resultMap id="BaseResultDTOMap" type="com.sgs.ecom.member.dto.order.OrderComponentDTO" >
    <id column="COMPONENT_ID" property="componentId" jdbcType="BIGINT" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="COMPONENT_TYPE" property="componentType" jdbcType="INTEGER" />
    <result column="COMPONENT_CODE" property="componentCode" jdbcType="VARCHAR" />
    <result column="COMPONENT_NAME" property="componentName" jdbcType="VARCHAR" />
    <result column="COMPONENT_NAME_EN" property="componentNameEn" jdbcType="VARCHAR" />
    <result column="OPERATOR_TYPE" property="operatorType" jdbcType="INTEGER" />
    <result column="MIN_VALUE" property="minValue" jdbcType="DECIMAL" />
    <result column="MAX_VALUE" property="maxValue" jdbcType="DECIMAL" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    COMPONENT_ID, ORDER_NO, COMPONENT_TYPE, COMPONENT_CODE, COMPONENT_NAME, COMPONENT_NAME_EN, 
    OPERATOR_TYPE, MIN_VALUE, MAX_VALUE, CREATE_DATE, STATE_DATE
  </sql>

  <sql id="baseWhere" >
        where   1=1
      <if test="orderNo != null" >
        and  ORDER_NO = #{orderNo}
      </if>

      <if test="componentType != null" >
      and  COMPONENT_TYPE = #{componentType}
      </if>
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from order_component
    where COMPONENT_ID = #{componentId,jdbcType=BIGINT}
  </select>
  <select id="qryOrderComponentListByMap" resultMap="BaseResultDTOMap">
    select
    <include refid="Base_Column_List" />
    from order_component
    <include refid="baseWhere" />
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from order_component
    where COMPONENT_ID = #{componentId,jdbcType=BIGINT}
  </delete>
    <delete id="deleteByOrderNoAndType">
      delete  from  order_component
      <include refid="baseWhere" />


    </delete>
    <insert id="insert" parameterType="com.sgs.ecom.member.entity.order.OrderComponent" >
    insert into order_component (COMPONENT_ID, ORDER_NO, COMPONENT_TYPE, 
      COMPONENT_CODE, COMPONENT_NAME, COMPONENT_NAME_EN, 
      OPERATOR_TYPE, MIN_VALUE, MAX_VALUE, 
      CREATE_DATE, STATE_DATE)
    values (#{componentId,jdbcType=BIGINT}, #{orderNo,jdbcType=VARCHAR}, #{componentType,jdbcType=INTEGER}, 
      #{componentCode,jdbcType=VARCHAR}, #{componentName,jdbcType=VARCHAR}, #{componentNameEn,jdbcType=VARCHAR}, 
      #{operatorType,jdbcType=INTEGER}, #{minValue,jdbcType=INTEGER}, #{maxValue,jdbcType=INTEGER}, 
      #{createDate,jdbcType=TIMESTAMP}, #{stateDate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.ecom.member.entity.order.OrderComponent" >
    insert into order_component
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="componentId != null" >
        COMPONENT_ID,
      </if>
      <if test="orderNo != null" >
        ORDER_NO,
      </if>
      <if test="componentType != null" >
        COMPONENT_TYPE,
      </if>
      <if test="componentCode != null" >
        COMPONENT_CODE,
      </if>
      <if test="componentName != null" >
        COMPONENT_NAME,
      </if>
      <if test="componentNameEn != null" >
        COMPONENT_NAME_EN,
      </if>
      <if test="operatorType != null" >
        OPERATOR_TYPE,
      </if>
      <if test="minValue != null" >
        MIN_VALUE,
      </if>
      <if test="maxValue != null" >
        MAX_VALUE,
      </if>
      <if test="createDate != null" >
        CREATE_DATE,
      </if>
      <if test="stateDate != null" >
        STATE_DATE,
      </if>

    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="componentId != null" >
        #{componentId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="componentType != null" >
        #{componentType,jdbcType=INTEGER},
      </if>
      <if test="componentCode != null" >
        #{componentCode,jdbcType=VARCHAR},
      </if>
      <if test="componentName != null" >
        #{componentName,jdbcType=VARCHAR},
      </if>
      <if test="componentNameEn != null" >
        #{componentNameEn,jdbcType=VARCHAR},
      </if>
      <if test="operatorType != null" >
        #{operatorType,jdbcType=INTEGER},
      </if>
      <if test="minValue != null" >
        #{minValue,jdbcType=INTEGER},
      </if>
      <if test="maxValue != null" >
        #{maxValue,jdbcType=INTEGER},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        #{stateDate,jdbcType=TIMESTAMP},
      </if>

    </trim>
  </insert>
  <insert id="insertEntityForeach">
    insert into order_component
    ( ORDER_NO, COMPONENT_TYPE,
    COMPONENT_CODE, COMPONENT_NAME, COMPONENT_NAME_EN,
    OPERATOR_TYPE, MIN_VALUE, MAX_VALUE,
    CREATE_DATE, STATE_DATE)
    values
    <foreach collection ="list" item="item" index= "index" separator =",">
      (
      #{item.orderNo}, #{item.componentType},#{item.componentCode},#{item.componentName},#{item.componentNameEn},#{item.operatorType},#{item.minValue},#{item.maxValue},
       #{item.createDate},#{item.stateDate}
      )
    </foreach>


  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.ecom.member.entity.order.OrderComponent" >
    update order_component
    <set >
      <if test="orderNo != null" >
        ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="componentType != null" >
        COMPONENT_TYPE = #{componentType,jdbcType=INTEGER},
      </if>
      <if test="componentCode != null" >
        COMPONENT_CODE = #{componentCode,jdbcType=VARCHAR},
      </if>
      <if test="componentName != null" >
        COMPONENT_NAME = #{componentName,jdbcType=VARCHAR},
      </if>
      <if test="componentNameEn != null" >
        COMPONENT_NAME_EN = #{componentNameEn,jdbcType=VARCHAR},
      </if>
      <if test="operatorType != null" >
        OPERATOR_TYPE = #{operatorType,jdbcType=INTEGER},
      </if>
      <if test="minValue != null" >
        MIN_VALUE = #{minValue,jdbcType=INTEGER},
      </if>
      <if test="maxValue != null" >
        MAX_VALUE = #{maxValue,jdbcType=INTEGER},
      </if>
      <if test="createDate != null" >
        CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        STATE_DATE = #{stateDate,jdbcType=TIMESTAMP},
      </if>

    </set>
    where COMPONENT_ID = #{componentId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.ecom.member.entity.order.OrderComponent" >
    update order_component
    set ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      COMPONENT_TYPE = #{componentType,jdbcType=INTEGER},
      COMPONENT_CODE = #{componentCode,jdbcType=VARCHAR},
      COMPONENT_NAME = #{componentName,jdbcType=VARCHAR},
      COMPONENT_NAME_EN = #{componentNameEn,jdbcType=VARCHAR},
      OPERATOR_TYPE = #{operatorType,jdbcType=INTEGER},
      MIN_VALUE = #{minValue,jdbcType=INTEGER},
      MAX_VALUE = #{maxValue,jdbcType=INTEGER},
      CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      STATE_DATE = #{stateDate,jdbcType=TIMESTAMP}
    where COMPONENT_ID = #{componentId,jdbcType=BIGINT}
  </update>
</mapper>