package com.sgs.ecom.member.controller.user;

import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.sgs.base.BaseBean;
import com.sgs.ecom.member.annotation.CheckUserToolKitPermission;
import com.sgs.ecom.member.domain.user.service.interfaces.IUserToolDomainService;
import com.sgs.ecom.member.request.report.ReportAuthReq;
import com.sgs.ecom.member.service.user.interfaces.IUserToolService;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import com.sgs.ecom.member.vo.VOComponentInfo;
import com.sgs.ecom.member.vo.VOUserToolkitRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/business/api.v2.user/tool")
public class UserToolController extends ControllerUtil {

    @Autowired
    private IUserToolService userToolService;

	@Resource
	private IUserToolDomainService userToolDomainService;

    /**
     * @param voUserToolkitRecord:
     * @param token:
     * @Description: 小工具制作-保存信息
     * @Author: bowen zhang
     * @Date: 2022/4/24
     * @return: com.sgs.ecom.member.util.ResultBody
     **/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "saveMakeToolRecord", method = {RequestMethod.POST})
    @CheckUserToolKitPermission("SL_TOOLKIT001,SL_TOOLKIT002")
    public ResultBody saveMakeToolRecord(
            @RequestBody VOUserToolkitRecord voUserToolkitRecord,
            @RequestHeader(value = "accessToken") String token) throws Exception {
        userToolService.saveMakeToolRecord(voUserToolkitRecord, getUserDTO(token));
        return ResultBody.success();
    }

	/**
	 * @Description : 用户报告验真操作日志记录
	 * <AUTHOR> Zhang
	 * @Date  2023/6/21
	 * @param reportAuthReq:
	 * @param token:
	 * @return: com.sgs.ecom.member.util.ResultBody
	 **/
//	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "reportAuthSaveRecord", method = {RequestMethod.POST})
//	@CheckUserToolKitPermission("SL_TOOLKIT007")
	public ResultBody reportAuthSaveRecord(
			@Validated(BaseBean.Insert.class)
			@RequestBody ReportAuthReq reportAuthReq,
			@RequestHeader(value = "accessToken") String token, HttpServletRequest request ) throws Exception {
		return ResultBody.success(userToolDomainService.reportAuthSaveRecord(reportAuthReq, getUserDTO(token),request));
	}

	/**
	 * @Description :更新数据
	 * <AUTHOR> Zhang
	 * @Date  2024/3/20
	 * @param reportAuthReq:
	 * @param token:
	 * @param request:
	 * @return: com.sgs.ecom.member.util.ResultBody
	 **/
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "updateReportRecord", method = {RequestMethod.POST})
//	@CheckUserToolKitPermission("SL_TOOLKIT007")
	public ResultBody updateReportRecord(
			@Validated(BaseBean.Update.class)
			@RequestBody ReportAuthReq reportAuthReq,
			@RequestHeader(value = "accessToken") String token, HttpServletRequest request ) throws Exception {
		userToolDomainService.updateReportRecord(reportAuthReq, getUserDTO(token),request);
		return  ResultBody.success();
	}

	/**
	 * @Description : 用户报告验真操作日志记录-针对压缩包功能
	 * <AUTHOR> Zhang
	 * @Date  2023/7/14
	 * @param reportAuthReq:
	 * @param token:
	 * @return: com.sgs.ecom.member.util.ResultBody
	 **/
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "reportAuthSaveRecordByZip", method = {RequestMethod.POST})
//	@CheckUserToolKitPermission("SL_TOOLKIT007")
	public ResultBody reportAuthSaveRecordByZip(
			@Validated(BaseBean.Insert.class)
			@RequestBody ReportAuthReq reportAuthReq,
			@RequestHeader(value = "accessToken") String token, HttpServletRequest request) throws Exception {
		return ResultBody.success(userToolDomainService.reportAuthSaveRecordByZip(reportAuthReq, getUserDTO(token),request));
	}

    /**   
	* @Function: fastFeek
	* @Description: 查询俗称
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2022-05-23
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2022-05-23  shenyi    v1.0                 新增
	*/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "fastFeek", method = {RequestMethod.POST})
    @CheckUserToolKitPermission("SL_TOOLKIT003")
    public ResultBody fastFeek(
            @RequestBody VOComponentInfo componentInfo) throws Exception {
        return ResultBody.newInstance(userToolService.fastFeek(componentInfo));
    }

    /**   
	* @Function: qryComponent
	* @Description: 查询成分信息
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2019-01-30
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2019-01-30  shenyi    v1.0                 新增
	*/
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "qry", method = {RequestMethod.POST})
    @CheckUserToolKitPermission("SL_TOOLKIT003")
    public ResultBody qryMakeToolRecord(
            @RequestBody VOComponentInfo componentInfo) throws Exception {
        return ResultBody.newInstance(userToolService.qryComponent(componentInfo));
    }


	/**
	 * @param voUserToolkitRecord:
	 * @param token:
	 * @Description: 进入合格证/水洗标界面默认查询最新的一条数据
	 * @Author: bowen zhang
	 * @Date: 2022/4/27
	 * @return: com.sgs.ecom.member.util.ResultBody
	 **/
	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "qryMakeToolRecord", method = {RequestMethod.POST})
	@CheckUserToolKitPermission("SL_TOOLKIT001,SL_TOOLKIT002")
	public ResultBody qryMakeToolRecord(
			@Validated(BaseBean.Query.class)
			@RequestBody VOUserToolkitRecord voUserToolkitRecord,
			@RequestHeader(value = "accessToken") String token) throws Exception {
		return ResultBody.newInstance(userToolService.qryMakeToolRecord(voUserToolkitRecord, getUserDTO(token)));
	}


	/**
	 * @Description: 欧标查询
	 * @Author: bowen zhang
	 * @Date: 2022/6/14
	 * @param voUserToolkitRecord:
	 * @param token:
	 * @return: com.sgs.ecom.member.util.ResultBody
	 **/
	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "qryEUMakeToolRecord", method = {RequestMethod.POST})
	@CheckUserToolKitPermission("SL_TOOLKIT005")
	public ResultBody qryEUMakeToolRecord(
			@Validated(BaseBean.Query.class)
			@RequestBody VOUserToolkitRecord voUserToolkitRecord,
			@RequestHeader(value = "accessToken") String token) throws Exception {
		return ResultBody.newInstance(userToolService.qryEUAndNAMakeToolRecord(voUserToolkitRecord, getUserDTO(token)));
	}

	/**
	 * @Description: 欧标更新
	 * @Author: bowen zhang
	 * @Date: 2022/6/14
	 * @param voUserToolkitRecord:
	 * @param token:
	 * @return: com.sgs.ecom.member.util.ResultBody
	 **/
	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "saveEUMakeToolRecord", method = {RequestMethod.POST})
	@CheckUserToolKitPermission("SL_TOOLKIT005")
	public ResultBody saveEUMakeToolRecord(
			@RequestBody VOUserToolkitRecord voUserToolkitRecord,
			@RequestHeader(value = "accessToken") String token) throws Exception {
		userToolService.saveMakeToolRecord(voUserToolkitRecord, getUserDTO(token));
		return ResultBody.success();
	}

	/**
	 * @Description: 美标查询
	 * @Author: bowen zhang
	 * @Date: 2022/6/14
	 * @param voUserToolkitRecord:
	 * @param token:
	 * @return: com.sgs.ecom.member.util.ResultBody
	 **/
	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "qryNAMakeToolRecord", method = {RequestMethod.POST})
	@CheckUserToolKitPermission("SL_TOOLKIT006")
	public ResultBody qryNAMakeToolRecord(
			@Validated(BaseBean.Query.class)
			@RequestBody VOUserToolkitRecord voUserToolkitRecord,
			@RequestHeader(value = "accessToken") String token) throws Exception {
		return ResultBody.newInstance(userToolService.qryEUAndNAMakeToolRecord(voUserToolkitRecord, getUserDTO(token)));
	}

	/**
	 * @Description: 美标更新
	 * @Author: bowen zhang
	 * @Date: 2022/6/14
	 * @param voUserToolkitRecord:
	 * @param token:
	 * @return: com.sgs.ecom.member.util.ResultBody
	 **/
	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "saveNAMakeToolRecord", method = {RequestMethod.POST})
	@CheckUserToolKitPermission("SL_TOOLKIT006")
	public ResultBody saveNAMakeToolRecord(
			@RequestBody VOUserToolkitRecord voUserToolkitRecord,
			@RequestHeader(value = "accessToken") String token) throws Exception {
		userToolService.saveMakeToolRecord(voUserToolkitRecord, getUserDTO(token));
		return ResultBody.success();
	}

	/**
	 * @Description: 重置历史数据
	 * @Author: bowen zhang
	 * @Date: 2022/6/29
	 * @param token:
	 * @return: com.sgs.ecom.member.util.ResultBody
	 **/
	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "resetHistoryData", method = {RequestMethod.POST})
	@CheckUserToolKitPermission("SL_TOOLKIT001,SL_TOOLKIT002")
	public ResultBody resetHistoryData(
			@RequestBody VOUserToolkitRecord voUserToolkitRecord,
			@RequestHeader(value = "accessToken") String token) throws Exception {
		userToolService.resetHistoryData(voUserToolkitRecord, getUserDTO(token));
		return ResultBody.success();
	}

	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "resetHistoryDataEU", method = {RequestMethod.POST})
	@CheckUserToolKitPermission("SL_TOOLKIT005")
	public ResultBody resetHistoryDataEU(
			@RequestBody VOUserToolkitRecord voUserToolkitRecord,
			@RequestHeader(value = "accessToken") String token) throws Exception {
		userToolService.resetHistoryData(voUserToolkitRecord, getUserDTO(token));
		return ResultBody.success();
	}

	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "resetHistoryDataNA", method = {RequestMethod.POST})
	@CheckUserToolKitPermission("SL_TOOLKIT006")
	public ResultBody resetHistoryDataNA(
			@RequestBody VOUserToolkitRecord voUserToolkitRecord,
			@RequestHeader(value = "accessToken") String token) throws Exception {
		userToolService.resetHistoryData(voUserToolkitRecord, getUserDTO(token));
		return ResultBody.success();
	}


	@HystrixCommand
	@AuthRequired(login = "NULL", sign = true)
	@RequestMapping(value = "exportReportList", method = {RequestMethod.POST})
//	@CheckUserToolKitPermission("SL_TOOLKIT006")
	public void exportReportList(
			@RequestBody VOUserToolkitRecord voUserToolkitRecord, HttpServletResponse res) throws Exception {
		userToolService.exportReportList(voUserToolkitRecord, res);
	}
}
