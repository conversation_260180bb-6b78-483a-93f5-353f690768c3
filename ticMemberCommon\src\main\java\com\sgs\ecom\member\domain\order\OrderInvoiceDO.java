package com.sgs.ecom.member.domain.order;

import com.platform.util.ValidationUtil;
import com.sgs.base.BaseCopyObj;
import com.sgs.ecom.member.dto.user.UserInvoiceDTO;
import com.sgs.ecom.member.entity.order.OrderInvoice;
import com.sgs.ecom.member.request.oiq.OiqUserInvoiceReq;

public class OrderInvoiceDO {

    private BaseCopyObj baseCopy = new BaseCopyObj();

    public OrderInvoice getInvoiceByUserInvoiceDTO(OrderInvoice orderInvoice, OiqUserInvoiceReq userInvoiceDTO){
        if(ValidationUtil.isEmpty(userInvoiceDTO)){
            return orderInvoice;
        }
        baseCopy.copy(orderInvoice,userInvoiceDTO);
        orderInvoice.setBankAddr(userInvoiceDTO.getBankName());
        orderInvoice.setRegisterPhone(userInvoiceDTO.getRegPhone());
        orderInvoice.setRegisterAddr(userInvoiceDTO.getRegAddress());
        return orderInvoice;
    }


}
