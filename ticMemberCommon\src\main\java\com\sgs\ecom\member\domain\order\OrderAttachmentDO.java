package com.sgs.ecom.member.domain.order;

import com.sgs.base.BaseCopyObj;
import com.sgs.ecom.member.dto.detail.OrderAttachmentDTO;
import com.sgs.ecom.member.entity.OrderAttachment;
import com.sgs.ecom.member.enumtool.OrderAttachmentTypeEnum;
import com.sgs.ecom.member.enumtool.BaseOrderStateEnum;
import com.sgs.ecom.member.enumtool.BaseOrderStateEnum;
import com.sgs.ecom.member.enumtool.OrderTypeEnum;
import com.sgs.ecom.member.enumtool.order.OrderPayStateEnum;
import com.sgs.ecom.member.request.FileReq;
import com.sgs.ecom.member.util.order.UseDateUtil;

import java.util.*;

public class OrderAttachmentDO {

    private BaseCopyObj baseCopy = new BaseCopyObj();

    public static Boolean isShowReportFile(String orderType,int payState, int orderState){
        if(OrderTypeEnum.OIQ_PORTAL_ORDER.getIndex().equals(orderType) ){
            return true;
        }

        if( OrderPayStateEnum.PAY.getIndex()==payState && BaseOrderStateEnum.END.getIndex()==orderState){
            return true;
        }
        return false;
    }

    public static List<OrderAttachmentDTO> getFileListByAttType(Map<String, List<OrderAttachmentDTO>> listMap, String orderNo, OrderAttachmentTypeEnum typeEnum){
        String key=orderNo+"-"+typeEnum.getIndex();
        return listMap.getOrDefault(key,new ArrayList<>());
    }

    //给附件添加url并且按订单分组
    public static Map<String,List<OrderAttachmentDTO>> orderAttachmentAddUrl(List<OrderAttachmentDTO> fileList,Map<String, String> fileUrlMap){
        Map<String,List<OrderAttachmentDTO>> attachmentDTOMap=new HashMap<>();
        for (OrderAttachmentDTO orderAttachmentDTO : fileList) {
            orderAttachmentDTO.setFileUrl(fileUrlMap.get(orderAttachmentDTO.getFileId()));
            if(attachmentDTOMap.containsKey(orderAttachmentDTO.getOrderNo())){
                List<OrderAttachmentDTO> list=attachmentDTOMap.get(orderAttachmentDTO.getOrderNo());
                list.add(orderAttachmentDTO);
                attachmentDTOMap.put(orderAttachmentDTO.getOrderNo(),list);
            }else{
                List<OrderAttachmentDTO> list=new ArrayList<>();
                list.add(orderAttachmentDTO);
                attachmentDTOMap.put(orderAttachmentDTO.getOrderNo(),list);
            }
        }
        return attachmentDTOMap;

    }

    public List<OrderAttachment> getListByReq(List<FileReq> fileReqList, String orderNo, String groupNo,
                                              OrderAttachmentTypeEnum orderAttachmentTypeEnum, String dateStr){
        List<OrderAttachment> list=new ArrayList<>();
        for(FileReq fileReq:fileReqList){
            OrderAttachment orderAttachment=new OrderAttachment();
            BaseCopyObj baseCopyObj=new BaseCopyObj();
            baseCopyObj.copyWithNull(orderAttachment,fileReq);
            orderAttachment.setCreateDate(dateStr);
            orderAttachment.setStateDate(dateStr);
            orderAttachment.setState(1);
            orderAttachment.setOrderNo(orderNo);
            orderAttachment.setGroupNo(groupNo);
            orderAttachment.setAttType(orderAttachmentTypeEnum.getIndex());
            orderAttachment.setUploadType(2);
            list.add(orderAttachment);
        }
        return list;
    }

    public OrderAttachment getBaseAttachment(String orderNo,String groupNo){
        OrderAttachment orderAttachment = new OrderAttachment();
        String dateStr = UseDateUtil.getDateString(new Date());
        orderAttachment.setCreateDate(dateStr);
        orderAttachment.setOrderNo(orderNo);
        orderAttachment.setStateDate(dateStr);
        orderAttachment.setState(1);
        orderAttachment.setGroupNo(groupNo);
        orderAttachment.setUploadType(2);
        return orderAttachment;
    }


}
