package com.sgs.ecom.member.domain.user;

import com.sgs.base.BaseCopyObj;
import com.sgs.ecom.member.dto.user.UserInvoiceDTO;
import com.sgs.ecom.member.entity.user.UserInvoice;
import com.sgs.ecom.member.request.oiq.OiqUserInvoiceReq;
import com.sgs.ecom.member.util.order.UseDateUtil;

import java.util.Date;

public class UserInvoiceDO {

    private BaseCopyObj baseCopy = new BaseCopyObj();

    public UserInvoice getUserInvoiceByDTO(OiqUserInvoiceReq userInvoiceDTO, Long userId){
        UserInvoice userInvoice=new UserInvoice();
        baseCopy.copyWithNull(userInvoice,userInvoiceDTO);
        userInvoice.setState(1);
        userInvoice.setUserId(userId);
        String dateStr= UseDateUtil.getDateString(new Date());
        userInvoice.setCreateDate(dateStr);
        userInvoice.setStateDate(dateStr);
        return userInvoice;
    }

    public UserInvoiceDTO entityToDTO(UserInvoice userInvoice){
        UserInvoiceDTO userInvoiceDTO=new UserInvoiceDTO();
        baseCopy.copyWithNull(userInvoiceDTO,userInvoice);
        userInvoiceDTO.setExtendFilesStr(userInvoice.getExtendFiles());
        return userInvoiceDTO;
    }

    public OiqUserInvoiceReq entityToReq(UserInvoice userInvoice){
        OiqUserInvoiceReq userInvoiceDTO=new OiqUserInvoiceReq();
        baseCopy.copyWithNull(userInvoiceDTO,userInvoice);
        userInvoiceDTO.setExtendFilesStr(userInvoice.getExtendFiles());
        return userInvoiceDTO;
    }
}
