<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.order.dao.OrderShareMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.vo.VOOrderShare" >
    <id column="SHARE_ID" property="shareId" jdbcType="BIGINT" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="STATE" property="state" jdbcType="INTEGER" />
    <result column="SHARE_DATE" property="shareDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.sgs.ecom.member.vo.VOOrderShare" extends="BaseResultMap" >
    <result column="SHARE_CONTENT" property="shareContent" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    SHARE_ID, ORDER_NO, USER_ID, STATE, SHARE_DATE
  </sql>
  <sql id="Blob_Column_List" >
    SHARE_CONTENT
  </sql>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from ORDER_SHARE
    where SHARE_ID = #{shareId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from ORDER_SHARE
    where SHARE_ID = #{shareId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.sgs.ecom.member.vo.VOOrderShare" >
    insert into ORDER_SHARE (SHARE_ID, ORDER_NO, USER_ID, 
      STATE, SHARE_DATE, SHARE_CONTENT
      )
    values (#{shareId,jdbcType=BIGINT}, #{orderNo,jdbcType=VARCHAR}, #{userId,jdbcType=BIGINT}, 
      #{state,jdbcType=INTEGER}, #{shareDate,jdbcType=TIMESTAMP}, #{shareContent,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.ecom.member.vo.VOOrderShare" >
    insert into ORDER_SHARE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="shareId != null" >
        SHARE_ID,
      </if>
      <if test="orderNo != null" >
        ORDER_NO,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
      <if test="state != null" >
        STATE,
      </if>
      <if test="shareDate != null" >
        SHARE_DATE,
      </if>
      <if test="shareContent != null" >
        SHARE_CONTENT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="shareId != null" >
        #{shareId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="state != null" >
        #{state,jdbcType=INTEGER},
      </if>
      <if test="shareDate != null" >
        #{shareDate,jdbcType=TIMESTAMP},
      </if>
      <if test="shareContent != null" >
        #{shareContent,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.ecom.member.vo.VOOrderShare" >
    update ORDER_SHARE
    <set >
      <if test="orderNo != null" >
        ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        USER_ID = #{userId,jdbcType=BIGINT},
      </if>
      <if test="state != null" >
        STATE = #{state,jdbcType=INTEGER},
      </if>
      <if test="shareDate != null" >
        SHARE_DATE = #{shareDate,jdbcType=TIMESTAMP},
      </if>
      <if test="shareContent != null" >
        SHARE_CONTENT = #{shareContent,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where SHARE_ID = #{shareId,jdbcType=BIGINT}
  </update>


  <select id="selectShareDetail" resultMap="ResultMapWithBLOBs" parameterType="java.lang.String" >
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from ORDER_SHARE
    where STATE=1 and ORDER_NO= #{orderNo} order by SHARE_ID desc limit 1
  </select>

  <update id="delOrderShare">
    update ORDER_SHARE set STATE=0 where  ORDER_NO=#{orderNo}
  </update>


</mapper>