package com.sgs.ecom.member.dto.oiq.applicationInfo;

import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.dto.order.OrderSampleFromDTO;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class OiqSampleDTO {

    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String sampleName;
    //oiq使用
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class,BaseOrderFilter.OrderToOther.class})
    private String sampleNo;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String sampleNameCn;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String sampleNameEn;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private Long sampleId;

    private String sampleShapeCode;

    private String sampleCategoryCode;

    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    @BeanAnno(dtocls = {OrderSampleFromDTO.class})
    private List<OrderSampleFromDTO> sampleFromDTOList=new ArrayList<>();
    @ApiAnno(groups={BaseOrderFilter.OrderToOther.class,BaseOrderFilter.OrderToOther.class})
    @BeanAnno(dtocls = {OrderSampleFromDTO.class})
    private List<OrderSampleFromDTO> sampleAttr=new ArrayList<>();

    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String row;


    public String getSampleName() {
        return sampleName;
    }

    public void setSampleName(String sampleName) {
        this.sampleName = sampleName;
    }

    public List<OrderSampleFromDTO> getSampleFromDTOList() {
        return sampleFromDTOList;
    }

    public void setSampleFromDTOList(List<OrderSampleFromDTO> sampleFromDTOList) {
        this.sampleFromDTOList = sampleFromDTOList;
    }

    public String getSampleNo() {
        return sampleNo;
    }

    public void setSampleNo(String sampleNo) {
        this.sampleNo = sampleNo;
    }

    public Long getSampleId() {
        return sampleId;
    }

    public void setSampleId(Long sampleId) {
        this.sampleId = sampleId;
    }

    public String getRow() {
        return row;
    }

    public void setRow(String row) {
        this.row = row;
    }

    public String getSampleNameCn() {
        return sampleNameCn;
    }

    public void setSampleNameCn(String sampleNameCn) {
        this.sampleNameCn = sampleNameCn;
    }

    public String getSampleNameEn() {
        return sampleNameEn;
    }

    public void setSampleNameEn(String sampleNameEn) {
        this.sampleNameEn = sampleNameEn;
    }

    public String getSampleShapeCode() {
        return sampleShapeCode;
    }

    public void setSampleShapeCode(String sampleShapeCode) {
        this.sampleShapeCode = sampleShapeCode;
    }

    public String getSampleCategoryCode() {
        return sampleCategoryCode;
    }

    public void setSampleCategoryCode(String sampleCategoryCode) {
        this.sampleCategoryCode = sampleCategoryCode;
    }

    public List<OrderSampleFromDTO> getSampleAttr() {
        return sampleAttr;
    }

    public void setSampleAttr(List<OrderSampleFromDTO> sampleAttr) {
        this.sampleAttr = sampleAttr;
    }
}
