package com.sgs.ecom.member.dto.oiq.applicationInfo;

import org.apache.commons.lang.StringUtils;

public class OiqAddressDTO {
    private Long addressId;
    private OiqUserAddressDTO userAddressDTO;

    public OiqAddressDTO() {
    }

    public OiqAddressDTO(Long addressId, OiqUserAddressDTO userAddressDTO) {
        this.addressId = addressId;
        this.userAddressDTO = userAddressDTO;
    }

    public static String getAddressInfo(OiqUserAddressDTO oiqUserAddressDTO) {
        if(oiqUserAddressDTO==null){
            return "";
        }
        StringBuilder stringBuilder=new StringBuilder();
        if(StringUtils.isNotBlank(oiqUserAddressDTO.getProvince())){
            stringBuilder.append(oiqUserAddressDTO.getProvince());
        }
        if(StringUtils.isNotBlank(oiqUserAddressDTO.getCity())){
            stringBuilder.append(oiqUserAddressDTO.getCity());
        }
        if(StringUtils.isNotBlank(oiqUserAddressDTO.getTown())){
            stringBuilder.append(oiqUserAddressDTO.getTown());
        }
        if(StringUtils.isNotBlank(oiqUserAddressDTO.getCompanyAddress())){
            stringBuilder.append(oiqUserAddressDTO.getCompanyAddress());
        }
        stringBuilder.append(" ");
        if(StringUtils.isNotBlank(oiqUserAddressDTO.getCompanyName())){
            stringBuilder.append(oiqUserAddressDTO.getCompanyName());
            stringBuilder.append(",");
        }
        if(StringUtils.isNotBlank(oiqUserAddressDTO.getUserName())){
            stringBuilder.append(oiqUserAddressDTO.getUserName());
            stringBuilder.append(",");
        }
        if(StringUtils.isNotBlank(oiqUserAddressDTO.getUserPhone())){
            stringBuilder.append(oiqUserAddressDTO.getUserPhone());
            stringBuilder.append(",");
        }
        String str=stringBuilder.toString();
        if(str.length()>0){
            str=str.substring(0,str.length()-1);
        }
        return str;
    }


    public Long getAddressId() {
        return addressId;
    }

    public void setAddressId(Long addressId) {
        this.addressId = addressId;
    }

    public OiqUserAddressDTO getUserAddressDTO() {
        return userAddressDTO;
    }

    public void setUserAddressDTO(OiqUserAddressDTO userAddressDTO) {
        this.userAddressDTO = userAddressDTO;
    }
}
