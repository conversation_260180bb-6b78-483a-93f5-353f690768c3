package com.sgs.ecom.member.entity.user;

import java.util.Date;

public class UserNoticeLog {
    private Long logId;

    private Integer noticeType;

    private String businessType;

    private String businessCode;

    private String businessNo;

    private Long userId;

    private String bu;

    private Integer noticeMethod;

    private String sendTemplate;

    private Date nextNoticeDate;

    private Integer state;

    private Date createDate;

    public Long getLogId() {
        return logId;
    }

    public void setLogId(Long logId) {
        this.logId = logId;
    }



    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType == null ? null : businessType.trim();
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode == null ? null : businessCode.trim();
    }

    public String getBusinessNo() {
        return businessNo;
    }

    public void setBusinessNo(String businessNo) {
        this.businessNo = businessNo == null ? null : businessNo.trim();
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getBu() {
        return bu;
    }

    public void setBu(String bu) {
        this.bu = bu == null ? null : bu.trim();
    }


    public String getSendTemplate() {
        return sendTemplate;
    }

    public void setSendTemplate(String sendTemplate) {
        this.sendTemplate = sendTemplate == null ? null : sendTemplate.trim();
    }

    public Date getNextNoticeDate() {
        return nextNoticeDate;
    }

    public void setNextNoticeDate(Date nextNoticeDate) {
        this.nextNoticeDate = nextNoticeDate;
    }



    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getNoticeType() {
        return noticeType;
    }

    public void setNoticeType(Integer noticeType) {
        this.noticeType = noticeType;
    }

    public Integer getNoticeMethod() {
        return noticeMethod;
    }

    public void setNoticeMethod(Integer noticeMethod) {
        this.noticeMethod = noticeMethod;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }
}