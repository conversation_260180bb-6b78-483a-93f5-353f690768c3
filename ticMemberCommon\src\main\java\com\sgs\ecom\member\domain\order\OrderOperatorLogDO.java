package com.sgs.ecom.member.domain.order;


import com.alibaba.fastjson.JSON;
import com.platform.util.ValidationUtil;
import com.sgs.ecom.member.dto.OrderBaseInfoDTO;
import com.sgs.ecom.member.dto.OrderOperatorLogDTO;
import com.sgs.ecom.member.dto.base.BaseOrderDTO;
import com.sgs.ecom.member.dto.user.OrderCouponDTO;
import com.sgs.ecom.member.entity.order.OrderOperatorLog;
import com.sgs.ecom.member.enumtool.OrderOperatorTypeEnum;
import com.sgs.ecom.member.util.order.UseDateUtil;
import org.apache.commons.lang.StringUtils;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

public class OrderOperatorLogDO extends OrderOperatorLog {

    public OrderOperatorLogDO() {
    }



    public  OrderOperatorLog addLog(BaseOrderDTO baseOrderDTO, OrderOperatorTypeEnum orderOperatorTypeEnum
    , String text, String memo, String csCode, int isShow){
        OrderOperatorLog orderOperatorLog=new OrderOperatorLog();
        //基础数据
        orderOperatorLog.setOrderNo(baseOrderDTO.getOrderNo());
        orderOperatorLog.setOrderType(Integer.parseInt(baseOrderDTO.getOrderType()));
        orderOperatorLog.setOperatorType(Integer.parseInt(orderOperatorTypeEnum.getIndex()));
        orderOperatorLog.setOperatorText(text);
        orderOperatorLog.setMemo(memo);
        orderOperatorLog.setCsCode(csCode);
        orderOperatorLog.setIsShow(isShow);
        orderOperatorLog.setOperatorDate(UseDateUtil.getDateString(new Date()));
        return orderOperatorLog;
    }

    public static OrderOperatorTypeEnum getSaveFormLogEnum(int type,Boolean isPortal){
        if(type==0 && !isPortal){
            return null;
        }
        if(type==1 && !isPortal){
            return  OrderOperatorTypeEnum.FORM;
        }
        if(type==0){
            return OrderOperatorTypeEnum.DML_CREATE_FORM;
        }
        if(type==1){
            return  OrderOperatorTypeEnum.DML_CONFIRM_FORM;
        }
        if(type==2){
            return OrderOperatorTypeEnum.DML_UPDATE_FORM;
        }
        return null;
    }


    public static OrderOperatorLogDTO getSubLogByMain(List<OrderOperatorLogDTO> logDTOList,String orderNo){
        List<OrderOperatorLogDTO> log=logDTOList.stream().filter(a->(orderNo.equals(a.getOperatorTextMore()))).
                sorted(Comparator.comparing(OrderOperatorLogDTO::getLogId).reversed()).
                collect(Collectors.toList());
        if(ValidationUtil.isEmpty(log)){
            return null;
        }
        return log.get(0);


    }

    //日志取最后一条
    public static OrderOperatorLogDTO logToLast(OrderOperatorLogDTO logDTO){
        if(ValidationUtil.isEmpty(logDTO)){
            return logDTO;
        }

        String operatorType = logDTO.getOperatorType();
        if (OrderOperatorTypeEnum.LEAVING_MESS.getIndex().equals(operatorType) || OrderOperatorTypeEnum.SYS_LEAVING_MESS.getIndex().equals(operatorType)) {
            String json = logDTO.getOperatorText();
            List<OrderOperatorLogDTO> jsonList = JSON.parseArray(json, OrderOperatorLogDTO.class);
            jsonList.stream().filter(a->a.getIsShow().equals("1")).sorted(Comparator.comparing(OrderOperatorLogDTO::getOperatorDate).reversed())
                    .limit(1).collect(Collectors.toList());
        }
        return logDTO;
    }




}
