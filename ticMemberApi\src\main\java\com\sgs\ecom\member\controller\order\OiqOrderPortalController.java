package com.sgs.ecom.member.controller.order;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.ecom.member.request.OrderNoReq;
import com.sgs.ecom.member.service.portal.interfaces.IOrderPortalService;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.portal/order")
public class OiqOrderPortalController extends ControllerUtil {

    @Autowired
    private IOrderPortalService orderPortalService;



    /**
     * @params [createInquiryRpcDTO]
     * @return com.sgs.ecom.order.util.ResultBody
     * @description 确认询价单
     * <AUTHOR> || created at 2023/5/24 11:07
     */
    @HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "quotationConfirm", method = {RequestMethod.POST})
    public ResultBody quotationConfirm(
            @RequestHeader(value="accessToken") String token,
            @RequestBody OrderNoReq orderNoReq) throws Exception {
        orderPortalService.quotationConfirm(orderNoReq, getUserDTO(token));
        return ResultBody.success();
    }

}
