#spring.cloud.nacos.config.server-addr=10.168.129.79:6701
#uat
#spring.cloud.nacos.config.namespace=6130b322-3b22-4696-8577-09df5f997576
#test
#spring.cloud.nacos.config.namespace=7f5dd9ae-1ae0-4430-8c17-d1d8cde069c3
#spring.cloud.nacos.config.prefix=public



#微软生产nacos
spring.cloud.nacos.config.server-addr=10.168.130.68:8848,10.168.130.70:8848
spring.cloud.nacos.config.namespace=6e84c8b4-b800-4220-b0ec-1923e3ecab0d
spring.cloud.nacos.config.prefix=public

#\u516c\u5171\u914d\u7f6e
#spring.cloud.nacos.config.extension-configs[0].data-id=public.properties
#spring.cloud.nacos.config.extension-configs[0].group=DEFAULT_GROUP
#spring.cloud.nacos.config.extension-configs[0].refresh=true

#\u591a\u914d\u7f6e
spring.cloud.nacos.config.shared-dataids=tic-member-basic.properties,tic-member-db.properties,tic-member-url.properties,tic-business-basic.yaml
spring.cloud.nacos.config.refreshable-dataids=tic-member-basic.properties,tic-member-url.properties,tic-business-basic.yaml

#\u7aef\u53e3
server.port=8007
# \u5e94\u7528\u540d\u79f0
spring.application.name=tic-member
spring.profiles.active=Aprod
spring.cloud.nacos.config.group=DEFAULT_GROUP
