package com.sgs.ecom.member.dto.permission;

public class OrderPermissionDTO {
    //数据权限
    public static final String DATA="DATA";
    public static final String SWITCH="SWITCH";
    /*{
        bu:"1001"
        authType:"DATA"
        authCode:"OIQ"
    }*/
  /*  {
        bu:"1001"
        authType:"SWITCH"
        authCode:"OIQ"
        isOpen:1
    }*/
    private String authType;
    private String authCode;
    private int isOpen;

    public String getAuthType() {
        return authType;
    }

    public void setAuthType(String authType) {
        this.authType = authType;
    }

    public String getAuthCode() {
        return authCode;
    }

    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }

    public int getIsOpen() {
        return isOpen;
    }

    public void setIsOpen(int isOpen) {
        this.isOpen = isOpen;
    }
}
