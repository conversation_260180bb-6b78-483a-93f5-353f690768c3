package com.sgs.ecom.member.enumtool.aplication;

import org.apache.commons.lang.StringUtils;

public enum CertificateLuaEnum {
    ZN("zn", "1", "中文证书"),
    EN("en", "2", "英文证书"),
    ZNANDEN("znanden", "3", "中文+英文证书"),
    OTHER("other", "4", "其他"),
    ZNEN("znen", "5", "中英文证书"),




    ;

    CertificateLuaEnum(String name, String index, String nameCh) {
        this.name = name;
        this.index = index;
        this.nameCh = nameCh;
    }

    public static String getName(String index) {
        for (CertificateLuaEnum c : CertificateLuaEnum.values()) {
            if (c.getIndex().equals(index)) {
                return c.getName();
            }
        }
        return "3";
    }

    public static String getNameChByName(String name) {
        if(StringUtils.isBlank(name)){
            return "";
        }

        for (CertificateLuaEnum c : CertificateLuaEnum.values()) {
            if (c.getName().equals(name.toLowerCase())) {
                return c.getNameCh();
            }
        }
        return "";
    }


    public static String getNameByNameCh(String nameCh) {
        for (CertificateLuaEnum c : CertificateLuaEnum.values()) {
            if (c.getNameCh().equals(nameCh)) {
                return c.getName().toUpperCase();
            }
        }
        return "";
    }


    public static String getIndexByName(String name) {
        for (CertificateLuaEnum c : CertificateLuaEnum.values()) {
            if (c.getName().equals(name)) {
                return c.getIndex();
            }
        }
        return "4";
    }


    private String name;
    private String index;
    private String nameCh;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }
}
