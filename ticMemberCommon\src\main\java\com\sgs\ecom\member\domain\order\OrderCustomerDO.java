package com.sgs.ecom.member.domain.order;



import com.sgs.ecom.member.entity.order.OrderCustomer;
import com.sgs.ecom.member.util.order.UseDateUtil;

import java.util.Date;

public class OrderCustomerDO extends OrderCustomer {


    public static OrderCustomer getBaseOrderCustomer(String orderNo){
        String dateStr= UseDateUtil.getDateString(new Date());
        OrderCustomer orderCustomer=new OrderCustomer();
        orderCustomer.setCreateDate(dateStr);
        orderCustomer.setStateDate(dateStr);
        orderCustomer.setState(1);
        orderCustomer.setOrderNo(orderNo);
        return orderCustomer;
    }
}
