package com.sgs.ecom.member.controller.order;


import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.ecom.member.request.OrderRefundReq;
import com.sgs.ecom.member.service.order.interfaces.IOrderPayService;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.order/orderRefund")
public class OrderRefundController extends ControllerUtil {
	@Autowired
	private IOrderPayService orderPayService;


	/**
	 * @Description : 客户发起退款功能接口
	 * <AUTHOR> Zhang
	 * @Date  2023/5/19
	 * @param orderRefundReq:
	 * @param token:
	 * @return: com.sgs.ecom.member.util.ResultBody
	 **/
	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "refund", method = { RequestMethod.POST })
	public ResultBody refund(
			@RequestBody OrderRefundReq orderRefundReq,
			@RequestHeader(value="accessToken") String token) throws Exception {
		orderPayService.refund(orderRefundReq, getUserDTO(token));
		return ResultBody.success();
	}
}
