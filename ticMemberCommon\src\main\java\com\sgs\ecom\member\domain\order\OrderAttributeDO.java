package com.sgs.ecom.member.domain.order;

import com.alibaba.fastjson.JSON;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseCopyObj;
import com.sgs.ecom.member.dto.OrderAttributeDTO;
import com.sgs.ecom.member.dto.pay.BankDTO;
import com.sgs.ecom.member.dto.pay.PaymentChannelsDTO;
import com.sgs.ecom.member.util.select.AttributeUtil;
import com.sgs.ecom.member.util.select.SelectMapUtil;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class OrderAttributeDO {

    public OrderAttributeDTO attributeMapToDTO(Map<String, List<OrderAttributeDTO>> map,String key){
        List<OrderAttributeDTO> list=map.getOrDefault(key,new ArrayList<>());
        if(!ValidationUtil.isEmpty(list)){
           return list.stream().sorted(Comparator.comparing(OrderAttributeDTO::getId).reversed()).collect(Collectors.toList()).get(0);
        }
        return null;
    }

    public static Map<String,OrderAttributeDTO> getPaymentByAttr(Map<String, List<OrderAttributeDTO>> map,String key){
        List<OrderAttributeDTO> list=map.getOrDefault(key,new ArrayList<>());
        if(ValidationUtil.isEmpty(list)){
            return new HashMap<>();
        }
        return list.stream().collect(Collectors.toMap(OrderAttributeDTO::getAttrCode, Function.identity(), (key1, key2) -> key1, LinkedHashMap::new));
    }


    public static List<String> getConfigAttrList(){
        List<String> list=new ArrayList<>();
        list.add(AttributeUtil.REPORT_FORM);
        list.add(AttributeUtil.REPORT_LUA);
        list.add(AttributeUtil.LAB_NAME);
        list.add(AttributeUtil.TEST_CYCLE);
        list.add(AttributeUtil.SPECIAL_NOTE);
        list.add(AttributeUtil.DISCOUNTAMOUNTNUM);
        list.add(AttributeUtil.NEW_DISCOUNT_AMOUNT_NUM);
        list.add(AttributeUtil.URGENT_AMOUNT_NUM);
        return list;
    }

    public static List<String> getLabAndReportList(){
        List<String> list=new ArrayList<>();
        list.add(SelectMapUtil.LAB_NAME);
        list.add(SelectMapUtil.REPORT_LUA);
        list.add(SelectMapUtil.REPORT_FORM);
        return list;
    }

    public static BankDTO getBankByAttribute(OrderAttributeDTO orderAttributeDTO){
        PaymentChannelsDTO paymentChannelsDTO= JSON.parseObject(orderAttributeDTO.getAttrName(), PaymentChannelsDTO.class);
        BankDTO bankDTO=new BankDTO();
        BaseCopyObj baseCopyObj=new BaseCopyObj();
        baseCopyObj.copyWithNull(bankDTO,paymentChannelsDTO);
        bankDTO.setAccountNo(paymentChannelsDTO.getBankNo());
        return bankDTO;
    }
}
