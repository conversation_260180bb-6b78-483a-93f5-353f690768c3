package com.sgs.ecom.member.dto.pay;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.dto.order.SubOrderDTO;
import com.sgs.ecom.member.dto.order.SubOrderInfoDTO;

import java.util.ArrayList;
import java.util.List;

public class PayInfoDTO {
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String orderNo;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private int subOrderFlg;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private List<SubOrderDTO> subOrderDTOList ;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private SubOrderInfoDTO subOrderInfoDTO;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private List<OrderPayDTO> orderPayDTOList=new ArrayList<>();
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private List<OrderPayDTO> orderRefundPayDTOList=new ArrayList<>();
	@ApiAnno(groups={BaseOrderFilter.Default.class})
	private List<PayRefundAlipayDTO> isTimeList;


	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public int getSubOrderFlg() {
		return subOrderFlg;
	}

	public void setSubOrderFlg(int subOrderFlg) {
		this.subOrderFlg = subOrderFlg;
	}

	public List<SubOrderDTO> getSubOrderDTOList() {
		return subOrderDTOList;
	}

	public void setSubOrderDTOList(List<SubOrderDTO> subOrderDTOList) {
		this.subOrderDTOList = subOrderDTOList;
	}

	public SubOrderInfoDTO getSubOrderInfoDTO() {
		return subOrderInfoDTO;
	}

	public void setSubOrderInfoDTO(SubOrderInfoDTO subOrderInfoDTO) {
		this.subOrderInfoDTO = subOrderInfoDTO;
	}

	public List<OrderPayDTO> getOrderPayDTOList() {
		return orderPayDTOList;
	}

	public void setOrderPayDTOList(List<OrderPayDTO> orderPayDTOList) {
		this.orderPayDTOList = orderPayDTOList;
	}

	public List<OrderPayDTO> getOrderRefundPayDTOList() {
		return orderRefundPayDTOList;
	}

	public void setOrderRefundPayDTOList(List<OrderPayDTO> orderRefundPayDTOList) {
		this.orderRefundPayDTOList = orderRefundPayDTOList;
	}

	public List<PayRefundAlipayDTO> getIsTimeList() {
		return isTimeList;
	}

	public void setIsTimeList(List<PayRefundAlipayDTO> isTimeList) {
		this.isTimeList = isTimeList;
	}
}
