package com.sgs.ecom.member.dto.order;
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.ValidationUtil;
import com.platform.util.json.TimeFormatSerializer;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.entity.order.OrderLink;
import com.sgs.ecom.member.request.detail.ApplicationReq;
import com.sgs.ecom.member.request.oiq.OiqOrderLinkReq;
import com.sgs.ecom.member.util.serializer.TimeStringFormatSerializer;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class OrderLinkDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select BUSI_TYPE,POSITION,CREATE_DATE,LINK_NAME,LINK_NAME_EN,LINK_ID,ORDER_NO,DEPARTMENT,LINK_EMAIL,LINK_PHONE from ORDER_LINK"; 
 
 
 	@ApiAnno(groups={Default.class,QuerySummary.class, BaseOrderFilter.OrderToOther.class})
 	@BeanAnno(value="BUSI_TYPE", getName="getBusiType", setName="setBusiType")
 	private int busiType;
 	@ApiAnno(groups={Default.class,QuerySummary.class})
 	@BeanAnno(value="POSITION", getName="getPosition", setName="setPosition")
 	private String position;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
	@JsonSerialize(using = TimeStringFormatSerializer.class)
 	private String createDate;
 	@ApiAnno(groups={Default.class,QuerySummary.class, BaseOrderFilter.OrderToOther.class})
 	@BeanAnno(value="LINK_NAME", getName="getLinkName", setName="setLinkName")
 	private String linkName;
 	@ApiAnno(groups={Default.class,QuerySummary.class})
 	@BeanAnno(value="LINK_NAME_EN", getName="getLinkNameEn", setName="setLinkNameEn")
 	private String linkNameEn;
 	@ApiAnno(groups={Default.class,QuerySummary.class})
 	@BeanAnno(value="LINK_ID", getName="getLinkId", setName="setLinkId")
 	private long linkId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="ORDER_NO", getName="getOrderNo", setName="setOrderNo")
 	private String orderNo;
 	@ApiAnno(groups={Default.class,QuerySummary.class})
 	@BeanAnno(value="DEPARTMENT", getName="getDepartment", setName="setDepartment")
 	private String department;
 	@ApiAnno(groups={Default.class,QuerySummary.class, BaseOrderFilter.OrderToOther.class})
 	@BeanAnno(value="LINK_EMAIL", getName="getLinkEmail", setName="setLinkEmail")
 	private String linkEmail;
 	@ApiAnno(groups={Default.class,QuerySummary.class, BaseOrderFilter.OrderToOther.class})
 	@BeanAnno(value="LINK_PHONE", getName="getLinkPhone", setName="setLinkPhone")
 	private String linkPhone;

	public OrderLinkDTO() {
	}

	public OrderLinkDTO(ApplicationReq applicationReq) {
		this.linkName=applicationReq.getLinkPerson();
		this.linkPhone=applicationReq.getLinkPhone();
		this.linkEmail=applicationReq.getLinkEmail();
	}

	public OrderLinkDTO(OrderLink orderLink) {
		this.linkName=orderLink.getLinkName();
		this.linkPhone=orderLink.getLinkPhone();
		this.linkEmail=orderLink.getLinkEmail();
	}

	public static String listToStr(Map<String, List<OrderLinkDTO>> linkMap,String key,String str){
		String returnStr="";
		List<OrderLinkDTO> list=linkMap.getOrDefault(key,new ArrayList<>());
		if(!ValidationUtil.isEmpty(list)){
			returnStr=list.stream().map(OrderLinkDTO::getLinkEmail).collect(Collectors.joining(str));
	}
		return returnStr;
	}

	public void setBusiType(int busiType){
 		 this.busiType=busiType;
 	}
 	public int getBusiType(){
 		 return this.busiType;
 	}
 
 	 
 	public void setPosition(String position){
 		 this.position=position;
 	}
 	public String getPosition(){
 		 return this.position;
 	}


	public String getCreateDate() {
		return createDate;
	}

	public void setCreateDate(String createDate) {
		this.createDate = createDate;
	}

	public void setLinkName(String linkName){
 		 this.linkName=linkName;
 	}
 	public String getLinkName(){
 		 return this.linkName;
 	}
 
 	 
 	public void setLinkNameEn(String linkNameEn){
 		 this.linkNameEn=linkNameEn;
 	}
 	public String getLinkNameEn(){
 		 return this.linkNameEn;
 	}
 
 	 
 	public void setLinkId(long linkId){
 		 this.linkId=linkId;
 	}
 	public long getLinkId(){
 		 return this.linkId;
 	}
 
 	 
 	public void setOrderNo(String orderNo){
 		 this.orderNo=orderNo;
 	}
 	public String getOrderNo(){
 		 return this.orderNo;
 	}
 
 	 
 	public void setDepartment(String department){
 		 this.department=department;
 	}
 	public String getDepartment(){
 		 return this.department;
 	}
 
 	 
 	public void setLinkEmail(String linkEmail){
 		 this.linkEmail=linkEmail;
 	}
 	public String getLinkEmail(){
 		 return this.linkEmail;
 	}
 
 	 
 	public void setLinkPhone(String linkPhone){
 		 this.linkPhone=linkPhone;
 	}
 	public String getLinkPhone(){
 		 return this.linkPhone;
 	}
 
 	 
}