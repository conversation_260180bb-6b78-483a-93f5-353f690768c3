package com.sgs.ecom.member.fegin;

import com.sgs.ecom.member.request.rpc.LeadsOrderListReq;
import com.sgs.ecom.member.service.util.interfaces.IOrderLeadsRpcService;
import com.sgs.ecom.member.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/business/rpc.v2.leads/order")
public class LeadsServiceRpc {

    @Autowired
    private IOrderLeadsRpcService orderLeadsRpcService;

    @RequestMapping(value = "qryList", method = { RequestMethod.POST })
    public ResultBody qryList(
            @RequestBody LeadsOrderListReq leadsOrderListReq) throws Exception{
        return ResultBody.newInstance(orderLeadsRpcService.qryList(leadsOrderListReq));
    }
}
