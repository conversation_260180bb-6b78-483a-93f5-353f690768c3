package com.sgs.ecom.member.dto.sys;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.json.TimeFormatSerializer;
import com.sgs.base.BaseQryFilter;

import java.sql.Timestamp;

public class SysPackageCategoryDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select CATEGORY_ID,CREATE_DATE,PERSON_CODE,STATE_DATE,STATE,ID,PACKAGE_ID from SYS_PACKAGE_CATEGORY"; 
 
 
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="CATEGORY_ID", getName="getCategoryId", setName="setCategoryId")
 	private long categoryId;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="CATEGORY_PATH", getName="getCategoryPath", setName="setCategoryPath")
 	private String categoryPath;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private Timestamp stateDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="ID", getName="getId", setName="setId")
 	private long id;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="PACKAGE_ID", getName="getPackageId", setName="setPackageId")
 	private long packageId;
 	@ApiAnno(groups={Default.class, QueryList.class})
 	@BeanAnno(value="CUST_ID", getName="getCustId", setName="setCustId")
 	private long custId;
 	
 	public void setCategoryId(long categoryId){
 		 this.categoryId=categoryId;
 	}
 	public long getCategoryId(){
 		 return this.categoryId;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setId(long id){
 		 this.id=id;
 	}
 	public long getId(){
 		 return this.id;
 	}
 
 	 
 	public void setPackageId(long packageId){
 		 this.packageId=packageId;
 	}
 	public long getPackageId(){
 		 return this.packageId;
 	}
	public String getCategoryPath() {
		return categoryPath;
	}
	public void setCategoryPath(String categoryPath) {
		this.categoryPath = categoryPath;
	}
	public long getCustId() {
		return custId;
	}
	public void setCustId(long custId) {
		this.custId = custId;
	}
 
 	 
}