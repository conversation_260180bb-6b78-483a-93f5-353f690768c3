<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.user.dao.SysMemberBenefitsMapper">
    <resultMap id="BaseResultMap" type="com.sgs.ecom.member.bo.SysMemberBenefits">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="LEVEL_ID" property="levelId" jdbcType="INTEGER"/>
        <result column="BENEFITS_TYPE" property="benefitsType" jdbcType="INTEGER"/>
        <result column="BENEFITS_CODE" property="benefitsCode" jdbcType="VARCHAR"/>
        <result column="BU" property="bu" jdbcType="VARCHAR"/>
        <result column="IS_BASIC" property="isBasic" jdbcType="INTEGER"/>
        <result column="EFF_DAYS" property="effDays" jdbcType="INTEGER"/>
        <result column="DAY_LIMIT" property="dayLimit" jdbcType="INTEGER"/>
        <result column="USE_LIMIT" property="useLimit" jdbcType="INTEGER"/>
        <result column="STATE" property="state" jdbcType="INTEGER"/>
        <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP"/>
        <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID
        , LEVEL_ID, BENEFITS_TYPE, BENEFITS_CODE, BU, IS_BASIC, EFF_DAYS, DAY_LIMIT, USE_LIMIT,
    STATE, CREATE_DATE, STATE_DATE
    </sql>
    <sql id="baseQueryWhere">
        <if test="levelId != null">
            and LEVEL_ID = #{levelId}
        </if>
        <if test="benefitsType != null">
            and BENEFITS_TYPE = #{benefitsType}
        </if>
        <if test="benefitsCode != null">
            and BENEFITS_CODE = #{benefitsCode}
        </if>
        <if test="state != null">
            and STATE = #{state}
        </if>
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from SYS_MEMBER_BENEFITS
        where ID = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from SYS_MEMBER_BENEFITS
        where ID = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.sgs.ecom.member.bo.SysMemberBenefits">
        insert into SYS_MEMBER_BENEFITS (ID, LEVEL_ID, BENEFITS_TYPE,
                                         BENEFITS_CODE, BU, IS_BASIC,
                                         EFF_DAYS, DAY_LIMIT, USE_LIMIT,
                                         STATE, CREATE_DATE, STATE_DATE)
        values (#{id,jdbcType=BIGINT}, #{levelId,jdbcType=INTEGER}, #{benefitsType,jdbcType=INTEGER},
                #{benefitsCode,jdbcType=VARCHAR}, #{bu,jdbcType=VARCHAR}, #{isBasic,jdbcType=INTEGER},
                #{effDays,jdbcType=INTEGER}, #{dayLimit,jdbcType=INTEGER}, #{useLimit,jdbcType=INTEGER},
                #{state,jdbcType=INTEGER}, #{createDate,jdbcType=TIMESTAMP}, #{stateDate,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.sgs.ecom.member.bo.SysMemberBenefits">
        insert into SYS_MEMBER_BENEFITS
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID,
            </if>
            <if test="levelId != null">
                LEVEL_ID,
            </if>
            <if test="benefitsType != null">
                BENEFITS_TYPE,
            </if>
            <if test="benefitsCode != null">
                BENEFITS_CODE,
            </if>
            <if test="bu != null">
                BU,
            </if>
            <if test="isBasic != null">
                IS_BASIC,
            </if>
            <if test="effDays != null">
                EFF_DAYS,
            </if>
            <if test="dayLimit != null">
                DAY_LIMIT,
            </if>
            <if test="useLimit != null">
                USE_LIMIT,
            </if>
            <if test="state != null">
                STATE,
            </if>
            <if test="createDate != null">
                CREATE_DATE,
            </if>
            <if test="stateDate != null">
                STATE_DATE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="levelId != null">
                #{levelId,jdbcType=INTEGER},
            </if>
            <if test="benefitsType != null">
                #{benefitsType,jdbcType=INTEGER},
            </if>
            <if test="benefitsCode != null">
                #{benefitsCode,jdbcType=VARCHAR},
            </if>
            <if test="bu != null">
                #{bu,jdbcType=VARCHAR},
            </if>
            <if test="isBasic != null">
                #{isBasic,jdbcType=INTEGER},
            </if>
            <if test="effDays != null">
                #{effDays,jdbcType=INTEGER},
            </if>
            <if test="dayLimit != null">
                #{dayLimit,jdbcType=INTEGER},
            </if>
            <if test="useLimit != null">
                #{useLimit,jdbcType=INTEGER},
            </if>
            <if test="state != null">
                #{state,jdbcType=INTEGER},
            </if>
            <if test="createDate != null">
                #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="stateDate != null">
                #{stateDate,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sgs.ecom.member.bo.SysMemberBenefits">
        update SYS_MEMBER_BENEFITS
        <set>
            <if test="levelId != null">
                LEVEL_ID = #{levelId,jdbcType=INTEGER},
            </if>
            <if test="benefitsType != null">
                BENEFITS_TYPE = #{benefitsType,jdbcType=INTEGER},
            </if>
            <if test="benefitsCode != null">
                BENEFITS_CODE = #{benefitsCode,jdbcType=VARCHAR},
            </if>
            <if test="bu != null">
                BU = #{bu,jdbcType=VARCHAR},
            </if>
            <if test="isBasic != null">
                IS_BASIC = #{isBasic,jdbcType=INTEGER},
            </if>
            <if test="effDays != null">
                EFF_DAYS = #{effDays,jdbcType=INTEGER},
            </if>
            <if test="dayLimit != null">
                DAY_LIMIT = #{dayLimit,jdbcType=INTEGER},
            </if>
            <if test="useLimit != null">
                USE_LIMIT = #{useLimit,jdbcType=INTEGER},
            </if>
            <if test="state != null">
                STATE = #{state,jdbcType=INTEGER},
            </if>
            <if test="createDate != null">
                CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="stateDate != null">
                STATE_DATE = #{stateDate,jdbcType=TIMESTAMP},
            </if>
        </set>
        where ID = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sgs.ecom.member.bo.SysMemberBenefits">
        update SYS_MEMBER_BENEFITS
        set LEVEL_ID      = #{levelId,jdbcType=INTEGER},
            BENEFITS_TYPE = #{benefitsType,jdbcType=INTEGER},
            BENEFITS_CODE = #{benefitsCode,jdbcType=VARCHAR},
            BU            = #{bu,jdbcType=VARCHAR},
            IS_BASIC      = #{isBasic,jdbcType=INTEGER},
            EFF_DAYS      = #{effDays,jdbcType=INTEGER},
            DAY_LIMIT     = #{dayLimit,jdbcType=INTEGER},
            USE_LIMIT     = #{useLimit,jdbcType=INTEGER},
            STATE         = #{state,jdbcType=INTEGER},
            CREATE_DATE   = #{createDate,jdbcType=TIMESTAMP},
            STATE_DATE    = #{stateDate,jdbcType=TIMESTAMP}
        where ID = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectListByMap" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from SYS_MEMBER_BENEFITS
        <where>
            <include refid="baseQueryWhere"></include>
        </where>
    </select>
    <select id="qryUserToolKitByMap" resultMap="BaseResultMap">
        SELECT
        BENEFITS_CODE
        FROM
        SYS_MEMBER_BENEFITS
        WHERE
        BENEFITS_TYPE = #{benefitsType}
        AND state = #{state}
        AND LEVEL_ID &lt;= #{levelId}
        <if test="toolCodeList != null">
            and BENEFITS_CODE IN
            <foreach collection="toolCodeList" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>

        UNION
        SELECT
        TOOL_CODE
        FROM
        USER_TOOLKIT
        WHERE
        STATE = #{state}
        AND USER_ID = #{userId}
        AND EXP_DATE >= now()
        AND (USE_NUMS > 0 or USE_NUMS = -1)
        <if test="toolCodeList != null">
            and TOOL_CODE IN
            <foreach collection="toolCodeList" separator="," open="(" close=")" item="item">
                 #{item}
            </foreach>
        </if>
    </select>

</mapper>