package com.sgs.ecom.member.bo; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import com.platform.annotation.CharacterVaild; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import com.platform.annotation.BeanAnno; 

public class BOSgsInternalLog{
 
 	public static final String SEQUENCE = "LOG_ID"; 
  
 	public static final String BO_SQL = "SGS_INTERNAL_LOG"; 
 
 	public static final String OWNER ="center";

 	public static final String CREATE_DATE="createDate";
 	public static final String REQ_PARA="reqPara";
 	public static final String STATE_DATE="stateDate";
 	public static final String INT_NAME="intName";
 	public static final String BATCH_NO="batchNo";
 	public static final String INT_PLATFORM="intPlatform";
 	public static final String LOG_ID="logId";
 	public static final String RES_PARA="resPara";

 	@BeanAnno("CREATE_DATE")
 	private Timestamp createDate;
 	@BeanAnno("REQ_PARA")
 	private String reqPara;
 	@BeanAnno("STATE_DATE")
 	private Timestamp stateDate;
 	@BeanAnno("INT_NAME")
 	private String intName;
 	@BeanAnno("BATCH_NO")
 	private String batchNo;
 	@BeanAnno("INT_PLATFORM")
 	private String intPlatform;
 	@BeanAnno("LOG_ID")
 	private long logId;
 	@BeanAnno("RES_PARA")
 	private String resPara;

 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	@CharacterVaild(len = 21845) 
 	public void setReqPara(String reqPara){
 		 this.reqPara=reqPara;
 	}
 	public String getReqPara(){
 		 return this.reqPara;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setIntName(String intName){
 		 this.intName=intName;
 	}
 	public String getIntName(){
 		 return this.intName;
 	}
 
 	 
 	@CharacterVaild(len = 20) 
 	public void setBatchNo(String batchNo){
 		 this.batchNo=batchNo;
 	}
 	public String getBatchNo(){
 		 return this.batchNo;
 	}
 
 	 
 	@CharacterVaild(len = 30) 
 	public void setIntPlatform(String intPlatform){
 		 this.intPlatform=intPlatform;
 	}
 	public String getIntPlatform(){
 		 return this.intPlatform;
 	}
 
 	 
 	public void setLogId(long logId){
 		 this.logId=logId;
 	}
 	public long getLogId(){
 		 return this.logId;
 	}
 
 	 
 	@CharacterVaild(len = 21845) 
 	public void setResPara(String resPara){
 		 this.resPara=resPara;
 	}
 	public String getResPara(){
 		 return this.resPara;
 	}
 
 	 
}