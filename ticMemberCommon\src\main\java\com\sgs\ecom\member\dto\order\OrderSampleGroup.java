package com.sgs.ecom.member.dto.order;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.util.serializer.TimeStringFormatSerializer;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 打印申请表时需要将订单的动态字段信息做一层转换
 * <AUTHOR>
 */
public class OrderSampleGroup extends BaseQryFilter {

 	@ApiAnno(groups={Default.class})
 	private String sampleKeyName;//动态字段对应的key中文

	@ApiAnno(groups={Default.class})
	private String sampleKeyNameEn;//动态字段对应的key英文

	@ApiAnno(groups={Default.class})
	private String sampleValue;//动态字段对应中文key的值

	@ApiAnno(groups={Default.class})
	private String sampleValueEn;//动态字段对应英文key的值

	public String getSampleKeyName() {
		return sampleKeyName;
	}

	public void setSampleKeyName(String sampleKeyName) {
		this.sampleKeyName = sampleKeyName;
	}

	public String getSampleKeyNameEn() {
		return sampleKeyNameEn;
	}

	public void setSampleKeyNameEn(String sampleKeyNameEn) {
		this.sampleKeyNameEn = sampleKeyNameEn;
	}

	public String getSampleValue() {
		return sampleValue;
	}

	public void setSampleValue(String sampleValue) {
		this.sampleValue = sampleValue;
	}

	public String getSampleValueEn() {
		return sampleValueEn;
	}

	public void setSampleValueEn(String sampleValueEn) {
		this.sampleValueEn = sampleValueEn;
	}
}