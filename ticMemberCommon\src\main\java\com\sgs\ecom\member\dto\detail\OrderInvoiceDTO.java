package com.sgs.ecom.member.dto.detail;
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import com.platform.util.json.TimeFormatSerializer; 
import com.platform.annotation.ApiAnno; 
import com.platform.annotation.BeanAnno; 
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.dto.oiq.OiqOrderLinkDTO;
import com.sgs.ecom.member.dto.user.UserAddressDTO;
import com.sgs.ecom.member.dto.user.UserInvoiceDTO;
import com.sgs.ecom.member.enumtool.user.UserInvoiceEnum;
import org.apache.commons.lang.StringUtils;

public class OrderInvoiceDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select TAX_NO,REGISTER_PHONE,BANK_NUMBER,DELIVERY_NAME,STATE_DATE,USER_ID,DELIVERY_TOWN,STATE,ORDER_NO,DELIVERY_PROVINCE,DETAIL_NO,INVOICE_ID,REGISTER_ADDR,CREATE_DATE,INVOICE_TYPE,DELIVERY_ADDR,DELIVERY_CITY,ID,INVOICE_TITLE,BANK_ADDR,DELIVERY_PHONE from ORDER_INVOICE";


 	@ApiAnno(groups={Default.class,BaseOrderFilter.OrderToOther.class, BaseOrderFilter.WordForm.class})
 	@BeanAnno(value="TAX_NO", getName="getTaxNo", setName="setTaxNo")
 	private String taxNo;
	@ApiAnno(groups={Default.class})
	 private String bossNo;
 	@ApiAnno(groups={Default.class,BaseOrderFilter.OrderToOther.class, BaseOrderFilter.WordForm.class})
 	@BeanAnno(value="BANK_NUMBER", getName="getBankNumber", setName="setBankNumber")
 	private String bankNumber;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private Timestamp stateDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="USER_ID", getName="getUserId", setName="setUserId")
 	private long userId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="ORDER_NO", getName="getOrderNo", setName="setOrderNo")
 	private String orderNo;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="DETAIL_NO", getName="getDetailNo", setName="setDetailNo")
 	private String detailNo;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="INVOICE_ID", getName="getInvoiceId", setName="setInvoiceId")
 	private Long invoiceId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(groups={Default.class,BaseOrderFilter.OrderToOther.class, BaseOrderFilter.WordForm.class})
 	@BeanAnno(value="INVOICE_TYPE", getName="getInvoiceType", setName="setInvoiceType")
 	private int invoiceType;

 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="ID", getName="getId", setName="setId")
 	private long id;
 	@ApiAnno(groups={Default.class,BaseOrderFilter.OrderToOther.class, BaseOrderFilter.WordForm.class})
 	@BeanAnno(value="INVOICE_TITLE", getName="getInvoiceTitle", setName="setInvoiceTitle")
 	private String invoiceTitle;


	@ApiAnno(groups={Default.class})
	private String backImg;
	@ApiAnno(groups={Default.class})
	private String frontImg;




	@ApiAnno(groups={Default.class, BaseOrderFilter.WordForm.class})
	private String bankName;
	@ApiAnno(groups={Default.class, BaseOrderFilter.WordForm.class})
	private String regPhone;
	@ApiAnno(groups={Default.class, BaseOrderFilter.WordForm.class})
	private String regAddress;

	@ApiAnno(groups={Default.class})
	private String province;
	@ApiAnno(groups={Default.class})
	private String town;
	@ApiAnno(groups={Default.class})
	private String city;
	@ApiAnno(groups={Default.class})
	private String companyAddress;
	@ApiAnno(groups={Default.class})
	private String userName;
	@ApiAnno(groups={Default.class})
	private String userPhone;
	@ApiAnno(groups={Default.class})
	private Long addressId;
	@ApiAnno(groups={Default.class})
	private String deliverCompany;
	@ApiAnno(groups={Default.class})
	private String deliverMail;
	@ApiAnno(groups={Default.class})
	private String linkPhone;
	@ApiAnno(groups={Default.class})
	private String linkPerson;
	@ApiAnno(groups={Default.class})
	private String linkEmail;
	@ApiAnno(groups={Default.class})
	private String bossTag;
	@ApiAnno(groups={Default.class})
	private int bossFlg;
	private String monthCompanyName;
	private String monthCompanyNameEn;
	private String monthAddress;
	private String monthAddressEn;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OrderToOther.class, BaseOrderFilter.WordForm.class})
	private String bankAddr;

	@ApiAnno(groups={Default.class,BaseOrderFilter.OrderToOther.class,BaseOrderFilter.WordQuotation.class, BaseOrderFilter.WordForm.class})
	private String payerName;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OrderToOther.class,BaseOrderFilter.WordQuotation.class, BaseOrderFilter.WordForm.class})
	private String payerPhone;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OrderToOther.class,BaseOrderFilter.WordQuotation.class, BaseOrderFilter.WordForm.class})
	private String payerEmail;




	private int isForeign;
	@ApiAnno(groups={Default.class})
	private String country;
	@ApiAnno(groups={Default.class,BaseOrderFilter.WordForm.class})
	private String foreignCity;
	@ApiAnno(groups={Default.class})
	private String postCode;
	@ApiAnno(groups={Default.class})
	private String contact;

	@ApiAnno(groups={Default.class})
	private String invoiceTitleForeign;
	@ApiAnno(groups={Default.class,BaseOrderFilter.WordForm.class})
	private String countryForeign;

	@ApiAnno(groups={Default.class,BaseOrderFilter.OrderToOther.class,BaseOrderFilter.WordQuotation.class,BaseOrderFilter.WordForm.class})
	private String registerAddr;

	@ApiAnno(groups={Default.class})
	private String registerAddrForeign;

	@ApiAnno(groups={Default.class,BaseOrderFilter.OrderToOther.class,BaseOrderFilter.WordForm.class})
	private String registerPhone;

	@ApiAnno(groups={Default.class,BaseOrderFilter.WordForm.class})
	private String postCodeForeign;

	@ApiAnno(groups={Default.class,BaseOrderFilter.WordForm.class})
	private String contactForeign;

	@ApiAnno(groups={Default.class})
	private String registerPhoneForeign;
	@ApiAnno(groups={BaseOrderFilter.WordForm.class})
	private String invoiceEmail;

	@ApiAnno(groups={Default.class})
	private String taxNoForeign;

	public String getTaxNoForeign() {
		return taxNoForeign;
	}

	public void setTaxNoForeign(String taxNoForeign) {
		this.taxNoForeign = taxNoForeign;
	}

	public String getInvoiceTitleForeign() {
		return invoiceTitleForeign;
	}

	public void setInvoiceTitleForeign(String invoiceTitleForeign) {
		this.invoiceTitleForeign = invoiceTitleForeign;
	}

	public String getCountryForeign() {
		return countryForeign;
	}

	public void setCountryForeign(String countryForeign) {
		this.countryForeign = countryForeign;
	}

	public String getRegisterAddr() {
		return registerAddr;
	}

	public void setRegisterAddr(String registerAddr) {
		this.registerAddr = registerAddr;
	}

	public String getRegisterAddrForeign() {
		return registerAddrForeign;
	}

	public void setRegisterAddrForeign(String registerAddrForeign) {
		this.registerAddrForeign = registerAddrForeign;
	}

	public String getRegisterPhone() {
		return registerPhone;
	}

	public void setRegisterPhone(String registerPhone) {
		this.registerPhone = registerPhone;
	}

	public String getPostCodeForeign() {
		return postCodeForeign;
	}

	public void setPostCodeForeign(String postCodeForeign) {
		this.postCodeForeign = postCodeForeign;
	}

	public String getContactForeign() {
		return contactForeign;
	}

	public void setContactForeign(String contactForeign) {
		this.contactForeign = contactForeign;
	}

	public String getRegisterPhoneForeign() {
		return registerPhoneForeign;
	}

	public void setRegisterPhoneForeign(String registerPhoneForeign) {
		this.registerPhoneForeign = registerPhoneForeign;
	}

	public String getContact() {
		return contact;
	}

	public void setContact(String contact) {
		this.contact = contact;
	}

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	public String getForeignCity() {
		return foreignCity;
	}

	public void setForeignCity(String foreignCity) {
		this.foreignCity = foreignCity;
	}

	public String getPostCode() {
		return postCode;
	}

	public void setPostCode(String postCode) {
		this.postCode = postCode;
	}

	public String getBankAddr() {
		return bankAddr;
	}
	public void setBankAddr(String bankAddr) {
		this.bankAddr = bankAddr;
	}
	public void setTaxNo(String taxNo){
 		 this.taxNo=taxNo;
 	}
 	public String getTaxNo(){
 		 return this.taxNo;
 	}
 
 	 

 	 
 	public void setBankNumber(String bankNumber){
 		 this.bankNumber=bankNumber;
 	}
 	public String getBankNumber(){
 		 return this.bankNumber;
 	}
 
 	 

 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setUserId(long userId){
 		 this.userId=userId;
 	}
 	public long getUserId(){
 		 return this.userId;
 	}
 
 	 

 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setOrderNo(String orderNo){
 		 this.orderNo=orderNo;
 	}
 	public String getOrderNo(){
 		 return this.orderNo;
 	}
 
 	 

 	 
 	public void setDetailNo(String detailNo){
 		 this.detailNo=detailNo;
 	}
 	public String getDetailNo(){
 		 return this.detailNo;
 	}
 
 	 
 	public void setInvoiceId(Long invoiceId){
 		 this.invoiceId=invoiceId;
 	}
 	public Long getInvoiceId(){
 		 return this.invoiceId;
 	}
 
 	 

 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setInvoiceType(int invoiceType){
 		 this.invoiceType=invoiceType;
 	}
 	public int getInvoiceType(){
 		 return this.invoiceType;
 	}
 
 	 

 
 	 

 
 	 
 	public void setId(long id){
 		 this.id=id;
 	}
 	public long getId(){
 		 return this.id;
 	}
 
 	 
 	public void setInvoiceTitle(String invoiceTitle){
 		 this.invoiceTitle=invoiceTitle;
 	}
 	public String getInvoiceTitle(){
 		 return this.invoiceTitle;
 	}
 
 	 

	public String getBackImg() {
		return backImg;
	}

	public void setBackImg(String backImg) {
		this.backImg = backImg;
	}

	public String getFrontImg() {
		return frontImg;
	}

	public void setFrontImg(String frontImg) {
		this.frontImg = frontImg;
	}


	public String getBankName() {
		return bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}

	public String getRegPhone() {
		return regPhone;
	}

	public void setRegPhone(String regPhone) {
		this.regPhone = regPhone;
	}

	public String getRegAddress() {
		return regAddress;
	}

	public void setRegAddress(String regAddress) {
		this.regAddress = regAddress;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getTown() {
		return town;
	}

	public void setTown(String town) {
		this.town = town;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getCompanyAddress() {
		return companyAddress;
	}

	public void setCompanyAddress(String companyAddress) {
		this.companyAddress = companyAddress;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getUserPhone() {
		return userPhone;
	}

	public void setUserPhone(String userPhone) {
		this.userPhone = userPhone;
	}

	public Long getAddressId() {
		return addressId;
	}

	public void setAddressId(Long addressId) {
		this.addressId = addressId;
	}

	public String getDeliverCompany() {
		return deliverCompany;
	}

	public void setDeliverCompany(String deliverCompany) {
		this.deliverCompany = deliverCompany;
	}

	public String getDeliverMail() {
		return deliverMail;
	}

	public void setDeliverMail(String deliverMail) {
		this.deliverMail = deliverMail;
	}

	public String getLinkPhone() {
		return linkPhone;
	}

	public void setLinkPhone(String linkPhone) {
		this.linkPhone = linkPhone;
	}

	public String getLinkPerson() {
		return linkPerson;
	}

	public void setLinkPerson(String linkPerson) {
		this.linkPerson = linkPerson;
	}

	public String getLinkEmail() {
		return linkEmail;
	}

	public void setLinkEmail(String linkEmail) {
		this.linkEmail = linkEmail;
	}

	public String getBossTag() {
		return bossTag;
	}

	public void setBossTag(String bossTag) {
		this.bossTag = bossTag;
	}

	public int getBossFlg() {
		return StringUtils.isBlank(bossTag)?0:1;
	}

	public void setBossFlg(int bossFlg) {
		this.bossFlg = bossFlg;
	}

	public String getMonthCompanyName() {
		return monthCompanyName;
	}

	public void setMonthCompanyName(String monthCompanyName) {
		this.monthCompanyName = monthCompanyName;
	}

	public String getMonthCompanyNameEn() {
		return monthCompanyNameEn;
	}

	public void setMonthCompanyNameEn(String monthCompanyNameEn) {
		this.monthCompanyNameEn = monthCompanyNameEn;
	}

	public String getMonthAddress() {
		return monthAddress;
	}

	public void setMonthAddress(String monthAddress) {
		this.monthAddress = monthAddress;
	}

	public String getMonthAddressEn() {
		return monthAddressEn;
	}

	public void setMonthAddressEn(String monthAddressEn) {
		this.monthAddressEn = monthAddressEn;
	}

	public int getIsForeign() {
		return isForeign;
	}

	public void setIsForeign(int isForeign) {
		this.isForeign = isForeign;
	}

	public String getInvoiceTypeShow() {
		return UserInvoiceEnum.getNameCh(invoiceType);
	}

	public String getBossNo() {
		return bossNo;
	}

	public void setBossNo(String bossNo) {
		this.bossNo = bossNo;
	}





	public String getPayerName() {
		return payerName;
	}

	public void setPayerName(String payerName) {
		this.payerName = payerName;
	}

	public String getPayerPhone() {
		return payerPhone;
	}

	public void setPayerPhone(String payerPhone) {
		this.payerPhone = payerPhone;
	}

	public String getPayerEmail() {
		return payerEmail;
	}

	public void setPayerEmail(String payerEmail) {
		this.payerEmail = payerEmail;
	}

	public String getInvoiceEmail() {
		return invoiceEmail;
	}

	public void setInvoiceEmail(String invoiceEmail) {
		this.invoiceEmail = invoiceEmail;
	}
}