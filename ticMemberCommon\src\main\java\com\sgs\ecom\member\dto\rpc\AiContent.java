package com.sgs.ecom.member.dto.rpc;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * AiContent
 *
 * @version: 1.0
 * @author: huangting.lu
 * @throws Exception
 * @date: 2025/5/16
 * <p>
 * Modification History: Date         Author          Version            Description
 * ---------------------------------------------------------* 修改时间     修改人           版本
 * 修改原因 2025/5/16      huangting.lu         v1.0               新增
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AiContent {

    /**
     * 提示词类型
     */
    private String type;

    /**
     * 提示词内容
     */
    private String text;

    /**
     * 提示词图片地址
     */
    private AiImgUrl image_url;

}
