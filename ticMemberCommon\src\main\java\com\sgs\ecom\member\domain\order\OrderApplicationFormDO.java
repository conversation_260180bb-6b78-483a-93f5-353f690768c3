package com.sgs.ecom.member.domain.order;

import com.sgs.base.BaseCopyObj;
import com.sgs.ecom.member.dto.detail.OrderApplicationFormDTO;
import com.sgs.ecom.member.dto.oiq.OiqOrderReqDTO;
import com.sgs.ecom.member.entity.order.OrderApplicationForm;
import com.sgs.ecom.member.request.oiq.OiqApplicationReq;

public class OrderApplicationFormDO extends OrderApplicationForm {


    private BaseCopyObj baseCopy = new BaseCopyObj();



    public OrderApplicationForm getFormByReq(OiqApplicationReq oiqApplicationReq, OiqOrderReqDTO oiqOrderReqDTO){

        OrderApplicationForm orderApplicationForm = new OrderApplicationForm();
        baseCopy.copyWithNull(orderApplicationForm,oiqApplicationReq);
        orderApplicationForm.setOrderNo(oiqOrderReqDTO.getOrderNo());
        orderApplicationForm.setState(1);
        orderApplicationForm.setCreateDate(oiqOrderReqDTO.getDateStr());
        orderApplicationForm.setStateDate(oiqOrderReqDTO.getDateStr());
        return orderApplicationForm;
    }


    public void entityToDTO(OrderApplicationFormDTO orderApplicationFormDTO,  OrderApplicationForm orderApplicationForm){
        baseCopy.copyWithNull(orderApplicationFormDTO,orderApplicationForm);
    }





}
