package com.sgs.ecom.member.controller.user;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.ecom.member.request.base.BasePage;
import com.sgs.ecom.member.request.order.OrderToDoReq;
import com.sgs.ecom.member.service.user.interfaces.IUserCenterService;
import com.sgs.ecom.member.util.BusinessException;
import com.sgs.ecom.member.util.ControllerUtil;
import com.sgs.ecom.member.util.ResultBody;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/business/api.v1.user/center")
@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
public class UserCenterController extends ControllerUtil {

	@Resource
	private IUserCenterService userCenterService;

	

	@HystrixCommand
    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "qryStateNum", method = { RequestMethod.POST })
    public ResultBody qryStateNum(@RequestHeader(value="accessToken") String token,
								  @RequestHeader(value = "bu",required = false) String bu) throws Exception {
		return ResultBody.newInstance(userCenterService.qryStateNum(new OrderToDoReq(bu,getUserDTO(token))));
	}

	@HystrixCommand
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "qryOrderToDo", method = { RequestMethod.POST })
	public ResultBody qryOrderToDo(@RequestHeader(value="accessToken") String token,
								  @RequestHeader(value = "bu",required = false) String bu,
								  @RequestBody BasePage basePage) throws Exception {
		return ResultBody.newInstance(userCenterService.qryOrderToDo((new OrderToDoReq(bu,basePage,getUserDTO(token)))));
	}



}
