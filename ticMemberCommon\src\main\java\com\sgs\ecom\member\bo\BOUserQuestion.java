package com.sgs.ecom.member.bo; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import com.platform.annotation.CharacterVaild; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import com.platform.annotation.BeanAnno; 

public class BOUserQuestion{
 
 	public static final String SEQUENCE = "REPLY_ID"; 
  
 	public static final String BO_SQL = "TB_USER_QUESTION"; 
 
 	public static final String OWNER ="member";

 	public static final String CLOUD_ID="cloudId";
 	public static final String QUESTION_NAME_EN="questionNameEn";
 	public static final String STATE_DATE="stateDate";
 	public static final String STATE="state";
 	public static final String USER_ID="userId";
 	public static final String ORDER_NO="orderNo";
 	public static final String FINAL_SCORE="finalScore";
 	public static final String QUESTION_NAME="questionName";
 	public static final String CREATE_DATE="createDate";
 	public static final String LANGUAGE_FLAG="languageFlag";
 	public static final String CURRENT_PART="currentPart";
 	public static final String QUESTION_ID="questionId";
 	public static final String REPLY_ID="replyId";
 	public static final String REAL_SCORE="realScore";
 	public static final String ATTACHMENT_NAME="attachmentName";
 	public static final String FILE_KEY="fileKey";

 	@BeanAnno("CLOUD_ID")
 	private String cloudId;
 	@BeanAnno("QUESTION_NAME_EN")
 	private String questionNameEn;
 	@BeanAnno("STATE_DATE")
 	private Timestamp stateDate;
 	@BeanAnno("STATE")
 	private int state;
 	@BeanAnno("USER_ID")
 	private long userId;
 	@BeanAnno("ORDER_NO")
 	private String orderNo;
 	@BeanAnno("FINAL_SCORE")
 	private long finalScore;
 	@BeanAnno("QUESTION_NAME")
 	private String questionName;
 	@BeanAnno("CREATE_DATE")
 	private Timestamp createDate;
 	@BeanAnno("LANGUAGE_FLAG")
 	private String languageFlag;
 	@BeanAnno("CURRENT_PART")
 	private String currentPart;
 	@BeanAnno("QUESTION_ID")
 	private long questionId;
 	@BeanAnno("REPLY_ID")
 	private long replyId;
 	@BeanAnno("REAL_SCORE")
 	private long realScore;
 	@BeanAnno("ATTACHMENT_NAME")
 	private String attachmentName;
 	@BeanAnno("FILE_KEY")
 	private String fileKey;

 	@CharacterVaild(len = 200) 
 	public void setCloudId(String cloudId){
 		 this.cloudId=cloudId;
 	}
 	public String getCloudId(){
 		 return this.cloudId;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setQuestionNameEn(String questionNameEn){
 		 this.questionNameEn=questionNameEn;
 	}
 	public String getQuestionNameEn(){
 		 return this.questionNameEn;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setUserId(long userId){
 		 this.userId=userId;
 	}
 	public long getUserId(){
 		 return this.userId;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setOrderNo(String orderNo){
 		 this.orderNo=orderNo;
 	}
 	public String getOrderNo(){
 		 return this.orderNo;
 	}
 
 	 
 	public void setFinalScore(long finalScore){
 		 this.finalScore=finalScore;
 	}
 	public long getFinalScore(){
 		 return this.finalScore;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setQuestionName(String questionName){
 		 this.questionName=questionName;
 	}
 	public String getQuestionName(){
 		 return this.questionName;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setLanguageFlag(String languageFlag){
 		 this.languageFlag=languageFlag;
 	}
 	public String getLanguageFlag(){
 		 return this.languageFlag;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setCurrentPart(String currentPart){
 		 this.currentPart=currentPart;
 	}
 	public String getCurrentPart(){
 		 return this.currentPart;
 	}
 
 	 
 	public void setQuestionId(long questionId){
 		 this.questionId=questionId;
 	}
 	public long getQuestionId(){
 		 return this.questionId;
 	}
 
 	 
 	public void setReplyId(long replyId){
 		 this.replyId=replyId;
 	}
 	public long getReplyId(){
 		 return this.replyId;
 	}
 
 	 
 	public void setRealScore(long realScore){
 		 this.realScore=realScore;
 	}
 	public long getRealScore(){
 		 return this.realScore;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setAttachmentName(String attachmentName){
 		 this.attachmentName=attachmentName;
 	}
 	public String getAttachmentName(){
 		 return this.attachmentName;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setFileKey(String fileKey){
 		 this.fileKey=fileKey;
 	}
 	public String getFileKey(){
 		 return this.fileKey;
 	}
 
 	 
}