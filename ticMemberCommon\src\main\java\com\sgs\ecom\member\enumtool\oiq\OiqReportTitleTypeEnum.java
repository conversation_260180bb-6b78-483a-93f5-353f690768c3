package com.sgs.ecom.member.enumtool.oiq;

public enum OiqReportTitleTypeEnum {
    ONE("申请方", 0),
    MORE("付款方", 1),
    OTHER("其他", 2),

    ;

    OiqReportTitleTypeEnum(String name, int index) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (OiqReportTitleTypeEnum c : OiqReportTitleTypeEnum.values()) {
            if (c.getIndex()==index) {
                return c.getName();
            }
        }
        return "";
    }



    private String name;
    private int index;



    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }



    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }
}
