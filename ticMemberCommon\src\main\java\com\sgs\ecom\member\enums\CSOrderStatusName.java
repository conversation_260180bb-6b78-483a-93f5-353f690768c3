package com.sgs.ecom.member.enums;

public enum CSOrderStatusName {

	NEW("包裹处理中", "NEW"), TRANSFER("包裹处理中", "TRANSFER"), PENDING("包裹暂缓处理", "PENDING");
	
    // 成员变量  
    private String name;  
    private String index;  
    // 构造方法  
    private CSOrderStatusName(String name, String index) {  
        this.name = name;  
        this.index = index;  
    }  

    // 普通方法  
    public static String getName(String index) {  
        for (CSOrderStatusName c : CSOrderStatusName.values()) {  
            if (c.getIndex().equals(index)) {  
                return c.name;  
            }  
        }  
        return null;  
    }  

    // get set 方法  
    public String getName() {  
        return name;  
    }  

    public String getIndex() {  
        return index;  
    }  
}
