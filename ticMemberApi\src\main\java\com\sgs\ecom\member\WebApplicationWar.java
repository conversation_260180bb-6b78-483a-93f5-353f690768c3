package com.sgs.ecom.member;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.ComponentScan;

@ServletComponentScan(basePackages = {"com.platform"})
@MapperScan("com.sgs.ecom.member.dao")
@ComponentScan(basePackages = {"com.sgs.ecom,com.platform"})
public class WebApplicationWar extends SpringBootServletInitializer  {
 
    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(WebApplicationWar.class);
    }
 
    public static void main(String[] args) throws Exception {
        SpringApplication.run(WebApplicationWar.class, args);
    }
    
}
