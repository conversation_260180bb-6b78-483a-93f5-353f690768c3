package com.sgs.ecom.member.order.controller;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.platform.annotation.AuthRequired;
import com.platform.util.CollectionService;
import com.platform.web.BaseAction;
import com.sgs.ecom.member.acct.service.interfaces.IAcctBalanceSV;
import com.sgs.ecom.member.util.ResultBody;

@RestController
@RequestMapping("/business/api.v2.acct/balance")
public class AcctBalanceController extends BaseAction {

//	@Autowired
//	private IAcctBalanceSV acctBalanceSV;
	private static IAcctBalanceSV acctBalanceSV = CollectionService.getService(IAcctBalanceSV.class);
	
    /**   
	* @Function: addBalanceByCust
	* @Description: 月结客户使用额度增加
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2020-1-7
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2020-1-7  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "NULL", sign = true)
    @RequestMapping(value = "addByCust", method = { RequestMethod.POST })
    public ResultBody addBalanceByCust(@RequestBody String data) throws Exception {
		acctBalanceSV.addBalanceByCust(data);
		return ResultBody.success();
	}
    
    /**   
	* @Function: subtractBalanceByCust
	* @Description: 月结客户使用额度减少
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2020-1-7
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2020-1-7  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "NULL", sign = true)
    @RequestMapping(value = "subtractByCust", method = { RequestMethod.POST })
    public ResultBody subtractBalanceByCust(@RequestBody String data) throws Exception {
    	acctBalanceSV.addBalanceByCust(data);
		return ResultBody.success();
	}
    
    /**   
	* @Function: qryAcctBalanceByType
	* @Description: 查询会员资金
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2018-07-16
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2018-07-16  shenyi    v1.0                 新增
	*/

    @AuthRequired(login = "SSO", sign = true)
    @RequestMapping(value = "qryBalance", method = { RequestMethod.POST })
    public ResultBody qryAcctBalanceByType(@RequestBody String data) throws Exception {
		return ResultBody.newInstance(acctBalanceSV.qryAcctBalanceByType(data));
	}
    
    /**   
	* @Function: addBalanceByCust
	* @Description: 月结客户使用额度增加
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2020-1-7
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2020-1-7  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "NULL", sign = false)
    @RequestMapping(value = "test", method = { RequestMethod.POST })
    public ResultBody test(@RequestBody String data) throws Exception {
		acctBalanceSV.qryTest(data);
		return ResultBody.success();
	}
}
