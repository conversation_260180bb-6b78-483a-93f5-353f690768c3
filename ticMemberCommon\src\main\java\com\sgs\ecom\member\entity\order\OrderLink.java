package com.sgs.ecom.member.entity.order;

import com.sgs.ecom.member.request.oiq.OiqOrderLinkReq;
import com.sgs.ecom.member.vo.order.OrderLinkVO;

import javax.persistence.Id;

public class OrderLink {

    public OrderLink() {
    }

    public OrderLink(OiqOrderLinkReq oiqOrderLinkDTO) {
        this.linkPhone = oiqOrderLinkDTO.getLinkPhone();
        this.linkEmail = oiqOrderLinkDTO.getLinkEmail();
        this.linkName = oiqOrderLinkDTO.getLinkName();
    }
    public OrderLink(OrderLinkVO orderLinkVO) {
        this.linkPhone = orderLinkVO.getLinkPhone();
        this.linkEmail = orderLinkVO.getLinkEmail();
        this.linkName = orderLinkVO.getLinkName();
    }

    @Id
    private Long linkId;

    private String orderNo;
    //1 申请表 2报告 3发票
    private int busiType;

    private String linkPhone;

    private String linkEmail;

    private String linkName;

    private String linkNameEn;

    private String department;

    private String position;

    private String createDate;

    public Long getLinkId() {
        return linkId;
    }

    public void setLinkId(Long linkId) {
        this.linkId = linkId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo == null ? null : orderNo.trim();
    }




    public String getLinkPhone() {
        return linkPhone;
    }

    public void setLinkPhone(String linkPhone) {
        this.linkPhone = linkPhone == null ? null : linkPhone.trim();
    }

    public String getLinkEmail() {
        return linkEmail;
    }

    public void setLinkEmail(String linkEmail) {
        this.linkEmail = linkEmail == null ? null : linkEmail.trim();
    }

    public String getLinkName() {
        return linkName;
    }

    public void setLinkName(String linkName) {
        this.linkName = linkName == null ? null : linkName.trim();
    }

    public String getLinkNameEn() {
        return linkNameEn;
    }

    public void setLinkNameEn(String linkNameEn) {
        this.linkNameEn = linkNameEn == null ? null : linkNameEn.trim();
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department == null ? null : department.trim();
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position == null ? null : position.trim();
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public int getBusiType() {
        return busiType;
    }

    public void setBusiType(int busiType) {
        this.busiType = busiType;
    }
}