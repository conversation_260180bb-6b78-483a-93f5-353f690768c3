package com.sgs.ecom.member.entity.order;

import javax.persistence.Id;
import java.math.BigDecimal;


public class OrderDetail {

	private BigDecimal originalPrice;

	private String testMemo;

	private String labNames;

	private Integer state;

	private String memoExplain;

	private String orderNo;

	private String detailNo;

	private String bu;

	private String sampleRequirements;

	private BigDecimal totalPrice;

	private String testName;


	private BigDecimal testDays;

	private String businessLine;

	private String standardCode;

	private String otherExplain;

	private String memo;

	private String stateDate;


	private BigDecimal disPrice;

	private String buName;

	private String createDate;

	private String quotaDate;

	private Long productId;

	private String groupNo;
	@Id
	private Long detailId;
	private String cnasLab;
	private String cmaLab;
	private Integer isOptional;//是否可选，1是0否
	private String labelName;//标签
	private Integer  isDefault;
	private Integer isDetermine;
	private String itemAlias;
	private Integer urgentType;
	private BigDecimal urgentAmount;


	private String categoryPath;

	private Long categoryId;


	private Long itemId;

	private String unit;

	private BigDecimal price;

	private Integer buyNums;

	private String itemName;



	private Integer itemType;

	private Long parentDetailId;
	private String extendName;//检测项目分组名称
	private String testLineId;

	public String getExtendName() {
		return extendName;
	}

	public void setExtendName(String extendName) {
		this.extendName = extendName;
	}

	public Long getProductId() {
		return productId;
	}

	public void setProductId(Long productId) {
		this.productId = productId;
	}

	public BigDecimal getOriginalPrice() {
		return originalPrice;
	}

	public void setOriginalPrice(BigDecimal originalPrice) {
		this.originalPrice = originalPrice;
	}

	public void setTestMemo(String testMemo){
		this.testMemo=testMemo;
	}
	public String getTestMemo(){
		return this.testMemo;
	}


	public void setLabNames(String labNames){
		this.labNames=labNames;
	}
	public String getLabNames(){
		return this.labNames;
	}


	public void setState(Integer state){
		this.state=state;
	}
	public Integer getState(){
		return this.state;
	}


	public void setMemoExplain(String memoExplain){
		this.memoExplain=memoExplain;
	}
	public String getMemoExplain(){
		return this.memoExplain;
	}


	public void setOrderNo(String orderNo){
		this.orderNo=orderNo;
	}
	public String getOrderNo(){
		return this.orderNo;
	}


	public void setItemName(String itemName){
		this.itemName=itemName;
	}
	public String getItemName(){
		return this.itemName;
	}


	public void setDetailNo(String detailNo){
		this.detailNo=detailNo;
	}
	public String getDetailNo(){
		return this.detailNo;
	}


	public void setBu(String bu){
		this.bu=bu;
	}
	public String getBu(){
		return this.bu;
	}


	public void setSampleRequirements(String sampleRequirements){
		this.sampleRequirements=sampleRequirements;
	}
	public String getSampleRequirements(){
		return this.sampleRequirements;
	}


	public BigDecimal getTotalPrice() {
		return totalPrice;
	}

	public void setTotalPrice(BigDecimal totalPrice) {
		this.totalPrice = totalPrice;
	}

	public void setTestName(String testName){
		this.testName=testName;
	}
	public String getTestName(){
		return this.testName;
	}


	public void setBuyNums(Integer buyNums){
		this.buyNums=buyNums;
	}
	public Integer getBuyNums(){
		return this.buyNums;
	}

	public BigDecimal getTestDays() {
		return testDays;
	}

	public void setTestDays(BigDecimal testDays) {
		this.testDays = testDays;
	}

	public void setBusinessLine(String businessLine){
		this.businessLine=businessLine;
	}
	public String getBusinessLine(){
		return this.businessLine;
	}


	public void setStandardCode(String standardCode){
		this.standardCode=standardCode;
	}
	public String getStandardCode(){
		return this.standardCode;
	}


	public void setOtherExplain(String otherExplain){
		this.otherExplain=otherExplain;
	}
	public String getOtherExplain(){
		return this.otherExplain;
	}


	public void setMemo(String memo){
		this.memo=memo;
	}
	public String getMemo(){
		return this.memo;
	}


	public void setUnit(String unit){
		this.unit=unit;
	}
	public String getUnit(){
		return this.unit;
	}


	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public void setStateDate(String stateDate){
		this.stateDate=stateDate;
	}
	public String getStateDate(){
		return this.stateDate;
	}


	public Long getItemId() {
		return itemId;
	}

	public void setItemId(Long itemId) {
		this.itemId = itemId;
	}

	public BigDecimal getDisPrice() {
		return disPrice;
	}

	public void setDisPrice(BigDecimal disPrice) {
		this.disPrice = disPrice;
	}

	public void setItemType(Integer itemType) {
		this.itemType = itemType;
	}

	public void setBuName(String buName){
		this.buName=buName;
	}
	public String getBuName(){
		return this.buName;
	}


	public void setCreateDate(String createDate){
		this.createDate=createDate;
	}
	public String getCreateDate(){
		return this.createDate;
	}


	public void setDetailId(Long detailId){
		this.detailId=detailId;
	}
	public Long getDetailId(){
		return this.detailId;
	}


	public void setQuotaDate(String quotaDate){
		this.quotaDate=quotaDate;
	}
	public String getQuotaDate(){
		return this.quotaDate;
	}

	public String getGroupNo() {
		return groupNo;
	}

	public void setGroupNo(String groupNo) {
		this.groupNo = groupNo;
	}

	public String getCnasLab() {
		return cnasLab;
	}

	public void setCnasLab(String cnasLab) {
		this.cnasLab = cnasLab;
	}

	public String getCmaLab() {
		return cmaLab;
	}

	public void setCmaLab(String cmaLab) {
		this.cmaLab = cmaLab;
	}



	public Long getParentDetailId() {
		return parentDetailId;
	}

	public void setParentDetailId(Long parentDetailId) {
		this.parentDetailId = parentDetailId;
	}



	public String getLabelName() {
		return labelName;
	}

	public void setLabelName(String labelName) {
		this.labelName = labelName;
	}


	public String getCategoryPath() {
		return categoryPath;
	}

	public void setCategoryPath(String categoryPath) {
		this.categoryPath = categoryPath;
	}

	public Long getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Long categoryId) {
		this.categoryId = categoryId;
	}

	public Integer getIsDetermine() {
		return isDetermine;
	}

	public void setIsDetermine(Integer isDetermine) {
		this.isDetermine = isDetermine;
	}

	public String getItemAlias() {
		return itemAlias;
	}

	public void setItemAlias(String itemAlias) {
		this.itemAlias = itemAlias;
	}

	public Integer getUrgentType() {
		return urgentType;
	}

	public void setUrgentType(Integer urgentType) {
		this.urgentType = urgentType;
	}

	public BigDecimal getUrgentAmount() {
		return urgentAmount;
	}

	public void setUrgentAmount(BigDecimal urgentAmount) {
		this.urgentAmount = urgentAmount;
	}

	public Integer getItemType() {
		return itemType;
	}

	public Integer getIsOptional() {
		return isOptional;
	}

	public void setIsOptional(Integer isOptional) {
		this.isOptional = isOptional;
	}

	public Integer getIsDefault() {
		return isDefault;
	}

	public void setIsDefault(Integer isDefault) {
		this.isDefault = isDefault;
	}

	public String getTestLineId() {
		return testLineId;
	}

	public void setTestLineId(String testLineId) {
		this.testLineId = testLineId;
	}
}