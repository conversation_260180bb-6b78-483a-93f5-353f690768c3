package com.sgs.ecom.member.config;
import com.google.common.collect.Maps;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
*
* @return
* <AUTHOR> || created at 2024/8/29 16:16 
* @throws Exception 抛出错误    
*/
@RefreshScope
@Component
@ConfigurationProperties(prefix = "order.oiq.application")
public class OiqApplicationProperties {
    /**
     * WEB SITE配置
     */

    private Map<String, Integer> sampleSize = Maps.newHashMap();

    private  Map<String, String> labRule = Maps.newHashMap();



    public Map<String, Integer> getSampleSize() {
        return sampleSize;
    }

    public void setSampleSize(Map<String, Integer> sampleSize) {
        this.sampleSize = sampleSize;
    }


    public Map<String, String> getLabRule() {
        return labRule;
    }

    public void setLabRule(Map<String, String> labRule) {
        this.labRule = labRule;
    }

    public int getSampleSizeByLine(String lineCode){
        return sampleSize.getOrDefault(lineCode,sampleSize.getOrDefault("DEFAULT",400));
    }

    public String getLabChoiceByLine(String lineCode){
        return  labRule.getOrDefault(lineCode,"businessAdvisor");
    }

}
