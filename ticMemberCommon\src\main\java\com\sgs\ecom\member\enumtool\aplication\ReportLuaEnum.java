package com.sgs.ecom.member.enumtool.aplication;

import org.apache.commons.lang.StringUtils;

public enum ReportLuaEnum {
    ZN("zn", "1", "中文报告","中文报告"),
    EN("en", "2", "英文报告","英文报告(加收英文报告费)"),
    ZNANDEN("znanden", "3", "中文+英文报告","中文+英文报告"),
    OTHER("other", "4", "其他","其他"),
    ZNEN("znen", "5", "中英文报告","中英文报告"),




    ;

    ReportLuaEnum(String name, String index, String nameCh,String minNameCh) {
        this.name = name;
        this.index = index;
        this.nameCh = nameCh;
        this.minNameCh = minNameCh;
    }

    public static String getName(String index) {
        for (ReportLuaEnum c : ReportLuaEnum.values()) {
            if (c.getIndex().equals(index)) {
                return c.getName();
            }
        }
        return "3";
    }

    public static String getNameChByName(String name) {
        if(StringUtils.isBlank(name)){
            return "";
        }

        for (ReportLuaEnum c : ReportLuaEnum.values()) {
            if (c.getName().equals(name.toLowerCase())) {
                return c.getNameCh();
            }
        }
        return "";
    }

    public static String getMinNameChByName(String name) {
        if(StringUtils.isBlank(name)){
            return "";
        }

        for (ReportLuaEnum c : ReportLuaEnum.values()) {
            if (c.getName().equals(name.toLowerCase())) {
                return c.getMinNameCh();
            }
        }
        return "";
    }


    public static String getNameByNameCh(String nameCh) {
        for (ReportLuaEnum c : ReportLuaEnum.values()) {
            if (c.getNameCh().equals(nameCh)) {
                return c.getName().toUpperCase();
            }
        }
        return "";
    }


    public static String getIndexByName(String name) {
        for (ReportLuaEnum c : ReportLuaEnum.values()) {
            if (c.getName().equals(name)) {
                return c.getIndex();
            }
        }
        return "4";
    }


    private String name;
    private String index;
    private String nameCh;
    private String minNameCh;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }

    public String getMinNameCh() {
        return minNameCh;
    }

    public void setMinNameCh(String minNameCh) {
        this.minNameCh = minNameCh;
    }
}
