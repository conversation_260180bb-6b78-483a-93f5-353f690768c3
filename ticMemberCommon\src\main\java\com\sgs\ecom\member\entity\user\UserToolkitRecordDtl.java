package com.sgs.ecom.member.entity.user;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

public class UserToolkitRecordDtl {
    private Long dtlId;

    private Long recordId;

    private String toolCode;

    private String attrName;

    private String attrCode;

    private String attrValue;

    private Timestamp createDate;

    private BigDecimal attrRate;

    public Long getDtlId() {
        return dtlId;
    }

    public void setDtlId(Long dtlId) {
        this.dtlId = dtlId;
    }

    public Long getRecordId() {
        return recordId;
    }

    public void setRecordId(Long recordId) {
        this.recordId = recordId;
    }

    public String getToolCode() {
        return toolCode;
    }

    public void setToolCode(String toolCode) {
        this.toolCode = toolCode == null ? null : toolCode.trim();
    }

    public String getAttrName() {
        return attrName;
    }

    public void setAttrName(String attrName) {
        this.attrName = attrName == null ? null : attrName.trim();
    }

    public String getAttrCode() {
        return attrCode;
    }

    public void setAttrCode(String attrCode) {
        this.attrCode = attrCode == null ? null : attrCode.trim();
    }

    public String getAttrValue() {
        return attrValue;
    }

    public void setAttrValue(String attrValue) {
        this.attrValue = attrValue == null ? null : attrValue.trim();
    }

    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public BigDecimal getAttrRate() {
        return attrRate;
    }

    public void setAttrRate(BigDecimal attrRate) {
        this.attrRate = attrRate;
    }
}