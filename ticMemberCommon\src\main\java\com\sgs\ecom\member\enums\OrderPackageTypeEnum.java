package com.sgs.ecom.member.enums;
/**
 * @Description :套餐类型分类
 * <AUTHOR>
 * @Date  2024/1/19
 **/
public enum OrderPackageTypeEnum {

	PACKAGE("套餐", "package"),
	OPTIONAL("自选", "optional"),
	CS_BESPOKE("客服定制", "csBespoke"),
    ;


    // 成员变量
    private String name;
    private String index;
    // 构造方法
    private OrderPackageTypeEnum(String name, String index) {
        this.name = name;  
        this.index = index;  
    }  

    // 普通方法  
    public static String getName(String index) {  
        for (OrderPackageTypeEnum c : OrderPackageTypeEnum.values()) {
            if (c.getIndex().equals(index)) {  
                return c.name;  
            }  
        }  
        return null;  
    }  
    // get set 方法  
    public String getName() {  
        return name;  
    }  

    public String getIndex() {  
        return index;  
    }   
}
