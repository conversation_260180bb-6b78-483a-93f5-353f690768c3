package com.sgs.ecom.member.dto.min;

import com.sgs.ecom.member.util.order.UseDateUtil;

import java.util.Date;
import java.util.List;

/**
* @params
* @return 5合1 推送邮件内容对象
* @description
* <AUTHOR> || created at 2023/5/9 11:44
*/
public class MinEmailDTO {
    //邮件类型：1：报价（RO发送给客户）2：发票（RO发送给客户）
    private String type;
    //线上系统单号
    private String order;
    //补差价订单号（平台方订单）
    private String platformOrder;
    //发件邮箱
    private String sender;
    //接收邮箱列表，以分号（;）分割
    private String receiver;
    //抄送邮箱列表，以分号（;）分割
    private String cc;
    //邮件标题
    private String subject;
    //邮件内容
    private String content;
    //附件列表
    private List<RSTSFileDTO> attachments;
    //发件时间，YYYY/MM/DD HH:MM:SS
    private String sendTime;
    //是否发送成功
    private Boolean ok;


    public MinEmailDTO() {
    }

    public MinEmailDTO(String order) {
        this.type = "1";
        this.order = order;
        this.platformOrder = "";
       // this.sender = sender;
       // this.receiver = receiver;
       // this.cc = cc;
       // this.subject = subject;
       // this.content = content;
       // this.attachemnts = attachemnts;
        this.sendTime = UseDateUtil.getRSTSDateString(new Date());
        this.ok = true;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }

    public String getPlatformOrder() {
        return platformOrder;
    }

    public void setPlatformOrder(String platformOrder) {
        this.platformOrder = platformOrder;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public String getReceiver() {
        return receiver;
    }

    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }

    public String getCc() {
        return cc;
    }

    public void setCc(String cc) {
        this.cc = cc;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<RSTSFileDTO> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<RSTSFileDTO> attachments) {
        this.attachments = attachments;
    }

    public String getSendTime() {
        return sendTime;
    }

    public void setSendTime(String sendTime) {
        this.sendTime = sendTime;
    }

    public Boolean getOk() {
        return ok;
    }

    public void setOk(Boolean ok) {
        this.ok = ok;
    }
}
