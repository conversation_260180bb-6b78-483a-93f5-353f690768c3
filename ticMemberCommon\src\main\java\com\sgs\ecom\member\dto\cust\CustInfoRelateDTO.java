package com.sgs.ecom.member.dto.cust;

public class CustInfoRelateDTO {

    private int num;

    private Long relateId;

    private Long custId;

    private Long userId;

    private String custCode;

    private String companyName;

    private int relateState;

    private int isDefault;

    private int state;

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public Long getRelateId() {
        return relateId;
    }

    public void setRelateId(Long relateId) {
        this.relateId = relateId;
    }

    public Long getCustId() {
        return custId;
    }

    public void setCustId(Long custId) {
        this.custId = custId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getCustCode() {
        return custCode;
    }

    public void setCustCode(String custCode) {
        this.custCode = custCode;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public int getRelateState() {
        return relateState;
    }

    public void setRelateState(int relateState) {
        this.relateState = relateState;
    }

    public int getIsDefault() {
        return num==1?1:0;
    }

    public void setIsDefault(int isDefault) {
        this.isDefault = isDefault;
    }

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }
}