<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd padding: 10px 0 0 0; margin: 0;">
<html xmlns="http://www.w3.org/1999/xhtml padding: 10px 0 0 0; margin: 0;">
<head>
  <meta charset="UTF-8"></meta>
  <meta name="viewport"
        content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"></meta>
  <meta http-equiv="X-UA-Compatible" content="ie=edge"></meta>
  <title>Document</title>
  <style>
    * {
      box-sizing: border-box;
      word-wrap: break-word;
    }
    p {
      margin: 0;
    }
    .clearfix:after {
      content: " ";
      display: block;
      height: 0;
      clear: both;
      visibility: hidden;
    }

    .clearfix {
      *+height: 1%;
    }
    .float-left {
      float: left;
    }
    .float-right {
      float: right;
    }
    table {
      border-top: 1px solid #333;
      border-right: 1px solid #333;
    }
    td {
      border-bottom: 1px solid #333;
      border-left: 1px solid #333;
    }
    .isEdit {
      background: yellow;
    }
  </style>
</head>
<body style="font-family: SimSun;">
<div style="margin-bottom: 20px;position: relative" class="clearfix">
  <img style="width: 20%;" src="${sqb2}" alt="" class="float-left"></img>
  <div style="width: 60%; position: absolute; left:200px;bottom: 20px;">
    <p style="text-align:center">
      <span style="font-size:16px;font-weight: bold">TESTING APPLICATION FORM</span>
    </p>
    <p style="text-align:center">
      <span style="font-size:16px;font-weight: bold">OIQ测试申请表</span>
    </p>
  </div>
  <div style="width: 20%;margin-top: 24px;" class="float-right">
    <p>
      <span style="font-size:10px">
        <#if indFormCode==0>
        <#if luaType==1>No.: SHTC-IND-P-7.1-01-F07</#if>
        <#if luaType==2>No.: SHTC-IND-P-7.1-01-F08</#if>
        <#if luaType==0>No.: SHTC-IND-P-7.1-01-F09</#if>
        </#if>
        <#if indFormCode==1>
        No.: OIQ-IND-P-7.1-01-F09
        </#if>
      </span>
    </p>
    <p>
      <span style="font-size:10px">Version.:A/01</span>
    </p>
    <p>
      <span style="font-size:10px">Issue Date: Feb 1st, 2021</span>
    </p>

  </div>
</div>


<table width="100%" style="width: 100%;" cellspacing="0">
  <col width="5%"/>
  <col width="9%"/>
  <col width="8%"/>
  <col width="10%"/>
  <col width="5%" />
  <col width="10%" />
  <col width="10%" />
  <col width="5%" />
  <col width="5%" />
  <col width="10%" />
  <col width="5%" />
  <col width="9%" />
  <col  width="9%"/>
  <tr style=";height:10px" class="firstRow">
    <td colspan="13" style="border: 1px solid #333; padding: 0px 7px;">
      <p style="margin-top:4px;margin-right:0;margin-bottom:   4px;margin-left:0;line-height:16px">
        <span style="font-size:12px;color:black;font-weight: bold">订单编号：</span>
        <span style="font-size:12px;color:#333">${(orderFormPDFDTO.orderNo)!''}</span>
      </p>
    </td>
  </tr>

  <#if luaType!=0>
    <tr>
      <td colspan="1" rowspan="3" style="border: 1px solid #333; padding: 0px 7px;text-align:center;border-right:none;border-top: none;">
        <p> <span style="font-size:12px;font-weight: bold">★</span></p>
        <p><span style="font-size:12px;font-weight: bold">申</span></p>
        <p><span style="font-size:12px;font-weight: bold">请</span></p>
        <p><span style="font-size:12px;font-weight: bold">方</span></p>
      </td>
      <td colspan="3" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;border-top: none;border-bottom: none;">


	   <#if luaType==1>
        <#if flgDTO.companyNameFlg==0>
         <span style="font-size:12px;">申请公司名称  </span>
        </#if>
         <#if flgDTO.companyNameFlg==1>
           <span style="font-size:12px;" class="isEdit">申请公司名称  </span>
         </#if>
       </#if>

        <#if luaType==2>
        <#if flgDTO.companyNameEnFlg==0>
          <span style="font-size:12px;">Company Name </span>
        </#if>
          <#if flgDTO.companyNameEnFlg==1>
            <span style="font-size:12px;" class="isEdit">Company Name </span>
          </#if>
        </#if>
      </td>
      <td colspan="9" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;border-bottom: none;border-top:none;border-left: none;">
	   <#if luaType==1>  <span style="font-size:12px;color:#333;"> ${(orderApplicationFormDTO.companyNameCn)!''}  </span></#if>
        <#if luaType==2><span style="font-size:12px;color:#333;">  ${(orderApplicationFormDTO.companyNameEn)!''}  </span></#if>
      </td>
    </tr>
    <tr>
      <td colspan="3" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;">

	  <#if luaType==1>
        <#if flgDTO.companyAddressCnFlg==0>
        <span style="font-size:12px;">申请公司地址</span>
        </#if>
        <#if flgDTO.companyAddressCnFlg==1>
          <span style="font-size:12px;" class="isEdit">申请公司地址</span>
        </#if>
      </#if>
      <#if luaType==2>
        <#if flgDTO.companyAddressEnFlg==0>
        <span style="font-size:12px;">Company Address</span>
        </#if>
        <#if flgDTO.companyAddressEnFlg==1>
          <span style="font-size:12px;" class="isEdit">Company Address</span>
        </#if>
      </#if>
      </td>
      <td colspan="9" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;border-left: none;">
      <span style="font-size:12px;color:#333;">
	  <#if luaType==1>${(orderApplicationFormDTO.companyAddressCn)!''}</#if>
        <#if luaType==2> ${(orderApplicationFormDTO.companyAddressEn)!''}</#if>
	  </span>
      </td>
    </tr>
    <tr>
      <td colspan="1" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;border-top: none;">
        <span style="font-size:12px;">联系人</span>
      </td>
      <td rowspan="1" valign="null" align="null" style="border: 1px solid #333; padding: 0px 7px;border-top: none;border-left:none;" colspan="2">
        <span style="font-size:12px;color:#333 ;">${(orderApplicationFormDTO.linkPerson)!''}</span>
      </td>
      <td colspan="2" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;border-right: none;border-top: none;border-left:none;">
        <span style="font-size:12px;">电话</span>
      </td>
      <td colspan="3" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;border-top: none;">
        <span style="font-size:12px;color:#333;">${(orderApplicationFormDTO.linkPhone)!''}</span>
      </td>
      <td colspan="1" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;border-left: none;border-top: none;">
        <span style="font-size:12px;">电子邮箱</span>
      </td>
      <td colspan="3" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;border-left: none;border-top: none;">
        <span style="font-size:12px;color:#333 ;">${(orderApplicationFormDTO.linkEmail)!''}</span>
      </td>
    </tr>

    <tr style=";height:23px">
      <td colspan="2"
          style="border-right: 1px solid #333; border-bottom: 1px solid #333; border-left: 1px solid #333; border-image: initial; border-top: none; padding: 0px 7px;">
        <p><span style="font-size:12px;">★</span>
          <span style="font-size:12px;font-weight: bold" >报告信息</span></p>
      </td>
      <td colspan="2"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:20px">
          <#if flgDTO.reportLuaFlg==0>
          <span style="font-size:12px;">报告语言</span>
          </#if>
          <#if flgDTO.reportLuaFlg==1>
            <span style="font-size:12px;" class="isEdit">报告语言</span>
          </#if>
        </p>
      </td>
      <td colspan="5"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:20px">
          <img src="${sqb1}" style="border: 1px solid #333;vertical-align: text-bottom;"></img>
          <span style="font-size:12px;">${orderReportDTO.reportLua}</span></p>
      </td>
      <td style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;"><p style="line-height:20px">
          <#if flgDTO.reportFormFlg==0>
            <span style="font-size:12px;">报告形式</span>
          </#if>
          <#if flgDTO.reportFormFlg==1>
            <span style="font-size:12px;" class="isEdit">报告形式</span>
          </#if>
        </p>
      </td>
      <td colspan="3"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:20px">
          <img src="${sqb1}" style="border: 1px solid #333;vertical-align: text-bottom;"></img>
          <span style="font-size:12px;">${orderReportDTO.reportForm}</span> </p>
      </td>
    </tr>


    <tr style=";height:23px">
      <td colspan="2"
          style="border-right: 1px solid #333; border-bottom: 1px solid #333; border-left: 1px solid #333; border-image: initial; border-top: none; padding: 0px 7px;">
        <p><span style="font-size:12px;">★</span><br></br>
          <#if flgDTO.reportMethodFlg==0>
            <span style="font-size:12px;font-weight: bold">报告出具方式</span>
          </#if>
          <#if flgDTO.reportMethodFlg==1>
            <span style="font-size:12px;font-weight: bold" class="isEdit">报告出具方式</span>
          </#if>
        </p>
      </td>

      <td colspan="11"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:20px">
          <span style="font-size:12px;">${(orderReportDTO.oiqReportMethodShow)!''}</span> </p>
      </td>
    </tr>

    <tr style=";page-break-inside:avoid;height:18px">
      <td rowspan="${n}"
          style="border-right: 1px solid #333; border-bottom: 1px solid #333; border-left: 1px solid #333; border-image: initial; border-top: none; padding: 0px 7px;text-align:center">
        <p style=""> <span style="font-size:12px;font-weight: bold">报</span></p>
        <p><span style="font-size:12px;font-weight: bold">告</span></p>
        <p><span style="font-size:12px;font-weight: bold">所</span></p>
        <p><span style="font-size:12px;font-weight: bold">需</span></p>
        <p><span style="font-size:12px;font-weight: bold">信</span></p>
        <p><span style="font-size:12px;font-weight: bold">息</span></p>
      </td>
      <td colspan="12"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <span style="font-size:12px;">注：以下信息，如出中文报告请用中文填写，如出英文报告请用英文填写。</span>
        </p>
      </td>
    </tr>
    <tr style=";page-break-inside:avoid;height:18px">
      <td colspan="2" rowspan="${nEn}"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <span style="font-size:12px;">★</span>
            <#if luaType==1>
              <#if flgDTO.reportCompanyNameCnFlg==0>
              <span style="font-size:12px;" >报告抬头:公司名称</span>
              </#if>
              <#if flgDTO.reportCompanyNameCnFlg==1>
                <span style="font-size:12px;"  class="isEdit">报告抬头:公司名称</span>
              </#if>
            </#if>
            <#if luaType==2>
             <#if flgDTO.reportCompanyNameEnFlg==0>
              <span style="font-size:12px;" >报告抬头:Company Name</span>
             </#if>
              <#if flgDTO.reportCompanyNameEnFlg==1>
                <span style="font-size:12px;"  class="isEdit">报告抬头:Company Name</span>
              </#if>
            </#if>

        </p>
      </td>

      <td colspan="10"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px ">
	  <span style="font-size:12px;color:#333;">
	<#if luaType==1>
      ${(orderReportDTO.reportCompanyNameCn)!''}
    </#if>
        <#if luaType==2> ${(orderReportDTO.reportCompanyNameEn)!''}</#if>
	  </span>
        </p>
      </td>
    </tr>
    <tr style=";page-break-inside:avoid;height:18px">
      <td colspan="2" rowspan="${nEn}" style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <span style="font-size:12px;">★</span><span style="font-size:12px;">
            <#if luaType==1>
              <#if flgDTO.reportAddressCnFlg==0>
                <span style="font-size:12px;" >报告地址:公司地址</span>
              </#if>
              <#if flgDTO.reportAddressCnFlg==1>
                <span style="font-size:12px;"  class="isEdit">报告地址:公司地址</span>
              </#if>
            </#if>
            <#if luaType==2>
              <#if flgDTO.reportAddressEnFlg==0>
                <span style="font-size:12px;" >报告地址:Company Address</span>
              </#if>
              <#if flgDTO.reportAddressEnFlg==1>
                <span style="font-size:12px;"  class="isEdit">报告地址:Company Address</span>
              </#if>
            </#if>
          </span>
        </p>
      </td>
      <td colspan="10"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
        <span style="font-size:12px;color:#333;">
		<#if luaType==1>
          ${(orderReportDTO.reportAddressCn)!''}
        </#if>
          <#if luaType==2> ${(orderReportDTO.reportAddressEn)!''}</#if>
		</span>
        </p>
      </td>
    </tr>
    <#if orderSampleDTOList?has_content>
      <#list orderSampleDTOList as orderSample>

        <#list orderSample.samplePDF as aa>
          <tr style=";page-break-inside:avoid;height:5px">
            <#list aa as moreSample>
              <td colspan="2"
                  style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
                <p style="line-height:16px">
                <#if moreSample.tableFlg==0>
                  <span style="font-size:12px;">
                 <#if moreSample.sortShow==-2><span style="font-size:12px;">★</span></#if>
                    ${(moreSample.sampleKeyName)!''}
                  </span>
                </#if>
                <#if moreSample.tableFlg==1>
                  <span style="font-size:12px;" class="isEdit">
                 <#if moreSample.sortShow==-2><span style="font-size:12px;">★</span></#if>
                    ${(moreSample.sampleKeyName)!''}
                  </span>
                </#if>
                </p>

              </td>
              <td colspan="4"
                  style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
                <p style="line-height:16px">
                  <span style="font-size:12px;color:#333;">${(moreSample.sampleValue)!''}</span>
                </p>
              </td>
            </#list>
          </tr>
        </#list>

      </#list>
    </#if>
  </#if>




  <#if luaType==0>
    <tr>
      <td colspan="1" rowspan="3" style="border: 1px solid #333; padding: 0px 7px;text-align:center;border-right:none;border-top: none;">
        <p> <span style="font-size:12px;font-weight: bold">★</span></p>
        <p><span style="font-size:12px;font-weight: bold">申</span></p>
        <p><span style="font-size:12px;font-weight: bold">请</span></p>
        <p><span style="font-size:12px;font-weight: bold">方</span></p>
      </td>
      <td colspan="2" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;border-top: none;border-bottom: none;">
        <#if flgDTO.companyNameFlg==0>
          <span style="font-size:12px;">申请公司名称  </span>
        </#if>
        <#if flgDTO.companyNameFlg==1>
          <span style="font-size:12px;" class="isEdit">申请公司名称  </span>
        </#if>
      </td>
      <td colspan="4" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;border-bottom: none;border-top:none;border-left: none;border-right: none;">
      <span style="font-size:12px;color:#333;">
	  ${(orderApplicationFormDTO.companyNameCn)!''}
	  </span>
      </td>
      <td colspan="2" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;border-top: none;border-bottom: none;">
        <#if flgDTO.companyNameEnFlg==0>
          <span style="font-size:12px;">Company Name </span>
        </#if>
        <#if flgDTO.companyNameEnFlg==1>
          <span style="font-size:12px;" class="isEdit">Company Name </span>
        </#if>
      </td>
      <td colspan="4" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;border-bottom: none;border-top:none;border-left: none;">
      <span style="font-size:12px;color:#333;">
	  ${(orderApplicationFormDTO.companyNameEn)!''}
	  </span>
      </td>
    </tr>
    <tr>
      <td colspan="2" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;">
        <#if flgDTO.companyAddressCnFlg==0>
          <span style="font-size:12px;">申请公司地址</span>
        </#if>
        <#if flgDTO.companyAddressCnFlg==1>
          <span style="font-size:12px;" class="isEdit">申请公司地址</span>
        </#if>
      </td>
      <td colspan="4" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;border-left: none;border-right: none;">
      <span style="font-size:12px;color:#333;">
        ${(orderApplicationFormDTO.companyAddressCn)!''}
	  </span>
      </td>
      <td colspan="2" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;">
        <#if flgDTO.companyAddressEnFlg==0>
          <span style="font-size:12px;">Company Address</span>
        </#if>
        <#if flgDTO.companyAddressEnFlg==1>
          <span style="font-size:12px;" class="isEdit">Company Address</span>
        </#if>
      </td>
      <td colspan="4" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;border-left: none;">
      <span style="font-size:12px;color:#333;">
	   ${(orderApplicationFormDTO.companyAddressEn)!''}
	  </span>
      </td>
    </tr>
    <tr>
      <td colspan="1" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;border-top: none;">
        <span style="font-size:12px;">联系人</span>
      </td>
      <td rowspan="1" valign="null" align="null" style="border: 1px solid #333; padding: 0px 7px;border-top: none;border-left:none;" colspan="2">
        <span style="font-size:12px;color:#333;">${(orderApplicationFormDTO.linkPerson)!''}</span>
      </td>
      <td colspan="2" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;border-right: none;border-top: none;border-left:none;">
        <span style="font-size:12px;">电话</span>
      </td>
      <td colspan="3" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;border-top: none;">
        <span style="font-size:12px;color:#333;">${(orderApplicationFormDTO.linkPhone)!''}</span>
      </td>
      <td colspan="1" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;border-left: none;border-top: none;">
        <span style="font-size:12px;">电子邮箱</span>
      </td>
      <td colspan="3" rowspan="1" style="border: 1px solid #333; padding: 0px 7px;border-left: none;border-top: none;">
        <span style="font-size:12px;color:#333;">${(orderApplicationFormDTO.linkEmail)!''}</span>
      </td>
    </tr>

    <tr style=";height:23px">
      <td colspan="2"
          style="border-right: 1px solid #333; border-bottom: 1px solid #333; border-left: 1px solid #333; border-image: initial; border-top: none; padding: 0px 7px;">
        <p><span style="font-size:12px;">★</span>
          <span style="font-size:12px;font-weight: bold">报告信息</span></p>
      </td>
      <td colspan="2"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:20px">
          <#if flgDTO.reportLuaFlg==0>
            <span style="font-size:12px;">报告语言</span>
          </#if>
          <#if flgDTO.reportLuaFlg==1>
            <span style="font-size:12px;" class="isEdit">报告语言</span>
          </#if>
        </p>
      </td>
      <td colspan="5"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:20px">
          <img src="${sqb1}" style="border: 1px solid #333;vertical-align: text-bottom;"></img>
          <span style="font-size:12px;">${orderFormPDFDTO.reportLua}</span></p>
      </td>
      <td style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;"><p style="line-height:20px">
          <#if flgDTO.reportFormFlg==0>
            <span style="font-size:12px;">报告形式</span>
          </#if>
          <#if flgDTO.reportFormFlg==1>
            <span style="font-size:12px;" class="isEdit">报告形式</span>
          </#if>
        </p>
      </td>
      <td colspan="3"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:20px">
          <img src="${sqb1}" style="border: 1px solid #333;vertical-align: text-bottom;"></img>
          <span style="font-size:12px;">${orderFormPDFDTO.reportForm}</span> </p>
      </td>
    </tr>

    <tr style=";height:23px">
      <td colspan="2"
          style="border-right: 1px solid #333; border-bottom: 1px solid #333; border-left: 1px solid #333; border-image: initial; border-top: none; padding: 0px 7px;">
        <p><span style="font-size:12px;">★</span><br></br>
          <#if flgDTO.reportMethodFlg==0>
            <span style="font-size:12px;font-weight: bold">报告出具方式</span>
          </#if>
          <#if flgDTO.reportMethodFlg==1>
            <span style="font-size:12px;font-weight: bold" class="isEdit">报告出具方式</span>
          </#if>
        </p>
      </td>

      <td colspan="11"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:20px">
          <span style="font-size:12px;">${(orderReportDTO.oiqReportMethodShow)!''}</span> </p>
      </td>
    </tr>

    <tr style=";page-break-inside:avoid;height:18px">
      <td rowspan="${n}"
          style="border-right: 1px solid #333; border-bottom: 1px solid #333; border-left: 1px solid #333; border-image: initial; border-top: none; padding: 0px 7px;text-align:center">
        <p style=""> <span style="font-size:12px;font-weight: bold">报</span></p>
        <p><span style="font-size:12px;font-weight: bold">告</span></p>
        <p><span style="font-size:12px;font-weight: bold">所</span></p>
        <p><span style="font-size:12px;font-weight: bold">需</span></p>
        <p><span style="font-size:12px;font-weight: bold">信</span></p>
        <p><span style="font-size:12px;font-weight: bold">息</span></p>
      </td>
      <td colspan="12"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <span style="font-size:12px;">注：以下信息，如出中文报告请用中文填写，如出英文报告请用英文填写。</span>
        </p>
      </td>
    </tr>
    <tr style=";page-break-inside:avoid;height:18px">
      <td colspan="2" rowspan="${nEn}"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <span style="font-size:12px;">★</span>
            <span style="font-size:12px;" >报告抬头</span>
        </p>
      </td>
      <td colspan="4"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <#if flgDTO.reportCompanyNameCnFlg==0>
            <span style="font-size:12px;" >公司名称(中文)</span>
          </#if>
          <#if flgDTO.reportCompanyNameCnFlg==1>
            <span style="font-size:12px;" class="isEdit">公司名称(中文)</span>
          </#if>
        </p>
      </td>
      <td colspan="6"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px ">
          <span style="font-size:12px;color:#333;">
            ${(orderReportDTO.reportCompanyNameCn)!''}</span>
        </p>
      </td>
    </tr>
    <tr style=";page-break-inside:avoid;height:19px">
      <td colspan="4"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <#if flgDTO.reportCompanyNameEnFlg==0>
            <span style="font-size:12px;" >Company Name(英文大写)</span>
          </#if>
          <#if flgDTO.reportCompanyNameEnFlg==1>
            <span style="font-size:12px;"  class="isEdit">Company Name(英文大写)</span>
          </#if>
        </p>
      </td>
      <td colspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <span style="font-size:12px;color:#333;">${(orderReportDTO.reportCompanyNameEn)!''}</span>
      </td>
    </tr>
    <tr style=";page-break-inside:avoid;height:18px">
      <td colspan="2" rowspan="${nEn}" style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <span style="font-size:12px;">★</span>
            <span style="font-size:12px;">报告地址</span>
        </p>
      </td>
      <td colspan="4"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <#if flgDTO.reportAddressCnFlg==0>
          <span style="font-size:12px;">公司地址</span><span style="font-size:12px">(</span><span
                  style="font-size:12px;">中文</span><span style="font-size:12px">)</span><span
                  style="font-size:12px;"></span>
          </#if>
          <#if flgDTO.reportAddressCnFlg==1>
            <span style="font-size:12px;" class="isEdit">公司地址</span><span style="font-size:12px"  class="isEdit">(</span><span
                  style="font-size:12px;"  class="isEdit">中文</span><span style="font-size:12px"  class="isEdit">)</span><span
                  style="font-size:12px;"></span>
          </#if>
        </p>

      </td>
      <td colspan="6"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <span style="font-size:12px;color:#333;">
           ${(orderReportDTO.reportAddressCn)!''}</span>
        </p>
      </td>
    </tr>
    <tr style=";page-break-inside:avoid;height:23px">
      <td colspan="4"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <#if flgDTO.reportAddressEnFlg==0>
          <span style="font-size:12px;">Address(英文大写)</span>
          </#if>
          <#if flgDTO.reportAddressEnFlg==1>
            <span style="font-size:12px;" class="isEdit">Address(英文大写)</span>
          </#if>
        </p>
      </td>
      <td colspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <span style="font-size:12px;color:#333;">${(orderReportDTO.reportAddressEn)!''}</span>
      </td>
    </tr>
    <#if orderSampleDTOList?has_content>
      <#list orderSampleDTOList as orderSample>

        <#list orderSample.samplePDF as aa>
          <tr style=";page-break-inside:avoid;height:5px">
            <#list aa as moreSample>
              <td colspan="2"
                  style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
                <p style="line-height:16px">

                 <#if moreSample.tableFlg==0>
                  <span style="font-size:12px;">
                 <#if moreSample.sortShow==-2><span style="font-size:12px;">★</span></#if>
                    ${(moreSample.sampleKeyName)!''}
                  </span>
                 </#if>
                  <#if moreSample.tableFlg==1>
                    <span style="font-size:12px;" class="isEdit">
                 <#if moreSample.sortShow==-2><span style="font-size:12px;">★</span></#if>
                      ${(moreSample.sampleKeyName)!''}
                  </span>
                  </#if>

                </p>
              </td>
              <td colspan="4"
                  style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
                <p style="line-height:16px">
                  <span style="font-size:12px;color:#333;">${(moreSample.sampleValue)!''}</span>
                </p>
              </td>
            </#list>
          </tr>
        </#list>

      </#list>
    </#if>
  </#if>







  <tr style=";page-break-inside:avoid;height:4px">
    <#if isForeign==0>
    <td rowspan="5"
        style="border-right: 1px solid #333; border-bottom: 1px solid #333; border-left: 1px solid #333; border-image: initial; border-top: none; text-align: center">
      </#if>
      <#if isForeign==1>
       <td rowspan="6"
        style="border-right: 1px solid #333; border-bottom: 1px solid #333; border-left: 1px solid #333; border-image: initial; border-top: none; text-align: center">
      </#if>
      <p style="text-align:center">
        <span style="font-size:12px;">★</span>
      </p>
      <p> <span style="font-size:12px;font-weight: bold">发&nbsp;付</span></p>
      <p> <span style="font-size:12px;font-weight: bold">票&nbsp;款</span></p>
      <p><span style="font-size:12px;font-weight: bold">信&nbsp;方</span></p>
      <p><span style="font-size:12px;font-weight: bold">息&nbsp;与</span></p>
      <p style="text-align:center"><br/></p>
    </td>

    <#if isForeign==0>
    <td colspan="2" valign="top"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="margin-top:1px;margin-right:0;margin-bottom:   1px;margin-left:0;line-height:16px">
        <#if flgDTO.oiqInvoiceFlgDTO.invoiceTitleFlg==0>
             <span style="font-size:12px;">
               <#if orderInvoiceDTO.invoiceType==2>发票抬头</#if>
               <#if orderInvoiceDTO.invoiceType!=2>公司名称</#if>
             </span>
        </#if>
        <#if flgDTO.oiqInvoiceFlgDTO.invoiceTitleFlg==1>
          <span style="font-size:12px;" class="isEdit">
                <#if orderInvoiceDTO.invoiceType==2>发票抬头</#if>
                <#if orderInvoiceDTO.invoiceType!=2>公司名称</#if>
          </span>
        </#if>
        <span style="font-size:12px">&nbsp;&nbsp; </span>
      </p>
    </td>
    <td colspan="6" valign="top"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px; position: relative;">
      <p style="line-height:16px">
        <span style="font-size:12px;color:#333;">
          ${(orderInvoiceDTO.invoiceTitle)!''}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          <b style="position: absolute; bottom: 2px; right: 1px;"><#if orderInvoiceDTO.bossFlg==1> <em>[${(orderInvoiceDTO.bossTag)!''}]</em> </#if>
          </b>
        </span>
      </p>
    </td>
    <td colspan="2" valign="top"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="margin-top:1px;margin-right:0;margin-bottom:   1px;margin-left:0;line-height:16px">
        <#if flgDTO.oiqInvoiceFlgDTO.bankNameFlg==0>
          <span style="font-size:12px;">开户行</span>
        </#if>
        <#if flgDTO.oiqInvoiceFlgDTO.bankNameFlg==1>
          <span style="font-size:12px;" class="isEdit">开户行</span>
        </#if>
      </p>
    </td>
    <td colspan="2" valign="top"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="margin-top:1px;margin-right:0;margin-bottom:   1px;margin-left:0;line-height:16px">
        <span style="font-size:12px;color:#333;">${(orderInvoiceDTO.bankName)!''}</span>
      </p>
    </td>
  </tr>
  <tr style=";page-break-inside:avoid;height:4px">
    <td colspan="2" valign="top"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="line-height:16px">
        <#if flgDTO.oiqInvoiceFlgDTO.regAddressFlg==0>
          <span style="font-size:12px;">公司地址</span>
        </#if>
        <#if flgDTO.oiqInvoiceFlgDTO.regAddressFlg==1>
          <span style="font-size:12px;" class="isEdit">公司地址</span>
        </#if>
      </p>
    </td>
    <td colspan="6" valign="top"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="line-height:16px">
        <span style="font-size:12px;color:#333;">${(orderInvoiceDTO.regAddress)!''}</span>
      </p>
    </td>
    <td colspan="2" valign="top"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="line-height:16px">
        <#if flgDTO.oiqInvoiceFlgDTO.regPhoneFlg==0>
          <span style="font-size:12px;">电话</span>
        </#if>
        <#if flgDTO.oiqInvoiceFlgDTO.regPhoneFlg==1>
          <span style="font-size:12px;" class="isEdit">电话</span>
        </#if>
      </p>
    </td>
    <td colspan="2" valign="top"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="line-height:16px">
        <span style="font-size:12px;color:#333;">${(orderInvoiceDTO.regPhone)!''}</span>
      </p>
    </td>
  </tr>
  <tr style=";page-break-inside:avoid;height:15px">
    <td colspan="2" valign="top"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="line-height:16px">
        <#if flgDTO.oiqInvoiceFlgDTO.taxNoFlg==0>
          <span style="font-size:12px;">税号</span>
        </#if>
        <#if flgDTO.oiqInvoiceFlgDTO.taxNoFlg==1>
          <span style="font-size:12px;" class="isEdit">税号</span>
        </#if>
      </p>
    </td>
    <td colspan="6" valign="top"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="line-height:16px">
        <span style="font-size:12px;color:#333;">${(orderInvoiceDTO.taxNo)!''} <#if  orderInvoiceDTO.isForeign ==1>(境外公司)</#if></span>
      </p>
    </td>
    <td colspan="2" valign="top"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="line-height:16px">
        <#if flgDTO.oiqInvoiceFlgDTO.bankNumberFlg==0>
          <span style="font-size:12px;">账号</span>
        </#if>
        <#if flgDTO.oiqInvoiceFlgDTO.bankNumberFlg==1>
          <span style="font-size:12px;" class="isEdit">账号</span>
        </#if>
      </p>
    </td>
    <td colspan="2" valign="top"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="line-height:16px">
        <span style="font-size:12px;color:#333;">${(orderInvoiceDTO.bankNumber)!''}</span>
      </p>
    </td>
  </tr>
  </#if>


  <#if isForeign==1>
    <td colspan="2" valign="top"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="margin-top:1px;margin-right:0;margin-bottom:   1px;margin-left:0;line-height:16px">
        <#if flgDTO.oiqInvoiceFlgDTO.invoiceTitleFlg==0>
          <span style="font-size:12px;">发票抬头</span>
        </#if>
        <#if flgDTO.oiqInvoiceFlgDTO.invoiceTitleFlg==1>
          <span style="font-size:12px;" class="isEdit">发票抬头</span>
        </#if>
        <span style="font-size:12px">&nbsp;&nbsp; </span>
      </p>
    </td>
    <td colspan="6" valign="top"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px; position: relative;">
      <p style="line-height:16px">
        <span style="font-size:12px;color:#333;">${(orderInvoiceDTO.invoiceTitle)!''}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          <b style="position: absolute; bottom: 2px; right: 1px;"><#if orderInvoiceDTO.bossFlg==1> <em>[${(orderInvoiceDTO.bossTag)!''}]</em> </#if></b>
        </span>
      </p>
    </td>
    <td colspan="2" valign="top"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="margin-top:1px;margin-right:0;margin-bottom:   1px;margin-left:0;line-height:16px">
        <#if flgDTO.oiqInvoiceFlgDTO.countryFlg==0>
          <span style="font-size:12px;">国家及地区</span>
        </#if>
        <#if flgDTO.oiqInvoiceFlgDTO.countryFlg==1>
          <span style="font-size:12px;" class="isEdit">国家及地区</span>
        </#if>
      </p>
    </td>
    <td colspan="2" valign="top"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="margin-top:1px;margin-right:0;margin-bottom:   1px;margin-left:0;line-height:16px">
        <span style="font-size:12px;color:#333;">${(orderInvoiceDTO.country)!''}</span>
      </p>
    </td>
    </tr>
    <tr style=";page-break-inside:avoid;height:4px">
      <td colspan="2" valign="top"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <#if flgDTO.oiqInvoiceFlgDTO.foreignCityFlg==0>
            <span style="font-size:12px;">城市</span>
          </#if>
          <#if flgDTO.oiqInvoiceFlgDTO.foreignCityFlg==1>
            <span style="font-size:12px;" class="isEdit">城市</span>
          </#if>
        </p>
      </td>
      <td colspan="6" valign="top"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <span style="font-size:12px;color:#333;">${(orderInvoiceDTO.foreignCity)!''}</span>
        </p>
      </td>
      <td colspan="2" valign="top"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <#if flgDTO.oiqInvoiceFlgDTO.postCodeFlg==0>
            <span style="font-size:12px;">邮编</span>
          </#if>
          <#if flgDTO.oiqInvoiceFlgDTO.postCodeFlg==1>
            <span style="font-size:12px;" class="isEdit">邮编</span>
          </#if>
        </p>
      </td>
      <td colspan="2" valign="top"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <span style="font-size:12px;color:#333;">${(orderInvoiceDTO.postCode)!''}</span>
        </p>
      </td>
    </tr>



    <tr style=";page-break-inside:avoid;height:15px">
      <td colspan="2" valign="top"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <#if flgDTO.oiqInvoiceFlgDTO.regAddressFlg==0>
            <span style="font-size:12px;">地址</span>
          </#if>
          <#if flgDTO.oiqInvoiceFlgDTO.regAddressFlg==1>
            <span style="font-size:12px;" class="isEdit">地址</span>
          </#if>
        </p>
      </td>
      <td colspan="6" valign="top"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <span style="font-size:12px;color:#333;">${(orderInvoiceDTO.regAddress)!''} </span>
        </p>
      </td>
      <td colspan="2" valign="top"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <#if flgDTO.oiqInvoiceFlgDTO.taxNoFlg==0>
            <span style="font-size:12px;">税号或注册证号</span>
          </#if>
          <#if flgDTO.oiqInvoiceFlgDTO.taxNoFlg==1>
            <span style="font-size:12px;" class="isEdit">税号或注册证号</span>
          </#if>
        </p>
      </td>
      <td colspan="2" valign="top"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <span style="font-size:12px;color:#333;">${(orderInvoiceDTO.taxNo)!''}</span>
        </p>
      </td>
    </tr>
    <tr style=";page-break-inside:avoid;height:15px">
      <td colspan="2" valign="top"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <#if flgDTO.oiqInvoiceFlgDTO.contactFlg==0>
            <span style="font-size:12px;">联系人</span>
          </#if>
          <#if flgDTO.oiqInvoiceFlgDTO.contactFlg==1>
            <span style="font-size:12px;" class="isEdit">联系人</span>
          </#if>
        </p>
      </td>
      <td colspan="6" valign="top"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <span style="font-size:12px;color:#333;">${(orderInvoiceDTO.contact)!''} </span>
        </p>
      </td>
      <td colspan="2" valign="top"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <#if flgDTO.oiqInvoiceFlgDTO.regPhoneFlg==0>
            <span style="font-size:12px;">电话</span>
          </#if>
          <#if flgDTO.oiqInvoiceFlgDTO.regPhoneFlg==1>
            <span style="font-size:12px;" class="isEdit">电话</span>
          </#if>
        </p>
      </td>
      <td colspan="2" valign="top"
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="line-height:16px">
          <span style="font-size:12px;color:#333;">${(orderInvoiceDTO.regPhone)!''}</span>
        </p>
      </td>
    </tr>
  </#if>




  <tr style=";page-break-inside:avoid;height:4px">
    <td colspan="2"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="margin-top:1px;margin-right:0;margin-bottom:   1px;margin-left:0;line-height:16px">
        <#if flgDTO.payerNameFlg==0>
          <span style="font-size:12px;" >付款方联系人</span>
        </#if>
        <#if flgDTO.payerNameFlg==1>
          <span style="font-size:12px;"  class="isEdit">付款方联系人</span>
        </#if>
      </p>
    </td>
    <td colspan="2"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="line-height:16px">
        <span style="font-size:12px;color:#333;"> ${(orderInvoiceDTO.payerName)!''}</span>
      </p>
    </td>
    <td colspan="1"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="line-height:16px">
        <#if flgDTO.payerPhoneFlg==0>
          <span style="font-size:12px;" >手机</span>
        </#if>
        <#if flgDTO.payerPhoneFlg==1>
          <span style="font-size:12px;"  class="isEdit">手机</span>
        </#if>
      </p>
    </td>
    <td colspan="3"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="line-height:16px">
        <span style="font-size:12px;color:#333;"> ${(orderInvoiceDTO.payerPhone)!''}</span>
      </p>
    </td>
    <td colspan="2"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="line-height:16px">
        <#if flgDTO.payerEmailFlg==0>
          <span style="font-size:12px;" >电子邮箱</span>
        </#if>
        <#if flgDTO.payerEmailFlg==1>
          <span style="font-size:12px;"  class="isEdit">电子邮箱</span>
        </#if>
      </p>
    </td>
    <td colspan="2"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="line-height:16px">
        <span style="font-size:12px;color:#333;">  ${(orderInvoiceDTO.payerEmail)!''}</span>
      </p>
    </td>
  </tr>
  <tr style=";page-break-inside:avoid;height:4px">
    <td colspan="2"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="margin-top:1px;margin-right:0;margin-bottom:   1px;margin-left:0;line-height:16px">
        <#if flgDTO.invoiceTypeShowFlg==0>
          <span style="font-size:12px;" >发票类型</span>
        </#if>
        <#if flgDTO.invoiceTypeShowFlg==1>
          <span style="font-size:12px;"  class="isEdit">发票类型</span>
        </#if>
      </p>
    </td>
    <td colspan="3"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="line-height:16px">
        <span style="font-size:12px;color:#333;">${(orderInvoiceDTO.invoiceTypeShow)!''}</span>
      </p>
    </td>
    <td colspan="3"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="line-height:16px">
        <#if flgDTO.invoiceEmailFlg==0>
          <span style="font-size:12px;">发票发送邮箱</span>
        </#if>
        <#if flgDTO.invoiceEmailFlg==1>
          <span style="font-size:12px;" class="isEdit">发票发送邮箱</span>
        </#if>
      </p>
    </td>
    <td colspan="4"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="line-height:16px">
    <span style="font-size:12px;color:#333;">
         ${(invoiceEmail)!''}
    </span>
      </p>
    </td>
  </tr>
</table>
<table width="100%" style="width: 100%;" cellspacing="0">
  <colgroup>
    <col width="22%" />
    <col width="25%" />
    <col width="20%" />
    <col width="20%" />
    <col width="8%" />
    <col width="7%" />
  </colgroup>
    <tr style=";height:4px">
      <td
          style="border-right: 1px solid #333; border-bottom: 1px solid #333; border-left: 1px solid #333; border-image: initial; border-top: none; padding: 0px 7px;">
        <p style="margin-top:3px;margin-right:0;margin-bottom:3px;margin-left:0;text-align:center">
          <span style="font-size:12px;">★</span>
          <#if flgDTO.oiqDetailFlgDTO.itemNameFlg==0>
            <span style="font-size:12px;font-weight: bold" >测试项目</span>
          </#if>
          <#if flgDTO.oiqDetailFlgDTO.itemNameFlg==1>
            <span style="font-size:12px;font-weight: bold"  class="isEdit">测试项目</span>
          </#if>
        </p>
      </td>
      <td
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="text-align:center">
          <span style="font-size:12px;font-weight: bold">测试样品</span>
        </p>
      </td>
      <td
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="text-align:center">
          <#if flgDTO.oiqDetailFlgDTO.standardCodeFlg==0>
            <span style="font-size:12px;font-weight: bold" >测试方法</span>
          </#if>
          <#if flgDTO.oiqDetailFlgDTO.standardCodeFlg==1>
            <span style="font-size:12px;font-weight: bold"  class="isEdit">测试方法</span>
          </#if>
        </p>
      </td>
      <td
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="margin-top:3px;margin-right:0;margin-bottom:3px;margin-left:0;text-align:center">
          <#if flgDTO.oiqDetailFlgDTO.testMemoFlg==0>
            <span style="font-size:12px;font-weight: bold" >测试备注</span>
          </#if>
          <#if flgDTO.oiqDetailFlgDTO.testMemoFlg==1>
            <span style="font-size:12px;font-weight: bold"  class="isEdit">测试备注</span>
          </#if>
        </p>
      </td>
      <td
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="margin-top:3px;margin-right:0;margin-bottom:3px;margin-left:0;text-align:center">
          <#if flgDTO.oiqDetailFlgDTO.isDetermineFlg==0>
            <span style="font-size:12px;font-weight: bold" >是否判定</span>
          </#if>
          <#if flgDTO.oiqDetailFlgDTO.isDetermineFlg==1>
            <span style="font-size:12px;font-weight: bold"  class="isEdit">是否判定</span>
          </#if>
        </p>
      </td>
      <td
          style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
        <p style="margin-top:3px;margin-right:0;margin-bottom:3px;margin-left:0;text-align:center">
          <span style="font-size:12px;font-weight: bold">数量</span>
        </p>
      </td>
    </tr>
    <#if orderDetailDTOList?has_content>
      <#list orderDetailDTOList as orderDetail>
        <tr style=";height:20px">
          <td
              style="border-right: 1px solid #333; border-bottom: 1px solid #333; border-left: 1px solid #333; border-image: initial; border-top: none; padding: 0px 7px;">
            <p>
              <span style="font-size:12px;color:#333"> ${(orderDetail.itemName)!''}</span>
            </p>
          </td>
          <td
              style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
            <p>
    <span style="font-size:12px;color:#333;">
     <#if orderDetail.orderSampleDTOList?? && (orderDetail.orderSampleDTOList?size > 0)  >
       <#list orderDetail.orderSampleDTOList as orderSample>
         <span style="font-size: 13px;color:#878787;">
             ${(orderSample.row)!''}#${(orderSample.sampleNameShow)!''}
         </span>
       </#list>
     </#if>
    </span>
            </p>
          </td>
          <td
              style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
            <p>
              <span style="font-size:12px;color:#333;">${(orderDetail.standardCode)!''}</span>
            </p>
          </td>
          <td
              style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
            <p style="line-height:20px">
              <span style="font-size:12px;color:#333;">${(orderDetail.testMemo)!''}</span>
            </p>
          </td>
          <td
              style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
            <p style="line-height:20px">
              <#if orderDetail.isDetermine==0>
                <span style="font-size:12px;color:#333;">否</span>
              </#if>
              <#if orderDetail.isDetermine==1>
                <span style="font-size:12px;color:#333;">是</span>
              </#if>
            </p>
          </td>
          <td
              style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
            <p style="text-align:center;line-height:20px"><span style="font-size:12px;color:#333;">
              <#if isPortal==1>/</#if>
              <#if isPortal==0>${(orderDetail.buyNums)!''}</#if>
              </span></p>
          </td>
        </tr>
      </#list>
    </#if>
</table>


<table width="100%" style="width: 100%;" cellspacing="0">
  <tr style=";height:10px" class="firstRow">
    <td width="100" colspan="13" style="border: 1px solid #333; padding: 0px 7px;">
      <p style="margin-top:4px;margin-right:0;margin-bottom:   4px;margin-left:0;line-height:16px">
        <#if flgDTO.testMemoFlg==0>
          <span style="font-size:12px;letter-spacing:-0;font-weight: bold">测试备注</span>
        </#if>
        <#if flgDTO.testMemoFlg==1>
          <span style="font-size:12px;letter-spacing:-0;font-weight: bold" class="isEdit">测试备注</span>
        </#if>
        <span style="font-size:12px;color:#333;">${(orderApplicationFormDTO.testMemo)!''}</span>
      </p>
    </td>
  </tr>
  <#if useDetermine==1>
    <tr style=";height:10px" class="firstRow">
      <td width="100" colspan="13" style="border: 1px solid #333; padding: 0px 7px;">
        <p style="margin-top:4px;margin-right:0;margin-bottom:   4px;margin-left:0;line-height:16px">
          <span style="font-size:12px;">★</span>
          <span style="font-size:12px;letter-spacing:-0;font-weight: bold">判定要求:</span>
          <span style="font-size:12px;color:#333;">
              <#if orderFormPDFDTO.isDetermine==1>
                需要判定,${(orderFormPDFDTO.determine)!''}
              </#if>
            <#if orderFormPDFDTO.isDetermine==0>
              不需要判定
            </#if>
        </span>
        </p>
      </td>
    </tr>
  </#if>


  <tr style=";height:23px">
    <td width="100" colspan="13"
        style="border-right: 1px solid #333; border-bottom: 1px solid #333; border-left: 1px solid #333; border-image: initial; border-top: none; padding: 0px 7px;">
      <p>
        <span style="font-size:12px;">★</span>
        <#if isPortal==0>
          <span style="font-size:12px;letter-spacing:-0;font-weight: bold">测试周期:</span>
        <span style="font-size:12px">
          <span style="color:#333"> ${(orderFormPDFDTO.isUrgentShow)!''}   ${orderFormPDFDTO.testCycle}</span></span>
        <span style="font-size:12px;color:#333">个工作日</span>
        <span style="font-size:12px;">（星期六、日及节假日不计入工作日内）</span><br></br>
        <span style="font-size:12px;color:#333 ;">
          ${(orderFormPDFDTO.testCycleMemo)!''}
        </span>
        </#if>
        <#if isPortal==1>
          <#if flgDTO.isUrgentShowFlg==0>
            <span style="font-size:12px;letter-spacing:-0;font-weight: bold">测试周期</span>
          </#if>
          <#if flgDTO.isUrgentShowFlg==1>
            <span style="font-size:12px;letter-spacing:-0;font-weight: bold" class="isEdit">测试周期</span>
          </#if>
          <span style="font-size:12px"> ${(orderFormPDFDTO.isUrgentShow)!''} </span>
        </#if>
      </p>
    </td>

  </tr>





  <tr style=";height:18px">
    <td width="19" colspan="3" rowspan="2"
        style="border-right: 1px solid #333; border-bottom: 1px solid #333; border-left: 1px solid #333; border-image: initial; border-top: none; padding: 0px 7px;">
      <p style="text-align:justify;text-justify:inter-ideograph;line-height:12px">
        <span style="font-size:12px;">★</span>
        <#if flgDTO.isRefundSampleFlg==0>
          <span style="font-size:12px;font-weight: bold">样品退回</span>
        </#if>
        <#if flgDTO.isRefundSampleFlg==1>
          <span style="font-size:12px;font-weight: bold" class="isEdit">样品退回</span>
        </#if>



      </p>
      <p style="margin-top:3px;margin-right:0;margin-bottom:   3px;margin-left:0">
        <span style="font-size:11px;">(</span><span
                style="font-size:10px;">不适用化学测试</span><span
                style="font-size:   11px;">)</span>
      </p>
    </td>
    <td width="80" colspan="10" valign="bottom"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="margin-top:3px;margin-right:0;margin-bottom:   3px;margin-left:0">
        <img src="${sqb1}" style="border: 1px solid #333;vertical-align: text-bottom;"></img>
        <#if orderApplicationFormDTO.isRefundSample==1>
          <span style="font-size:12px;">退样（退回快递,顺丰到付）</span>
        </#if>
        <#if orderApplicationFormDTO.isRefundSample==0>
          <span style="font-size:12px;">不需要退回</span>
        </#if>
      </p>
    </td>
  </tr>
  <tr style=";height:13px">
    <td width="18" colspan="4" valign="bottom"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="margin-top:3px;margin-right:0;margin-bottom:   3px;margin-left:0">
        <#if flgDTO.backStrFlg==0>
          <span style="font-size:12px;">样品退回地址</span>
        </#if>
        <#if flgDTO.backStrFlg==1>
          <span style="font-size:12px;" class="isEdit">样品退回地址</span>
        </#if>
      </p>
    </td>
    <td width="62" colspan="6" valign="bottom"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="margin-top:3px;margin-right:0;margin-bottom:   3px;margin-left:0">
    <span style="font-size:12px;color:#333;">
        <#if orderApplicationFormDTO.isRefundSample==1>
          ${(backStr)!''}
        </#if>
      <#if orderApplicationFormDTO.isRefundSample==0>
        不需要退回地址
      </#if>
    </span>
      </p>
    </td>
  </tr>

  <tr style=";height:20px">
    <td width="19" colspan="3" rowspan="2"
        style="border-right: 1px solid #333; border-bottom: 1px solid #333; border-left: 1px solid #333; border-image: initial; border-top: none; padding: 0px 7px;">
      <p style="text-align:justify;text-justify:inter-ideograph;line-height:12px">
        <span style="font-size:12px;">★</span>
        <span style="font-size:12px;font-weight: bold">报告交付</span>
      </p>
    </td>
    <td width="18" colspan="4" valign="bottom"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="margin-top:3px;margin-right:0;margin-bottom:   3px;margin-left:0">
        <#if flgDTO.reportEmailFlg==0>
          <span style="font-size:12px;">报告发送邮箱</span>
        </#if>
        <#if flgDTO.reportEmailFlg==1>
          <span style="font-size:12px;" class="isEdit">报告发送邮箱</span>
        </#if>
      </p>
    </td>
    <td width="62" colspan="6" valign="bottom"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="margin-top:3px;margin-right:0;margin-bottom:   3px;margin-left:0">
        <span style="font-size:12px;color:#333;">${(email)!''}</span>
      </p>
    </td>
  </tr>
  <tr style=";height:5px">
    <td width="18" colspan="4" valign="bottom"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="margin-top:3px;margin-right:0;margin-bottom:   3px;margin-left:0">
        <#if flgDTO.backStrFlg==0>
          <span style="font-size:12px;">报告邮寄地址</span>
        </#if>
        <#if flgDTO.backStrFlg==1>
          <span style="font-size:12px;" class="isEdit">报告邮寄地址</span>
        </#if>
      </p>
    </td>
    <td width="62" colspan="6" valign="bottom"
        style="border-top: none; border-left: none; border-bottom: 1px solid #333; border-right: 1px solid #333; padding: 0px 7px;">
      <p style="margin-top:3px;margin-right:0;margin-bottom:   3px;margin-left:0">
    <span style="font-size:12px;color:#333">
        ${(reportStr)!''}
	 </span>
      </p>
    </td>
  </tr>
  <#if attrFlg==1>
  <tr style=";height:23px">
    <td width="100" colspan="13"
        style="border-right: 1px solid #333; border-bottom: 1px solid #333; border-left: 1px solid #333; border-image: initial; border-top: none; padding: 0px 7px;">
      <p>
            <#if oiqOrderDTO.application.isTransfer==0>
            <span > <img src="${sqb1}" style="border: 1px solid #333;vertical-align: text-bottom;"></img></span>
            </#if>
            <#if oiqOrderDTO.application.isTransfer==1>
              <span  style="width: 13px; height: 13px; display: inline-block; border: 1px solid #ddd;"></span>
            </#if>
            <span style="font-size:12px;">不接受SGS以外的分包。若未勾选，则视为接受分包。</span>
      </p>
    </td>
  </tr>
    <tr style=";height:23px">
      <td width="100" colspan="13"
          style="border-right: 1px solid #333; border-bottom: 1px solid #333; border-left: 1px solid #333; border-image: initial; border-top: none; padding: 0px 7px;">
        <p>
          <#if oiqOrderDTO.application.applicationAttr.isDispute==1>
            <span > <img src="${sqb1}" style="border: 1px solid #333;vertical-align: text-bottom;"></img></span>
          </#if>
          <#if oiqOrderDTO.application.applicationAttr.isDispute==0>
            <span  style="width: 13px; height: 13px; display: inline-block; border: 1px solid #ddd;"></span>
          </#if>
          <span style="font-size:12px;">本申请是否涉及法律纠纷?如有请勾选，并说明。</span>
          <#if oiqOrderDTO.application.applicationAttr.isDispute==1>
            <br></br>
            <span style="font-size:12px;">${(isDisputeMemo)!''}</span>
          </#if>
        </p>
      </td>
    </tr>


  </#if>

  <tr style=";height:15px">
    <td width="100" colspan="13" valign="top"
        style="border-right: 1px solid #333; border-bottom: 1px solid #333; border-left: 1px solid #333; border-image: initial; border-top: none; padding: 0px 7px;">
      <p style="margin-top:3px;margin-right:0;margin-bottom:   3px;margin-left:0">
        <span style="font-size:12px;font-weight: bold">提示信息：</span>
      </p>
      <p style="line-height:13px">
        <span style="font-size:12px"> 1.  </span><span style="font-size:12px;">如需CMA或CNAS盖章，请提前告知工程师。</span>
      </p>

      <p style="line-height:13px">
        <span style="font-size:12px">2. </span><span style="font-size:12px;">实验室的评价仅基于实验室活动的实际值，未将实验室活动的结果不确定度影响计入。</span>
      </p>
      <p style="line-height:13px">
        <span style="font-size:12px">3. </span><span style="font-size:12px;">若未指明测试标准</span><span
                style="font-size:12px">/</span><span style="font-size:12px;">产品标准及年代号，则默认为客户接受</span><span
                style="font-size:12px">SGS</span><span style="font-size:12px;">推荐的测试标准</span><span
                style="font-size:12px">/</span><span style="font-size:12px;">产品标准。</span>
      </p>
      <p style="line-height:13px">
        <span style="font-size:12px">4. </span><span style="font-size:12px;">若未告知不接受分包，则视为接受。</span><span
                style="font-size:12px">SGS</span><span style="font-size:12px;">将分包到认可的检测机构。</span>
      </p>
      <p style="line-height:13px">
        <span style="font-size:12px"> 5.  </span><span style="font-size:12px;">测试后样品会有不同程度的损坏/损耗，余样保留时间30天，化学检测不适用余样保留时间，逾期销毁。</span>
      </p>
      <p style="line-height:13px">
        <span style="font-size:12px"> 6.  </span><span style="font-size:12px;">报告正式发布后，如需进行修改，根据修改的测试内容和项目另行评估收费。</span>
      </p>
      <p style="line-height:13px">
        &nbsp;
      </p>
    </td>
  </tr>
  <tr style=";height:15px">
    <td width="100" colspan="13" valign="top"
        style="border-right: 1px solid #333; border-bottom: 1px solid #333; border-left: 1px solid #333; border-image: initial; border-top: none; padding: 0px 7px;">
      <p style="margin-top:3px;margin-right:0;margin-bottom:   3px;margin-left:0">
        <span style="font-size:12px;">我们申请做以上测试并同意</span><span style="font-size:12px">SGS</span><span
                style="font-size:12px;">通用服务条款上的所有条款。（请务必在此位置签名或盖章）</span>
      </p>
      <p>
        <span style="font-size:12px;">申请人签名</span><span style="font-size:12px">/</span><span
                style="font-size:12px;">盖章：</span><span style="font-size:12px"> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span
                style="font-size:12px;">日期</span> <span style="font-size:12px;">：</span>
      </p>
      <p>

      </p>
    </td>
  </tr>
  <tr>
    <td width="51" style="border:none"></td>
    <td width="70" style="border:none"></td>
    <td width="30" style="border:none"></td>
    <td width="70" style="border:none"></td>
    <td width="10" style="border:none"></td>
    <td width="60" style="border:none"></td>
    <td width="0" style="border:none"></td>
    <td width="40" style="border:none"></td>
    <td width="70" style="border:none"></td>
    <td width="110" style="border:none"></td>
    <td width="1" style="border:none"></td>
    <td width="218" style="border:none"></td>
    <td width="44" style="border:none"></td>
  </tr>
</table>
<p style="text-align: center;margin-top: 30px;">
  <span style="text-align: center; text-indent: 20px; font-size: 10px;">本公司测试申请和测试报告签发均依据本公司的</span><span
          style="text-align: center; text-indent: 20px; font-size: 10px;">”</span><span
          style="text-align: center; text-indent: 20px; font-size: 10px; ">检验和测试服务通用条款</span><span
          style="text-align: center; text-indent: 20px; font-size: 10px;">”</span><span
          style="text-align: center; text-indent: 20px; font-size: 10px; ">，详细内容见：</span><span
          style="text-align: center; text-indent: 20px; font-size: 11px; "><ins>https://www.sgsgroup.com.cn/zh-cn/terms-and-conditions</ins></span>
</p>
</body>
</html>
