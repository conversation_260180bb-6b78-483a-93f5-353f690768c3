package com.sgs.ecom.member.dto.min;

import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.sgs.ecom.member.base.BaseOrderFilter;

public class FormatDTO {
    @ApiAnno(groups={BaseOrderFilter.WordForm.class})
    @BeanAnno(dtocls = {Style.class})
    private Style style=new Style();

    public Style getStyle() {
        return style;
    }

    public void setStyle(Style style) {
        this.style = style;
    }
}
