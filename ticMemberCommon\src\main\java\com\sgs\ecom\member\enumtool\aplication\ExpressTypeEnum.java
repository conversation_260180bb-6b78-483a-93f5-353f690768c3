package com.sgs.ecom.member.enumtool.aplication;

public enum ExpressTypeEnum {
    //'快递类型100顺丰标快，101顺丰特惠，102顺丰次晨，103顺丰即日',
    A("a", "100", "顺丰标快"),
    B("b", "101", "'顺丰特惠"),
    C("c", "102", "顺丰次晨"),
    D("d", "103", "顺丰即日"),

    ;

    ExpressTypeEnum(String name, String index, String nameCh) {
        this.name = name;
        this.index = index;
        this.nameCh = nameCh;
    }

    public static String getName(String index) {
        for (ExpressTypeEnum c : ExpressTypeEnum.values()) {
            if (c.getIndex().equals(index)) {
                return c.getName();
            }
        }
        return null;
    }

    public static ExpressTypeEnum getEnum(String index) {
        for (ExpressTypeEnum c : ExpressTypeEnum.values()) {
            if (c.getIndex().equals(index)) {
                return c;
            }
        }
        return null;
    }

    public static String getNameCh(String index) {
        for (ExpressTypeEnum c : ExpressTypeEnum.values()) {
            if (c.getIndex().equals(index)) {
                return c.getNameCh();
            }
        }

        return null;
    }





    private String name;
    private String index;
    private String nameCh;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }
}
