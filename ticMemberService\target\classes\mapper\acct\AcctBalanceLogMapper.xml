<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.mapper.acct.AcctBalanceLogMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.entity.AcctBalanceLog" >
    <id column="LOG_ID" property="logId" jdbcType="BIGINT" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="BALANCE_ID" property="balanceId" jdbcType="BIGINT" />
    <result column="BALANCE_TYPE" property="balanceType" jdbcType="INTEGER" />
    <result column="TRANS_TYPE" property="transType" jdbcType="INTEGER" />
    <result column="INOUT_TYPE" property="inoutType" jdbcType="INTEGER" />
    <result column="IN_AMOUNT" property="inAmount" jdbcType="INTEGER" />
    <result column="OUT_AMOUNT" property="outAmount" jdbcType="INTEGER" />
    <result column="AMOUNT" property="amount" jdbcType="INTEGER" />
    <result column="BALANCE" property="balance" jdbcType="INTEGER" />
    <result column="STATE" property="state" jdbcType="INTEGER" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
    <result column="OPP_FLOW_NUM" property="oppFlowNum" jdbcType="VARCHAR" />
    <result column="MEMO" property="memo" jdbcType="VARCHAR" />
    <result column="USER_TYPE" property="userType" jdbcType="TINYINT" />
  </resultMap>
  <sql id="Base_Column_List" >
    LOG_ID, USER_ID, BALANCE_ID, BALANCE_TYPE, TRANS_TYPE, INOUT_TYPE, IN_AMOUNT, OUT_AMOUNT, 
    AMOUNT, BALANCE, STATE, CREATE_DATE, STATE_DATE, OPP_FLOW_NUM, MEMO, USER_TYPE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from ACCT_BALANCE_LOG
    where LOG_ID = #{logId,jdbcType=BIGINT}
  </select>
  <insert id="insertSelective" parameterType="com.sgs.ecom.member.entity.AcctBalanceLog" >
    insert into ACCT_BALANCE_LOG
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="logId != null" >
        LOG_ID,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
      <if test="balanceId != null" >
        BALANCE_ID,
      </if>
      <if test="balanceType != null" >
        BALANCE_TYPE,
      </if>
      <if test="transType != null" >
        TRANS_TYPE,
      </if>
      <if test="inoutType != null" >
        INOUT_TYPE,
      </if>
      <if test="inAmount != null" >
        IN_AMOUNT,
      </if>
      <if test="outAmount != null" >
        OUT_AMOUNT,
      </if>
      <if test="amount != null" >
        AMOUNT,
      </if>
      <if test="balance != null" >
        BALANCE,
      </if>
      <if test="state != null" >
        STATE,
      </if>
      <if test="createDate != null" >
        CREATE_DATE,
      </if>
      <if test="stateDate != null" >
        STATE_DATE,
      </if>
      <if test="oppFlowNum != null" >
        OPP_FLOW_NUM,
      </if>
      <if test="memo != null" >
        MEMO,
      </if>
      <if test="userType != null" >
        USER_TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="logId != null" >
        #{logId,jdbcType=BIGINT},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="balanceId != null" >
        #{balanceId,jdbcType=BIGINT},
      </if>
      <if test="balanceType != null" >
        #{balanceType,jdbcType=INTEGER},
      </if>
      <if test="transType != null" >
        #{transType,jdbcType=INTEGER},
      </if>
      <if test="inoutType != null" >
        #{inoutType,jdbcType=INTEGER},
      </if>
      <if test="inAmount != null" >
        #{inAmount,jdbcType=INTEGER},
      </if>
      <if test="outAmount != null" >
        #{outAmount,jdbcType=INTEGER},
      </if>
      <if test="amount != null" >
        #{amount,jdbcType=INTEGER},
      </if>
      <if test="balance != null" >
        #{balance,jdbcType=INTEGER},
      </if>
      <if test="state != null" >
        #{state,jdbcType=INTEGER},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        #{stateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="oppFlowNum != null" >
        #{oppFlowNum,jdbcType=VARCHAR},
      </if>
      <if test="memo != null" >
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="userType != null" >
        #{userType,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
</mapper>