package com.sgs.ecom.member.dto.rsts;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sgs.ecom.member.dto.OrderAttributeDTO;
import com.sgs.ecom.member.dto.center.EnumDTO;
import com.sgs.ecom.member.util.serializer.PriceNumFormatSerializer;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class SampleInfoDTO {

    @JsonSerialize(using = PriceNumFormatSerializer.class)
    private BigDecimal orderAmount;
    @JsonSerialize(using = PriceNumFormatSerializer.class)
    private BigDecimal originalAmount;


    //用于传值
    private List<EnumDTO> labEnumDTO=new ArrayList<>();

    //当没有测试项目的时候为true
    private Boolean throwType=false;
    //当测试项目有被删除的数据
    private Boolean throwItemType=false;

    private Boolean throwRoType=false;//rost四项的type

    private Boolean throwLabType=false;

    private String currency;
    //用于传值 传完销毁
    private List<SampleItemDTO> list;


    

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    public BigDecimal getOriginalAmount() {
        return originalAmount;
    }

    public void setOriginalAmount(BigDecimal originalAmount) {
        this.originalAmount = originalAmount;
    }

    public Boolean getThrowType() {
        return throwType;
    }

    public void setThrowType(Boolean throwType) {
        this.throwType = throwType;
    }


    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public Boolean getThrowRoType() {
        return throwRoType;
    }

    public void setThrowRoType(Boolean throwRoType) {
        this.throwRoType = throwRoType;
    }


    public List<EnumDTO> getLabEnumDTO() {
        return labEnumDTO;
    }

    public void setLabEnumDTO(List<EnumDTO> labEnumDTO) {
        this.labEnumDTO = labEnumDTO;
    }

    public List<SampleItemDTO> getList() {
        return list;
    }

    public void setList(List<SampleItemDTO> list) {
        this.list = list;
    }

    public Boolean getThrowLabType() {
        return throwLabType;
    }

    public void setThrowLabType(Boolean throwLabType) {
        this.throwLabType = throwLabType;
    }

    public Boolean getThrowItemType() {
        return throwItemType;
    }

    public void setThrowItemType(Boolean throwItemType) {
        this.throwItemType = throwItemType;
    }
}
