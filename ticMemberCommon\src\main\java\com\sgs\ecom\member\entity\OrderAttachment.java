package com.sgs.ecom.member.entity;


public class OrderAttachment  {

 	private String cloudId;
 	private String createDate;

 	private String groupNo;

 	private String stateDate;

 	private String attType;

 	private Integer uploadType;

 	private Integer state;

 	private Long attachmentId;

 	private String orderNo;

 	private String fileId;

 	private String fileName;

 	private String fileUrl;





 	public void setCloudId(String cloudId){
 		 this.cloudId=cloudId;
 	}
 	public String getCloudId(){
 		 return this.cloudId;
 	}
 
 	 
 	public void setCreateDate(String createDate){
 		 this.createDate=createDate;
 	}
 	public String getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setGroupNo(String groupNo){
 		 this.groupNo=groupNo;
 	}
 	public String getGroupNo(){
 		 return this.groupNo;
 	}
 
 	 
 	public void setStateDate(String stateDate){
 		 this.stateDate=stateDate;
 	}
 	public String getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setAttType(String attType){
 		 this.attType=attType;
 	}
 	public String getAttType(){
 		 return this.attType;
 	}
 
 	 
 	public void setUploadType(Integer uploadType){
 		 this.uploadType=uploadType;
 	}
 	public Integer getUploadType(){
 		 return this.uploadType;
 	}
 
 	 
 	public void setState(Integer state){
 		 this.state=state;
 	}
 	public Integer getState(){
 		 return this.state;
 	}
 
 	 
 	public void setAttachmentId(Long attachmentId){
 		 this.attachmentId=attachmentId;
 	}
 	public Long getAttachmentId(){
 		 return this.attachmentId;
 	}
 
 	 
 	public void setOrderNo(String orderNo){
 		 this.orderNo=orderNo;
 	}
 	public String getOrderNo(){
 		 return this.orderNo;
 	}
 
 	 
 	public void setFileId(String fileId){
 		 this.fileId=fileId;
 	}
 	public String getFileId(){
 		 return this.fileId;
 	}
 
 	 
 	public void setFileName(String fileName){
 		 this.fileName=fileName;
 	}
 	public String getFileName(){
 		 return this.fileName;
 	}
 
 	 
 	public void setFileUrl(String fileUrl){
 		 this.fileUrl=fileUrl;
 	}
 	public String getFileUrl(){
 		 return this.fileUrl;
 	}
 
 	 
}