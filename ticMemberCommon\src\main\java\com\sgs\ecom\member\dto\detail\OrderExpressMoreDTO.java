package com.sgs.ecom.member.dto.detail;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.base.BaseOrderFilter;
import com.sgs.ecom.member.dto.center.LabDTO;
import com.sgs.ecom.member.dto.user.UserAddressDTO;
import com.sgs.ecom.member.enumtool.aplication.ExpressCodeEnum;
import com.sgs.ecom.member.enumtool.aplication.ExpressPayEnum;
import com.sgs.ecom.member.enumtool.aplication.ExpressTypeEnum;

import java.math.BigDecimal;
import java.util.List;

public class OrderExpressMoreDTO  {
    @ApiAnno(groups={BaseQryFilter.Default.class, BaseOrderFilter.OrderDetail.class})
    private LabDTO labDTO;
    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OrderDetail.class})
    private UserAddressDTO userAddressDTO;

    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OrderDetail.class})
    private Long labId;
    //投递方式10样品自寄,11样品上门取件,20发票快递',
    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OrderDetail.class})
    private int deliverType;
    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OrderDetail.class})
    private String orderNo;
    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OrderDetail.class})
    private String goodsName;
    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OrderDetail.class})
    private Integer packageNums;
    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OrderDetail.class})
    private BigDecimal goodsPrice;
    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OrderDetail.class})
    private String expressCode;
    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OrderDetail.class})
    private String preExpressDate;
    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OrderDetail.class})
    private String expressNo;
    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OrderDetail.class})
    private String expressType;
    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OrderDetail.class})
    private String payMethond;
    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OrderDetail.class})
    private String monthlyCard;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String salesCode;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String salesPhone;
    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OrderDetail.class})
    private Integer goodsType;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private List<OrderExpressListDTO> list ;
    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OrderDetail.class})
    private Integer orderState;

    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OrderDetail.class})
    private Integer submitType;  //0-保存  1-提交


    @ApiAnno(groups={BaseQryFilter.Default.class})
    private List<OrderExpressDTO> csList ;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String memo;

    public List<OrderExpressDTO> getCsList() {
        return csList;
    }

    public void setCsList(List<OrderExpressDTO> csList) {
        this.csList = csList;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getOrderState() {
        return orderState;
    }

    public void setOrderState(Integer orderState) {
        this.orderState = orderState;
    }

    public Integer getSubmitType() {
        return submitType;
    }

    public void setSubmitType(Integer submitType) {
        this.submitType = submitType;
    }

    public List<OrderExpressListDTO> getList() {
        return list;
    }

    public void setList(List<OrderExpressListDTO> list) {
        this.list = list;
    }

    public Integer getGoodsType() {
        return goodsType;
    }

    public void setGoodsType(Integer goodsType) {
        this.goodsType = goodsType;
    }

    public void setLabDTO(LabDTO labDTO) {
        this.labDTO = labDTO;
    }

    public UserAddressDTO getUserAddressDTO() {
        return userAddressDTO;
    }

    public void setUserAddressDTO(UserAddressDTO userAddressDTO) {
        this.userAddressDTO = userAddressDTO;
    }

    public Long getLabId() {
        return labId;
    }

    public void setLabId(Long labId) {
        this.labId = labId;
    }

    public int getDeliverType() {
        return deliverType;
    }

    public void setDeliverType(int deliverType) {
        this.deliverType = deliverType;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public Integer getPackageNums() {
        return packageNums;
    }

    public void setPackageNums(Integer packageNums) {
        this.packageNums = packageNums;
    }

    public BigDecimal getGoodsPrice() {
        return goodsPrice;
    }

    public void setGoodsPrice(BigDecimal goodsPrice) {
        this.goodsPrice = goodsPrice;
    }

    public String getExpressCode() {
        return expressCode;
    }

    public void setExpressCode(String expressCode) {
        this.expressCode = expressCode;
    }

    public String getPreExpressDate() {
        return preExpressDate;
    }

    public void setPreExpressDate(String preExpressDate) {
        this.preExpressDate = preExpressDate;
    }

    public String getExpressNo() {
        return expressNo;
    }

    public void setExpressNo(String expressNo) {
        this.expressNo = expressNo;
    }

    public String getExpressType() {
        return expressType;
    }

    public void setExpressType(String expressType) {
        this.expressType = expressType;
    }

    public String getPayMethond() {
        return payMethond;
    }

    public void setPayMethond(String payMethond) {
        this.payMethond = payMethond;
    }

    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OrderDetail.class, BaseQryFilter.QueryList.class})
    private String expressCodeShow;
    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OrderDetail.class, BaseQryFilter.QueryList.class})
    private String expressTypeShow;
    @ApiAnno(groups={BaseQryFilter.Default.class,BaseOrderFilter.OrderDetail.class, BaseQryFilter.QueryList.class})
    private String payMethondShow;

    public String getExpressCodeShow() {
        expressCodeShow= ExpressCodeEnum.getNameCh(expressCode);
        return expressCodeShow==null?"":expressCodeShow;
    }

    public String getExpressTypeShow() {
        expressTypeShow= ExpressTypeEnum.getNameCh(expressType);
        return expressTypeShow==null?"":expressTypeShow;
    }

    public String getPayMethondShow() {
        payMethondShow= ExpressPayEnum.getNameCh(payMethond);
        return payMethondShow==null?"":payMethondShow;
    }

    public String getMonthlyCard() {
        return monthlyCard;
    }

    public void setMonthlyCard(String monthlyCard) {
        this.monthlyCard = monthlyCard;
    }

    public LabDTO getLabDTO() {
        return labDTO;
    }

    public String getSalesCode() {
        return salesCode;
    }

    public void setSalesCode(String salesCode) {
        this.salesCode = salesCode;
    }

    public String getSalesPhone() {
        return salesPhone;
    }

    public void setSalesPhone(String salesPhone) {
        this.salesPhone = salesPhone;
    }
}
