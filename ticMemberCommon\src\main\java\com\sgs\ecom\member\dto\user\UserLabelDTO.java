package com.sgs.ecom.member.dto.user;

import java.util.Date;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter.QuerySummary;

public class UserLabelDTO {
	
    private Long labelId;

    @ApiAnno(groups={QuerySummary.class})
    private Long userId;

    private String labelGroup;

    private String labelCode;

    @ApiAnno(groups={QuerySummary.class})
    private String labelValue;

    private Integer state;

    private Date createDate;

    private Date stateDate;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getLabelValue() {
        return labelValue;
    }

    public void setLabelValue(String labelValue) {
        this.labelValue = labelValue;
    }

	public Long getLabelId() {
		return labelId;
	}

	public void setLabelId(Long labelId) {
		this.labelId = labelId;
	}

	public String getLabelGroup() {
		return labelGroup;
	}

	public void setLabelGroup(String labelGroup) {
		this.labelGroup = labelGroup;
	}

	public String getLabelCode() {
		return labelCode;
	}

	public void setLabelCode(String labelCode) {
		this.labelCode = labelCode;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Date getStateDate() {
		return stateDate;
	}

	public void setStateDate(Date stateDate) {
		this.stateDate = stateDate;
	}
    
}
