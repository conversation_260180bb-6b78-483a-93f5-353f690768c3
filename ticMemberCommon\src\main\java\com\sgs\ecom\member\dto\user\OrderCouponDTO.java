package com.sgs.ecom.member.dto.user;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;

import java.math.BigDecimal;

public class OrderCouponDTO {
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private Long couponId;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String couponName;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int couponFlg=1;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String couponRange;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String businessRelate;

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BigDecimal useAmount;

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BigDecimal discountAmount;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String effDate;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String expDate;

    public Long getCouponId() {
        return couponId;
    }

    public void setCouponId(Long couponId) {
        this.couponId = couponId;
    }

    public int getCouponFlg() {
        return couponFlg;
    }

    public void setCouponFlg(int couponFlg) {
        this.couponFlg = couponFlg;
    }

    public BigDecimal getUseAmount() {
        return useAmount;
    }

    public void setUseAmount(BigDecimal useAmount) {
        this.useAmount = useAmount;
    }

    public BigDecimal getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount;
    }

    public String getCouponName() {
        return couponName;
    }

    public void setCouponName(String couponName) {
        this.couponName = couponName;
    }

    public String getEffDate() {
        return effDate;
    }

    public void setEffDate(String effDate) {
        this.effDate = effDate;
    }

    public String getExpDate() {
        return expDate;
    }

    public void setExpDate(String expDate) {
        this.expDate = expDate;
    }

    public String getCouponRange() {
        return couponRange;
    }

    public void setCouponRange(String couponRange) {
        this.couponRange = couponRange;
    }

    public String getBusinessRelate() {
        return businessRelate;
    }

    public void setBusinessRelate(String businessRelate) {
        this.businessRelate = businessRelate;
    }
}
