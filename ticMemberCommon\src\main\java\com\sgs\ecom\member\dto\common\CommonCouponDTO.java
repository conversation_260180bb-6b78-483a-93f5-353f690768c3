package com.sgs.ecom.member.dto.common;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.member.util.time.TimeCalendarUtil;

import java.math.BigDecimal;
import java.util.Date;

public class CommonCouponDTO extends BaseQryFilter {

	@ApiAnno(groups={Default.class})
	private String configCode;
	@ApiAnno(groups={Default.class})
	private String couponMemo;
	@ApiAnno(groups={Default.class})
	private String couponName;
	@ApiAnno(groups={Default.class})
	private String couponDesc;
	@ApiAnno(groups={Default.class})
	private String couponScope;
	@ApiAnno(groups={Default.class})
	private String couponType;
	@ApiAnno(groups={Default.class})
	private BigDecimal discountAmount;
	@ApiAnno(groups={Default.class})
	private String effDate;
	@ApiAnno(groups={Default.class})
	private String effDays;
	@ApiAnno(groups={Default.class})
	private String expDate;
	@ApiAnno(groups={Default.class})
	private String getMethod;
	@ApiAnno(groups={Default.class})
	private String receiveEndTime;
	@ApiAnno(groups={Default.class})
	private String receiveStartTime;
	@ApiAnno(groups={Default.class})
	private String urlLink;
	@ApiAnno(groups={Default.class})
	private BigDecimal useAmount;
	@ApiAnno(groups={Default.class})
	private int validRule;
	@ApiAnno(groups={Default.class})
	private int vipLevel;
	@ApiAnno(groups={Default.class})
	private int canUseNum;
	@ApiAnno(groups={Default.class})
	private int limitNums;
	@ApiAnno(groups={Default.class})
	private int surplusNums;

	@ApiAnno(groups={Default.class})
	private int timeFlg;
	@ApiAnno(groups={Default.class})
	private String couponRange;
	@ApiAnno(groups={Default.class})
	private String couponExplain;

	public String getCouponRange() {
		return couponRange;
	}

	public void setCouponRange(String couponRange) {
		this.couponRange = couponRange;
	}

	public String getCouponExplain() {
		return couponExplain;
	}

	public void setCouponExplain(String couponExplain) {
		this.couponExplain = couponExplain;
	}

	public String getConfigCode() {
		return configCode;
	}

	public void setConfigCode(String configCode) {
		this.configCode = configCode;
	}

	public String getCouponMemo() {
		return couponMemo;
	}

	public void setCouponMemo(String couponMemo) {
		this.couponMemo = couponMemo;
	}

	public String getCouponName() {
		return couponName;
	}

	public void setCouponName(String couponName) {
		this.couponName = couponName;
	}

	public String getCouponScope() {
		return couponScope;
	}

	public void setCouponScope(String couponScope) {
		this.couponScope = couponScope;
	}

	public String getCouponType() {
		return couponType;
	}

	public void setCouponType(String couponType) {
		this.couponType = couponType;
	}

	public BigDecimal getDiscountAmount() {
		return discountAmount;
	}

	public void setDiscountAmount(BigDecimal discountAmount) {
		this.discountAmount = discountAmount;
	}

	public String getEffDate() {
		return effDate;
	}

	public void setEffDate(String effDate) {
		this.effDate = effDate;
	}

	public String getEffDays() {
		return effDays;
	}

	public void setEffDays(String effDays) {
		this.effDays = effDays;
	}

	public String getExpDate() {
		return expDate;
	}

	public void setExpDate(String expDate) {
		this.expDate = expDate;
	}

	public String getGetMethod() {
		return getMethod;
	}

	public void setGetMethod(String getMethod) {
		this.getMethod = getMethod;
	}

	public String getReceiveEndTime() {
		return receiveEndTime;
	}

	public void setReceiveEndTime(String receiveEndTime) {
		this.receiveEndTime = receiveEndTime;
	}

	public String getReceiveStartTime() {
		return receiveStartTime;
	}

	public void setReceiveStartTime(String receiveStartTime) {
		this.receiveStartTime = receiveStartTime;
	}

	public String getUrlLink() {
		return urlLink;
	}

	public void setUrlLink(String urlLink) {
		this.urlLink = urlLink;
	}

	public BigDecimal getUseAmount() {
		return useAmount;
	}

	public void setUseAmount(BigDecimal useAmount) {
		this.useAmount = useAmount;
	}

	public int getValidRule() {
		return validRule;
	}

	public void setValidRule(int validRule) {
		this.validRule = validRule;
	}

	public int getVipLevel() {
		return vipLevel;
	}

	public void setVipLevel(int vipLevel) {
		this.vipLevel = vipLevel;
	}

	public int getCanUseNum() {
		return canUseNum;
	}

	public void setCanUseNum(int canUseNum) {
		this.canUseNum = canUseNum;
	}

	public int getLimitNums() {
		return limitNums;
	}

	public void setLimitNums(int limitNums) {
		this.limitNums = limitNums;
	}

	public int getSurplusNums() {
		return surplusNums;
	}

	public void setSurplusNums(int surplusNums) {
		this.surplusNums = surplusNums;
	}

	public int getTimeFlg() {
		if(TimeCalendarUtil.getStringToDate(effDays).after(new Date())){
			return 1;
		}
		return 0;
	}

	public void setTimeFlg(int timeFlg) {
		this.timeFlg = timeFlg;
	}

	public String getCouponDesc() {
		return couponDesc;
	}

	public void setCouponDesc(String couponDesc) {
		this.couponDesc = couponDesc;
	}
}
