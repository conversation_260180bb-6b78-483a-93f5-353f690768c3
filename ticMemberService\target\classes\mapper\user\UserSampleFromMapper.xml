<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.member.domain.user.dao.UserSampleFromMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.ecom.member.entity.user.UserSampleFrom" >
    <id column="FORM_ID" property="formId" jdbcType="BIGINT" />
    <result column="USER_ID" property="userId" jdbcType="VARCHAR" />
    <result column="SAMPLE_NO" property="sampleNo" jdbcType="VARCHAR" />
    <result column="SAMPLE_KEY_NAME" property="sampleKeyName" jdbcType="VARCHAR" />
    <result column="SAMPLE_KEY" property="sampleKey" jdbcType="VARCHAR" />
    <result column="SAMPLE_VALUE" property="sampleValue" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="TINYINT" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP" />
    <result column="AREA_CODE" property="areaCode" jdbcType="VARCHAR" />
    <result column="sample_value_remark" property="sampleValueRemark" jdbcType="VARCHAR" />
    <result column="ENUM_CONFIG" property="enumConfig" jdbcType="VARCHAR" />
    <result column="TYPE" property="type" jdbcType="TINYINT" />
    <result column="SAMPLE_EXPLAIN" property="sampleExplain" jdbcType="VARCHAR" />
    <result column="SAMPLE_EXPLAIN_EN" property="sampleExplainEn" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    FORM_ID, ORDER_NO, SAMPLE_NO, GROUP_NO, SAMPLE_KEY_NAME, SAMPLE_KEY, SAMPLE_VALUE, 
    STATE, CREATE_DATE, STATE_DATE,AREA_CODE,sample_value_remark,ENUM_CONFIG,TYPE,SAMPLE_EXPLAIN,SAMPLE_EXPLAIN_EN,remark
  </sql>




  <!--自定义 -->
  <resultMap id="resultDTO" type="com.sgs.ecom.member.dto.user.UserSampleFromDTO" >
    <id column="FORM_ID" property="formId" jdbcType="BIGINT" />
    <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    <result column="SAMPLE_NO" property="sampleNo" jdbcType="VARCHAR" />
    <result column="SAMPLE_KEY_NAME" property="sampleKeyName" jdbcType="VARCHAR" />
    <result column="SAMPLE_KEY" property="sampleKey" jdbcType="VARCHAR" />
    <result column="SAMPLE_VALUE" property="sampleValue" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="TINYINT" />
    <result column="SORT_SHOW" property="sortShow" jdbcType="INTEGER" />
    <result column="AREA_CODE" property="areaCode" jdbcType="VARCHAR" />
    <result column="sample_value_remark" property="sampleValueRemark" jdbcType="VARCHAR" />
    <result column="ENUM_CONFIG" property="enumConfig" jdbcType="VARCHAR" />
    <result column="TYPE" property="type" jdbcType="TINYINT" />
    <result column="SAMPLE_EXPLAIN" property="sampleExplain" jdbcType="VARCHAR" />
    <result column="SAMPLE_EXPLAIN_EN" property="sampleExplainEn" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
  </resultMap>

  <sql id="sqlListDTO" >
    FORM_ID, USER_ID, SAMPLE_NO,SAMPLE_KEY_NAME, SAMPLE_KEY, SAMPLE_VALUE,  STATE,SORT_SHOW,AREA_CODE,sample_value_remark,ENUM_CONFIG,TYPE,SAMPLE_EXPLAIN,SAMPLE_EXPLAIN_EN,remark
  </sql>

  <select id="selectListByMap" resultMap="resultDTO" >
    select
    <include refid="sqlListDTO" />
    from USER_SAMPLE_FORM
    <include refid="baseQueryWhere"/>
  </select>
  <sql id="baseQueryWhere">
   <where>
    <if test="userId != null">
      AND USER_ID=#{userId}
    </if>

     <if test="state != null">
       AND STATE=#{state}
     </if>
   </where>
  </sql>

  <insert id="insertForeach"  >
    insert into USER_SAMPLE_FORM
    (USER_ID, SAMPLE_NO, SAMPLE_KEY_NAME, SAMPLE_KEY, SAMPLE_VALUE,
     STATE,SORT_SHOW,AREA_CODE,sample_value_remark,ENUM_CONFIG,TYPE,SAMPLE_EXPLAIN,SAMPLE_EXPLAIN_EN,remark)
    values
    <foreach collection ="list" item="item" index= "index" separator =",">
      (
      #{item.userId}, #{item.sampleNo},#{item.sampleKeyName},#{item.sampleKey}, #{item.sampleValue},
      #{item.state},#{item.sortShow},#{item.areaCode},#{item.sampleValueRemark},#{item.enumConfig},#{item.type},#{item.sampleExplain},#{item.sampleExplainEn},#{item.remark}
      )
    </foreach>
  </insert>
  <update id="updateForm">
    update USER_SAMPLE_FORM set STATE=0 where USER_ID=#{userId}
    <if test="sampleNoList != null ">
      AND SAMPLE_NO in
      <foreach collection="sampleNoList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </update>
</mapper>