package com.sgs.ecom.member.dto.rsts;

import com.sgs.ecom.member.dto.center.EnumDTO;
import com.sgs.ecom.member.dto.cust.CustInvoiceDTO;
import com.sgs.ecom.member.dto.custom.SysPersonDTO;
import com.sgs.ecom.member.dto.rpc.QuestionQryDtlDTO;
import com.sgs.ecom.member.dto.user.UserDTO;
import com.sgs.ecom.member.request.OrderBaseAddReq;
import com.sgs.ecom.member.request.rsts.RstsApplicationReq;

import java.util.Date;
import java.util.List;

/**
 * @Description :RSTS-saveOrder接口参数对象
 * <AUTHOR> Zhang
 * @Date  2023/10/24
 **/
public class RSTSSaveOrderBaseDTO {

    private String questionId;
    private String replyNo;
    private String catagoryId;
    private QuestionQryDtlDTO questionQryDtlDTO;
    private String bu;
    private String orderNo;
    private Long userId;
    private String baseKey;
    private Date date;
    private String dateStr;
    private String phone;
    private Boolean isAdd;
    private String groupNo;

    private SysPersonDTO sysPersonDTO;
    private RstsApplicationReq rstsApplicationReq;
    private SampleInfoDTO sampleInfoDTO;
    private List<SampleItemDTO> sampleItemDTOList;
    private CustBossDTO custBossDTO;
    private Boolean expressFlg;
    private Long custId;
    private Long orderId;
    private EnumDTO enumDTO;
    private CustInvoiceDTO custInvoiceDTO;

    public RSTSSaveOrderBaseDTO() {
    }

    public RSTSSaveOrderBaseDTO(OrderBaseAddReq orderBaseAddReq, QuestionQryDtlDTO questionQryDtlDTO, String orderNo, UserDTO userDTO, String baseKey, Date date, String dateStr) {
        this.questionId = orderBaseAddReq.getQuestionId();
        this.replyNo = orderBaseAddReq.getReplyNo();
        this.catagoryId = orderBaseAddReq.getCatagoryId();
        this.questionQryDtlDTO = questionQryDtlDTO;
        this.bu = questionQryDtlDTO.getBu();
        this.orderNo = orderNo;
        this.userId = userDTO.getUserId();
        this.baseKey = baseKey;
        this.date = date;
        this.dateStr = dateStr;
    }

    public EnumDTO getEnumDTO() {
        return enumDTO;
    }

    public void setEnumDTO(EnumDTO enumDTO) {
        this.enumDTO = enumDTO;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getCustId() {
        return custId;
    }

    public void setCustId(Long custId) {
        this.custId = custId;
    }

    public Boolean getExpressFlg() {
        return expressFlg;
    }

    public void setExpressFlg(Boolean expressFlg) {
        this.expressFlg = expressFlg;
    }

    public CustBossDTO getCustBossDTO() {
        return custBossDTO;
    }

    public void setCustBossDTO(CustBossDTO custBossDTO) {
        this.custBossDTO = custBossDTO;
    }

    public SampleInfoDTO getSampleInfoDTO() {
        return sampleInfoDTO;
    }

    public void setSampleInfoDTO(SampleInfoDTO sampleInfoDTO) {
        this.sampleInfoDTO = sampleInfoDTO;
    }

    public RstsApplicationReq getRstsApplicationReq() {
        return rstsApplicationReq;
    }

    public void setRstsApplicationReq(RstsApplicationReq rstsApplicationReq) {
        this.rstsApplicationReq = rstsApplicationReq;
    }

    public List<SampleItemDTO> getSampleItemDTOList() {
        return sampleItemDTOList;
    }

    public void setSampleItemDTOList(List<SampleItemDTO> sampleItemDTOList) {
        this.sampleItemDTOList = sampleItemDTOList;
    }

    public String getGroupNo() {
        return groupNo;
    }

    public void setGroupNo(String groupNo) {
        this.groupNo = groupNo;
    }

    public Boolean getAdd() {
        return isAdd;
    }

    public void setAdd(Boolean add) {
        isAdd = add;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public SysPersonDTO getSysPersonDTO() {
        return sysPersonDTO;
    }

    public void setSysPersonDTO(SysPersonDTO sysPersonDTO) {
        this.sysPersonDTO = sysPersonDTO;
    }

    public String getQuestionId() {
        return questionId;
    }

    public void setQuestionId(String questionId) {
        this.questionId = questionId;
    }

    public String getReplyNo() {
        return replyNo;
    }

    public void setReplyNo(String replyNo) {
        this.replyNo = replyNo;
    }

    public String getCatagoryId() {
        return catagoryId;
    }

    public void setCatagoryId(String catagoryId) {
        this.catagoryId = catagoryId;
    }

    public QuestionQryDtlDTO getQuestionQryDtlDTO() {
        return questionQryDtlDTO;
    }

    public void setQuestionQryDtlDTO(QuestionQryDtlDTO questionQryDtlDTO) {
        this.questionQryDtlDTO = questionQryDtlDTO;
    }

    public String getBu() {
        return bu;
    }

    public void setBu(String bu) {
        this.bu = bu;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getBaseKey() {
        return baseKey;
    }

    public void setBaseKey(String baseKey) {
        this.baseKey = baseKey;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public String getDateStr() {
        return dateStr;
    }

    public void setDateStr(String dateStr) {
        this.dateStr = dateStr;
    }

    public CustInvoiceDTO getCustInvoiceDTO() {
        return custInvoiceDTO;
    }

    public void setCustInvoiceDTO(CustInvoiceDTO custInvoiceDTO) {
        this.custInvoiceDTO = custInvoiceDTO;
    }
}
