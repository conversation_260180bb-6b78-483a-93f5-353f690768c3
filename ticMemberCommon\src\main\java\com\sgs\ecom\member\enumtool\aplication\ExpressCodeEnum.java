package com.sgs.ecom.member.enumtool.aplication;

public enum ExpressCodeEnum {
    SFEXPRESS("SFEXPRESS", "SFEXPRESS", "顺丰"),
    STO("STO", "STO", "申通快递"),
    YTO("YTO", "YTO", "圆通速递"),

    HTKY("HTKY", "HTKY", "百世快递"),
    TTKDEX("TTKDEX", "TTKDEX", "天天快递"),
    JTSD("JTSD", "JTSD", "极兔速递"),

    DBL("DBL", "DBL", "德邦快递"),
    UC("UC", "UC", "优速快递"),
    JDKY("JDKY", "JDKY", "京东快运"),
    JD("JD", "JD", "京东快递"),
    EMS("EMS", "EMS", "EMS"),
    YZPY("YZPY", "YZPY", "邮政快递包裹"),
    YD("YD", "YD", "韵达速递"),
    ZTO("ZTO", "ZTO", "中通快递"),
    SF("SF", "SF", "顺丰速运"),

    DNWL("DNWL", "DNWL", "东骏快捷物流"),
    FEDEX("FEDEX", "FEDEX", "FEDEX联邦（国内）"),
    RRS("RRS", "RRS", "日日顺物流"),
    ANEKY("ANEKY", "ANEKY", "安能快运/物流"),
    ZTOKY("ZTOKY", "ZTOKY", "中通快运"),
    ANE("ANE", "ANE", "安能快递"),
    SX("SX", "SX", "顺心捷达"),
    BTWL("BTWL", "BTWL", "百世快运"),
    XFEX("XFEX", "XFEX", "信丰物流"),
    YMDD("YMDD", "YMDD", "壹米滴答"),
    JGSD("JGSD", "JGSD", "京广速递"),
    DBLKY("DBLKY", "DBLKY", "德邦快运/德邦物流"),
    KYSY("KYSY", "KYSY", "跨越速运"),
    YDKY("YDKY", "YDKY", "韵达快运"),
    TJS("TJS", "TJS", "特急送"),
    UPS("UPS", "UPS", "UPS"),
    SHUNFENGKUAIYUN("SHUNFENGKUAIYUN", "SHUNFENGKUAIYUN", "顺丰快运"),
    YAD("YAD", "YAD", "源安达快递"),
    ANNTO("ANNTO", "ANNTO", "安得物流"),
    LHT("LHT", "LHT", "联昊通速递"),
    ZJS("ZJS", "ZJS", "宅急送"),
    YF("YF", "YF", "耀飞快递"),
    USPS("USPS", "USPS", "USPS美国邮政"),
    DHL("DHL", "DHL", "DHL-中国件"),
    FASTGO("FASTGO", "FASTGO", "速派快递"),
    HSSY("HSSY", "HSSY", "汇森速运"),
    JYM("JYM", "JYM", "加运美"),
    ZTOGLOBAL("ZTOGLOBAL", "ZTOGLOBAL", "中通国际"),
    HOAU("HOAU", "HOAU", "天地华宇"),
    TNJEX("TNJEX", "TNJEX", "明通国际快递"),
    DPDUK("DPDUK", "DPDUK", "DPD(UK)"),
    DSWL("DSWL", "DSWL", "D速物流"),
    YUANTONGKUAIYUN("YUANTONGKUAIYUN", "YUANTONGKUAIYUN", "圆通快运"),
    EWE("EWE", "EWE", "EWE"),
    SHWL("SHWL", "SHWL", "盛辉物流"),
    YJWL("YJWL", "YJWL", "云聚物流"),
    ZY_FY("ZY_FY", "ZY_FY", "飞洋快递"),
    YXWL("YXWL", "YXWL", "宇鑫物流"),
    PADTF("PADTF", "PADTF", "平安达腾飞快递"),
    ZYE("ZYE", "ZYE", "众邮快递"),
    UBONEX("UBONEX", "UBONEX", "优邦国际速运"),
    ZHONGTIEWULIU("ZHONGTIEWULIU", "ZHONGTIEWULIU", "中铁飞豹"),
    HDWL("HDWL", "HDWL", "宏递快运"),
    CRAZY("CRAZY", "CRAZY", "疯狂快递"),
    STWL("STWL", "STWL", "速腾快递"),
    CTWL("CTWL", "CTWL", "长通物流"),
    PANEX("PANEX", "PANEX", "泛捷快递"),
    LB("LB", "LB", "龙邦快递"),
    BETWL("BETWL", "BETWL", "百腾物流"),
    AUEXPRESS("AUEXPRESS", "AUEXPRESS", "澳邮中国快运AuExpress"),
    ESDEX("ESDEX", "ESDEX", "卓志速运"),
    SFWL("SFWL", "SFWL", "盛丰物流"),
    JP("JP", "JP", "日本邮政"),
    ZY100("ZY100", "ZY100", "中远快运"),
    HTB56("HTB56", "HTB56", "徽托邦物流"),
    YBG("YBG", "YBG", "洋包裹"),
    ZTWL("ZTWL", "ZTWL", "中铁物流"),
    ZHONGHUAN("ZHONGHUAN", "ZHONGHUAN", "中环国际快递"),
    HTWL("HTWL", "HTWL", "鸿泰物流"),
    BCTWL("BCTWL", "BCTWL", "百城通物流"),
    SNWL("SNWL", "SNWL", "苏宁物流"),
    TU("TU", "TU", "运联科技"),
    TBZS("TBZS", "TBZS", "特宝专送"),
    FTD("FTD", "FTD", "富腾达"),
    JIUYE("JIUYE", "JIUYE", "九曳供应链"),
    CSTD("CSTD", "CSTD", "畅顺通达"),
    HISENSE("HISENSE", "HISENSE", "海信物流"),
    AUODEXPRESS("AUODEXPRESS", "AUODEXPRESS", "澳德物流"),
    GJYZBG("GJYZBG", "GJYZBG", "国际邮政包裹"),
    YLFWL("YLFWL", "YLFWL", "一路发物流"),
    WHXBWL("WHXBWL", "WHXBWL", "武汉晓毕物流"),
    YSDF("YSDF", "YSDF", "余氏东风"),
    JAD("JAD", "JAD", "捷安达"),
    AUEX("AUEX", "AUEX", "澳货通"),
    SUBIDA("SUBIDA", "SUBIDA", "速必达物流"),
    SYJWDX("SYJWDX", "SYJWDX", "佳旺达物流"),
    ROYALMAIL("ROYALMAIL", "ROYALMAIL", "RoyalMail"),
    FZGJ("FZGJ", "FZGJ", "方舟国际速递"),
    XJ("XJ", "XJ", "新杰物流"),
    TNT("TNT", "TNT", "TNT国际快递"),
    SENDCN("SENDCN", "SENDCN", "速递中国"),
    EMSBG("EMSBG", "EMSBG", "EMS包裹"),
    DTWL("DTWL", "DTWL", "大田物流"),
    D4PX("D4PX", "D4PX", "递四方速递"),
    CJKOREAEXPRESS("CJKOREAEXPRESS", "CJKOREAEXPRESS", "大韩通运"),
    JGWL("JGWL", "JGWL", "景光物流"),
    CJEXPRESS("CJEXPRESS", "CJEXPRESS", "长江国际速递"),
    CITY56("CITY56", "CITY56", "城市映急"),
    CHT361("CHT361", "CHT361", "诚和通"),
    ONEEXPRESS("ONEEXPRESS", "ONEEXPRESS", "一速递"),
    XDEXPRESS("XDEXPRESS", "XDEXPRESS", "迅达速递"),
    BN("BN", "BN", "笨鸟国际"),
    NSF("NSF", "NSF", "新顺丰国际速递"),
    EPOST("EPOST", "EPOST", "韩国邮政"),
    JUMSTC("JUMSTC", "JUMSTC", "聚盟共建"),
    TAQBINJP("TAQBINJP", "TAQBINJP", "Yamato宅急便"),
    A4PX("A4PX", "A4PX", "转运四方"),
    DEKUN("DEKUN", "DEKUN", "德坤"),
    COE("COE", "COE", "COE东方快递"),
    DFWL("DFWL", "DFWL", "达发物流"),
    TZKY("TZKY", "TZKY", "铁中快运"),
    LYT("LYT", "LYT", "联运通物流"),
    DHLGLOBALLOGISTICS("DHLGLOBALLOGISTICS", "DHLGLOBALLOGISTICS", "DHL全球货运"),
    SWEDENPOST("SWEDENPOST", "SWEDENPOST", "瑞典邮政"),
    BEEBIRD("BEEBIRD", "BEEBIRD", "锋鸟物流"),
    CFWL("CFWL", "CFWL", "春风物流"),
    JYWL("JYWL", "JYWL", "佳怡物流"),
    XYJ("XYJ", "XYJ", "西邮寄"),
    LINGSONG("LINGSONG", "LINGSONG", "领送送"),
    SJA56("SJA56", "SJA56", "四季安物流"),
    WTP("WTP", "WTP", "微特派"),
    HAOXIANGWULIU("HAOXIANGWULIU", "HAOXIANGWULIU", "豪翔物流"),
    HCT("HCT", "HCT", "新竹物流HCT"),
    SUYD56("SUYD56", "SUYD56", "速邮达物流"),
    YBWL("YBWL", "YBWL", "优拜物流"),
    COSCO("COSCO", "COSCO", "中远e环球"),
    GOEX("GOEX", "GOEX", "时安达速递"),
    IADLYYZ("IADLYYZ", "IADLYYZ", "澳大利亚邮政"),
    LHTEX("LHTEX", "LHTEX", "联昊通"),
    LNET("LNET", "LNET", "新易泰"),
    WJWL("WJWL", "WJWL", "万家物流"),
    AX("AX", "AX", "安迅物流"),
    CCES("CCES", "CCES", "CCES/国通快递"),
    DPD("DPD", "DPD", "DPD"),
    FAMIPORT("FAMIPORT", "FAMIPORT", "全家快递"),
    HJWL("HJWL", "HJWL", "汇捷物流"),
    HNTLWL("HNTLWL", "HNTLWL", "泰联物流"),
    HQSY("HQSY", "HQSY", "环球速运"),
    JTEXPRESS("JTEXPRESS", "JTEXPRESS", "J&TExpress"),
    KEJIE("KEJIE", "KEJIE", "科捷物流"),
    KZWL("KZWL", "KZWL", "科紫物流"),
    MHKD("MHKD", "MHKD", "民航快递"),
    SHPOST("SHPOST", "SHPOST", "同城快寄"),
    SINGAPOREPOST("SINGAPOREPOST", "SINGAPOREPOST", "新加坡邮政"),
    SINGAPORESPEEDPOST("SINGAPORESPEEDPOST", "SINGAPORESPEEDPOST", "新加坡特快专递"),
    STSD("STSD", "STSD", "三态速递"),
    UPSEN("UPSEN", "UPSEN", "UPS-全球件"),
    WJK("WJK", "WJK", "万家康"),
    XCWL("XCWL", "XCWL", "迅驰物流"),
    XLOBO("XLOBO", "XLOBO", "贝海国际速递"),
    YYSD("YYSD", "YYSD", "优优速递")
    ;

    ExpressCodeEnum(String name, String index, String nameCh) {
        this.name = name;
        this.index = index;
        this.nameCh = nameCh;
    }

    public static String getName(String index) {
        for (ExpressCodeEnum c : ExpressCodeEnum.values()) {
            if (c.getIndex().equals(index)) {
                return c.getName();
            }
        }
        return null;
    }

    public static ExpressCodeEnum getEnum(String index) {
        for (ExpressCodeEnum c : ExpressCodeEnum.values()) {
            if (c.getIndex().equals(index)) {
                return c;
            }
        }
        return null;
    }

    public static String getNameCh(String index) {
        for (ExpressCodeEnum c : ExpressCodeEnum.values()) {
            if (c.getIndex().equals(index)) {
                return c.getNameCh();
            }
        }
        //当不存在枚举的时候返回code 相当于其他
        return index;
    }





    private String name;
    private String index;
    private String nameCh;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }

}
